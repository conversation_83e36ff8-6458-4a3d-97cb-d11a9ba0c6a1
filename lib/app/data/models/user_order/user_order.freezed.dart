// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserOrder _$UserOrderFromJson(Map<String, dynamic> json) {
  return _UserOrder.fromJson(json);
}

/// @nodoc
mixin _$UserOrder {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_code')
  String? get orderCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'amount')
  double? get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_status')
  PaymentStatus? get paymentStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'unified_order_type')
  UnifiedOrderType? get unifiedOrderType => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_status')
  OrdersStatus? get orderStatus => throw _privateConstructorUsedError;
  @JsonKey(name: 'delivered_on')
  DateTime? get deliveredOn => throw _privateConstructorUsedError;
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_method')
  String? get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_mode')
  String? get paymentMode => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_details')
  dynamic get paymentDetails => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_paid')
  bool? get isPaid => throw _privateConstructorUsedError;
  @JsonKey(name: 'invoice_number')
  String? get invoiceNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'invoice_url')
  String? get invoiceUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'financial_year')
  String? get financialYear => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_invoice_sent')
  bool? get isInvoiceSent => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_details')
  UserEntity? get userDetails => throw _privateConstructorUsedError;
  @JsonKey(name: 'failure_reason')
  dynamic get failureReason => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_cts')
  double? get totalCts => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_amount')
  double? get totalAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipment_id')
  String? get shippingId => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipment_price')
  double? get shippingPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'tax_price')
  double? get taxPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'grand_total')
  double? get grandTotal => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_per_carat')
  double? get pricePerCarat => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_details')
  BuyRequest? get buyRequestDetails => throw _privateConstructorUsedError;
  @JsonKey(name: 'return_orders')
  List<ReturnOrder>? get returnOrders => throw _privateConstructorUsedError;
  @JsonKey(name: 'jewellery_array')
  List<JewelleryArray>? get jewelleryArray =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'melee_array')
  List<MeleeArray>? get meleeArray => throw _privateConstructorUsedError;
  @JsonKey(name: 'awb_number')
  String? get awbNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'authorize_payment_details')
  dynamic get authorizePaymentDetails => throw _privateConstructorUsedError;
  @JsonKey(name: 'courier_company')
  String? get courierCompany => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserOrderCopyWith<UserOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserOrderCopyWith<$Res> {
  factory $UserOrderCopyWith(UserOrder value, $Res Function(UserOrder) then) =
      _$UserOrderCopyWithImpl<$Res, UserOrder>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'order_code') String? orderCode,
      @JsonKey(name: 'amount') double? amount,
      @JsonKey(name: 'payment_status') PaymentStatus? paymentStatus,
      @JsonKey(name: 'unified_order_type') UnifiedOrderType? unifiedOrderType,
      @JsonKey(name: 'order_status') OrdersStatus? orderStatus,
      @JsonKey(name: 'delivered_on') DateTime? deliveredOn,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'payment_method') String? paymentMethod,
      @JsonKey(name: 'payment_mode') String? paymentMode,
      @JsonKey(name: 'payment_details') dynamic paymentDetails,
      @JsonKey(name: 'is_paid') bool? isPaid,
      @JsonKey(name: 'invoice_number') String? invoiceNumber,
      @JsonKey(name: 'invoice_url') String? invoiceUrl,
      @JsonKey(name: 'financial_year') String? financialYear,
      @JsonKey(name: 'is_invoice_sent') bool? isInvoiceSent,
      @JsonKey(name: 'user_details') UserEntity? userDetails,
      @JsonKey(name: 'failure_reason') dynamic failureReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'total_cts') double? totalCts,
      @JsonKey(name: 'total_amount') double? totalAmount,
      @JsonKey(name: 'shipment_id') String? shippingId,
      @JsonKey(name: 'shipment_price') double? shippingPrice,
      @JsonKey(name: 'tax_price') double? taxPrice,
      @JsonKey(name: 'grand_total') double? grandTotal,
      @JsonKey(name: 'price_per_carat') double? pricePerCarat,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'buy_request_details') BuyRequest? buyRequestDetails,
      @JsonKey(name: 'return_orders') List<ReturnOrder>? returnOrders,
      @JsonKey(name: 'jewellery_array') List<JewelleryArray>? jewelleryArray,
      @JsonKey(name: 'melee_array') List<MeleeArray>? meleeArray,
      @JsonKey(name: 'awb_number') String? awbNumber,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'authorize_payment_details')
      dynamic authorizePaymentDetails,
      @JsonKey(name: 'courier_company') String? courierCompany});

  $IngAddressCopyWith<$Res>? get billingAddress;
  $IngAddressCopyWith<$Res>? get shippingAddress;
  $UserEntityCopyWith<$Res>? get userDetails;
  $BuyRequestCopyWith<$Res>? get buyRequestDetails;
}

/// @nodoc
class _$UserOrderCopyWithImpl<$Res, $Val extends UserOrder>
    implements $UserOrderCopyWith<$Res> {
  _$UserOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? buyRequestId = freezed,
    Object? orderCode = freezed,
    Object? amount = freezed,
    Object? paymentStatus = freezed,
    Object? unifiedOrderType = freezed,
    Object? orderStatus = freezed,
    Object? deliveredOn = freezed,
    Object? billingAddress = freezed,
    Object? shippingAddress = freezed,
    Object? paymentMethod = freezed,
    Object? paymentMode = freezed,
    Object? paymentDetails = freezed,
    Object? isPaid = freezed,
    Object? invoiceNumber = freezed,
    Object? invoiceUrl = freezed,
    Object? financialYear = freezed,
    Object? isInvoiceSent = freezed,
    Object? userDetails = freezed,
    Object? failureReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? totalCts = freezed,
    Object? totalAmount = freezed,
    Object? shippingId = freezed,
    Object? shippingPrice = freezed,
    Object? taxPrice = freezed,
    Object? grandTotal = freezed,
    Object? pricePerCarat = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? buyRequestDetails = freezed,
    Object? returnOrders = freezed,
    Object? jewelleryArray = freezed,
    Object? meleeArray = freezed,
    Object? awbNumber = freezed,
    Object? type = freezed,
    Object? authorizePaymentDetails = freezed,
    Object? courierCompany = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderCode: freezed == orderCode
          ? _value.orderCode
          : orderCode // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus?,
      unifiedOrderType: freezed == unifiedOrderType
          ? _value.unifiedOrderType
          : unifiedOrderType // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderType?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrdersStatus?,
      deliveredOn: freezed == deliveredOn
          ? _value.deliveredOn
          : deliveredOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMode: freezed == paymentMode
          ? _value.paymentMode
          : paymentMode // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isPaid: freezed == isPaid
          ? _value.isPaid
          : isPaid // ignore: cast_nullable_to_non_nullable
              as bool?,
      invoiceNumber: freezed == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceUrl: freezed == invoiceUrl
          ? _value.invoiceUrl
          : invoiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      financialYear: freezed == financialYear
          ? _value.financialYear
          : financialYear // ignore: cast_nullable_to_non_nullable
              as String?,
      isInvoiceSent: freezed == isInvoiceSent
          ? _value.isInvoiceSent
          : isInvoiceSent // ignore: cast_nullable_to_non_nullable
              as bool?,
      userDetails: freezed == userDetails
          ? _value.userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as UserEntity?,
      failureReason: freezed == failureReason
          ? _value.failureReason
          : failureReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      totalCts: freezed == totalCts
          ? _value.totalCts
          : totalCts // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      shippingId: freezed == shippingId
          ? _value.shippingId
          : shippingId // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingPrice: freezed == shippingPrice
          ? _value.shippingPrice
          : shippingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      taxPrice: freezed == taxPrice
          ? _value.taxPrice
          : taxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      grandTotal: freezed == grandTotal
          ? _value.grandTotal
          : grandTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as double?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      buyRequestDetails: freezed == buyRequestDetails
          ? _value.buyRequestDetails
          : buyRequestDetails // ignore: cast_nullable_to_non_nullable
              as BuyRequest?,
      returnOrders: freezed == returnOrders
          ? _value.returnOrders
          : returnOrders // ignore: cast_nullable_to_non_nullable
              as List<ReturnOrder>?,
      jewelleryArray: freezed == jewelleryArray
          ? _value.jewelleryArray
          : jewelleryArray // ignore: cast_nullable_to_non_nullable
              as List<JewelleryArray>?,
      meleeArray: freezed == meleeArray
          ? _value.meleeArray
          : meleeArray // ignore: cast_nullable_to_non_nullable
              as List<MeleeArray>?,
      awbNumber: freezed == awbNumber
          ? _value.awbNumber
          : awbNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizePaymentDetails: freezed == authorizePaymentDetails
          ? _value.authorizePaymentDetails
          : authorizePaymentDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      courierCompany: freezed == courierCompany
          ? _value.courierCompany
          : courierCompany // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get billingAddress {
    if (_value.billingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.billingAddress!, (value) {
      return _then(_value.copyWith(billingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get shippingAddress {
    if (_value.shippingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.shippingAddress!, (value) {
      return _then(_value.copyWith(shippingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $UserEntityCopyWith<$Res>? get userDetails {
    if (_value.userDetails == null) {
      return null;
    }

    return $UserEntityCopyWith<$Res>(_value.userDetails!, (value) {
      return _then(_value.copyWith(userDetails: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $BuyRequestCopyWith<$Res>? get buyRequestDetails {
    if (_value.buyRequestDetails == null) {
      return null;
    }

    return $BuyRequestCopyWith<$Res>(_value.buyRequestDetails!, (value) {
      return _then(_value.copyWith(buyRequestDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserOrderImplCopyWith<$Res>
    implements $UserOrderCopyWith<$Res> {
  factory _$$UserOrderImplCopyWith(
          _$UserOrderImpl value, $Res Function(_$UserOrderImpl) then) =
      __$$UserOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'order_code') String? orderCode,
      @JsonKey(name: 'amount') double? amount,
      @JsonKey(name: 'payment_status') PaymentStatus? paymentStatus,
      @JsonKey(name: 'unified_order_type') UnifiedOrderType? unifiedOrderType,
      @JsonKey(name: 'order_status') OrdersStatus? orderStatus,
      @JsonKey(name: 'delivered_on') DateTime? deliveredOn,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'payment_method') String? paymentMethod,
      @JsonKey(name: 'payment_mode') String? paymentMode,
      @JsonKey(name: 'payment_details') dynamic paymentDetails,
      @JsonKey(name: 'is_paid') bool? isPaid,
      @JsonKey(name: 'invoice_number') String? invoiceNumber,
      @JsonKey(name: 'invoice_url') String? invoiceUrl,
      @JsonKey(name: 'financial_year') String? financialYear,
      @JsonKey(name: 'is_invoice_sent') bool? isInvoiceSent,
      @JsonKey(name: 'user_details') UserEntity? userDetails,
      @JsonKey(name: 'failure_reason') dynamic failureReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'total_cts') double? totalCts,
      @JsonKey(name: 'total_amount') double? totalAmount,
      @JsonKey(name: 'shipment_id') String? shippingId,
      @JsonKey(name: 'shipment_price') double? shippingPrice,
      @JsonKey(name: 'tax_price') double? taxPrice,
      @JsonKey(name: 'grand_total') double? grandTotal,
      @JsonKey(name: 'price_per_carat') double? pricePerCarat,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'buy_request_details') BuyRequest? buyRequestDetails,
      @JsonKey(name: 'return_orders') List<ReturnOrder>? returnOrders,
      @JsonKey(name: 'jewellery_array') List<JewelleryArray>? jewelleryArray,
      @JsonKey(name: 'melee_array') List<MeleeArray>? meleeArray,
      @JsonKey(name: 'awb_number') String? awbNumber,
      @JsonKey(name: 'type') String? type,
      @JsonKey(name: 'authorize_payment_details')
      dynamic authorizePaymentDetails,
      @JsonKey(name: 'courier_company') String? courierCompany});

  @override
  $IngAddressCopyWith<$Res>? get billingAddress;
  @override
  $IngAddressCopyWith<$Res>? get shippingAddress;
  @override
  $UserEntityCopyWith<$Res>? get userDetails;
  @override
  $BuyRequestCopyWith<$Res>? get buyRequestDetails;
}

/// @nodoc
class __$$UserOrderImplCopyWithImpl<$Res>
    extends _$UserOrderCopyWithImpl<$Res, _$UserOrderImpl>
    implements _$$UserOrderImplCopyWith<$Res> {
  __$$UserOrderImplCopyWithImpl(
      _$UserOrderImpl _value, $Res Function(_$UserOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? buyRequestId = freezed,
    Object? orderCode = freezed,
    Object? amount = freezed,
    Object? paymentStatus = freezed,
    Object? unifiedOrderType = freezed,
    Object? orderStatus = freezed,
    Object? deliveredOn = freezed,
    Object? billingAddress = freezed,
    Object? shippingAddress = freezed,
    Object? paymentMethod = freezed,
    Object? paymentMode = freezed,
    Object? paymentDetails = freezed,
    Object? isPaid = freezed,
    Object? invoiceNumber = freezed,
    Object? invoiceUrl = freezed,
    Object? financialYear = freezed,
    Object? isInvoiceSent = freezed,
    Object? userDetails = freezed,
    Object? failureReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? totalCts = freezed,
    Object? totalAmount = freezed,
    Object? shippingId = freezed,
    Object? shippingPrice = freezed,
    Object? taxPrice = freezed,
    Object? grandTotal = freezed,
    Object? pricePerCarat = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? buyRequestDetails = freezed,
    Object? returnOrders = freezed,
    Object? jewelleryArray = freezed,
    Object? meleeArray = freezed,
    Object? awbNumber = freezed,
    Object? type = freezed,
    Object? authorizePaymentDetails = freezed,
    Object? courierCompany = freezed,
  }) {
    return _then(_$UserOrderImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderCode: freezed == orderCode
          ? _value.orderCode
          : orderCode // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      paymentStatus: freezed == paymentStatus
          ? _value.paymentStatus
          : paymentStatus // ignore: cast_nullable_to_non_nullable
              as PaymentStatus?,
      unifiedOrderType: freezed == unifiedOrderType
          ? _value.unifiedOrderType
          : unifiedOrderType // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderType?,
      orderStatus: freezed == orderStatus
          ? _value.orderStatus
          : orderStatus // ignore: cast_nullable_to_non_nullable
              as OrdersStatus?,
      deliveredOn: freezed == deliveredOn
          ? _value.deliveredOn
          : deliveredOn // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      paymentMethod: freezed == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMode: freezed == paymentMode
          ? _value.paymentMode
          : paymentMode // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentDetails: freezed == paymentDetails
          ? _value.paymentDetails
          : paymentDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isPaid: freezed == isPaid
          ? _value.isPaid
          : isPaid // ignore: cast_nullable_to_non_nullable
              as bool?,
      invoiceNumber: freezed == invoiceNumber
          ? _value.invoiceNumber
          : invoiceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceUrl: freezed == invoiceUrl
          ? _value.invoiceUrl
          : invoiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      financialYear: freezed == financialYear
          ? _value.financialYear
          : financialYear // ignore: cast_nullable_to_non_nullable
              as String?,
      isInvoiceSent: freezed == isInvoiceSent
          ? _value.isInvoiceSent
          : isInvoiceSent // ignore: cast_nullable_to_non_nullable
              as bool?,
      userDetails: freezed == userDetails
          ? _value.userDetails
          : userDetails // ignore: cast_nullable_to_non_nullable
              as UserEntity?,
      failureReason: freezed == failureReason
          ? _value.failureReason
          : failureReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      totalCts: freezed == totalCts
          ? _value.totalCts
          : totalCts // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      shippingId: freezed == shippingId
          ? _value.shippingId
          : shippingId // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingPrice: freezed == shippingPrice
          ? _value.shippingPrice
          : shippingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      taxPrice: freezed == taxPrice
          ? _value.taxPrice
          : taxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      grandTotal: freezed == grandTotal
          ? _value.grandTotal
          : grandTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as double?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      buyRequestDetails: freezed == buyRequestDetails
          ? _value.buyRequestDetails
          : buyRequestDetails // ignore: cast_nullable_to_non_nullable
              as BuyRequest?,
      returnOrders: freezed == returnOrders
          ? _value._returnOrders
          : returnOrders // ignore: cast_nullable_to_non_nullable
              as List<ReturnOrder>?,
      jewelleryArray: freezed == jewelleryArray
          ? _value._jewelleryArray
          : jewelleryArray // ignore: cast_nullable_to_non_nullable
              as List<JewelleryArray>?,
      meleeArray: freezed == meleeArray
          ? _value._meleeArray
          : meleeArray // ignore: cast_nullable_to_non_nullable
              as List<MeleeArray>?,
      awbNumber: freezed == awbNumber
          ? _value.awbNumber
          : awbNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizePaymentDetails: freezed == authorizePaymentDetails
          ? _value.authorizePaymentDetails
          : authorizePaymentDetails // ignore: cast_nullable_to_non_nullable
              as dynamic,
      courierCompany: freezed == courierCompany
          ? _value.courierCompany
          : courierCompany // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserOrderImpl implements _UserOrder {
  const _$UserOrderImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'buy_request_id') this.buyRequestId,
      @JsonKey(name: 'order_code') this.orderCode,
      @JsonKey(name: 'amount') this.amount,
      @JsonKey(name: 'payment_status') this.paymentStatus,
      @JsonKey(name: 'unified_order_type') this.unifiedOrderType,
      @JsonKey(name: 'order_status') this.orderStatus,
      @JsonKey(name: 'delivered_on') this.deliveredOn,
      @JsonKey(name: 'billing_address') this.billingAddress,
      @JsonKey(name: 'shipping_address') this.shippingAddress,
      @JsonKey(name: 'payment_method') this.paymentMethod,
      @JsonKey(name: 'payment_mode') this.paymentMode,
      @JsonKey(name: 'payment_details') this.paymentDetails,
      @JsonKey(name: 'is_paid') this.isPaid,
      @JsonKey(name: 'invoice_number') this.invoiceNumber,
      @JsonKey(name: 'invoice_url') this.invoiceUrl,
      @JsonKey(name: 'financial_year') this.financialYear,
      @JsonKey(name: 'is_invoice_sent') this.isInvoiceSent,
      @JsonKey(name: 'user_details') this.userDetails,
      @JsonKey(name: 'failure_reason') this.failureReason,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'total_cts') this.totalCts,
      @JsonKey(name: 'total_amount') this.totalAmount,
      @JsonKey(name: 'shipment_id') this.shippingId,
      @JsonKey(name: 'shipment_price') this.shippingPrice,
      @JsonKey(name: 'tax_price') this.taxPrice,
      @JsonKey(name: 'grand_total') this.grandTotal,
      @JsonKey(name: 'price_per_carat') this.pricePerCarat,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'buy_request_details') this.buyRequestDetails,
      @JsonKey(name: 'return_orders') final List<ReturnOrder>? returnOrders,
      @JsonKey(name: 'jewellery_array')
      final List<JewelleryArray>? jewelleryArray,
      @JsonKey(name: 'melee_array') final List<MeleeArray>? meleeArray,
      @JsonKey(name: 'awb_number') this.awbNumber,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'authorize_payment_details') this.authorizePaymentDetails,
      @JsonKey(name: 'courier_company') this.courierCompany})
      : _returnOrders = returnOrders,
        _jewelleryArray = jewelleryArray,
        _meleeArray = meleeArray;

  factory _$UserOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserOrderImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'buy_request_id')
  final String? buyRequestId;
  @override
  @JsonKey(name: 'order_code')
  final String? orderCode;
  @override
  @JsonKey(name: 'amount')
  final double? amount;
  @override
  @JsonKey(name: 'payment_status')
  final PaymentStatus? paymentStatus;
  @override
  @JsonKey(name: 'unified_order_type')
  final UnifiedOrderType? unifiedOrderType;
  @override
  @JsonKey(name: 'order_status')
  final OrdersStatus? orderStatus;
  @override
  @JsonKey(name: 'delivered_on')
  final DateTime? deliveredOn;
  @override
  @JsonKey(name: 'billing_address')
  final IngAddress? billingAddress;
  @override
  @JsonKey(name: 'shipping_address')
  final IngAddress? shippingAddress;
  @override
  @JsonKey(name: 'payment_method')
  final String? paymentMethod;
  @override
  @JsonKey(name: 'payment_mode')
  final String? paymentMode;
  @override
  @JsonKey(name: 'payment_details')
  final dynamic paymentDetails;
  @override
  @JsonKey(name: 'is_paid')
  final bool? isPaid;
  @override
  @JsonKey(name: 'invoice_number')
  final String? invoiceNumber;
  @override
  @JsonKey(name: 'invoice_url')
  final String? invoiceUrl;
  @override
  @JsonKey(name: 'financial_year')
  final String? financialYear;
  @override
  @JsonKey(name: 'is_invoice_sent')
  final bool? isInvoiceSent;
  @override
  @JsonKey(name: 'user_details')
  final UserEntity? userDetails;
  @override
  @JsonKey(name: 'failure_reason')
  final dynamic failureReason;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'total_cts')
  final double? totalCts;
  @override
  @JsonKey(name: 'total_amount')
  final double? totalAmount;
  @override
  @JsonKey(name: 'shipment_id')
  final String? shippingId;
  @override
  @JsonKey(name: 'shipment_price')
  final double? shippingPrice;
  @override
  @JsonKey(name: 'tax_price')
  final double? taxPrice;
  @override
  @JsonKey(name: 'grand_total')
  final double? grandTotal;
  @override
  @JsonKey(name: 'price_per_carat')
  final double? pricePerCarat;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'buy_request_details')
  final BuyRequest? buyRequestDetails;
  final List<ReturnOrder>? _returnOrders;
  @override
  @JsonKey(name: 'return_orders')
  List<ReturnOrder>? get returnOrders {
    final value = _returnOrders;
    if (value == null) return null;
    if (_returnOrders is EqualUnmodifiableListView) return _returnOrders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<JewelleryArray>? _jewelleryArray;
  @override
  @JsonKey(name: 'jewellery_array')
  List<JewelleryArray>? get jewelleryArray {
    final value = _jewelleryArray;
    if (value == null) return null;
    if (_jewelleryArray is EqualUnmodifiableListView) return _jewelleryArray;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<MeleeArray>? _meleeArray;
  @override
  @JsonKey(name: 'melee_array')
  List<MeleeArray>? get meleeArray {
    final value = _meleeArray;
    if (value == null) return null;
    if (_meleeArray is EqualUnmodifiableListView) return _meleeArray;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'awb_number')
  final String? awbNumber;
  @override
  @JsonKey(name: 'type')
  final String? type;
  @override
  @JsonKey(name: 'authorize_payment_details')
  final dynamic authorizePaymentDetails;
  @override
  @JsonKey(name: 'courier_company')
  final String? courierCompany;

  @override
  String toString() {
    return 'UserOrder(id: $id, userId: $userId, buyRequestId: $buyRequestId, orderCode: $orderCode, amount: $amount, paymentStatus: $paymentStatus, unifiedOrderType: $unifiedOrderType, orderStatus: $orderStatus, deliveredOn: $deliveredOn, billingAddress: $billingAddress, shippingAddress: $shippingAddress, paymentMethod: $paymentMethod, paymentMode: $paymentMode, paymentDetails: $paymentDetails, isPaid: $isPaid, invoiceNumber: $invoiceNumber, invoiceUrl: $invoiceUrl, financialYear: $financialYear, isInvoiceSent: $isInvoiceSent, userDetails: $userDetails, failureReason: $failureReason, isActive: $isActive, deleted: $deleted, totalCts: $totalCts, totalAmount: $totalAmount, shippingId: $shippingId, shippingPrice: $shippingPrice, taxPrice: $taxPrice, grandTotal: $grandTotal, pricePerCarat: $pricePerCarat, createdAt: $createdAt, updatedAt: $updatedAt, buyRequestDetails: $buyRequestDetails, returnOrders: $returnOrders, jewelleryArray: $jewelleryArray, meleeArray: $meleeArray, awbNumber: $awbNumber, type: $type, authorizePaymentDetails: $authorizePaymentDetails, courierCompany: $courierCompany)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.buyRequestId, buyRequestId) ||
                other.buyRequestId == buyRequestId) &&
            (identical(other.orderCode, orderCode) ||
                other.orderCode == orderCode) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.paymentStatus, paymentStatus) ||
                other.paymentStatus == paymentStatus) &&
            (identical(other.unifiedOrderType, unifiedOrderType) ||
                other.unifiedOrderType == unifiedOrderType) &&
            (identical(other.orderStatus, orderStatus) ||
                other.orderStatus == orderStatus) &&
            (identical(other.deliveredOn, deliveredOn) ||
                other.deliveredOn == deliveredOn) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            (identical(other.shippingAddress, shippingAddress) ||
                other.shippingAddress == shippingAddress) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.paymentMode, paymentMode) ||
                other.paymentMode == paymentMode) &&
            const DeepCollectionEquality()
                .equals(other.paymentDetails, paymentDetails) &&
            (identical(other.isPaid, isPaid) || other.isPaid == isPaid) &&
            (identical(other.invoiceNumber, invoiceNumber) ||
                other.invoiceNumber == invoiceNumber) &&
            (identical(other.invoiceUrl, invoiceUrl) ||
                other.invoiceUrl == invoiceUrl) &&
            (identical(other.financialYear, financialYear) ||
                other.financialYear == financialYear) &&
            (identical(other.isInvoiceSent, isInvoiceSent) ||
                other.isInvoiceSent == isInvoiceSent) &&
            (identical(other.userDetails, userDetails) ||
                other.userDetails == userDetails) &&
            const DeepCollectionEquality()
                .equals(other.failureReason, failureReason) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.totalCts, totalCts) ||
                other.totalCts == totalCts) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.shippingId, shippingId) ||
                other.shippingId == shippingId) &&
            (identical(other.shippingPrice, shippingPrice) ||
                other.shippingPrice == shippingPrice) &&
            (identical(other.taxPrice, taxPrice) ||
                other.taxPrice == taxPrice) &&
            (identical(other.grandTotal, grandTotal) ||
                other.grandTotal == grandTotal) &&
            (identical(other.pricePerCarat, pricePerCarat) ||
                other.pricePerCarat == pricePerCarat) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.buyRequestDetails, buyRequestDetails) ||
                other.buyRequestDetails == buyRequestDetails) &&
            const DeepCollectionEquality()
                .equals(other._returnOrders, _returnOrders) &&
            const DeepCollectionEquality()
                .equals(other._jewelleryArray, _jewelleryArray) &&
            const DeepCollectionEquality()
                .equals(other._meleeArray, _meleeArray) &&
            (identical(other.awbNumber, awbNumber) ||
                other.awbNumber == awbNumber) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(
                other.authorizePaymentDetails, authorizePaymentDetails) &&
            (identical(other.courierCompany, courierCompany) ||
                other.courierCompany == courierCompany));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        buyRequestId,
        orderCode,
        amount,
        paymentStatus,
        unifiedOrderType,
        orderStatus,
        deliveredOn,
        billingAddress,
        shippingAddress,
        paymentMethod,
        paymentMode,
        const DeepCollectionEquality().hash(paymentDetails),
        isPaid,
        invoiceNumber,
        invoiceUrl,
        financialYear,
        isInvoiceSent,
        userDetails,
        const DeepCollectionEquality().hash(failureReason),
        isActive,
        deleted,
        totalCts,
        totalAmount,
        shippingId,
        shippingPrice,
        taxPrice,
        grandTotal,
        pricePerCarat,
        createdAt,
        updatedAt,
        buyRequestDetails,
        const DeepCollectionEquality().hash(_returnOrders),
        const DeepCollectionEquality().hash(_jewelleryArray),
        const DeepCollectionEquality().hash(_meleeArray),
        awbNumber,
        type,
        const DeepCollectionEquality().hash(authorizePaymentDetails),
        courierCompany
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserOrderImplCopyWith<_$UserOrderImpl> get copyWith =>
      __$$UserOrderImplCopyWithImpl<_$UserOrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserOrderImplToJson(
      this,
    );
  }
}

abstract class _UserOrder implements UserOrder {
  const factory _UserOrder(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'buy_request_id') final String? buyRequestId,
      @JsonKey(name: 'order_code') final String? orderCode,
      @JsonKey(name: 'amount') final double? amount,
      @JsonKey(name: 'payment_status') final PaymentStatus? paymentStatus,
      @JsonKey(name: 'unified_order_type')
      final UnifiedOrderType? unifiedOrderType,
      @JsonKey(name: 'order_status') final OrdersStatus? orderStatus,
      @JsonKey(name: 'delivered_on') final DateTime? deliveredOn,
      @JsonKey(name: 'billing_address') final IngAddress? billingAddress,
      @JsonKey(name: 'shipping_address') final IngAddress? shippingAddress,
      @JsonKey(name: 'payment_method') final String? paymentMethod,
      @JsonKey(name: 'payment_mode') final String? paymentMode,
      @JsonKey(name: 'payment_details') final dynamic paymentDetails,
      @JsonKey(name: 'is_paid') final bool? isPaid,
      @JsonKey(name: 'invoice_number') final String? invoiceNumber,
      @JsonKey(name: 'invoice_url') final String? invoiceUrl,
      @JsonKey(name: 'financial_year') final String? financialYear,
      @JsonKey(name: 'is_invoice_sent') final bool? isInvoiceSent,
      @JsonKey(name: 'user_details') final UserEntity? userDetails,
      @JsonKey(name: 'failure_reason') final dynamic failureReason,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'total_cts') final double? totalCts,
      @JsonKey(name: 'total_amount') final double? totalAmount,
      @JsonKey(name: 'shipment_id') final String? shippingId,
      @JsonKey(name: 'shipment_price') final double? shippingPrice,
      @JsonKey(name: 'tax_price') final double? taxPrice,
      @JsonKey(name: 'grand_total') final double? grandTotal,
      @JsonKey(name: 'price_per_carat') final double? pricePerCarat,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'buy_request_details') final BuyRequest? buyRequestDetails,
      @JsonKey(name: 'return_orders') final List<ReturnOrder>? returnOrders,
      @JsonKey(name: 'jewellery_array')
      final List<JewelleryArray>? jewelleryArray,
      @JsonKey(name: 'melee_array') final List<MeleeArray>? meleeArray,
      @JsonKey(name: 'awb_number') final String? awbNumber,
      @JsonKey(name: 'type') final String? type,
      @JsonKey(name: 'authorize_payment_details')
      final dynamic authorizePaymentDetails,
      @JsonKey(name: 'courier_company')
      final String? courierCompany}) = _$UserOrderImpl;

  factory _UserOrder.fromJson(Map<String, dynamic> json) =
      _$UserOrderImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId;
  @override
  @JsonKey(name: 'order_code')
  String? get orderCode;
  @override
  @JsonKey(name: 'amount')
  double? get amount;
  @override
  @JsonKey(name: 'payment_status')
  PaymentStatus? get paymentStatus;
  @override
  @JsonKey(name: 'unified_order_type')
  UnifiedOrderType? get unifiedOrderType;
  @override
  @JsonKey(name: 'order_status')
  OrdersStatus? get orderStatus;
  @override
  @JsonKey(name: 'delivered_on')
  DateTime? get deliveredOn;
  @override
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress;
  @override
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress;
  @override
  @JsonKey(name: 'payment_method')
  String? get paymentMethod;
  @override
  @JsonKey(name: 'payment_mode')
  String? get paymentMode;
  @override
  @JsonKey(name: 'payment_details')
  dynamic get paymentDetails;
  @override
  @JsonKey(name: 'is_paid')
  bool? get isPaid;
  @override
  @JsonKey(name: 'invoice_number')
  String? get invoiceNumber;
  @override
  @JsonKey(name: 'invoice_url')
  String? get invoiceUrl;
  @override
  @JsonKey(name: 'financial_year')
  String? get financialYear;
  @override
  @JsonKey(name: 'is_invoice_sent')
  bool? get isInvoiceSent;
  @override
  @JsonKey(name: 'user_details')
  UserEntity? get userDetails;
  @override
  @JsonKey(name: 'failure_reason')
  dynamic get failureReason;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'total_cts')
  double? get totalCts;
  @override
  @JsonKey(name: 'total_amount')
  double? get totalAmount;
  @override
  @JsonKey(name: 'shipment_id')
  String? get shippingId;
  @override
  @JsonKey(name: 'shipment_price')
  double? get shippingPrice;
  @override
  @JsonKey(name: 'tax_price')
  double? get taxPrice;
  @override
  @JsonKey(name: 'grand_total')
  double? get grandTotal;
  @override
  @JsonKey(name: 'price_per_carat')
  double? get pricePerCarat;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'buy_request_details')
  BuyRequest? get buyRequestDetails;
  @override
  @JsonKey(name: 'return_orders')
  List<ReturnOrder>? get returnOrders;
  @override
  @JsonKey(name: 'jewellery_array')
  List<JewelleryArray>? get jewelleryArray;
  @override
  @JsonKey(name: 'melee_array')
  List<MeleeArray>? get meleeArray;
  @override
  @JsonKey(name: 'awb_number')
  String? get awbNumber;
  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'authorize_payment_details')
  dynamic get authorizePaymentDetails;
  @override
  @JsonKey(name: 'courier_company')
  String? get courierCompany;
  @override
  @JsonKey(ignore: true)
  _$$UserOrderImplCopyWith<_$UserOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

JewelleryArray _$JewelleryArrayFromJson(Map<String, dynamic> json) {
  return _JewelleryArray.fromJson(json);
}

/// @nodoc
mixin _$JewelleryArray {
  @JsonKey(name: 'data')
  Jewellery? get data => throw _privateConstructorUsedError;
  @JsonKey(name: 'quantity')
  String? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'product_id')
  String? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: 'variant_id')
  String? get variantId => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_amount')
  String? get totalAmount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $JewelleryArrayCopyWith<JewelleryArray> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JewelleryArrayCopyWith<$Res> {
  factory $JewelleryArrayCopyWith(
          JewelleryArray value, $Res Function(JewelleryArray) then) =
      _$JewelleryArrayCopyWithImpl<$Res, JewelleryArray>;
  @useResult
  $Res call(
      {@JsonKey(name: 'data') Jewellery? data,
      @JsonKey(name: 'quantity') String? quantity,
      @JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'variant_id') String? variantId,
      @JsonKey(name: 'total_amount') String? totalAmount});
}

/// @nodoc
class _$JewelleryArrayCopyWithImpl<$Res, $Val extends JewelleryArray>
    implements $JewelleryArrayCopyWith<$Res> {
  _$JewelleryArrayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? quantity = freezed,
    Object? productId = freezed,
    Object? variantId = freezed,
    Object? totalAmount = freezed,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Jewellery?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      variantId: freezed == variantId
          ? _value.variantId
          : variantId // ignore: cast_nullable_to_non_nullable
              as String?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JewelleryArrayImplCopyWith<$Res>
    implements $JewelleryArrayCopyWith<$Res> {
  factory _$$JewelleryArrayImplCopyWith(_$JewelleryArrayImpl value,
          $Res Function(_$JewelleryArrayImpl) then) =
      __$$JewelleryArrayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'data') Jewellery? data,
      @JsonKey(name: 'quantity') String? quantity,
      @JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'variant_id') String? variantId,
      @JsonKey(name: 'total_amount') String? totalAmount});
}

/// @nodoc
class __$$JewelleryArrayImplCopyWithImpl<$Res>
    extends _$JewelleryArrayCopyWithImpl<$Res, _$JewelleryArrayImpl>
    implements _$$JewelleryArrayImplCopyWith<$Res> {
  __$$JewelleryArrayImplCopyWithImpl(
      _$JewelleryArrayImpl _value, $Res Function(_$JewelleryArrayImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? quantity = freezed,
    Object? productId = freezed,
    Object? variantId = freezed,
    Object? totalAmount = freezed,
  }) {
    return _then(_$JewelleryArrayImpl(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Jewellery?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      variantId: freezed == variantId
          ? _value.variantId
          : variantId // ignore: cast_nullable_to_non_nullable
              as String?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JewelleryArrayImpl implements _JewelleryArray {
  const _$JewelleryArrayImpl(
      {@JsonKey(name: 'data') this.data,
      @JsonKey(name: 'quantity') this.quantity,
      @JsonKey(name: 'product_id') this.productId,
      @JsonKey(name: 'variant_id') this.variantId,
      @JsonKey(name: 'total_amount') this.totalAmount});

  factory _$JewelleryArrayImpl.fromJson(Map<String, dynamic> json) =>
      _$$JewelleryArrayImplFromJson(json);

  @override
  @JsonKey(name: 'data')
  final Jewellery? data;
  @override
  @JsonKey(name: 'quantity')
  final String? quantity;
  @override
  @JsonKey(name: 'product_id')
  final String? productId;
  @override
  @JsonKey(name: 'variant_id')
  final String? variantId;
  @override
  @JsonKey(name: 'total_amount')
  final String? totalAmount;

  @override
  String toString() {
    return 'JewelleryArray(data: $data, quantity: $quantity, productId: $productId, variantId: $variantId, totalAmount: $totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JewelleryArrayImpl &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.variantId, variantId) ||
                other.variantId == variantId) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, data, quantity, productId, variantId, totalAmount);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$JewelleryArrayImplCopyWith<_$JewelleryArrayImpl> get copyWith =>
      __$$JewelleryArrayImplCopyWithImpl<_$JewelleryArrayImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JewelleryArrayImplToJson(
      this,
    );
  }
}

abstract class _JewelleryArray implements JewelleryArray {
  const factory _JewelleryArray(
          {@JsonKey(name: 'data') final Jewellery? data,
          @JsonKey(name: 'quantity') final String? quantity,
          @JsonKey(name: 'product_id') final String? productId,
          @JsonKey(name: 'variant_id') final String? variantId,
          @JsonKey(name: 'total_amount') final String? totalAmount}) =
      _$JewelleryArrayImpl;

  factory _JewelleryArray.fromJson(Map<String, dynamic> json) =
      _$JewelleryArrayImpl.fromJson;

  @override
  @JsonKey(name: 'data')
  Jewellery? get data;
  @override
  @JsonKey(name: 'quantity')
  String? get quantity;
  @override
  @JsonKey(name: 'product_id')
  String? get productId;
  @override
  @JsonKey(name: 'variant_id')
  String? get variantId;
  @override
  @JsonKey(name: 'total_amount')
  String? get totalAmount;
  @override
  @JsonKey(ignore: true)
  _$$JewelleryArrayImplCopyWith<_$JewelleryArrayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MeleeArray _$MeleeArrayFromJson(Map<String, dynamic> json) {
  return _MeleeArray.fromJson(json);
}

/// @nodoc
mixin _$MeleeArray {
  @JsonKey(name: 'data')
  MeleeEntity? get data => throw _privateConstructorUsedError;
  @JsonKey(name: 'quantity')
  String? get quantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_amount')
  String? get totalAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_per_carat')
  String? get pricePerCarat => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $MeleeArrayCopyWith<MeleeArray> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MeleeArrayCopyWith<$Res> {
  factory $MeleeArrayCopyWith(
          MeleeArray value, $Res Function(MeleeArray) then) =
      _$MeleeArrayCopyWithImpl<$Res, MeleeArray>;
  @useResult
  $Res call(
      {@JsonKey(name: 'data') MeleeEntity? data,
      @JsonKey(name: 'quantity') String? quantity,
      @JsonKey(name: 'total_amount') String? totalAmount,
      @JsonKey(name: 'price_per_carat') String? pricePerCarat});
}

/// @nodoc
class _$MeleeArrayCopyWithImpl<$Res, $Val extends MeleeArray>
    implements $MeleeArrayCopyWith<$Res> {
  _$MeleeArrayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? quantity = freezed,
    Object? totalAmount = freezed,
    Object? pricePerCarat = freezed,
  }) {
    return _then(_value.copyWith(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MeleeEntity?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as String?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MeleeArrayImplCopyWith<$Res>
    implements $MeleeArrayCopyWith<$Res> {
  factory _$$MeleeArrayImplCopyWith(
          _$MeleeArrayImpl value, $Res Function(_$MeleeArrayImpl) then) =
      __$$MeleeArrayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'data') MeleeEntity? data,
      @JsonKey(name: 'quantity') String? quantity,
      @JsonKey(name: 'total_amount') String? totalAmount,
      @JsonKey(name: 'price_per_carat') String? pricePerCarat});
}

/// @nodoc
class __$$MeleeArrayImplCopyWithImpl<$Res>
    extends _$MeleeArrayCopyWithImpl<$Res, _$MeleeArrayImpl>
    implements _$$MeleeArrayImplCopyWith<$Res> {
  __$$MeleeArrayImplCopyWithImpl(
      _$MeleeArrayImpl _value, $Res Function(_$MeleeArrayImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = freezed,
    Object? quantity = freezed,
    Object? totalAmount = freezed,
    Object? pricePerCarat = freezed,
  }) {
    return _then(_$MeleeArrayImpl(
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as MeleeEntity?,
      quantity: freezed == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as String?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as String?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MeleeArrayImpl implements _MeleeArray {
  const _$MeleeArrayImpl(
      {@JsonKey(name: 'data') this.data,
      @JsonKey(name: 'quantity') this.quantity,
      @JsonKey(name: 'total_amount') this.totalAmount,
      @JsonKey(name: 'price_per_carat') this.pricePerCarat});

  factory _$MeleeArrayImpl.fromJson(Map<String, dynamic> json) =>
      _$$MeleeArrayImplFromJson(json);

  @override
  @JsonKey(name: 'data')
  final MeleeEntity? data;
  @override
  @JsonKey(name: 'quantity')
  final String? quantity;
  @override
  @JsonKey(name: 'total_amount')
  final String? totalAmount;
  @override
  @JsonKey(name: 'price_per_carat')
  final String? pricePerCarat;

  @override
  String toString() {
    return 'MeleeArray(data: $data, quantity: $quantity, totalAmount: $totalAmount, pricePerCarat: $pricePerCarat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MeleeArrayImpl &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.pricePerCarat, pricePerCarat) ||
                other.pricePerCarat == pricePerCarat));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, data, quantity, totalAmount, pricePerCarat);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MeleeArrayImplCopyWith<_$MeleeArrayImpl> get copyWith =>
      __$$MeleeArrayImplCopyWithImpl<_$MeleeArrayImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MeleeArrayImplToJson(
      this,
    );
  }
}

abstract class _MeleeArray implements MeleeArray {
  const factory _MeleeArray(
          {@JsonKey(name: 'data') final MeleeEntity? data,
          @JsonKey(name: 'quantity') final String? quantity,
          @JsonKey(name: 'total_amount') final String? totalAmount,
          @JsonKey(name: 'price_per_carat') final String? pricePerCarat}) =
      _$MeleeArrayImpl;

  factory _MeleeArray.fromJson(Map<String, dynamic> json) =
      _$MeleeArrayImpl.fromJson;

  @override
  @JsonKey(name: 'data')
  MeleeEntity? get data;
  @override
  @JsonKey(name: 'quantity')
  String? get quantity;
  @override
  @JsonKey(name: 'total_amount')
  String? get totalAmount;
  @override
  @JsonKey(name: 'price_per_carat')
  String? get pricePerCarat;
  @override
  @JsonKey(ignore: true)
  _$$MeleeArrayImplCopyWith<_$MeleeArrayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReturnOrder _$ReturnOrderFromJson(Map<String, dynamic> json) {
  return _ReturnOrder.fromJson(json);
}

/// @nodoc
mixin _$ReturnOrder {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_id')
  String? get vendorId => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_id')
  String? get adminId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  ReturnOrderStatus? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'refund_amount')
  double? get refundAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'received_amount')
  int? get receivedAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_accepted')
  bool? get isAccepted => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock')
  DiamondEntity? get stock => throw _privateConstructorUsedError;
  @JsonKey(name: 'order')
  UserOrder? get order => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken => throw _privateConstructorUsedError;
  @JsonKey(name: 'reason')
  String? get reason => throw _privateConstructorUsedError;
  @JsonKey(name: 'reject_reason')
  String? get rejectReason => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ReturnOrderCopyWith<ReturnOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReturnOrderCopyWith<$Res> {
  factory $ReturnOrderCopyWith(
          ReturnOrder value, $Res Function(ReturnOrder) then) =
      _$ReturnOrderCopyWithImpl<$Res, ReturnOrder>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') ReturnOrderStatus? status,
      @JsonKey(name: 'refund_amount') double? refundAmount,
      @JsonKey(name: 'received_amount') int? receivedAmount,
      @JsonKey(name: 'is_accepted') bool? isAccepted,
      @JsonKey(name: 'stock') DiamondEntity? stock,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'reject_reason') String? rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt});

  $UserOrderCopyWith<$Res>? get order;
}

/// @nodoc
class _$ReturnOrderCopyWithImpl<$Res, $Val extends ReturnOrder>
    implements $ReturnOrderCopyWith<$Res> {
  _$ReturnOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? adminId = freezed,
    Object? userId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? refundAmount = freezed,
    Object? receivedAmount = freezed,
    Object? isAccepted = freezed,
    Object? stock = freezed,
    Object? order = freezed,
    Object? isActionTaken = freezed,
    Object? reason = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ReturnOrderStatus?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivedAmount: freezed == receivedAmount
          ? _value.receivedAmount
          : receivedAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      isAccepted: freezed == isAccepted
          ? _value.isAccepted
          : isAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserOrderCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $UserOrderCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReturnOrderImplCopyWith<$Res>
    implements $ReturnOrderCopyWith<$Res> {
  factory _$$ReturnOrderImplCopyWith(
          _$ReturnOrderImpl value, $Res Function(_$ReturnOrderImpl) then) =
      __$$ReturnOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') ReturnOrderStatus? status,
      @JsonKey(name: 'refund_amount') double? refundAmount,
      @JsonKey(name: 'received_amount') int? receivedAmount,
      @JsonKey(name: 'is_accepted') bool? isAccepted,
      @JsonKey(name: 'stock') DiamondEntity? stock,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'reject_reason') String? rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt});

  @override
  $UserOrderCopyWith<$Res>? get order;
}

/// @nodoc
class __$$ReturnOrderImplCopyWithImpl<$Res>
    extends _$ReturnOrderCopyWithImpl<$Res, _$ReturnOrderImpl>
    implements _$$ReturnOrderImplCopyWith<$Res> {
  __$$ReturnOrderImplCopyWithImpl(
      _$ReturnOrderImpl _value, $Res Function(_$ReturnOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? adminId = freezed,
    Object? userId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? refundAmount = freezed,
    Object? receivedAmount = freezed,
    Object? isAccepted = freezed,
    Object? stock = freezed,
    Object? order = freezed,
    Object? isActionTaken = freezed,
    Object? reason = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ReturnOrderImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ReturnOrderStatus?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivedAmount: freezed == receivedAmount
          ? _value.receivedAmount
          : receivedAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      isAccepted: freezed == isAccepted
          ? _value.isAccepted
          : isAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReturnOrderImpl implements _ReturnOrder {
  const _$ReturnOrderImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'vendor_id') this.vendorId,
      @JsonKey(name: 'admin_id') this.adminId,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'refund_amount') this.refundAmount,
      @JsonKey(name: 'received_amount') this.receivedAmount,
      @JsonKey(name: 'is_accepted') this.isAccepted,
      @JsonKey(name: 'stock') this.stock,
      @JsonKey(name: 'order') this.order,
      @JsonKey(name: 'is_action_taken') this.isActionTaken,
      @JsonKey(name: 'reason') this.reason,
      @JsonKey(name: 'reject_reason') this.rejectReason,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt});

  factory _$ReturnOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReturnOrderImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'order_id')
  final String? orderId;
  @override
  @JsonKey(name: 'vendor_id')
  final String? vendorId;
  @override
  @JsonKey(name: 'admin_id')
  final String? adminId;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'status')
  final ReturnOrderStatus? status;
  @override
  @JsonKey(name: 'refund_amount')
  final double? refundAmount;
  @override
  @JsonKey(name: 'received_amount')
  final int? receivedAmount;
  @override
  @JsonKey(name: 'is_accepted')
  final bool? isAccepted;
  @override
  @JsonKey(name: 'stock')
  final DiamondEntity? stock;
  @override
  @JsonKey(name: 'order')
  final UserOrder? order;
  @override
  @JsonKey(name: 'is_action_taken')
  final bool? isActionTaken;
  @override
  @JsonKey(name: 'reason')
  final String? reason;
  @override
  @JsonKey(name: 'reject_reason')
  final String? rejectReason;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ReturnOrder(id: $id, orderId: $orderId, vendorId: $vendorId, adminId: $adminId, userId: $userId, stockId: $stockId, status: $status, refundAmount: $refundAmount, receivedAmount: $receivedAmount, isAccepted: $isAccepted, stock: $stock, order: $order, isActionTaken: $isActionTaken, reason: $reason, rejectReason: $rejectReason, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReturnOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.vendorId, vendorId) ||
                other.vendorId == vendorId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.refundAmount, refundAmount) ||
                other.refundAmount == refundAmount) &&
            (identical(other.receivedAmount, receivedAmount) ||
                other.receivedAmount == receivedAmount) &&
            (identical(other.isAccepted, isAccepted) ||
                other.isAccepted == isAccepted) &&
            (identical(other.stock, stock) || other.stock == stock) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.isActionTaken, isActionTaken) ||
                other.isActionTaken == isActionTaken) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.rejectReason, rejectReason) ||
                other.rejectReason == rejectReason) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        orderId,
        vendorId,
        adminId,
        userId,
        stockId,
        status,
        refundAmount,
        receivedAmount,
        isAccepted,
        stock,
        order,
        isActionTaken,
        reason,
        rejectReason,
        isActive,
        deleted,
        createdAt,
        updatedAt
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ReturnOrderImplCopyWith<_$ReturnOrderImpl> get copyWith =>
      __$$ReturnOrderImplCopyWithImpl<_$ReturnOrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReturnOrderImplToJson(
      this,
    );
  }
}

abstract class _ReturnOrder implements ReturnOrder {
  const factory _ReturnOrder(
          {@JsonKey(name: 'id') final String? id,
          @JsonKey(name: 'order_id') final String? orderId,
          @JsonKey(name: 'vendor_id') final String? vendorId,
          @JsonKey(name: 'admin_id') final String? adminId,
          @JsonKey(name: 'user_id') final String? userId,
          @JsonKey(name: 'stock_id') final String? stockId,
          @JsonKey(name: 'status') final ReturnOrderStatus? status,
          @JsonKey(name: 'refund_amount') final double? refundAmount,
          @JsonKey(name: 'received_amount') final int? receivedAmount,
          @JsonKey(name: 'is_accepted') final bool? isAccepted,
          @JsonKey(name: 'stock') final DiamondEntity? stock,
          @JsonKey(name: 'order') final UserOrder? order,
          @JsonKey(name: 'is_action_taken') final bool? isActionTaken,
          @JsonKey(name: 'reason') final String? reason,
          @JsonKey(name: 'reject_reason') final String? rejectReason,
          @JsonKey(name: 'is_active') final bool? isActive,
          @JsonKey(name: '_deleted') final bool? deleted,
          @JsonKey(name: 'createdAt') final DateTime? createdAt,
          @JsonKey(name: 'updatedAt') final DateTime? updatedAt}) =
      _$ReturnOrderImpl;

  factory _ReturnOrder.fromJson(Map<String, dynamic> json) =
      _$ReturnOrderImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'order_id')
  String? get orderId;
  @override
  @JsonKey(name: 'vendor_id')
  String? get vendorId;
  @override
  @JsonKey(name: 'admin_id')
  String? get adminId;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'status')
  ReturnOrderStatus? get status;
  @override
  @JsonKey(name: 'refund_amount')
  double? get refundAmount;
  @override
  @JsonKey(name: 'received_amount')
  int? get receivedAmount;
  @override
  @JsonKey(name: 'is_accepted')
  bool? get isAccepted;
  @override
  @JsonKey(name: 'stock')
  DiamondEntity? get stock;
  @override
  @JsonKey(name: 'order')
  UserOrder? get order;
  @override
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken;
  @override
  @JsonKey(name: 'reason')
  String? get reason;
  @override
  @JsonKey(name: 'reject_reason')
  String? get rejectReason;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ReturnOrderImplCopyWith<_$ReturnOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IngAddress _$IngAddressFromJson(Map<String, dynamic> json) {
  return _IngAddress.fromJson(json);
}

/// @nodoc
mixin _$IngAddress {
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'state')
  String? get state => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'zip_code')
  String? get zipCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'first_name')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'full_name')
  String? get fullName => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo => throw _privateConstructorUsedError;
  @JsonKey(name: 'street')
  String? get street => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IngAddressCopyWith<IngAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IngAddressCopyWith<$Res> {
  factory $IngAddressCopyWith(
          IngAddress value, $Res Function(IngAddress) then) =
      _$IngAddressCopyWithImpl<$Res, IngAddress>;
  @useResult
  $Res call(
      {@JsonKey(name: 'city') String? city,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'street') String? street});
}

/// @nodoc
class _$IngAddressCopyWithImpl<$Res, $Val extends IngAddress>
    implements $IngAddressCopyWith<$Res> {
  _$IngAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? city = freezed,
    Object? email = freezed,
    Object? state = freezed,
    Object? phone = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? fullName = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? street = freezed,
  }) {
    return _then(_value.copyWith(
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IngAddressImplCopyWith<$Res>
    implements $IngAddressCopyWith<$Res> {
  factory _$$IngAddressImplCopyWith(
          _$IngAddressImpl value, $Res Function(_$IngAddressImpl) then) =
      __$$IngAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'city') String? city,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'street') String? street});
}

/// @nodoc
class __$$IngAddressImplCopyWithImpl<$Res>
    extends _$IngAddressCopyWithImpl<$Res, _$IngAddressImpl>
    implements _$$IngAddressImplCopyWith<$Res> {
  __$$IngAddressImplCopyWithImpl(
      _$IngAddressImpl _value, $Res Function(_$IngAddressImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? city = freezed,
    Object? email = freezed,
    Object? state = freezed,
    Object? phone = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? fullName = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? street = freezed,
  }) {
    return _then(_$IngAddressImpl(
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IngAddressImpl implements _IngAddress {
  const _$IngAddressImpl(
      {@JsonKey(name: 'city') this.city,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'state') this.state,
      @JsonKey(name: 'phone') this.phone,
      @JsonKey(name: 'country') this.country,
      @JsonKey(name: 'zip_code') this.zipCode,
      @JsonKey(name: 'last_name') this.lastName,
      @JsonKey(name: 'first_name') this.firstName,
      @JsonKey(name: 'full_name') this.fullName,
      @JsonKey(name: 'address_line_one') this.addressLineOne,
      @JsonKey(name: 'address_line_two') this.addressLineTwo,
      @JsonKey(name: 'street') this.street});

  factory _$IngAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$IngAddressImplFromJson(json);

  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'state')
  final String? state;
  @override
  @JsonKey(name: 'phone')
  final String? phone;
  @override
  @JsonKey(name: 'country')
  final String? country;
  @override
  @JsonKey(name: 'zip_code')
  final String? zipCode;
  @override
  @JsonKey(name: 'last_name')
  final String? lastName;
  @override
  @JsonKey(name: 'first_name')
  final String? firstName;
  @override
  @JsonKey(name: 'full_name')
  final String? fullName;
  @override
  @JsonKey(name: 'address_line_one')
  final String? addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  final String? addressLineTwo;
  @override
  @JsonKey(name: 'street')
  final String? street;

  @override
  String toString() {
    return 'IngAddress(city: $city, email: $email, state: $state, phone: $phone, country: $country, zipCode: $zipCode, lastName: $lastName, firstName: $firstName, fullName: $fullName, addressLineOne: $addressLineOne, addressLineTwo: $addressLineTwo, street: $street)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IngAddressImpl &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.addressLineOne, addressLineOne) ||
                other.addressLineOne == addressLineOne) &&
            (identical(other.addressLineTwo, addressLineTwo) ||
                other.addressLineTwo == addressLineTwo) &&
            (identical(other.street, street) || other.street == street));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      city,
      email,
      state,
      phone,
      country,
      zipCode,
      lastName,
      firstName,
      fullName,
      addressLineOne,
      addressLineTwo,
      street);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      __$$IngAddressImplCopyWithImpl<_$IngAddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IngAddressImplToJson(
      this,
    );
  }
}

abstract class _IngAddress implements IngAddress {
  const factory _IngAddress(
      {@JsonKey(name: 'city') final String? city,
      @JsonKey(name: 'email') final String? email,
      @JsonKey(name: 'state') final String? state,
      @JsonKey(name: 'phone') final String? phone,
      @JsonKey(name: 'country') final String? country,
      @JsonKey(name: 'zip_code') final String? zipCode,
      @JsonKey(name: 'last_name') final String? lastName,
      @JsonKey(name: 'first_name') final String? firstName,
      @JsonKey(name: 'full_name') final String? fullName,
      @JsonKey(name: 'address_line_one') final String? addressLineOne,
      @JsonKey(name: 'address_line_two') final String? addressLineTwo,
      @JsonKey(name: 'street') final String? street}) = _$IngAddressImpl;

  factory _IngAddress.fromJson(Map<String, dynamic> json) =
      _$IngAddressImpl.fromJson;

  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'state')
  String? get state;
  @override
  @JsonKey(name: 'phone')
  String? get phone;
  @override
  @JsonKey(name: 'country')
  String? get country;
  @override
  @JsonKey(name: 'zip_code')
  String? get zipCode;
  @override
  @JsonKey(name: 'last_name')
  String? get lastName;
  @override
  @JsonKey(name: 'first_name')
  String? get firstName;
  @override
  @JsonKey(name: 'full_name')
  String? get fullName;
  @override
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo;
  @override
  @JsonKey(name: 'street')
  String? get street;
  @override
  @JsonKey(ignore: true)
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
