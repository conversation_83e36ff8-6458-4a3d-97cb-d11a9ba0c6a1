import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/diamond_price/diamond_price.dart';
import 'package:diamond_company_app/app/data/models/jewellery/jewellery.dart';
import 'package:diamond_company_app/app/data/models/melee/melee_entity.dart';
import 'package:diamond_company_app/app/data/models/supplier/supplier_entity.dart';
import 'package:isar/isar.dart';

part 'cart_item.g.dart';

enum CartItemType { diamond, jewellery, melee }

@collection
class CartItem {
  CartItem({
    this.id = 0,
    required this.type,
    this.diamond,
    this.jewellery,
    this.melee,
    this.addedOn,
    this.quantity = 1,
    this.available = true,
  });

  final int id;

  final CartItemType type;

  DiamondEntity? diamond;

  Jewellery? jewellery;

  MeleeEntity? melee;

  final DateTime? addedOn;

  final bool available;

  int quantity;
}
