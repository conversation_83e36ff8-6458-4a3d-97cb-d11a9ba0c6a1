import 'package:diamond_company_app/app/data/local/db/app_isar.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/jewellery/jewellery.dart';
import 'package:diamond_company_app/app/data/models/melee/melee_entity.dart';
import 'package:diamond_company_app/app/utils/melee_ext.dart';
import 'package:isar/isar.dart';

class CartItemHelper {
  static final Isar _store = AppIsar.instance.store;

  static List<CartItem> get({
    int? offset,
    int? limit,
  }) =>
      _store.cartItems.where().sortByAddedOnDesc().findAll(
            offset: offset,
            limit: limit,
          );

  static int count() => _store.cartItems.count();

  /// add diamond
  static void addDiamond(DiamondEntity diamond) {
    _store.write(
      (Isar isar) {
        isar.cartItems.put(
          CartItem(
            id: AppIsar.instance.store.cartItems.autoIncrement(),
            type: CartItemType.diamond,
            diamond: diamond,
            addedOn: DateTime.now().toLocal(),
          ),
        );
      },
    );
  }

  /// add jewelery
  static void addJewelery(Jewellery jewellery) {
    _store.write(
      (Isar isar) {
        isar.cartItems.put(
          CartItem(
            id: AppIsar.instance.store.cartItems.autoIncrement(),
            type: CartItemType.jewellery,
            jewellery: jewellery,
            addedOn: DateTime.now().toLocal(),
          ),
        );
      },
    );
  }

  /// add melee
  static void addMelee(MeleeEntity melee) {
    melee.id = melee.uniqueId;
    _store.write(
      (Isar isar) {
        isar.cartItems.put(
          CartItem(
            id: AppIsar.instance.store.cartItems.autoIncrement(),
            type: CartItemType.melee,
            melee: melee,
            addedOn: DateTime.now().toLocal(),
          ),
        );
      },
    );
  }

  /// remove diamond
  static void removeDiamond(DiamondEntity diamond) {
    _store.write((Isar isar) {
      isar.cartItems
          .where()
          .diamond((q) => q
              .stockIdEqualTo(
                diamond.stockId,
                caseSensitive: false,
              )
              .and()
              .certificateNumberEqualTo(
                diamond.certificateNumber,
                caseSensitive: false,
              ))
          .deleteAll();
    });
  }

  /// remove Jewelery
  static void removeJewelery(Jewellery jewellery) {
    _store.write((Isar isar) {
      isar.cartItems
          .where()
          .jewellery((q) => q.selectedVariant(
                (q) => q.idEqualTo(
                  jewellery.selectedVariant?.id,
                  caseSensitive: false,
                ),
              ))
          .deleteAll();
    });
  }

  /// remove Melee
  static void removeMelee(MeleeEntity melee) {
    _store.write((Isar isar) {
      isar.cartItems
          .where()
          .melee(
            (q) => q.idEqualTo(
              melee.uniqueId,
              caseSensitive: false,
            ),
          )
          .deleteAll();
    });
  }

  /// remove all diamond
  static void removeAllDiamond() {
    _store.write((Isar isar) {
      isar.cartItems.where().typeEqualTo(CartItemType.diamond).deleteAll();
    });
  }

  /// remove all melee
  static void removeAllMelee() {
    _store.write((Isar isar) {
      isar.cartItems.where().typeEqualTo(CartItemType.melee).deleteAll();
    });
  }

  /// remove all jewellery
  static void removeAllJewellery() {
    _store.write((Isar isar) {
      isar.cartItems.where().typeEqualTo(CartItemType.jewellery).deleteAll();
    });
  }

  /// remove all
  static void removeAll() {
    _store.write((isar) {
      isar.cartItems.clear();
    });
  }

  /// is diamond added
  static bool isDiamondAdded(DiamondEntity diamond) {
    int count = _store.cartItems
        .where()
        .diamond((q) => q
            .stockIdEqualTo(
              diamond.stockId,
              caseSensitive: false,
            )
            .or()
            .certificateNumberEqualTo(
              diamond.certificateNumber,
              caseSensitive: false,
            ))
        .count();

    return count > 0;
  }

  /// is Jewelery added
  static bool isJeweleryAdded(Jewellery jewellery) {
    int count = _store.cartItems
        .where()
        .jewellery((q) => q.selectedVariant(
              (q) => q.idEqualTo(
                jewellery.selectedVariant?.id,
                caseSensitive: false,
              ),
            ))
        .count();

    return count > 0;
  }

  /// is melee added
  static bool isMeleeAdded(MeleeEntity melee) {
    int count = _store.cartItems
        .where()
        .melee(
          (q) => q.idEqualTo(
            melee.uniqueId,
            caseSensitive: false,
          ),
        )
        .count();

    return count > 0;
  }
}
