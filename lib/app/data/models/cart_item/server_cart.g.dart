// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'server_cart.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ServerCartImpl _$$ServerCartImplFromJson(Map<String, dynamic> json) =>
    _$ServerCartImpl(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      type: json['type'] as String?,
      diamondId: json['diamond_id'] as String?,
      jewelryId: json['jewellery_id'] as String?,
      melleeId: json['mellee_id'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      quantity: (json['quantity'] as num?)?.toInt(),
      isActive: json['is_active'] as bool?,
      deleted: json['_deleted'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ServerCartImplToJson(_$ServerCartImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'type': instance.type,
      'diamond_id': instance.diamondId,
      'jewellery_id': instance.jewelryId,
      'mellee_id': instance.melleeId,
      'data': instance.data,
      'quantity': instance.quantity,
      'is_active': instance.isActive,
      '_deleted': instance.deleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      adminId: json['admin_id'] as String?,
      stockId: json['stock_id'] as String?,
      vendorId: json['vendor_id'] as String?,
      certificateNumber: json['certificate_number'] as String?,
      stock: json['stock'] == null
          ? null
          : DiamondEntity.fromJson(json['stock'] as Map<String, dynamic>),
      melee: json['melee'] == null
          ? null
          : MeleeEntity.fromJson(json['melee'] as Map<String, dynamic>),
      jewelry: json['jewelry'] == null
          ? null
          : Jewellery.fromJson(json['jewelry'] as Map<String, dynamic>),
      productId: json['product_id'] as String?,
      selectedVariant: json['selected_variant'] == null
          ? null
          : Variant.fromJson(json['selected_variant'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'admin_id': instance.adminId,
      'stock_id': instance.stockId,
      'vendor_id': instance.vendorId,
      'certificate_number': instance.certificateNumber,
      'stock': instance.stock,
      'melee': instance.melee,
      'jewelry': instance.jewelry,
      'product_id': instance.productId,
      'selected_variant': instance.selectedVariant,
    };
