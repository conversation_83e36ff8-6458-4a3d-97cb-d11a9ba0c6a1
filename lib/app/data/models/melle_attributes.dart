import 'package:freezed_annotation/freezed_annotation.dart';

part 'melle_attributes.freezed.dart';
part 'melle_attributes.g.dart';

enum MeasurementType { seiveSize, milimeter, pointer }

@freezed
class MelleAttributes with _$MelleAttributes {
  const factory MelleAttributes({
    @JsonKey(name: 'fancy_color') List<String>? fancyColor,
    @Json<PERSON><PERSON>(name: 'fancy_intensity') List<String>? fancyIntensity,
    @J<PERSON><PERSON><PERSON>(name: 'fancy_overtone') List<String>? fancyOvertone,
    @J<PERSON><PERSON><PERSON>(name: 'white_color') List<String>? whiteColor,
    @J<PERSON><PERSON><PERSON>(name: 'seive_size') List<String>? seiveSize,
    @J<PERSON><PERSON><PERSON>(name: 'milimeter') List<String>? milimeter,
    @Json<PERSON><PERSON>(name: 'pointer') List<String>? pointer,
    @J<PERSON><PERSON>ey(name: 'clarity') List<String>? clarity,
    @Json<PERSON>ey(name: 'shape') List<String>? shape,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'max_quantity') int? maxQuantity,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'per_diamond_caret') double? perDiamondCaret,
    @J<PERSON><PERSON><PERSON>(name: 'price_per_caet') double? pricePerCaret,
    @Json<PERSON>ey(name: 'total_price') double? totalPrice,
    @JsonKey(name: 'measurements') List<Measurement>? measurements,
  }) = _MelleAttributes;

  factory MelleAttributes.fromJson(Map<String, dynamic> json) =>
      _$MelleAttributesFromJson(json);
}

@freezed
class Measurement with _$Measurement {
  const factory Measurement({
    @JsonKey(name: 'sieve_size') String? sieveSize,
    @JsonKey(name: 'milimeter') String? milimeter,
    @JsonKey(name: 'pointer') String? pointer,
  }) = _Measurement;

  factory Measurement.fromJson(Map<String, dynamic> json) =>
      _$MeasurementFromJson(json);
}
