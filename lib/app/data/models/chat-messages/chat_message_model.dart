import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_message_model.freezed.dart';
part 'chat_message_model.g.dart';

@freezed
class ChatMessage with _$ChatMessage {
  const factory ChatMessage({
    @<PERSON><PERSON><PERSON><PERSON>(name: "_id") String? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: "room_id") String? roomId,
    @<PERSON><PERSON><PERSON><PERSON>(name: "message") String? message,
    @<PERSON><PERSON><PERSON><PERSON>(name: "type") String? type,
    @<PERSON><PERSON><PERSON><PERSON>(name: "media") List<Map<String, dynamic>>? media,
    @<PERSON><PERSON><PERSON><PERSON>(name: "from_user") bool? fromUser,
    @<PERSON><PERSON><PERSON><PERSON>(name: "_deleted") bool? deleted,
    @<PERSON><PERSON><PERSON><PERSON>(name: "createdAt") DateTime? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "updatedAt") DateTime? updatedAt,
    @J<PERSON><PERSON><PERSON>(name: "__v") int? v,
  }) = _ChatMessage;
  factory ChatMessage.fromJson(Map<String, dynamic> str) =>
      _$ChatMessageFromJson(str);
}
