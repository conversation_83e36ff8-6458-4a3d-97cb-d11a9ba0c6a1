// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hold_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HoldRequestImpl _$$HoldRequestImplFromJson(Map<String, dynamic> json) =>
    _$HoldRequestImpl(
      stockId: json['stock_id'] as String?,
      vendorId: json['vendor_id'] as String?,
      rejectReason: json['reject_reason'] as String?,
      isAvailable: json['is_available'] as bool?,
      isAccepting: json['is_accepting'] as bool?,
      isDeclining: json['is_declining'] as bool?,
      isActionTaken: json['is_action_taken'] as bool?,
      buyRequestId: json['buy_request_id'] as String?,
      stock: json['stock'] == null
          ? null
          : DiamondEntity.fromJson(json['stock'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$HoldRequestImplToJson(_$HoldRequestImpl instance) =>
    <String, dynamic>{
      'stock_id': instance.stockId,
      'vendor_id': instance.vendorId,
      'reject_reason': instance.rejectReason,
      'is_available': instance.isAvailable,
      'is_accepting': instance.isAccepting,
      'is_declining': instance.isDeclining,
      'is_action_taken': instance.isActionTaken,
      'buy_request_id': instance.buyRequestId,
      'stock': instance.stock,
    };
