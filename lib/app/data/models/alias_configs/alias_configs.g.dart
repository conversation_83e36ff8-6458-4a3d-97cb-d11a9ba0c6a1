// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alias_configs.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AliasConfigsImpl _$$AliasConfigsImplFromJson(Map<String, dynamic> json) =>
    _$AliasConfigsImpl(
      shapeAlias: (json['shapeAlias'] as Map<String, dynamic>?)?.map(
        (k, e) =>
            MapEntry(k, (e as List<dynamic>).map((e) => e as String).toList()),
      ),
      shapeDisplayNames: json['shapeDisplayNames'] == null
          ? null
          : ShapeDisplayNames.fromJson(
              json['shapeDisplayNames'] as Map<String, dynamic>),
      cutAlias: json['cutAlias'] == null
          ? null
          : CutAlias.fromJson(json['cutAlias'] as Map<String, dynamic>),
      cutDisplayNames: json['cutDisplayNames'] == null
          ? null
          : CutDisplayNames.fromJson(
              json['cutDisplayNames'] as Map<String, dynamic>),
      intensityAlias: json['intensityAlias'] == null
          ? null
          : IntensityAlias.fromJson(
              json['intensityAlias'] as Map<String, dynamic>),
      intensityDisplayNames: json['intensityDisplayNames'] == null
          ? null
          : IntensityDisplayNames.fromJson(
              json['intensityDisplayNames'] as Map<String, dynamic>),
      fluorescenceColorAlias: json['fluorescenceColorAlias'] == null
          ? null
          : FluorescenceColorAlias.fromJson(
              json['fluorescenceColorAlias'] as Map<String, dynamic>),
      fluorescenceColorDisplayNames: json['fluorescenceColorDisplayNames'] ==
              null
          ? null
          : FluorescenceColorDisplayNames.fromJson(
              json['fluorescenceColorDisplayNames'] as Map<String, dynamic>),
      whiteColor: (json['whiteColor'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      fancyColor: (json['fancyColor'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$AliasConfigsImplToJson(_$AliasConfigsImpl instance) =>
    <String, dynamic>{
      'shapeAlias': instance.shapeAlias,
      'shapeDisplayNames': instance.shapeDisplayNames,
      'cutAlias': instance.cutAlias,
      'cutDisplayNames': instance.cutDisplayNames,
      'intensityAlias': instance.intensityAlias,
      'intensityDisplayNames': instance.intensityDisplayNames,
      'fluorescenceColorAlias': instance.fluorescenceColorAlias,
      'fluorescenceColorDisplayNames': instance.fluorescenceColorDisplayNames,
      'whiteColor': instance.whiteColor,
      'fancyColor': instance.fancyColor,
    };

_$CutAliasImpl _$$CutAliasImplFromJson(Map<String, dynamic> json) =>
    _$CutAliasImpl(
      id: (json['id'] as List<dynamic>?)?.map((e) => e as String).toList(),
      ex: (json['ex'] as List<dynamic>?)?.map((e) => e as String).toList(),
      vg: (json['vg'] as List<dynamic>?)?.map((e) => e as String).toList(),
      gd: (json['gd'] as List<dynamic>?)?.map((e) => e as String).toList(),
      f: (json['f'] as List<dynamic>?)?.map((e) => e as String).toList(),
      p: (json['p'] as List<dynamic>?)?.map((e) => e as String).toList(),
      na: (json['na'] as List<dynamic>?)?.map((e) => e as String?).toList(),
    );

Map<String, dynamic> _$$CutAliasImplToJson(_$CutAliasImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'ex': instance.ex,
      'vg': instance.vg,
      'gd': instance.gd,
      'f': instance.f,
      'p': instance.p,
      'na': instance.na,
    };

_$CutDisplayNamesImpl _$$CutDisplayNamesImplFromJson(
        Map<String, dynamic> json) =>
    _$CutDisplayNamesImpl(
      id: json['id'] as String?,
      ex: json['ex'] as String?,
      vg: json['vg'] as String?,
      gd: json['gd'] as String?,
      f: json['f'] as String?,
      p: json['p'] as String?,
      na: json['na'] as String?,
    );

Map<String, dynamic> _$$CutDisplayNamesImplToJson(
        _$CutDisplayNamesImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'ex': instance.ex,
      'vg': instance.vg,
      'gd': instance.gd,
      'f': instance.f,
      'p': instance.p,
      'na': instance.na,
    };

_$FluorescenceColorAliasImpl _$$FluorescenceColorAliasImplFromJson(
        Map<String, dynamic> json) =>
    _$FluorescenceColorAliasImpl(
      na: json['na'] as List<dynamic>?,
      blue: (json['blue'] as List<dynamic>?)?.map((e) => e as String).toList(),
      slt: (json['slt'] as List<dynamic>?)?.map((e) => e as String).toList(),
      vslt: (json['vslt'] as List<dynamic>?)?.map((e) => e as String).toList(),
      yellow:
          (json['yellow'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$$FluorescenceColorAliasImplToJson(
        _$FluorescenceColorAliasImpl instance) =>
    <String, dynamic>{
      'na': instance.na,
      'blue': instance.blue,
      'slt': instance.slt,
      'vslt': instance.vslt,
      'yellow': instance.yellow,
    };

_$FluorescenceColorDisplayNamesImpl
    _$$FluorescenceColorDisplayNamesImplFromJson(Map<String, dynamic> json) =>
        _$FluorescenceColorDisplayNamesImpl(
          na: json['na'] as String?,
          blue: json['blue'] as String?,
          slt: json['slt'] as String?,
          vslt: json['vslt'] as String?,
          yellow: json['yellow'] as String?,
        );

Map<String, dynamic> _$$FluorescenceColorDisplayNamesImplToJson(
        _$FluorescenceColorDisplayNamesImpl instance) =>
    <String, dynamic>{
      'na': instance.na,
      'blue': instance.blue,
      'slt': instance.slt,
      'vslt': instance.vslt,
      'yellow': instance.yellow,
    };

_$IntensityAliasImpl _$$IntensityAliasImplFromJson(Map<String, dynamic> json) =>
    _$IntensityAliasImpl(
      n: (json['n'] as List<dynamic>?)?.map((e) => e as String).toList(),
      vsl: (json['vsl'] as List<dynamic>?)?.map((e) => e as String).toList(),
      sl: (json['sl'] as List<dynamic>?)?.map((e) => e as String).toList(),
      f: (json['f'] as List<dynamic>?)?.map((e) => e as String).toList(),
      m: (json['m'] as List<dynamic>?)?.map((e) => e as String).toList(),
      s: (json['s'] as List<dynamic>?)?.map((e) => e as String).toList(),
      vs: (json['vs'] as List<dynamic>?)?.map((e) => e as String).toList(),
      na: (json['na'] as List<dynamic>?)?.map((e) => e as String?).toList(),
    );

Map<String, dynamic> _$$IntensityAliasImplToJson(
        _$IntensityAliasImpl instance) =>
    <String, dynamic>{
      'n': instance.n,
      'vsl': instance.vsl,
      'sl': instance.sl,
      'f': instance.f,
      'm': instance.m,
      's': instance.s,
      'vs': instance.vs,
      'na': instance.na,
    };

_$IntensityDisplayNamesImpl _$$IntensityDisplayNamesImplFromJson(
        Map<String, dynamic> json) =>
    _$IntensityDisplayNamesImpl(
      na: json['na'] as String?,
      vsl: json['vsl'] as String?,
      sl: json['sl'] as String?,
      f: json['f'] as String?,
      m: json['m'] as String?,
      s: json['s'] as String?,
      vs: json['vs'] as String?,
    );

Map<String, dynamic> _$$IntensityDisplayNamesImplToJson(
        _$IntensityDisplayNamesImpl instance) =>
    <String, dynamic>{
      'na': instance.na,
      'vsl': instance.vsl,
      'sl': instance.sl,
      'f': instance.f,
      'm': instance.m,
      's': instance.s,
      'vs': instance.vs,
    };

_$ShapeDisplayNamesImpl _$$ShapeDisplayNamesImplFromJson(
        Map<String, dynamic> json) =>
    _$ShapeDisplayNamesImpl(
      round: json['round'] as String?,
      pear: json['pear'] as String?,
      emerald: json['emerald'] as String?,
      trilliant: json['trilliant'] as String?,
      princess: json['princess'] as String?,
      marquise: json['marquise'] as String?,
      asscher: json['asscher'] as String?,
      cushion: json['cushion'] as String?,
      cushMod: json['cush_mod'] as String?,
      cushBrill: json['cush_brill'] as String?,
      heart: json['heart'] as String?,
      oval: json['oval'] as String?,
      radiant: json['radiant'] as String?,
      square: json['square'] as String?,
      euroCut: json['euro_cut'] as String?,
      oldMiner: json['old_miner'] as String?,
      briolette: json['briolette'] as String?,
      roseCut: json['rose_cut'] as String?,
      lozenge: json['lozenge'] as String?,
      baguette: json['baguette'] as String?,
      tBaguette: json['t_baguette'] as String?,
      halfMoon: json['half_moon'] as String?,
      flanders: json['flanders'] as String?,
      trapezoid: json['trapezoid'] as String?,
      bullets: json['bullets'] as String?,
      kite: json['kite'] as String?,
      shield: json['shield'] as String?,
      star: json['star'] as String?,
      pentagonal: json['pentagonal'] as String?,
      hexagonal: json['hexagonal'] as String?,
      octagonal: json['octagonal'] as String?,
    );

Map<String, dynamic> _$$ShapeDisplayNamesImplToJson(
        _$ShapeDisplayNamesImpl instance) =>
    <String, dynamic>{
      'round': instance.round,
      'pear': instance.pear,
      'emerald': instance.emerald,
      'trilliant': instance.trilliant,
      'princess': instance.princess,
      'marquise': instance.marquise,
      'asscher': instance.asscher,
      'cushion': instance.cushion,
      'cush_mod': instance.cushMod,
      'cush_brill': instance.cushBrill,
      'heart': instance.heart,
      'oval': instance.oval,
      'radiant': instance.radiant,
      'square': instance.square,
      'euro_cut': instance.euroCut,
      'old_miner': instance.oldMiner,
      'briolette': instance.briolette,
      'rose_cut': instance.roseCut,
      'lozenge': instance.lozenge,
      'baguette': instance.baguette,
      't_baguette': instance.tBaguette,
      'half_moon': instance.halfMoon,
      'flanders': instance.flanders,
      'trapezoid': instance.trapezoid,
      'bullets': instance.bullets,
      'kite': instance.kite,
      'shield': instance.shield,
      'star': instance.star,
      'pentagonal': instance.pentagonal,
      'hexagonal': instance.hexagonal,
      'octagonal': instance.octagonal,
    };
