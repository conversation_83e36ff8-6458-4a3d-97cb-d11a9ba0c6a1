// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'credit_limit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CreditLimitImpl _$$CreditLimitImplFromJson(Map<String, dynamic> json) =>
    _$CreditLimitImpl(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      credit: (json['credit'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$TypeEnumMap, json['type']),
      transactionType: $enumDecodeNullable(
          _$TransactionTypeEnumMap, json['transaction_type']),
      buyRequestId: json['buy_request_id'],
      isActive: json['is_active'] as bool?,
      deleted: json['_deleted'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$CreditLimitImplToJson(_$CreditLimitImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'credit': instance.credit,
      'type': _$TypeEnumMap[instance.type],
      'transaction_type': _$TransactionTypeEnumMap[instance.transactionType],
      'buy_request_id': instance.buyRequestId,
      'is_active': instance.isActive,
      '_deleted': instance.deleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$TypeEnumMap = {
  Type.CREATE_BUY_REQUEST: 'CREATE-BUY-REQUEST',
  Type.VERIFY: 'VERIFY',
};

const _$TransactionTypeEnumMap = {
  TransactionType.CREDIT: 'CREDIT',
  TransactionType.DEBIT: 'DEBIT',
};
