// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'buy_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BuyRequest _$BuyRequestFromJson(Map<String, dynamic> json) {
  return _BuyRequest.fromJson(json);
}

/// @nodoc
mixin _$BuyRequest {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_ids')
  List<StockId>? get stockIds => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_ids')
  List<String>? get vendorIds => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  BuyRequestStatus? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_by_id')
  String? get updatedById => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_mode')
  PaymentMode? get paymentMode => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipping_method')
  String? get shippingMethod => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'reject_reason')
  dynamic get rejectReason => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_cts')
  double? get totalCts => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_amount')
  double? get totalAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipment_id')
  String? get shippingId => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipment_price')
  double? get shippingPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'amount')
  double? get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'grand_total')
  double? get grandTotal => throw _privateConstructorUsedError;
  @JsonKey(name: 'tax_price')
  double? get taxPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_per_carat')
  double? get pricePerCarat => throw _privateConstructorUsedError;
  @JsonKey(name: 'user')
  User? get user => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_code')
  String? get orderCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'card_holder_name')
  String? get cardHolderName => throw _privateConstructorUsedError;
  @JsonKey(name: 'card_number')
  String? get cardNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'card_type')
  String? get cardType => throw _privateConstructorUsedError;
  @JsonKey(name: 'exp_date')
  String? get expDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'trans_id')
  String? get transId => throw _privateConstructorUsedError;
  @JsonKey(name: 'authorized_amount')
  double? get authorizedAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'type')
  BuyRequestType? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_capture_failed')
  bool? get isCaptureFailed => throw _privateConstructorUsedError;
  @JsonKey(name: 'tax')
  double? get tax => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $BuyRequestCopyWith<BuyRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyRequestCopyWith<$Res> {
  factory $BuyRequestCopyWith(
          BuyRequest value, $Res Function(BuyRequest) then) =
      _$BuyRequestCopyWithImpl<$Res, BuyRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_ids') List<StockId>? stockIds,
      @JsonKey(name: 'vendor_ids') List<String>? vendorIds,
      @JsonKey(name: 'status') BuyRequestStatus? status,
      @JsonKey(name: 'updated_by_id') String? updatedById,
      @JsonKey(name: 'payment_mode') PaymentMode? paymentMode,
      @JsonKey(name: 'shipping_method') String? shippingMethod,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'reject_reason') dynamic rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'total_cts') double? totalCts,
      @JsonKey(name: 'total_amount') double? totalAmount,
      @JsonKey(name: 'shipment_id') String? shippingId,
      @JsonKey(name: 'shipment_price') double? shippingPrice,
      @JsonKey(name: 'amount') double? amount,
      @JsonKey(name: 'grand_total') double? grandTotal,
      @JsonKey(name: 'tax_price') double? taxPrice,
      @JsonKey(name: 'price_per_carat') double? pricePerCarat,
      @JsonKey(name: 'user') User? user,
      @JsonKey(name: 'order_code') String? orderCode,
      @JsonKey(name: 'card_holder_name') String? cardHolderName,
      @JsonKey(name: 'card_number') String? cardNumber,
      @JsonKey(name: 'card_type') String? cardType,
      @JsonKey(name: 'exp_date') String? expDate,
      @JsonKey(name: 'trans_id') String? transId,
      @JsonKey(name: 'authorized_amount') double? authorizedAmount,
      @JsonKey(name: 'type') BuyRequestType? type,
      @JsonKey(name: 'is_capture_failed') bool? isCaptureFailed,
      @JsonKey(name: 'tax') double? tax});

  $IngAddressCopyWith<$Res>? get shippingAddress;
  $IngAddressCopyWith<$Res>? get billingAddress;
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$BuyRequestCopyWithImpl<$Res, $Val extends BuyRequest>
    implements $BuyRequestCopyWith<$Res> {
  _$BuyRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? stockIds = freezed,
    Object? vendorIds = freezed,
    Object? status = freezed,
    Object? updatedById = freezed,
    Object? paymentMode = freezed,
    Object? shippingMethod = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? totalCts = freezed,
    Object? totalAmount = freezed,
    Object? shippingId = freezed,
    Object? shippingPrice = freezed,
    Object? amount = freezed,
    Object? grandTotal = freezed,
    Object? taxPrice = freezed,
    Object? pricePerCarat = freezed,
    Object? user = freezed,
    Object? orderCode = freezed,
    Object? cardHolderName = freezed,
    Object? cardNumber = freezed,
    Object? cardType = freezed,
    Object? expDate = freezed,
    Object? transId = freezed,
    Object? authorizedAmount = freezed,
    Object? type = freezed,
    Object? isCaptureFailed = freezed,
    Object? tax = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockIds: freezed == stockIds
          ? _value.stockIds
          : stockIds // ignore: cast_nullable_to_non_nullable
              as List<StockId>?,
      vendorIds: freezed == vendorIds
          ? _value.vendorIds
          : vendorIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BuyRequestStatus?,
      updatedById: freezed == updatedById
          ? _value.updatedById
          : updatedById // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMode: freezed == paymentMode
          ? _value.paymentMode
          : paymentMode // ignore: cast_nullable_to_non_nullable
              as PaymentMode?,
      shippingMethod: freezed == shippingMethod
          ? _value.shippingMethod
          : shippingMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalCts: freezed == totalCts
          ? _value.totalCts
          : totalCts // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      shippingId: freezed == shippingId
          ? _value.shippingId
          : shippingId // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingPrice: freezed == shippingPrice
          ? _value.shippingPrice
          : shippingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      grandTotal: freezed == grandTotal
          ? _value.grandTotal
          : grandTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      taxPrice: freezed == taxPrice
          ? _value.taxPrice
          : taxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as double?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      orderCode: freezed == orderCode
          ? _value.orderCode
          : orderCode // ignore: cast_nullable_to_non_nullable
              as String?,
      cardHolderName: freezed == cardHolderName
          ? _value.cardHolderName
          : cardHolderName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNumber: freezed == cardNumber
          ? _value.cardNumber
          : cardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as String?,
      expDate: freezed == expDate
          ? _value.expDate
          : expDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transId: freezed == transId
          ? _value.transId
          : transId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizedAmount: freezed == authorizedAmount
          ? _value.authorizedAmount
          : authorizedAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as BuyRequestType?,
      isCaptureFailed: freezed == isCaptureFailed
          ? _value.isCaptureFailed
          : isCaptureFailed // ignore: cast_nullable_to_non_nullable
              as bool?,
      tax: freezed == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get shippingAddress {
    if (_value.shippingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.shippingAddress!, (value) {
      return _then(_value.copyWith(shippingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get billingAddress {
    if (_value.billingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.billingAddress!, (value) {
      return _then(_value.copyWith(billingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BuyRequestImplCopyWith<$Res>
    implements $BuyRequestCopyWith<$Res> {
  factory _$$BuyRequestImplCopyWith(
          _$BuyRequestImpl value, $Res Function(_$BuyRequestImpl) then) =
      __$$BuyRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_ids') List<StockId>? stockIds,
      @JsonKey(name: 'vendor_ids') List<String>? vendorIds,
      @JsonKey(name: 'status') BuyRequestStatus? status,
      @JsonKey(name: 'updated_by_id') String? updatedById,
      @JsonKey(name: 'payment_mode') PaymentMode? paymentMode,
      @JsonKey(name: 'shipping_method') String? shippingMethod,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'reject_reason') dynamic rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'total_cts') double? totalCts,
      @JsonKey(name: 'total_amount') double? totalAmount,
      @JsonKey(name: 'shipment_id') String? shippingId,
      @JsonKey(name: 'shipment_price') double? shippingPrice,
      @JsonKey(name: 'amount') double? amount,
      @JsonKey(name: 'grand_total') double? grandTotal,
      @JsonKey(name: 'tax_price') double? taxPrice,
      @JsonKey(name: 'price_per_carat') double? pricePerCarat,
      @JsonKey(name: 'user') User? user,
      @JsonKey(name: 'order_code') String? orderCode,
      @JsonKey(name: 'card_holder_name') String? cardHolderName,
      @JsonKey(name: 'card_number') String? cardNumber,
      @JsonKey(name: 'card_type') String? cardType,
      @JsonKey(name: 'exp_date') String? expDate,
      @JsonKey(name: 'trans_id') String? transId,
      @JsonKey(name: 'authorized_amount') double? authorizedAmount,
      @JsonKey(name: 'type') BuyRequestType? type,
      @JsonKey(name: 'is_capture_failed') bool? isCaptureFailed,
      @JsonKey(name: 'tax') double? tax});

  @override
  $IngAddressCopyWith<$Res>? get shippingAddress;
  @override
  $IngAddressCopyWith<$Res>? get billingAddress;
  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$BuyRequestImplCopyWithImpl<$Res>
    extends _$BuyRequestCopyWithImpl<$Res, _$BuyRequestImpl>
    implements _$$BuyRequestImplCopyWith<$Res> {
  __$$BuyRequestImplCopyWithImpl(
      _$BuyRequestImpl _value, $Res Function(_$BuyRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? stockIds = freezed,
    Object? vendorIds = freezed,
    Object? status = freezed,
    Object? updatedById = freezed,
    Object? paymentMode = freezed,
    Object? shippingMethod = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? totalCts = freezed,
    Object? totalAmount = freezed,
    Object? shippingId = freezed,
    Object? shippingPrice = freezed,
    Object? amount = freezed,
    Object? grandTotal = freezed,
    Object? taxPrice = freezed,
    Object? pricePerCarat = freezed,
    Object? user = freezed,
    Object? orderCode = freezed,
    Object? cardHolderName = freezed,
    Object? cardNumber = freezed,
    Object? cardType = freezed,
    Object? expDate = freezed,
    Object? transId = freezed,
    Object? authorizedAmount = freezed,
    Object? type = freezed,
    Object? isCaptureFailed = freezed,
    Object? tax = freezed,
  }) {
    return _then(_$BuyRequestImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockIds: freezed == stockIds
          ? _value._stockIds
          : stockIds // ignore: cast_nullable_to_non_nullable
              as List<StockId>?,
      vendorIds: freezed == vendorIds
          ? _value._vendorIds
          : vendorIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BuyRequestStatus?,
      updatedById: freezed == updatedById
          ? _value.updatedById
          : updatedById // ignore: cast_nullable_to_non_nullable
              as String?,
      paymentMode: freezed == paymentMode
          ? _value.paymentMode
          : paymentMode // ignore: cast_nullable_to_non_nullable
              as PaymentMode?,
      shippingMethod: freezed == shippingMethod
          ? _value.shippingMethod
          : shippingMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalCts: freezed == totalCts
          ? _value.totalCts
          : totalCts // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      shippingId: freezed == shippingId
          ? _value.shippingId
          : shippingId // ignore: cast_nullable_to_non_nullable
              as String?,
      shippingPrice: freezed == shippingPrice
          ? _value.shippingPrice
          : shippingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      grandTotal: freezed == grandTotal
          ? _value.grandTotal
          : grandTotal // ignore: cast_nullable_to_non_nullable
              as double?,
      taxPrice: freezed == taxPrice
          ? _value.taxPrice
          : taxPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCarat: freezed == pricePerCarat
          ? _value.pricePerCarat
          : pricePerCarat // ignore: cast_nullable_to_non_nullable
              as double?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      orderCode: freezed == orderCode
          ? _value.orderCode
          : orderCode // ignore: cast_nullable_to_non_nullable
              as String?,
      cardHolderName: freezed == cardHolderName
          ? _value.cardHolderName
          : cardHolderName // ignore: cast_nullable_to_non_nullable
              as String?,
      cardNumber: freezed == cardNumber
          ? _value.cardNumber
          : cardNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      cardType: freezed == cardType
          ? _value.cardType
          : cardType // ignore: cast_nullable_to_non_nullable
              as String?,
      expDate: freezed == expDate
          ? _value.expDate
          : expDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transId: freezed == transId
          ? _value.transId
          : transId // ignore: cast_nullable_to_non_nullable
              as String?,
      authorizedAmount: freezed == authorizedAmount
          ? _value.authorizedAmount
          : authorizedAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as BuyRequestType?,
      isCaptureFailed: freezed == isCaptureFailed
          ? _value.isCaptureFailed
          : isCaptureFailed // ignore: cast_nullable_to_non_nullable
              as bool?,
      tax: freezed == tax
          ? _value.tax
          : tax // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BuyRequestImpl implements _BuyRequest {
  const _$BuyRequestImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'stock_ids') final List<StockId>? stockIds,
      @JsonKey(name: 'vendor_ids') final List<String>? vendorIds,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'updated_by_id') this.updatedById,
      @JsonKey(name: 'payment_mode') this.paymentMode,
      @JsonKey(name: 'shipping_method') this.shippingMethod,
      @JsonKey(name: 'shipping_address') this.shippingAddress,
      @JsonKey(name: 'billing_address') this.billingAddress,
      @JsonKey(name: 'reject_reason') this.rejectReason,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'total_cts') this.totalCts,
      @JsonKey(name: 'total_amount') this.totalAmount,
      @JsonKey(name: 'shipment_id') this.shippingId,
      @JsonKey(name: 'shipment_price') this.shippingPrice,
      @JsonKey(name: 'amount') this.amount,
      @JsonKey(name: 'grand_total') this.grandTotal,
      @JsonKey(name: 'tax_price') this.taxPrice,
      @JsonKey(name: 'price_per_carat') this.pricePerCarat,
      @JsonKey(name: 'user') this.user,
      @JsonKey(name: 'order_code') this.orderCode,
      @JsonKey(name: 'card_holder_name') this.cardHolderName,
      @JsonKey(name: 'card_number') this.cardNumber,
      @JsonKey(name: 'card_type') this.cardType,
      @JsonKey(name: 'exp_date') this.expDate,
      @JsonKey(name: 'trans_id') this.transId,
      @JsonKey(name: 'authorized_amount') this.authorizedAmount,
      @JsonKey(name: 'type') this.type,
      @JsonKey(name: 'is_capture_failed') this.isCaptureFailed,
      @JsonKey(name: 'tax') this.tax})
      : _stockIds = stockIds,
        _vendorIds = vendorIds;

  factory _$BuyRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$BuyRequestImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  final List<StockId>? _stockIds;
  @override
  @JsonKey(name: 'stock_ids')
  List<StockId>? get stockIds {
    final value = _stockIds;
    if (value == null) return null;
    if (_stockIds is EqualUnmodifiableListView) return _stockIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _vendorIds;
  @override
  @JsonKey(name: 'vendor_ids')
  List<String>? get vendorIds {
    final value = _vendorIds;
    if (value == null) return null;
    if (_vendorIds is EqualUnmodifiableListView) return _vendorIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'status')
  final BuyRequestStatus? status;
  @override
  @JsonKey(name: 'updated_by_id')
  final String? updatedById;
  @override
  @JsonKey(name: 'payment_mode')
  final PaymentMode? paymentMode;
  @override
  @JsonKey(name: 'shipping_method')
  final String? shippingMethod;
  @override
  @JsonKey(name: 'shipping_address')
  final IngAddress? shippingAddress;
  @override
  @JsonKey(name: 'billing_address')
  final IngAddress? billingAddress;
  @override
  @JsonKey(name: 'reject_reason')
  final dynamic rejectReason;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'total_cts')
  final double? totalCts;
  @override
  @JsonKey(name: 'total_amount')
  final double? totalAmount;
  @override
  @JsonKey(name: 'shipment_id')
  final String? shippingId;
  @override
  @JsonKey(name: 'shipment_price')
  final double? shippingPrice;
  @override
  @JsonKey(name: 'amount')
  final double? amount;
  @override
  @JsonKey(name: 'grand_total')
  final double? grandTotal;
  @override
  @JsonKey(name: 'tax_price')
  final double? taxPrice;
  @override
  @JsonKey(name: 'price_per_carat')
  final double? pricePerCarat;
  @override
  @JsonKey(name: 'user')
  final User? user;
  @override
  @JsonKey(name: 'order_code')
  final String? orderCode;
  @override
  @JsonKey(name: 'card_holder_name')
  final String? cardHolderName;
  @override
  @JsonKey(name: 'card_number')
  final String? cardNumber;
  @override
  @JsonKey(name: 'card_type')
  final String? cardType;
  @override
  @JsonKey(name: 'exp_date')
  final String? expDate;
  @override
  @JsonKey(name: 'trans_id')
  final String? transId;
  @override
  @JsonKey(name: 'authorized_amount')
  final double? authorizedAmount;
  @override
  @JsonKey(name: 'type')
  final BuyRequestType? type;
  @override
  @JsonKey(name: 'is_capture_failed')
  final bool? isCaptureFailed;
  @override
  @JsonKey(name: 'tax')
  final double? tax;

  @override
  String toString() {
    return 'BuyRequest(id: $id, userId: $userId, stockIds: $stockIds, vendorIds: $vendorIds, status: $status, updatedById: $updatedById, paymentMode: $paymentMode, shippingMethod: $shippingMethod, shippingAddress: $shippingAddress, billingAddress: $billingAddress, rejectReason: $rejectReason, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, totalCts: $totalCts, totalAmount: $totalAmount, shippingId: $shippingId, shippingPrice: $shippingPrice, amount: $amount, grandTotal: $grandTotal, taxPrice: $taxPrice, pricePerCarat: $pricePerCarat, user: $user, orderCode: $orderCode, cardHolderName: $cardHolderName, cardNumber: $cardNumber, cardType: $cardType, expDate: $expDate, transId: $transId, authorizedAmount: $authorizedAmount, type: $type, isCaptureFailed: $isCaptureFailed, tax: $tax)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(other._stockIds, _stockIds) &&
            const DeepCollectionEquality()
                .equals(other._vendorIds, _vendorIds) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.updatedById, updatedById) ||
                other.updatedById == updatedById) &&
            (identical(other.paymentMode, paymentMode) ||
                other.paymentMode == paymentMode) &&
            (identical(other.shippingMethod, shippingMethod) ||
                other.shippingMethod == shippingMethod) &&
            (identical(other.shippingAddress, shippingAddress) ||
                other.shippingAddress == shippingAddress) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            const DeepCollectionEquality()
                .equals(other.rejectReason, rejectReason) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.totalCts, totalCts) ||
                other.totalCts == totalCts) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            (identical(other.shippingId, shippingId) ||
                other.shippingId == shippingId) &&
            (identical(other.shippingPrice, shippingPrice) ||
                other.shippingPrice == shippingPrice) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.grandTotal, grandTotal) ||
                other.grandTotal == grandTotal) &&
            (identical(other.taxPrice, taxPrice) ||
                other.taxPrice == taxPrice) &&
            (identical(other.pricePerCarat, pricePerCarat) ||
                other.pricePerCarat == pricePerCarat) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.orderCode, orderCode) ||
                other.orderCode == orderCode) &&
            (identical(other.cardHolderName, cardHolderName) ||
                other.cardHolderName == cardHolderName) &&
            (identical(other.cardNumber, cardNumber) ||
                other.cardNumber == cardNumber) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.expDate, expDate) || other.expDate == expDate) &&
            (identical(other.transId, transId) || other.transId == transId) &&
            (identical(other.authorizedAmount, authorizedAmount) ||
                other.authorizedAmount == authorizedAmount) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isCaptureFailed, isCaptureFailed) ||
                other.isCaptureFailed == isCaptureFailed) &&
            (identical(other.tax, tax) || other.tax == tax));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        const DeepCollectionEquality().hash(_stockIds),
        const DeepCollectionEquality().hash(_vendorIds),
        status,
        updatedById,
        paymentMode,
        shippingMethod,
        shippingAddress,
        billingAddress,
        const DeepCollectionEquality().hash(rejectReason),
        isActive,
        deleted,
        createdAt,
        updatedAt,
        totalCts,
        totalAmount,
        shippingId,
        shippingPrice,
        amount,
        grandTotal,
        taxPrice,
        pricePerCarat,
        user,
        orderCode,
        cardHolderName,
        cardNumber,
        cardType,
        expDate,
        transId,
        authorizedAmount,
        type,
        isCaptureFailed,
        tax
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyRequestImplCopyWith<_$BuyRequestImpl> get copyWith =>
      __$$BuyRequestImplCopyWithImpl<_$BuyRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BuyRequestImplToJson(
      this,
    );
  }
}

abstract class _BuyRequest implements BuyRequest {
  const factory _BuyRequest(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'stock_ids') final List<StockId>? stockIds,
      @JsonKey(name: 'vendor_ids') final List<String>? vendorIds,
      @JsonKey(name: 'status') final BuyRequestStatus? status,
      @JsonKey(name: 'updated_by_id') final String? updatedById,
      @JsonKey(name: 'payment_mode') final PaymentMode? paymentMode,
      @JsonKey(name: 'shipping_method') final String? shippingMethod,
      @JsonKey(name: 'shipping_address') final IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') final IngAddress? billingAddress,
      @JsonKey(name: 'reject_reason') final dynamic rejectReason,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'total_cts') final double? totalCts,
      @JsonKey(name: 'total_amount') final double? totalAmount,
      @JsonKey(name: 'shipment_id') final String? shippingId,
      @JsonKey(name: 'shipment_price') final double? shippingPrice,
      @JsonKey(name: 'amount') final double? amount,
      @JsonKey(name: 'grand_total') final double? grandTotal,
      @JsonKey(name: 'tax_price') final double? taxPrice,
      @JsonKey(name: 'price_per_carat') final double? pricePerCarat,
      @JsonKey(name: 'user') final User? user,
      @JsonKey(name: 'order_code') final String? orderCode,
      @JsonKey(name: 'card_holder_name') final String? cardHolderName,
      @JsonKey(name: 'card_number') final String? cardNumber,
      @JsonKey(name: 'card_type') final String? cardType,
      @JsonKey(name: 'exp_date') final String? expDate,
      @JsonKey(name: 'trans_id') final String? transId,
      @JsonKey(name: 'authorized_amount') final double? authorizedAmount,
      @JsonKey(name: 'type') final BuyRequestType? type,
      @JsonKey(name: 'is_capture_failed') final bool? isCaptureFailed,
      @JsonKey(name: 'tax') final double? tax}) = _$BuyRequestImpl;

  factory _BuyRequest.fromJson(Map<String, dynamic> json) =
      _$BuyRequestImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'stock_ids')
  List<StockId>? get stockIds;
  @override
  @JsonKey(name: 'vendor_ids')
  List<String>? get vendorIds;
  @override
  @JsonKey(name: 'status')
  BuyRequestStatus? get status;
  @override
  @JsonKey(name: 'updated_by_id')
  String? get updatedById;
  @override
  @JsonKey(name: 'payment_mode')
  PaymentMode? get paymentMode;
  @override
  @JsonKey(name: 'shipping_method')
  String? get shippingMethod;
  @override
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress;
  @override
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress;
  @override
  @JsonKey(name: 'reject_reason')
  dynamic get rejectReason;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'total_cts')
  double? get totalCts;
  @override
  @JsonKey(name: 'total_amount')
  double? get totalAmount;
  @override
  @JsonKey(name: 'shipment_id')
  String? get shippingId;
  @override
  @JsonKey(name: 'shipment_price')
  double? get shippingPrice;
  @override
  @JsonKey(name: 'amount')
  double? get amount;
  @override
  @JsonKey(name: 'grand_total')
  double? get grandTotal;
  @override
  @JsonKey(name: 'tax_price')
  double? get taxPrice;
  @override
  @JsonKey(name: 'price_per_carat')
  double? get pricePerCarat;
  @override
  @JsonKey(name: 'user')
  User? get user;
  @override
  @JsonKey(name: 'order_code')
  String? get orderCode;
  @override
  @JsonKey(name: 'card_holder_name')
  String? get cardHolderName;
  @override
  @JsonKey(name: 'card_number')
  String? get cardNumber;
  @override
  @JsonKey(name: 'card_type')
  String? get cardType;
  @override
  @JsonKey(name: 'exp_date')
  String? get expDate;
  @override
  @JsonKey(name: 'trans_id')
  String? get transId;
  @override
  @JsonKey(name: 'authorized_amount')
  double? get authorizedAmount;
  @override
  @JsonKey(name: 'type')
  BuyRequestType? get type;
  @override
  @JsonKey(name: 'is_capture_failed')
  bool? get isCaptureFailed;
  @override
  @JsonKey(name: 'tax')
  double? get tax;
  @override
  @JsonKey(ignore: true)
  _$$BuyRequestImplCopyWith<_$BuyRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IngAddress _$IngAddressFromJson(Map<String, dynamic> json) {
  return _IngAddress.fromJson(json);
}

/// @nodoc
mixin _$IngAddress {
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'state')
  String? get state => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'zip_code')
  String? get zipCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'first_name')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'full_name')
  String? get fullName => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo => throw _privateConstructorUsedError;
  @JsonKey(name: 'street')
  String? get street => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IngAddressCopyWith<IngAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IngAddressCopyWith<$Res> {
  factory $IngAddressCopyWith(
          IngAddress value, $Res Function(IngAddress) then) =
      _$IngAddressCopyWithImpl<$Res, IngAddress>;
  @useResult
  $Res call(
      {@JsonKey(name: 'city') String? city,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'street') String? street});
}

/// @nodoc
class _$IngAddressCopyWithImpl<$Res, $Val extends IngAddress>
    implements $IngAddressCopyWith<$Res> {
  _$IngAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? city = freezed,
    Object? email = freezed,
    Object? state = freezed,
    Object? phone = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? fullName = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? street = freezed,
  }) {
    return _then(_value.copyWith(
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IngAddressImplCopyWith<$Res>
    implements $IngAddressCopyWith<$Res> {
  factory _$$IngAddressImplCopyWith(
          _$IngAddressImpl value, $Res Function(_$IngAddressImpl) then) =
      __$$IngAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'city') String? city,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'street') String? street});
}

/// @nodoc
class __$$IngAddressImplCopyWithImpl<$Res>
    extends _$IngAddressCopyWithImpl<$Res, _$IngAddressImpl>
    implements _$$IngAddressImplCopyWith<$Res> {
  __$$IngAddressImplCopyWithImpl(
      _$IngAddressImpl _value, $Res Function(_$IngAddressImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? city = freezed,
    Object? email = freezed,
    Object? state = freezed,
    Object? phone = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
    Object? lastName = freezed,
    Object? firstName = freezed,
    Object? fullName = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? street = freezed,
  }) {
    return _then(_$IngAddressImpl(
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IngAddressImpl implements _IngAddress {
  const _$IngAddressImpl(
      {@JsonKey(name: 'city') this.city,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'state') this.state,
      @JsonKey(name: 'phone') this.phone,
      @JsonKey(name: 'country') this.country,
      @JsonKey(name: 'zip_code') this.zipCode,
      @JsonKey(name: 'last_name') this.lastName,
      @JsonKey(name: 'first_name') this.firstName,
      @JsonKey(name: 'full_name') this.fullName,
      @JsonKey(name: 'address_line_one') this.addressLineOne,
      @JsonKey(name: 'address_line_two') this.addressLineTwo,
      @JsonKey(name: 'street') this.street});

  factory _$IngAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$IngAddressImplFromJson(json);

  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'state')
  final String? state;
  @override
  @JsonKey(name: 'phone')
  final String? phone;
  @override
  @JsonKey(name: 'country')
  final String? country;
  @override
  @JsonKey(name: 'zip_code')
  final String? zipCode;
  @override
  @JsonKey(name: 'last_name')
  final String? lastName;
  @override
  @JsonKey(name: 'first_name')
  final String? firstName;
  @override
  @JsonKey(name: 'full_name')
  final String? fullName;
  @override
  @JsonKey(name: 'address_line_one')
  final String? addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  final String? addressLineTwo;
  @override
  @JsonKey(name: 'street')
  final String? street;

  @override
  String toString() {
    return 'IngAddress(city: $city, email: $email, state: $state, phone: $phone, country: $country, zipCode: $zipCode, lastName: $lastName, firstName: $firstName, fullName: $fullName, addressLineOne: $addressLineOne, addressLineTwo: $addressLineTwo, street: $street)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IngAddressImpl &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.addressLineOne, addressLineOne) ||
                other.addressLineOne == addressLineOne) &&
            (identical(other.addressLineTwo, addressLineTwo) ||
                other.addressLineTwo == addressLineTwo) &&
            (identical(other.street, street) || other.street == street));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      city,
      email,
      state,
      phone,
      country,
      zipCode,
      lastName,
      firstName,
      fullName,
      addressLineOne,
      addressLineTwo,
      street);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      __$$IngAddressImplCopyWithImpl<_$IngAddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IngAddressImplToJson(
      this,
    );
  }
}

abstract class _IngAddress implements IngAddress {
  const factory _IngAddress(
      {@JsonKey(name: 'city') final String? city,
      @JsonKey(name: 'email') final String? email,
      @JsonKey(name: 'state') final String? state,
      @JsonKey(name: 'phone') final String? phone,
      @JsonKey(name: 'country') final String? country,
      @JsonKey(name: 'zip_code') final String? zipCode,
      @JsonKey(name: 'last_name') final String? lastName,
      @JsonKey(name: 'first_name') final String? firstName,
      @JsonKey(name: 'full_name') final String? fullName,
      @JsonKey(name: 'address_line_one') final String? addressLineOne,
      @JsonKey(name: 'address_line_two') final String? addressLineTwo,
      @JsonKey(name: 'street') final String? street}) = _$IngAddressImpl;

  factory _IngAddress.fromJson(Map<String, dynamic> json) =
      _$IngAddressImpl.fromJson;

  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'state')
  String? get state;
  @override
  @JsonKey(name: 'phone')
  String? get phone;
  @override
  @JsonKey(name: 'country')
  String? get country;
  @override
  @JsonKey(name: 'zip_code')
  String? get zipCode;
  @override
  @JsonKey(name: 'last_name')
  String? get lastName;
  @override
  @JsonKey(name: 'first_name')
  String? get firstName;
  @override
  @JsonKey(name: 'full_name')
  String? get fullName;
  @override
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo;
  @override
  @JsonKey(name: 'street')
  String? get street;
  @override
  @JsonKey(ignore: true)
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StockId _$StockIdFromJson(Map<String, dynamic> json) {
  return _StockId.fromJson(json);
}

/// @nodoc
mixin _$StockId {
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_id')
  String? get vendorId => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_available')
  bool? get isAvailable => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_id')
  String? get adminId => throw _privateConstructorUsedError;
  @JsonKey(name: 'suggested_for')
  String? get suggestedFor => throw _privateConstructorUsedError;
  @JsonKey(name: 'suggested_stock')
  StockId? get suggestedStock => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock')
  DiamondEntity? get stock => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StockIdCopyWith<StockId> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StockIdCopyWith<$Res> {
  factory $StockIdCopyWith(StockId value, $Res Function(StockId) then) =
      _$StockIdCopyWithImpl<$Res, StockId>;
  @useResult
  $Res call(
      {@JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'is_available') bool? isAvailable,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'suggested_for') String? suggestedFor,
      @JsonKey(name: 'suggested_stock') StockId? suggestedStock,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  $StockIdCopyWith<$Res>? get suggestedStock;
}

/// @nodoc
class _$StockIdCopyWithImpl<$Res, $Val extends StockId>
    implements $StockIdCopyWith<$Res> {
  _$StockIdCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stockId = freezed,
    Object? vendorId = freezed,
    Object? isAvailable = freezed,
    Object? isActionTaken = freezed,
    Object? adminId = freezed,
    Object? suggestedFor = freezed,
    Object? suggestedStock = freezed,
    Object? stock = freezed,
  }) {
    return _then(_value.copyWith(
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestedFor: freezed == suggestedFor
          ? _value.suggestedFor
          : suggestedFor // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestedStock: freezed == suggestedStock
          ? _value.suggestedStock
          : suggestedStock // ignore: cast_nullable_to_non_nullable
              as StockId?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $StockIdCopyWith<$Res>? get suggestedStock {
    if (_value.suggestedStock == null) {
      return null;
    }

    return $StockIdCopyWith<$Res>(_value.suggestedStock!, (value) {
      return _then(_value.copyWith(suggestedStock: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StockIdImplCopyWith<$Res> implements $StockIdCopyWith<$Res> {
  factory _$$StockIdImplCopyWith(
          _$StockIdImpl value, $Res Function(_$StockIdImpl) then) =
      __$$StockIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'is_available') bool? isAvailable,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'suggested_for') String? suggestedFor,
      @JsonKey(name: 'suggested_stock') StockId? suggestedStock,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  @override
  $StockIdCopyWith<$Res>? get suggestedStock;
}

/// @nodoc
class __$$StockIdImplCopyWithImpl<$Res>
    extends _$StockIdCopyWithImpl<$Res, _$StockIdImpl>
    implements _$$StockIdImplCopyWith<$Res> {
  __$$StockIdImplCopyWithImpl(
      _$StockIdImpl _value, $Res Function(_$StockIdImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stockId = freezed,
    Object? vendorId = freezed,
    Object? isAvailable = freezed,
    Object? isActionTaken = freezed,
    Object? adminId = freezed,
    Object? suggestedFor = freezed,
    Object? suggestedStock = freezed,
    Object? stock = freezed,
  }) {
    return _then(_$StockIdImpl(
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      isAvailable: freezed == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestedFor: freezed == suggestedFor
          ? _value.suggestedFor
          : suggestedFor // ignore: cast_nullable_to_non_nullable
              as String?,
      suggestedStock: freezed == suggestedStock
          ? _value.suggestedStock
          : suggestedStock // ignore: cast_nullable_to_non_nullable
              as StockId?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StockIdImpl implements _StockId {
  const _$StockIdImpl(
      {@JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'vendor_id') this.vendorId,
      @JsonKey(name: 'is_available') this.isAvailable,
      @JsonKey(name: 'is_action_taken') this.isActionTaken,
      @JsonKey(name: 'admin_id') this.adminId,
      @JsonKey(name: 'suggested_for') this.suggestedFor,
      @JsonKey(name: 'suggested_stock') this.suggestedStock,
      @JsonKey(name: 'stock') this.stock});

  factory _$StockIdImpl.fromJson(Map<String, dynamic> json) =>
      _$$StockIdImplFromJson(json);

  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'vendor_id')
  final String? vendorId;
  @override
  @JsonKey(name: 'is_available')
  final bool? isAvailable;
  @override
  @JsonKey(name: 'is_action_taken')
  final bool? isActionTaken;
  @override
  @JsonKey(name: 'admin_id')
  final String? adminId;
  @override
  @JsonKey(name: 'suggested_for')
  final String? suggestedFor;
  @override
  @JsonKey(name: 'suggested_stock')
  final StockId? suggestedStock;
  @override
  @JsonKey(name: 'stock')
  final DiamondEntity? stock;

  @override
  String toString() {
    return 'StockId(stockId: $stockId, vendorId: $vendorId, isAvailable: $isAvailable, isActionTaken: $isActionTaken, adminId: $adminId, suggestedFor: $suggestedFor, suggestedStock: $suggestedStock, stock: $stock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StockIdImpl &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.vendorId, vendorId) ||
                other.vendorId == vendorId) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.isActionTaken, isActionTaken) ||
                other.isActionTaken == isActionTaken) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.suggestedFor, suggestedFor) ||
                other.suggestedFor == suggestedFor) &&
            (identical(other.suggestedStock, suggestedStock) ||
                other.suggestedStock == suggestedStock) &&
            (identical(other.stock, stock) || other.stock == stock));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, stockId, vendorId, isAvailable,
      isActionTaken, adminId, suggestedFor, suggestedStock, stock);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StockIdImplCopyWith<_$StockIdImpl> get copyWith =>
      __$$StockIdImplCopyWithImpl<_$StockIdImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StockIdImplToJson(
      this,
    );
  }
}

abstract class _StockId implements StockId {
  const factory _StockId(
      {@JsonKey(name: 'stock_id') final String? stockId,
      @JsonKey(name: 'vendor_id') final String? vendorId,
      @JsonKey(name: 'is_available') final bool? isAvailable,
      @JsonKey(name: 'is_action_taken') final bool? isActionTaken,
      @JsonKey(name: 'admin_id') final String? adminId,
      @JsonKey(name: 'suggested_for') final String? suggestedFor,
      @JsonKey(name: 'suggested_stock') final StockId? suggestedStock,
      @JsonKey(name: 'stock') final DiamondEntity? stock}) = _$StockIdImpl;

  factory _StockId.fromJson(Map<String, dynamic> json) = _$StockIdImpl.fromJson;

  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'vendor_id')
  String? get vendorId;
  @override
  @JsonKey(name: 'is_available')
  bool? get isAvailable;
  @override
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken;
  @override
  @JsonKey(name: 'admin_id')
  String? get adminId;
  @override
  @JsonKey(name: 'suggested_for')
  String? get suggestedFor;
  @override
  @JsonKey(name: 'suggested_stock')
  StockId? get suggestedStock;
  @override
  @JsonKey(name: 'stock')
  DiamondEntity? get stock;
  @override
  @JsonKey(ignore: true)
  _$$StockIdImplCopyWith<_$StockIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'first_name')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'legal_registered_name')
  String? get legalRegisteredName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'legal_registered_name') String? legalRegisteredName});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? legalRegisteredName = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      legalRegisteredName: freezed == legalRegisteredName
          ? _value.legalRegisteredName
          : legalRegisteredName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'legal_registered_name') String? legalRegisteredName});
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? legalRegisteredName = freezed,
  }) {
    return _then(_$UserImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      legalRegisteredName: freezed == legalRegisteredName
          ? _value.legalRegisteredName
          : legalRegisteredName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'first_name') this.firstName,
      @JsonKey(name: 'last_name') this.lastName,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'legal_registered_name') this.legalRegisteredName});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'first_name')
  final String? firstName;
  @override
  @JsonKey(name: 'last_name')
  final String? lastName;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'legal_registered_name')
  final String? legalRegisteredName;

  @override
  String toString() {
    return 'User(id: $id, firstName: $firstName, lastName: $lastName, email: $email, legalRegisteredName: $legalRegisteredName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.legalRegisteredName, legalRegisteredName) ||
                other.legalRegisteredName == legalRegisteredName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, firstName, lastName, email, legalRegisteredName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'first_name') final String? firstName,
      @JsonKey(name: 'last_name') final String? lastName,
      @JsonKey(name: 'email') final String? email,
      @JsonKey(name: 'legal_registered_name')
      final String? legalRegisteredName}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'first_name')
  String? get firstName;
  @override
  @JsonKey(name: 'last_name')
  String? get lastName;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'legal_registered_name')
  String? get legalRegisteredName;
  @override
  @JsonKey(ignore: true)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
