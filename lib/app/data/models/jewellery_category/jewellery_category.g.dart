// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jewellery_category.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JewelleryCategoryImpl _$$JewelleryCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$JewelleryCategoryImpl(
      id: json['id'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      name: json['name'] as String?,
      description: json['description'] as String?,
      handle: json['handle'] as String?,
      parentCategoryId: json['parent_category_id'],
      rank: (json['rank'] as num?)?.toInt(),
      metadata: json['metadata'] == null
          ? null
          : Metadata.fromJson(json['metadata'] as Map<String, dynamic>),
      categoryChildren: json['category_children'] as List<dynamic>?,
      parentCategory: json['parent_category'],
      totalItems: (json['total_items'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$JewelleryCategoryImplToJson(
        _$JewelleryCategoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'name': instance.name,
      'description': instance.description,
      'handle': instance.handle,
      'parent_category_id': instance.parentCategoryId,
      'rank': instance.rank,
      'metadata': instance.metadata,
      'category_children': instance.categoryChildren,
      'parent_category': instance.parentCategory,
      'total_items': instance.totalItems,
    };

_$MetadataImpl _$$MetadataImplFromJson(Map<String, dynamic> json) =>
    _$MetadataImpl(
      imageUrl: json['image_url'] as String?,
    );

Map<String, dynamic> _$$MetadataImplToJson(_$MetadataImpl instance) =>
    <String, dynamic>{
      'image_url': instance.imageUrl,
    };
