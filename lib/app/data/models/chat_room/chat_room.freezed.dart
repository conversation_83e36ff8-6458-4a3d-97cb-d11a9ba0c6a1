// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_room.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ChatRoom _$ChatRoomFromJson(Map<String, dynamic> json) {
  return _ChatRoom.fromJson(json);
}

/// @nodoc
mixin _$ChatRoom {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'inquiry')
  Inquiry? get inquiry => throw _privateConstructorUsedError;
  @JsonKey(name: 'room_id')
  String? get roomId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  dynamic get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_messageAt')
  String? get lastMessageAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_unread')
  int? get userUnread => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_unread')
  int? get adminUnread => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_message')
  String? get lastMessage => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: '__v')
  int? get v => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ChatRoomCopyWith<ChatRoom> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChatRoomCopyWith<$Res> {
  factory $ChatRoomCopyWith(ChatRoom value, $Res Function(ChatRoom) then) =
      _$ChatRoomCopyWithImpl<$Res, ChatRoom>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'inquiry') Inquiry? inquiry,
      @JsonKey(name: 'room_id') String? roomId,
      @JsonKey(name: 'stock_id') dynamic stockId,
      @JsonKey(name: 'last_messageAt') String? lastMessageAt,
      @JsonKey(name: 'user_unread') int? userUnread,
      @JsonKey(name: 'admin_unread') int? adminUnread,
      @JsonKey(name: 'last_message') String? lastMessage,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: '__v') int? v});

  $InquiryCopyWith<$Res>? get inquiry;
}

/// @nodoc
class _$ChatRoomCopyWithImpl<$Res, $Val extends ChatRoom>
    implements $ChatRoomCopyWith<$Res> {
  _$ChatRoomCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? inquiry = freezed,
    Object? roomId = freezed,
    Object? stockId = freezed,
    Object? lastMessageAt = freezed,
    Object? userUnread = freezed,
    Object? adminUnread = freezed,
    Object? lastMessage = freezed,
    Object? status = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? v = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      inquiry: freezed == inquiry
          ? _value.inquiry
          : inquiry // ignore: cast_nullable_to_non_nullable
              as Inquiry?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      lastMessageAt: freezed == lastMessageAt
          ? _value.lastMessageAt
          : lastMessageAt // ignore: cast_nullable_to_non_nullable
              as String?,
      userUnread: freezed == userUnread
          ? _value.userUnread
          : userUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      adminUnread: freezed == adminUnread
          ? _value.adminUnread
          : adminUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      lastMessage: freezed == lastMessage
          ? _value.lastMessage
          : lastMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      v: freezed == v
          ? _value.v
          : v // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $InquiryCopyWith<$Res>? get inquiry {
    if (_value.inquiry == null) {
      return null;
    }

    return $InquiryCopyWith<$Res>(_value.inquiry!, (value) {
      return _then(_value.copyWith(inquiry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ChatRoomImplCopyWith<$Res>
    implements $ChatRoomCopyWith<$Res> {
  factory _$$ChatRoomImplCopyWith(
          _$ChatRoomImpl value, $Res Function(_$ChatRoomImpl) then) =
      __$$ChatRoomImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'inquiry') Inquiry? inquiry,
      @JsonKey(name: 'room_id') String? roomId,
      @JsonKey(name: 'stock_id') dynamic stockId,
      @JsonKey(name: 'last_messageAt') String? lastMessageAt,
      @JsonKey(name: 'user_unread') int? userUnread,
      @JsonKey(name: 'admin_unread') int? adminUnread,
      @JsonKey(name: 'last_message') String? lastMessage,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: '__v') int? v});

  @override
  $InquiryCopyWith<$Res>? get inquiry;
}

/// @nodoc
class __$$ChatRoomImplCopyWithImpl<$Res>
    extends _$ChatRoomCopyWithImpl<$Res, _$ChatRoomImpl>
    implements _$$ChatRoomImplCopyWith<$Res> {
  __$$ChatRoomImplCopyWithImpl(
      _$ChatRoomImpl _value, $Res Function(_$ChatRoomImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? inquiry = freezed,
    Object? roomId = freezed,
    Object? stockId = freezed,
    Object? lastMessageAt = freezed,
    Object? userUnread = freezed,
    Object? adminUnread = freezed,
    Object? lastMessage = freezed,
    Object? status = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? v = freezed,
  }) {
    return _then(_$ChatRoomImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      inquiry: freezed == inquiry
          ? _value.inquiry
          : inquiry // ignore: cast_nullable_to_non_nullable
              as Inquiry?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      lastMessageAt: freezed == lastMessageAt
          ? _value.lastMessageAt
          : lastMessageAt // ignore: cast_nullable_to_non_nullable
              as String?,
      userUnread: freezed == userUnread
          ? _value.userUnread
          : userUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      adminUnread: freezed == adminUnread
          ? _value.adminUnread
          : adminUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      lastMessage: freezed == lastMessage
          ? _value.lastMessage
          : lastMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      v: freezed == v
          ? _value.v
          : v // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChatRoomImpl implements _ChatRoom {
  const _$ChatRoomImpl(
      {@JsonKey(name: '_id') this.id,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'inquiry') this.inquiry,
      @JsonKey(name: 'room_id') this.roomId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'last_messageAt') this.lastMessageAt,
      @JsonKey(name: 'user_unread') this.userUnread,
      @JsonKey(name: 'admin_unread') this.adminUnread,
      @JsonKey(name: 'last_message') this.lastMessage,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: '__v') this.v});

  factory _$ChatRoomImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChatRoomImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'inquiry')
  final Inquiry? inquiry;
  @override
  @JsonKey(name: 'room_id')
  final String? roomId;
  @override
  @JsonKey(name: 'stock_id')
  final dynamic stockId;
  @override
  @JsonKey(name: 'last_messageAt')
  final String? lastMessageAt;
  @override
  @JsonKey(name: 'user_unread')
  final int? userUnread;
  @override
  @JsonKey(name: 'admin_unread')
  final int? adminUnread;
  @override
  @JsonKey(name: 'last_message')
  final String? lastMessage;
  @override
  @JsonKey(name: 'status')
  final String? status;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: '__v')
  final int? v;

  @override
  String toString() {
    return 'ChatRoom(id: $id, userId: $userId, inquiry: $inquiry, roomId: $roomId, stockId: $stockId, lastMessageAt: $lastMessageAt, userUnread: $userUnread, adminUnread: $adminUnread, lastMessage: $lastMessage, status: $status, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, v: $v)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChatRoomImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.inquiry, inquiry) || other.inquiry == inquiry) &&
            (identical(other.roomId, roomId) || other.roomId == roomId) &&
            const DeepCollectionEquality().equals(other.stockId, stockId) &&
            (identical(other.lastMessageAt, lastMessageAt) ||
                other.lastMessageAt == lastMessageAt) &&
            (identical(other.userUnread, userUnread) ||
                other.userUnread == userUnread) &&
            (identical(other.adminUnread, adminUnread) ||
                other.adminUnread == adminUnread) &&
            (identical(other.lastMessage, lastMessage) ||
                other.lastMessage == lastMessage) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.v, v) || other.v == v));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      inquiry,
      roomId,
      const DeepCollectionEquality().hash(stockId),
      lastMessageAt,
      userUnread,
      adminUnread,
      lastMessage,
      status,
      deleted,
      createdAt,
      updatedAt,
      v);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ChatRoomImplCopyWith<_$ChatRoomImpl> get copyWith =>
      __$$ChatRoomImplCopyWithImpl<_$ChatRoomImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChatRoomImplToJson(
      this,
    );
  }
}

abstract class _ChatRoom implements ChatRoom {
  const factory _ChatRoom(
      {@JsonKey(name: '_id') final String? id,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'inquiry') final Inquiry? inquiry,
      @JsonKey(name: 'room_id') final String? roomId,
      @JsonKey(name: 'stock_id') final dynamic stockId,
      @JsonKey(name: 'last_messageAt') final String? lastMessageAt,
      @JsonKey(name: 'user_unread') final int? userUnread,
      @JsonKey(name: 'admin_unread') final int? adminUnread,
      @JsonKey(name: 'last_message') final String? lastMessage,
      @JsonKey(name: 'status') final String? status,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: '__v') final int? v}) = _$ChatRoomImpl;

  factory _ChatRoom.fromJson(Map<String, dynamic> json) =
      _$ChatRoomImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'inquiry')
  Inquiry? get inquiry;
  @override
  @JsonKey(name: 'room_id')
  String? get roomId;
  @override
  @JsonKey(name: 'stock_id')
  dynamic get stockId;
  @override
  @JsonKey(name: 'last_messageAt')
  String? get lastMessageAt;
  @override
  @JsonKey(name: 'user_unread')
  int? get userUnread;
  @override
  @JsonKey(name: 'admin_unread')
  int? get adminUnread;
  @override
  @JsonKey(name: 'last_message')
  String? get lastMessage;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: '__v')
  int? get v;
  @override
  @JsonKey(ignore: true)
  _$$ChatRoomImplCopyWith<_$ChatRoomImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Inquiry _$InquiryFromJson(Map<String, dynamic> json) {
  return _Inquiry.fromJson(json);
}

/// @nodoc
mixin _$Inquiry {
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'title')
  dynamic get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'logo')
  List<dynamic>? get logo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $InquiryCopyWith<Inquiry> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InquiryCopyWith<$Res> {
  factory $InquiryCopyWith(Inquiry value, $Res Function(Inquiry) then) =
      _$InquiryCopyWithImpl<$Res, Inquiry>;
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'title') dynamic title,
      @JsonKey(name: 'logo') List<dynamic>? logo});
}

/// @nodoc
class _$InquiryCopyWithImpl<$Res, $Val extends Inquiry>
    implements $InquiryCopyWith<$Res> {
  _$InquiryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? logo = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as dynamic,
      logo: freezed == logo
          ? _value.logo
          : logo // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InquiryImplCopyWith<$Res> implements $InquiryCopyWith<$Res> {
  factory _$$InquiryImplCopyWith(
          _$InquiryImpl value, $Res Function(_$InquiryImpl) then) =
      __$$InquiryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'title') dynamic title,
      @JsonKey(name: 'logo') List<dynamic>? logo});
}

/// @nodoc
class __$$InquiryImplCopyWithImpl<$Res>
    extends _$InquiryCopyWithImpl<$Res, _$InquiryImpl>
    implements _$$InquiryImplCopyWith<$Res> {
  __$$InquiryImplCopyWithImpl(
      _$InquiryImpl _value, $Res Function(_$InquiryImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? logo = freezed,
  }) {
    return _then(_$InquiryImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as dynamic,
      logo: freezed == logo
          ? _value._logo
          : logo // ignore: cast_nullable_to_non_nullable
              as List<dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InquiryImpl implements _Inquiry {
  const _$InquiryImpl(
      {@JsonKey(name: 'type') this.type,
      @JsonKey(name: 'title') this.title,
      @JsonKey(name: 'logo') final List<dynamic>? logo})
      : _logo = logo;

  factory _$InquiryImpl.fromJson(Map<String, dynamic> json) =>
      _$$InquiryImplFromJson(json);

  @override
  @JsonKey(name: 'type')
  final String? type;
  @override
  @JsonKey(name: 'title')
  final dynamic title;
  final List<dynamic>? _logo;
  @override
  @JsonKey(name: 'logo')
  List<dynamic>? get logo {
    final value = _logo;
    if (value == null) return null;
    if (_logo is EqualUnmodifiableListView) return _logo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Inquiry(type: $type, title: $title, logo: $logo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InquiryImpl &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other.title, title) &&
            const DeepCollectionEquality().equals(other._logo, _logo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      const DeepCollectionEquality().hash(title),
      const DeepCollectionEquality().hash(_logo));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$InquiryImplCopyWith<_$InquiryImpl> get copyWith =>
      __$$InquiryImplCopyWithImpl<_$InquiryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InquiryImplToJson(
      this,
    );
  }
}

abstract class _Inquiry implements Inquiry {
  const factory _Inquiry(
      {@JsonKey(name: 'type') final String? type,
      @JsonKey(name: 'title') final dynamic title,
      @JsonKey(name: 'logo') final List<dynamic>? logo}) = _$InquiryImpl;

  factory _Inquiry.fromJson(Map<String, dynamic> json) = _$InquiryImpl.fromJson;

  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'title')
  dynamic get title;
  @override
  @JsonKey(name: 'logo')
  List<dynamic>? get logo;
  @override
  @JsonKey(ignore: true)
  _$$InquiryImplCopyWith<_$InquiryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
