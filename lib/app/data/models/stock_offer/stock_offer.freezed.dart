// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stock_offer.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StockOffer _$StockOfferFromJson(Map<String, dynamic> json) {
  return _StockOffer.fromJson(json);
}

/// @nodoc
mixin _$StockOffer {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'offer_id')
  String? get offerId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_id')
  String? get adminId => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_id')
  dynamic get vendorId => throw _privateConstructorUsedError;
  @JsonKey(name: 'offer_price')
  double? get offerPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_offer_price')
  double? get lastOfferPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_last_offer_price')
  double? get updatedLastOfferPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_offer_price')
  double? get updatedOfferPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_price')
  double? get basePrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'base_price_vendor')
  double? get basePriceVendor => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_margin')
  double? get vendorMargin => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  OfferStatus? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_offer_id')
  String? get lastOfferId => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_action_id')
  String? get lastActionId => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_action_type')
  String? get lastActionType => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_response_required')
  bool? get isResponseRequired => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_completed')
  bool? get isCompleted => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock')
  DiamondEntity? get stock => throw _privateConstructorUsedError;
  @JsonKey(name: 'user')
  User? get user => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor')
  dynamic get vendor => throw _privateConstructorUsedError;

  /// Serializes this StockOffer to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StockOfferCopyWith<StockOffer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StockOfferCopyWith<$Res> {
  factory $StockOfferCopyWith(
          StockOffer value, $Res Function(StockOffer) then) =
      _$StockOfferCopyWithImpl<$Res, StockOffer>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'offer_id') String? offerId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'vendor_id') dynamic vendorId,
      @JsonKey(name: 'offer_price') double? offerPrice,
      @JsonKey(name: 'last_offer_price') double? lastOfferPrice,
      @JsonKey(name: 'updated_last_offer_price') double? updatedLastOfferPrice,
      @JsonKey(name: 'updated_offer_price') double? updatedOfferPrice,
      @JsonKey(name: 'base_price') double? basePrice,
      @JsonKey(name: 'base_price_vendor') double? basePriceVendor,
      @JsonKey(name: 'vendor_margin') double? vendorMargin,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'status') OfferStatus? status,
      @JsonKey(name: 'last_offer_id') String? lastOfferId,
      @JsonKey(name: 'last_action_id') String? lastActionId,
      @JsonKey(name: 'last_action_type') String? lastActionType,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: 'is_response_required') bool? isResponseRequired,
      @JsonKey(name: 'is_completed') bool? isCompleted,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'stock') DiamondEntity? stock,
      @JsonKey(name: 'user') User? user,
      @JsonKey(name: 'vendor') dynamic vendor});

  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$StockOfferCopyWithImpl<$Res, $Val extends StockOffer>
    implements $StockOfferCopyWith<$Res> {
  _$StockOfferCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? offerId = freezed,
    Object? stockId = freezed,
    Object? adminId = freezed,
    Object? vendorId = freezed,
    Object? offerPrice = freezed,
    Object? lastOfferPrice = freezed,
    Object? updatedLastOfferPrice = freezed,
    Object? updatedOfferPrice = freezed,
    Object? basePrice = freezed,
    Object? basePriceVendor = freezed,
    Object? vendorMargin = freezed,
    Object? buyRequestId = freezed,
    Object? orderId = freezed,
    Object? status = freezed,
    Object? lastOfferId = freezed,
    Object? lastActionId = freezed,
    Object? lastActionType = freezed,
    Object? isActive = freezed,
    Object? isResponseRequired = freezed,
    Object? isCompleted = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? stock = freezed,
    Object? user = freezed,
    Object? vendor = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      offerId: freezed == offerId
          ? _value.offerId
          : offerId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      offerPrice: freezed == offerPrice
          ? _value.offerPrice
          : offerPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      lastOfferPrice: freezed == lastOfferPrice
          ? _value.lastOfferPrice
          : lastOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedLastOfferPrice: freezed == updatedLastOfferPrice
          ? _value.updatedLastOfferPrice
          : updatedLastOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedOfferPrice: freezed == updatedOfferPrice
          ? _value.updatedOfferPrice
          : updatedOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      basePrice: freezed == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      basePriceVendor: freezed == basePriceVendor
          ? _value.basePriceVendor
          : basePriceVendor // ignore: cast_nullable_to_non_nullable
              as double?,
      vendorMargin: freezed == vendorMargin
          ? _value.vendorMargin
          : vendorMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OfferStatus?,
      lastOfferId: freezed == lastOfferId
          ? _value.lastOfferId
          : lastOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastActionId: freezed == lastActionId
          ? _value.lastActionId
          : lastActionId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastActionType: freezed == lastActionType
          ? _value.lastActionType
          : lastActionType // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isResponseRequired: freezed == isResponseRequired
          ? _value.isResponseRequired
          : isResponseRequired // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StockOfferImplCopyWith<$Res>
    implements $StockOfferCopyWith<$Res> {
  factory _$$StockOfferImplCopyWith(
          _$StockOfferImpl value, $Res Function(_$StockOfferImpl) then) =
      __$$StockOfferImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'offer_id') String? offerId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'admin_id') String? adminId,
      @JsonKey(name: 'vendor_id') dynamic vendorId,
      @JsonKey(name: 'offer_price') double? offerPrice,
      @JsonKey(name: 'last_offer_price') double? lastOfferPrice,
      @JsonKey(name: 'updated_last_offer_price') double? updatedLastOfferPrice,
      @JsonKey(name: 'updated_offer_price') double? updatedOfferPrice,
      @JsonKey(name: 'base_price') double? basePrice,
      @JsonKey(name: 'base_price_vendor') double? basePriceVendor,
      @JsonKey(name: 'vendor_margin') double? vendorMargin,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'status') OfferStatus? status,
      @JsonKey(name: 'last_offer_id') String? lastOfferId,
      @JsonKey(name: 'last_action_id') String? lastActionId,
      @JsonKey(name: 'last_action_type') String? lastActionType,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: 'is_response_required') bool? isResponseRequired,
      @JsonKey(name: 'is_completed') bool? isCompleted,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'stock') DiamondEntity? stock,
      @JsonKey(name: 'user') User? user,
      @JsonKey(name: 'vendor') dynamic vendor});

  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$StockOfferImplCopyWithImpl<$Res>
    extends _$StockOfferCopyWithImpl<$Res, _$StockOfferImpl>
    implements _$$StockOfferImplCopyWith<$Res> {
  __$$StockOfferImplCopyWithImpl(
      _$StockOfferImpl _value, $Res Function(_$StockOfferImpl) _then)
      : super(_value, _then);

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? offerId = freezed,
    Object? stockId = freezed,
    Object? adminId = freezed,
    Object? vendorId = freezed,
    Object? offerPrice = freezed,
    Object? lastOfferPrice = freezed,
    Object? updatedLastOfferPrice = freezed,
    Object? updatedOfferPrice = freezed,
    Object? basePrice = freezed,
    Object? basePriceVendor = freezed,
    Object? vendorMargin = freezed,
    Object? buyRequestId = freezed,
    Object? orderId = freezed,
    Object? status = freezed,
    Object? lastOfferId = freezed,
    Object? lastActionId = freezed,
    Object? lastActionType = freezed,
    Object? isActive = freezed,
    Object? isResponseRequired = freezed,
    Object? isCompleted = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? stock = freezed,
    Object? user = freezed,
    Object? vendor = freezed,
  }) {
    return _then(_$StockOfferImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      offerId: freezed == offerId
          ? _value.offerId
          : offerId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      offerPrice: freezed == offerPrice
          ? _value.offerPrice
          : offerPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      lastOfferPrice: freezed == lastOfferPrice
          ? _value.lastOfferPrice
          : lastOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedLastOfferPrice: freezed == updatedLastOfferPrice
          ? _value.updatedLastOfferPrice
          : updatedLastOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      updatedOfferPrice: freezed == updatedOfferPrice
          ? _value.updatedOfferPrice
          : updatedOfferPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      basePrice: freezed == basePrice
          ? _value.basePrice
          : basePrice // ignore: cast_nullable_to_non_nullable
              as double?,
      basePriceVendor: freezed == basePriceVendor
          ? _value.basePriceVendor
          : basePriceVendor // ignore: cast_nullable_to_non_nullable
              as double?,
      vendorMargin: freezed == vendorMargin
          ? _value.vendorMargin
          : vendorMargin // ignore: cast_nullable_to_non_nullable
              as double?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as OfferStatus?,
      lastOfferId: freezed == lastOfferId
          ? _value.lastOfferId
          : lastOfferId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastActionId: freezed == lastActionId
          ? _value.lastActionId
          : lastActionId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastActionType: freezed == lastActionType
          ? _value.lastActionType
          : lastActionType // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      isResponseRequired: freezed == isResponseRequired
          ? _value.isResponseRequired
          : isResponseRequired // ignore: cast_nullable_to_non_nullable
              as bool?,
      isCompleted: freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StockOfferImpl implements _StockOffer {
  const _$StockOfferImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'offer_id') this.offerId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'admin_id') this.adminId,
      @JsonKey(name: 'vendor_id') this.vendorId,
      @JsonKey(name: 'offer_price') this.offerPrice,
      @JsonKey(name: 'last_offer_price') this.lastOfferPrice,
      @JsonKey(name: 'updated_last_offer_price') this.updatedLastOfferPrice,
      @JsonKey(name: 'updated_offer_price') this.updatedOfferPrice,
      @JsonKey(name: 'base_price') this.basePrice,
      @JsonKey(name: 'base_price_vendor') this.basePriceVendor,
      @JsonKey(name: 'vendor_margin') this.vendorMargin,
      @JsonKey(name: 'buy_request_id') this.buyRequestId,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'last_offer_id') this.lastOfferId,
      @JsonKey(name: 'last_action_id') this.lastActionId,
      @JsonKey(name: 'last_action_type') this.lastActionType,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: 'is_response_required') this.isResponseRequired,
      @JsonKey(name: 'is_completed') this.isCompleted,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'stock') this.stock,
      @JsonKey(name: 'user') this.user,
      @JsonKey(name: 'vendor') this.vendor});

  factory _$StockOfferImpl.fromJson(Map<String, dynamic> json) =>
      _$$StockOfferImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'offer_id')
  final String? offerId;
  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'admin_id')
  final String? adminId;
  @override
  @JsonKey(name: 'vendor_id')
  final dynamic vendorId;
  @override
  @JsonKey(name: 'offer_price')
  final double? offerPrice;
  @override
  @JsonKey(name: 'last_offer_price')
  final double? lastOfferPrice;
  @override
  @JsonKey(name: 'updated_last_offer_price')
  final double? updatedLastOfferPrice;
  @override
  @JsonKey(name: 'updated_offer_price')
  final double? updatedOfferPrice;
  @override
  @JsonKey(name: 'base_price')
  final double? basePrice;
  @override
  @JsonKey(name: 'base_price_vendor')
  final double? basePriceVendor;
  @override
  @JsonKey(name: 'vendor_margin')
  final double? vendorMargin;
  @override
  @JsonKey(name: 'buy_request_id')
  final String? buyRequestId;
  @override
  @JsonKey(name: 'order_id')
  final String? orderId;
  @override
  @JsonKey(name: 'status')
  final OfferStatus? status;
  @override
  @JsonKey(name: 'last_offer_id')
  final String? lastOfferId;
  @override
  @JsonKey(name: 'last_action_id')
  final String? lastActionId;
  @override
  @JsonKey(name: 'last_action_type')
  final String? lastActionType;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: 'is_response_required')
  final bool? isResponseRequired;
  @override
  @JsonKey(name: 'is_completed')
  final bool? isCompleted;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'stock')
  final DiamondEntity? stock;
  @override
  @JsonKey(name: 'user')
  final User? user;
  @override
  @JsonKey(name: 'vendor')
  final dynamic vendor;

  @override
  String toString() {
    return 'StockOffer(id: $id, userId: $userId, offerId: $offerId, stockId: $stockId, adminId: $adminId, vendorId: $vendorId, offerPrice: $offerPrice, lastOfferPrice: $lastOfferPrice, updatedLastOfferPrice: $updatedLastOfferPrice, updatedOfferPrice: $updatedOfferPrice, basePrice: $basePrice, basePriceVendor: $basePriceVendor, vendorMargin: $vendorMargin, buyRequestId: $buyRequestId, orderId: $orderId, status: $status, lastOfferId: $lastOfferId, lastActionId: $lastActionId, lastActionType: $lastActionType, isActive: $isActive, isResponseRequired: $isResponseRequired, isCompleted: $isCompleted, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, stock: $stock, user: $user, vendor: $vendor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StockOfferImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.offerId, offerId) || other.offerId == offerId) &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            const DeepCollectionEquality().equals(other.vendorId, vendorId) &&
            (identical(other.offerPrice, offerPrice) ||
                other.offerPrice == offerPrice) &&
            (identical(other.lastOfferPrice, lastOfferPrice) ||
                other.lastOfferPrice == lastOfferPrice) &&
            (identical(other.updatedLastOfferPrice, updatedLastOfferPrice) ||
                other.updatedLastOfferPrice == updatedLastOfferPrice) &&
            (identical(other.updatedOfferPrice, updatedOfferPrice) ||
                other.updatedOfferPrice == updatedOfferPrice) &&
            (identical(other.basePrice, basePrice) ||
                other.basePrice == basePrice) &&
            (identical(other.basePriceVendor, basePriceVendor) ||
                other.basePriceVendor == basePriceVendor) &&
            (identical(other.vendorMargin, vendorMargin) ||
                other.vendorMargin == vendorMargin) &&
            (identical(other.buyRequestId, buyRequestId) ||
                other.buyRequestId == buyRequestId) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastOfferId, lastOfferId) ||
                other.lastOfferId == lastOfferId) &&
            (identical(other.lastActionId, lastActionId) ||
                other.lastActionId == lastActionId) &&
            (identical(other.lastActionType, lastActionType) ||
                other.lastActionType == lastActionType) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isResponseRequired, isResponseRequired) ||
                other.isResponseRequired == isResponseRequired) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.stock, stock) || other.stock == stock) &&
            (identical(other.user, user) || other.user == user) &&
            const DeepCollectionEquality().equals(other.vendor, vendor));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        offerId,
        stockId,
        adminId,
        const DeepCollectionEquality().hash(vendorId),
        offerPrice,
        lastOfferPrice,
        updatedLastOfferPrice,
        updatedOfferPrice,
        basePrice,
        basePriceVendor,
        vendorMargin,
        buyRequestId,
        orderId,
        status,
        lastOfferId,
        lastActionId,
        lastActionType,
        isActive,
        isResponseRequired,
        isCompleted,
        deleted,
        createdAt,
        updatedAt,
        stock,
        user,
        const DeepCollectionEquality().hash(vendor)
      ]);

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StockOfferImplCopyWith<_$StockOfferImpl> get copyWith =>
      __$$StockOfferImplCopyWithImpl<_$StockOfferImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StockOfferImplToJson(
      this,
    );
  }
}

abstract class _StockOffer implements StockOffer {
  const factory _StockOffer(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'offer_id') final String? offerId,
      @JsonKey(name: 'stock_id') final String? stockId,
      @JsonKey(name: 'admin_id') final String? adminId,
      @JsonKey(name: 'vendor_id') final dynamic vendorId,
      @JsonKey(name: 'offer_price') final double? offerPrice,
      @JsonKey(name: 'last_offer_price') final double? lastOfferPrice,
      @JsonKey(name: 'updated_last_offer_price')
      final double? updatedLastOfferPrice,
      @JsonKey(name: 'updated_offer_price') final double? updatedOfferPrice,
      @JsonKey(name: 'base_price') final double? basePrice,
      @JsonKey(name: 'base_price_vendor') final double? basePriceVendor,
      @JsonKey(name: 'vendor_margin') final double? vendorMargin,
      @JsonKey(name: 'buy_request_id') final String? buyRequestId,
      @JsonKey(name: 'order_id') final String? orderId,
      @JsonKey(name: 'status') final OfferStatus? status,
      @JsonKey(name: 'last_offer_id') final String? lastOfferId,
      @JsonKey(name: 'last_action_id') final String? lastActionId,
      @JsonKey(name: 'last_action_type') final String? lastActionType,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: 'is_response_required') final bool? isResponseRequired,
      @JsonKey(name: 'is_completed') final bool? isCompleted,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'stock') final DiamondEntity? stock,
      @JsonKey(name: 'user') final User? user,
      @JsonKey(name: 'vendor') final dynamic vendor}) = _$StockOfferImpl;

  factory _StockOffer.fromJson(Map<String, dynamic> json) =
      _$StockOfferImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'offer_id')
  String? get offerId;
  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'admin_id')
  String? get adminId;
  @override
  @JsonKey(name: 'vendor_id')
  dynamic get vendorId;
  @override
  @JsonKey(name: 'offer_price')
  double? get offerPrice;
  @override
  @JsonKey(name: 'last_offer_price')
  double? get lastOfferPrice;
  @override
  @JsonKey(name: 'updated_last_offer_price')
  double? get updatedLastOfferPrice;
  @override
  @JsonKey(name: 'updated_offer_price')
  double? get updatedOfferPrice;
  @override
  @JsonKey(name: 'base_price')
  double? get basePrice;
  @override
  @JsonKey(name: 'base_price_vendor')
  double? get basePriceVendor;
  @override
  @JsonKey(name: 'vendor_margin')
  double? get vendorMargin;
  @override
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId;
  @override
  @JsonKey(name: 'order_id')
  String? get orderId;
  @override
  @JsonKey(name: 'status')
  OfferStatus? get status;
  @override
  @JsonKey(name: 'last_offer_id')
  String? get lastOfferId;
  @override
  @JsonKey(name: 'last_action_id')
  String? get lastActionId;
  @override
  @JsonKey(name: 'last_action_type')
  String? get lastActionType;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: 'is_response_required')
  bool? get isResponseRequired;
  @override
  @JsonKey(name: 'is_completed')
  bool? get isCompleted;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'stock')
  DiamondEntity? get stock;
  @override
  @JsonKey(name: 'user')
  User? get user;
  @override
  @JsonKey(name: 'vendor')
  dynamic get vendor;

  /// Create a copy of StockOffer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StockOfferImplCopyWith<_$StockOfferImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
