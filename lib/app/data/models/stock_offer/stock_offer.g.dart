// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_offer.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StockOfferImpl _$$StockOfferImplFromJson(Map<String, dynamic> json) =>
    _$StockOfferImpl(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      offerId: json['offer_id'] as String?,
      stockId: json['stock_id'] as String?,
      adminId: json['admin_id'] as String?,
      vendorId: json['vendor_id'],
      offerPrice: (json['offer_price'] as num?)?.toDouble(),
      lastOfferPrice: (json['last_offer_price'] as num?)?.toDouble(),
      updatedLastOfferPrice:
          (json['updated_last_offer_price'] as num?)?.toDouble(),
      updatedOfferPrice: (json['updated_offer_price'] as num?)?.toDouble(),
      basePrice: (json['base_price'] as num?)?.toDouble(),
      basePriceVendor: (json['base_price_vendor'] as num?)?.toDouble(),
      vendorMargin: (json['vendor_margin'] as num?)?.toDouble(),
      buyRequestId: json['buy_request_id'] as String?,
      orderId: json['order_id'] as String?,
      status: $enumDecodeNullable(_$OfferStatusEnumMap, json['status']),
      lastOfferId: json['last_offer_id'] as String?,
      lastActionId: json['last_action_id'] as String?,
      lastActionType: json['last_action_type'] as String?,
      isActive: json['is_active'] as bool?,
      isResponseRequired: json['is_response_required'] as bool?,
      isCompleted: json['is_completed'] as bool?,
      deleted: json['_deleted'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      stock: json['stock'] == null
          ? null
          : DiamondEntity.fromJson(json['stock'] as Map<String, dynamic>),
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
      vendor: json['vendor'],
    );

Map<String, dynamic> _$$StockOfferImplToJson(_$StockOfferImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'offer_id': instance.offerId,
      'stock_id': instance.stockId,
      'admin_id': instance.adminId,
      'vendor_id': instance.vendorId,
      'offer_price': instance.offerPrice,
      'last_offer_price': instance.lastOfferPrice,
      'updated_last_offer_price': instance.updatedLastOfferPrice,
      'updated_offer_price': instance.updatedOfferPrice,
      'base_price': instance.basePrice,
      'base_price_vendor': instance.basePriceVendor,
      'vendor_margin': instance.vendorMargin,
      'buy_request_id': instance.buyRequestId,
      'order_id': instance.orderId,
      'status': _$OfferStatusEnumMap[instance.status],
      'last_offer_id': instance.lastOfferId,
      'last_action_id': instance.lastActionId,
      'last_action_type': instance.lastActionType,
      'is_active': instance.isActive,
      'is_response_required': instance.isResponseRequired,
      'is_completed': instance.isCompleted,
      '_deleted': instance.deleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'stock': instance.stock,
      'user': instance.user,
      'vendor': instance.vendor,
    };

const _$OfferStatusEnumMap = {
  OfferStatus.PENDING: 'PENDING',
  OfferStatus.ACCEPTED: 'ACCEPTED',
  OfferStatus.REJECTED: 'REJECTED',
  OfferStatus.REVISED: 'REVISED',
};
