import 'package:freezed_annotation/freezed_annotation.dart';

part 'crm_model.freezed.dart';
part 'crm_model.g.dart';

@freezed
class CRMModel with _$CRMModel {
  const factory CRMModel({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'whats_app') String? whatsApp,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'telegram') String? telegram,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone') String? phone,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'appointment') String? appointment,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'contact_name') String? contactName,
  }) = _CRMModel;

  factory CRMModel.fromJson(Map<String, dynamic> json) =>
      _$CRMModelFromJson(json);
}
