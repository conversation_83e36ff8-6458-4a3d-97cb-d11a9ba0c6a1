import 'dart:ui';

import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:isar/isar.dart';

part 'supplier_entity.g.dart';

@embedded
class Supplier {
  String? id;
  String? name;
  String? description;
  String? address;
  String? phone;
  String? email;
  String? website;
  String? skype;
  String? whatsapp;
  String? wechat;
  String? country;
  String? city;
  String? logo;
  String? firstName;
  String? lastName;
  bool isKycDone;

  Supplier({
    this.id,
    this.name,
    this.description,
    this.address,
    this.phone,
    this.email,
    this.website,
    this.skype,
    this.whatsapp,
    this.wechat,
    this.logo,
    this.country,
    this.city,
    this.isKycDone = false,
    this.firstName,
    this.lastName,
  });

  factory Supplier.fromJson(Map<String, dynamic> json) => Supplier(
        id: json['_id'] as String?,
        name: json['company_name'] as String?,
        description: json['description'] as String?,
        address: json['address'] as String?,
        phone: json['phone'] as String?,
        email: json['email'] as String?,
        website: json['website'] as String?,
        skype: json['skype'] as String?,
        whatsapp: json['whatsapp'] as String?,
        wechat: json['wechat'] as String?,
        logo: json['logo'] as String?,
        country: json['country'] as String?,
        city: json['city'] as String?,
        firstName: json['first_name'] as String?,
        lastName: json['last_name'] as String?,
        isKycDone: json['kyc_done'] as bool? ?? false,
      );

  String? get fullName {
    var name = StringUtils.listToString(
      [
        firstName,
        lastName,
      ],
      separator: ' ',
    );
    return name.isEmpty ? 'Phone Call' : name;
  }

  @ignore
  String? get countryDisplay {
    var countryStr = country;

    if (country != null) {
      var _ = CountryParser.tryParse(country!);
      if (_ == null) {
        countryStr = country;
      } else {
        countryStr = '${_.flagEmoji} ${_.name}';
      }
    }

    return countryStr;
  }

  String? get whatsAppLink {
    if (whatsapp != null) {
      var data = whatsapp!.replaceFirst('+', '').split('-').join();

      var _ = 'https://wa.me/$data';
      return _;
    }
    return null;
  }

  String? get phoneLink {
    if (phone != null) {
      var _ = 'tel://+$phone';
      return _;
    }
    return null;
  }

  String? get skypeLink {
    // Demo link to open skype chat using username
    // https://join.skype.com/invite/deepss121

    if (skype != null) {
      var _ = 'skype://$skype?chat';
      return _;
    }
    return null;
  }

  String? get wechatLink {
    if (wechat != null) {
      var _ = 'weixin://dl/chat?$wechat';
      return _;
    }
    return null;
  }

  bool get hasContact {
    return (phone?.isNotEmpty ?? false) ||
        (whatsapp?.isNotEmpty ?? false) ||
        (wechat?.isNotEmpty ?? false) ||
        (skype?.isNotEmpty ?? false);
  }

  String get addressOrCityCountry {
    if (address != null && address!.isNotEmpty) {
      return address!;
    }

    return StringUtils.listToString(
      [
        city,
        country,
      ],
      separator: ', ',
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id,
        'company_name': name,
        'description': description,
        'address': address,
        'phone': phone,
        'email': email,
        'website': website,
        'skype': skype,
        'whatsapp': whatsapp,
        'wechat': wechat,
        'logo': logo,
        'country': country,
        'city': city,
      };

  Color getColor() {
    if (name == null || name!.length < 2) {
      return AppColors.k1A47E8;
    }

    int asciiValue1 = name![0].codeUnitAt(0);
    int asciiValue2 = name![1].codeUnitAt(0);

    int red = ((asciiValue1 * 23 + asciiValue2 * 37) % 150 + 50).clamp(50, 200);
    int green =
        ((asciiValue2 * 47 + asciiValue1 * 19) % 150 + 50).clamp(50, 200);
    int blue =
        ((asciiValue1 * 53 + asciiValue2 * 29) % 150 + 50).clamp(50, 200);

    return Color.fromRGBO(red, green, blue, 1.0);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is Supplier && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
