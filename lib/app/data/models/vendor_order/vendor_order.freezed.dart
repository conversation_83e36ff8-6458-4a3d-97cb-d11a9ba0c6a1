// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vendor_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VendorOrder _$VendorOrderFromJson(Map<String, dynamic> json) {
  return _VendorOrder.fromJson(json);
}

/// @nodoc
mixin _$VendorOrder {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_id')
  String? get vendorId => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'invoice_name')
  String? get invoiceName => throw _privateConstructorUsedError;
  @JsonKey(name: 'invoice_url')
  String? get invoiceUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'reject_reason')
  String? get rejectReason => throw _privateConstructorUsedError;
  @JsonKey(name: 'dollar_rate')
  dynamic get dollarRate => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_uploading_invoice')
  bool? get isUploadingInvoice => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_invoice_accepted')
  bool? get isInvoiceAccepted => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_invoice_action_taken')
  bool? get isInvoiceActionTaken => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'order')
  UserOrder? get order => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor')
  AdminUserEntity? get vendor => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_offer')
  StockOffer? get stockOffer => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock')
  DiamondEntity? get stock => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VendorOrderCopyWith<VendorOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VendorOrderCopyWith<$Res> {
  factory $VendorOrderCopyWith(
          VendorOrder value, $Res Function(VendorOrder) then) =
      _$VendorOrderCopyWithImpl<$Res, VendorOrder>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'invoice_name') String? invoiceName,
      @JsonKey(name: 'invoice_url') String? invoiceUrl,
      @JsonKey(name: 'reject_reason') String? rejectReason,
      @JsonKey(name: 'dollar_rate') dynamic dollarRate,
      @JsonKey(name: 'is_uploading_invoice') bool? isUploadingInvoice,
      @JsonKey(name: 'is_invoice_accepted') bool? isInvoiceAccepted,
      @JsonKey(name: 'is_invoice_action_taken') bool? isInvoiceActionTaken,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'vendor') AdminUserEntity? vendor,
      @JsonKey(name: 'stock_offer') StockOffer? stockOffer,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  $UserOrderCopyWith<$Res>? get order;
  $StockOfferCopyWith<$Res>? get stockOffer;
}

/// @nodoc
class _$VendorOrderCopyWithImpl<$Res, $Val extends VendorOrder>
    implements $VendorOrderCopyWith<$Res> {
  _$VendorOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? buyRequestId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? invoiceName = freezed,
    Object? invoiceUrl = freezed,
    Object? rejectReason = freezed,
    Object? dollarRate = freezed,
    Object? isUploadingInvoice = freezed,
    Object? isInvoiceAccepted = freezed,
    Object? isInvoiceActionTaken = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? order = freezed,
    Object? vendor = freezed,
    Object? stockOffer = freezed,
    Object? stock = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceName: freezed == invoiceName
          ? _value.invoiceName
          : invoiceName // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceUrl: freezed == invoiceUrl
          ? _value.invoiceUrl
          : invoiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
      dollarRate: freezed == dollarRate
          ? _value.dollarRate
          : dollarRate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isUploadingInvoice: freezed == isUploadingInvoice
          ? _value.isUploadingInvoice
          : isUploadingInvoice // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInvoiceAccepted: freezed == isInvoiceAccepted
          ? _value.isInvoiceAccepted
          : isInvoiceAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInvoiceActionTaken: freezed == isInvoiceActionTaken
          ? _value.isInvoiceActionTaken
          : isInvoiceActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as AdminUserEntity?,
      stockOffer: freezed == stockOffer
          ? _value.stockOffer
          : stockOffer // ignore: cast_nullable_to_non_nullable
              as StockOffer?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserOrderCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $UserOrderCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $StockOfferCopyWith<$Res>? get stockOffer {
    if (_value.stockOffer == null) {
      return null;
    }

    return $StockOfferCopyWith<$Res>(_value.stockOffer!, (value) {
      return _then(_value.copyWith(stockOffer: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VendorOrderImplCopyWith<$Res>
    implements $VendorOrderCopyWith<$Res> {
  factory _$$VendorOrderImplCopyWith(
          _$VendorOrderImpl value, $Res Function(_$VendorOrderImpl) then) =
      __$$VendorOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'invoice_name') String? invoiceName,
      @JsonKey(name: 'invoice_url') String? invoiceUrl,
      @JsonKey(name: 'reject_reason') String? rejectReason,
      @JsonKey(name: 'dollar_rate') dynamic dollarRate,
      @JsonKey(name: 'is_uploading_invoice') bool? isUploadingInvoice,
      @JsonKey(name: 'is_invoice_accepted') bool? isInvoiceAccepted,
      @JsonKey(name: 'is_invoice_action_taken') bool? isInvoiceActionTaken,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'vendor') AdminUserEntity? vendor,
      @JsonKey(name: 'stock_offer') StockOffer? stockOffer,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  @override
  $UserOrderCopyWith<$Res>? get order;
  @override
  $StockOfferCopyWith<$Res>? get stockOffer;
}

/// @nodoc
class __$$VendorOrderImplCopyWithImpl<$Res>
    extends _$VendorOrderCopyWithImpl<$Res, _$VendorOrderImpl>
    implements _$$VendorOrderImplCopyWith<$Res> {
  __$$VendorOrderImplCopyWithImpl(
      _$VendorOrderImpl _value, $Res Function(_$VendorOrderImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? buyRequestId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? invoiceName = freezed,
    Object? invoiceUrl = freezed,
    Object? rejectReason = freezed,
    Object? dollarRate = freezed,
    Object? isUploadingInvoice = freezed,
    Object? isInvoiceAccepted = freezed,
    Object? isInvoiceActionTaken = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? order = freezed,
    Object? vendor = freezed,
    Object? stockOffer = freezed,
    Object? stock = freezed,
  }) {
    return _then(_$VendorOrderImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceName: freezed == invoiceName
          ? _value.invoiceName
          : invoiceName // ignore: cast_nullable_to_non_nullable
              as String?,
      invoiceUrl: freezed == invoiceUrl
          ? _value.invoiceUrl
          : invoiceUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
      dollarRate: freezed == dollarRate
          ? _value.dollarRate
          : dollarRate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isUploadingInvoice: freezed == isUploadingInvoice
          ? _value.isUploadingInvoice
          : isUploadingInvoice // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInvoiceAccepted: freezed == isInvoiceAccepted
          ? _value.isInvoiceAccepted
          : isInvoiceAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isInvoiceActionTaken: freezed == isInvoiceActionTaken
          ? _value.isInvoiceActionTaken
          : isInvoiceActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as AdminUserEntity?,
      stockOffer: freezed == stockOffer
          ? _value.stockOffer
          : stockOffer // ignore: cast_nullable_to_non_nullable
              as StockOffer?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VendorOrderImpl implements _VendorOrder {
  const _$VendorOrderImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'vendor_id') this.vendorId,
      @JsonKey(name: 'buy_request_id') this.buyRequestId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'invoice_name') this.invoiceName,
      @JsonKey(name: 'invoice_url') this.invoiceUrl,
      @JsonKey(name: 'reject_reason') this.rejectReason,
      @JsonKey(name: 'dollar_rate') this.dollarRate,
      @JsonKey(name: 'is_uploading_invoice') this.isUploadingInvoice,
      @JsonKey(name: 'is_invoice_accepted') this.isInvoiceAccepted,
      @JsonKey(name: 'is_invoice_action_taken') this.isInvoiceActionTaken,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'order') this.order,
      @JsonKey(name: 'vendor') this.vendor,
      @JsonKey(name: 'stock_offer') this.stockOffer,
      @JsonKey(name: 'stock') this.stock});

  factory _$VendorOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$VendorOrderImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'order_id')
  final String? orderId;
  @override
  @JsonKey(name: 'vendor_id')
  final String? vendorId;
  @override
  @JsonKey(name: 'buy_request_id')
  final String? buyRequestId;
  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'status')
  final String? status;
  @override
  @JsonKey(name: 'invoice_name')
  final String? invoiceName;
  @override
  @JsonKey(name: 'invoice_url')
  final String? invoiceUrl;
  @override
  @JsonKey(name: 'reject_reason')
  final String? rejectReason;
  @override
  @JsonKey(name: 'dollar_rate')
  final dynamic dollarRate;
  @override
  @JsonKey(name: 'is_uploading_invoice')
  final bool? isUploadingInvoice;
  @override
  @JsonKey(name: 'is_invoice_accepted')
  final bool? isInvoiceAccepted;
  @override
  @JsonKey(name: 'is_invoice_action_taken')
  final bool? isInvoiceActionTaken;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'order')
  final UserOrder? order;
  @override
  @JsonKey(name: 'vendor')
  final AdminUserEntity? vendor;
  @override
  @JsonKey(name: 'stock_offer')
  final StockOffer? stockOffer;
  @override
  @JsonKey(name: 'stock')
  final DiamondEntity? stock;

  @override
  String toString() {
    return 'VendorOrder(id: $id, orderId: $orderId, vendorId: $vendorId, buyRequestId: $buyRequestId, stockId: $stockId, status: $status, invoiceName: $invoiceName, invoiceUrl: $invoiceUrl, rejectReason: $rejectReason, dollarRate: $dollarRate, isUploadingInvoice: $isUploadingInvoice, isInvoiceAccepted: $isInvoiceAccepted, isInvoiceActionTaken: $isInvoiceActionTaken, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, order: $order, vendor: $vendor, stockOffer: $stockOffer, stock: $stock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VendorOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.vendorId, vendorId) ||
                other.vendorId == vendorId) &&
            (identical(other.buyRequestId, buyRequestId) ||
                other.buyRequestId == buyRequestId) &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.invoiceName, invoiceName) ||
                other.invoiceName == invoiceName) &&
            (identical(other.invoiceUrl, invoiceUrl) ||
                other.invoiceUrl == invoiceUrl) &&
            (identical(other.rejectReason, rejectReason) ||
                other.rejectReason == rejectReason) &&
            const DeepCollectionEquality()
                .equals(other.dollarRate, dollarRate) &&
            (identical(other.isUploadingInvoice, isUploadingInvoice) ||
                other.isUploadingInvoice == isUploadingInvoice) &&
            (identical(other.isInvoiceAccepted, isInvoiceAccepted) ||
                other.isInvoiceAccepted == isInvoiceAccepted) &&
            (identical(other.isInvoiceActionTaken, isInvoiceActionTaken) ||
                other.isInvoiceActionTaken == isInvoiceActionTaken) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.vendor, vendor) || other.vendor == vendor) &&
            (identical(other.stockOffer, stockOffer) ||
                other.stockOffer == stockOffer) &&
            (identical(other.stock, stock) || other.stock == stock));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        orderId,
        vendorId,
        buyRequestId,
        stockId,
        status,
        invoiceName,
        invoiceUrl,
        rejectReason,
        const DeepCollectionEquality().hash(dollarRate),
        isUploadingInvoice,
        isInvoiceAccepted,
        isInvoiceActionTaken,
        isActive,
        deleted,
        createdAt,
        updatedAt,
        order,
        vendor,
        stockOffer,
        stock
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VendorOrderImplCopyWith<_$VendorOrderImpl> get copyWith =>
      __$$VendorOrderImplCopyWithImpl<_$VendorOrderImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VendorOrderImplToJson(
      this,
    );
  }
}

abstract class _VendorOrder implements VendorOrder {
  const factory _VendorOrder(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'order_id') final String? orderId,
      @JsonKey(name: 'vendor_id') final String? vendorId,
      @JsonKey(name: 'buy_request_id') final String? buyRequestId,
      @JsonKey(name: 'stock_id') final String? stockId,
      @JsonKey(name: 'status') final String? status,
      @JsonKey(name: 'invoice_name') final String? invoiceName,
      @JsonKey(name: 'invoice_url') final String? invoiceUrl,
      @JsonKey(name: 'reject_reason') final String? rejectReason,
      @JsonKey(name: 'dollar_rate') final dynamic dollarRate,
      @JsonKey(name: 'is_uploading_invoice') final bool? isUploadingInvoice,
      @JsonKey(name: 'is_invoice_accepted') final bool? isInvoiceAccepted,
      @JsonKey(name: 'is_invoice_action_taken')
      final bool? isInvoiceActionTaken,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'order') final UserOrder? order,
      @JsonKey(name: 'vendor') final AdminUserEntity? vendor,
      @JsonKey(name: 'stock_offer') final StockOffer? stockOffer,
      @JsonKey(name: 'stock') final DiamondEntity? stock}) = _$VendorOrderImpl;

  factory _VendorOrder.fromJson(Map<String, dynamic> json) =
      _$VendorOrderImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'order_id')
  String? get orderId;
  @override
  @JsonKey(name: 'vendor_id')
  String? get vendorId;
  @override
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId;
  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: 'invoice_name')
  String? get invoiceName;
  @override
  @JsonKey(name: 'invoice_url')
  String? get invoiceUrl;
  @override
  @JsonKey(name: 'reject_reason')
  String? get rejectReason;
  @override
  @JsonKey(name: 'dollar_rate')
  dynamic get dollarRate;
  @override
  @JsonKey(name: 'is_uploading_invoice')
  bool? get isUploadingInvoice;
  @override
  @JsonKey(name: 'is_invoice_accepted')
  bool? get isInvoiceAccepted;
  @override
  @JsonKey(name: 'is_invoice_action_taken')
  bool? get isInvoiceActionTaken;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'order')
  UserOrder? get order;
  @override
  @JsonKey(name: 'vendor')
  AdminUserEntity? get vendor;
  @override
  @JsonKey(name: 'stock_offer')
  StockOffer? get stockOffer;
  @override
  @JsonKey(name: 'stock')
  DiamondEntity? get stock;
  @override
  @JsonKey(ignore: true)
  _$$VendorOrderImplCopyWith<_$VendorOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
