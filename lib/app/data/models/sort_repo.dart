import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:get/get_utils/get_utils.dart';

enum SortBy {
  finalPrice,
  weight,
  discounts,
  pricePerCarat,
  color,
}

enum SortOrder { asc, desc }

List<SortBy> sortOptions = [
  SortBy.finalPrice,
  // SortBy.discounts,
  SortBy.pricePerCarat,
  SortBy.weight,
  SortBy.color,
];

extension SortOptionExt on SortBy {
  String get name {
    switch (this) {
      case SortBy.finalPrice:
        return LocaleKeys.filter_final_price.tr;
      case SortBy.weight:
        return LocaleKeys.filter_weight.tr;
      /*case SortBy.updatedAt:
        return "Updated At";*/
      case SortBy.discounts:
        return LocaleKeys.filter_discounts.tr;
      case SortBy.pricePerCarat:
        return LocaleKeys.filter_price_per_carat.tr;
      case SortBy.color:
        return LocaleKeys.filter_color.tr;
      default:
        return "Updated At";
    }
  }

  String get apiValue {
    switch (this) {
      case SortBy.finalPrice:
        return "final_price";
      case SortBy.weight:
        return "weight";
      /*case SortBy.updatedAt:
        return "updatedAt";*/
      case SortBy.discounts:
        return "discounts";
      case SortBy.pricePerCarat:
        return "price_per_caret";
      case SortBy.color:
        return "color";
      default:
        return "updatedAt";
    }
  }

  String get ascExample {
    switch (this) {
      case SortBy.finalPrice:
        return r'$2,345, $3,345, $4,345, ...';
      case SortBy.weight:
        return r'1.23, 2.24, 3.25, ...';
      case SortBy.discounts:
        return r'-30, -29, -27, ...';
      case SortBy.pricePerCarat:
        return r'$345, $445, $545, ...';
      case SortBy.color:
        return r'D, E, F, ...';
      default:
        return '';
    }
  }

  String get descExample {
    switch (this) {
      case SortBy.finalPrice:
        return r'$4,345, $3,345, $2,345, ...';
      case SortBy.weight:
        return r'3.25, 2.24, 1.23, ...';
      case SortBy.discounts:
        return r'-27, -29, -30, ...';
      case SortBy.pricePerCarat:
        return r'$545, $445, $345, ...';
      case SortBy.color:
        return r'Z, Y, X, ...';
      default:
        return '';
    }
  }

  SortOrder get defaultOrder {
    switch (this) {
      case SortBy.finalPrice:
        return SortOrder.asc;
      case SortBy.weight:
        return SortOrder.desc;
      case SortBy.discounts:
        return SortOrder.asc;
      case SortBy.pricePerCarat:
        return SortOrder.asc;
      case SortBy.color:
        return SortOrder.asc;
      default:
        return SortOrder.desc;
    }
  }
}

extension SortDirectionExt on SortOrder {
  String get name {
    switch (this) {
      case SortOrder.asc:
        return "Asc";
      case SortOrder.desc:
        return "Desc";
      default:
        return "Desc";
    }
  }

  String get apiValue {
    switch (this) {
      case SortOrder.asc:
        return "ASC";
      case SortOrder.desc:
        return "DESC";
      default:
        return "DESC";
    }
  }
}

class SortOption {
  SortBy sortBy;
  SortOrder sortOrder;

  SortOption({
    required this.sortBy,
    required this.sortOrder,
  });

  @override
  bool operator ==(Object other) {
    return other is SortOption &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder;
  }

  String get name => sortBy.name;

  String get by => sortBy.apiValue;

  String get order => sortOrder.apiValue;

  bool get isAscending => sortOrder == SortOrder.asc;

  bool get isDescending => sortOrder == SortOrder.desc;

  bool get isDefault =>
      sortBy == SortBy.discounts && sortOrder == SortOrder.asc;

  bool get isNotDefault => !isDefault;

  void toggleSortOrder() {
    if (sortOrder == SortOrder.asc) {
      sortOrder = SortOrder.desc;
    } else {
      sortOrder = SortOrder.asc;
    }
  }

  @override
  int get hashCode => by.hashCode + order.hashCode;
}
