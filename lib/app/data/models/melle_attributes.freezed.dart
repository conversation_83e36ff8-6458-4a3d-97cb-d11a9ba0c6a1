// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'melle_attributes.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MelleAttributes _$MelleAttributesFromJson(Map<String, dynamic> json) {
  return _MelleAttributes.fromJson(json);
}

/// @nodoc
mixin _$MelleAttributes {
  @JsonKey(name: 'fancy_color')
  List<String>? get fancyColor => throw _privateConstructorUsedError;
  @JsonKey(name: 'fancy_intensity')
  List<String>? get fancyIntensity => throw _privateConstructorUsedError;
  @JsonKey(name: 'fancy_overtone')
  List<String>? get fancyOvertone => throw _privateConstructorUsedError;
  @JsonKey(name: 'white_color')
  List<String>? get whiteColor => throw _privateConstructorUsedError;
  @JsonKey(name: 'seive_size')
  List<String>? get seiveSize => throw _privateConstructorUsedError;
  @JsonKey(name: 'milimeter')
  List<String>? get milimeter => throw _privateConstructorUsedError;
  @JsonKey(name: 'pointer')
  List<String>? get pointer => throw _privateConstructorUsedError;
  @JsonKey(name: 'clarity')
  List<String>? get clarity => throw _privateConstructorUsedError;
  @JsonKey(name: 'shape')
  List<String>? get shape => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_quantity')
  int? get maxQuantity => throw _privateConstructorUsedError;
  @JsonKey(name: 'per_diamond_caret')
  double? get perDiamondCaret => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_per_caet')
  double? get pricePerCaret => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_price')
  double? get totalPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'measurements')
  List<Measurement>? get measurements => throw _privateConstructorUsedError;

  /// Serializes this MelleAttributes to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MelleAttributes
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MelleAttributesCopyWith<MelleAttributes> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MelleAttributesCopyWith<$Res> {
  factory $MelleAttributesCopyWith(
          MelleAttributes value, $Res Function(MelleAttributes) then) =
      _$MelleAttributesCopyWithImpl<$Res, MelleAttributes>;
  @useResult
  $Res call(
      {@JsonKey(name: 'fancy_color') List<String>? fancyColor,
      @JsonKey(name: 'fancy_intensity') List<String>? fancyIntensity,
      @JsonKey(name: 'fancy_overtone') List<String>? fancyOvertone,
      @JsonKey(name: 'white_color') List<String>? whiteColor,
      @JsonKey(name: 'seive_size') List<String>? seiveSize,
      @JsonKey(name: 'milimeter') List<String>? milimeter,
      @JsonKey(name: 'pointer') List<String>? pointer,
      @JsonKey(name: 'clarity') List<String>? clarity,
      @JsonKey(name: 'shape') List<String>? shape,
      @JsonKey(name: 'max_quantity') int? maxQuantity,
      @JsonKey(name: 'per_diamond_caret') double? perDiamondCaret,
      @JsonKey(name: 'price_per_caet') double? pricePerCaret,
      @JsonKey(name: 'total_price') double? totalPrice,
      @JsonKey(name: 'measurements') List<Measurement>? measurements});
}

/// @nodoc
class _$MelleAttributesCopyWithImpl<$Res, $Val extends MelleAttributes>
    implements $MelleAttributesCopyWith<$Res> {
  _$MelleAttributesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MelleAttributes
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fancyColor = freezed,
    Object? fancyIntensity = freezed,
    Object? fancyOvertone = freezed,
    Object? whiteColor = freezed,
    Object? seiveSize = freezed,
    Object? milimeter = freezed,
    Object? pointer = freezed,
    Object? clarity = freezed,
    Object? shape = freezed,
    Object? maxQuantity = freezed,
    Object? perDiamondCaret = freezed,
    Object? pricePerCaret = freezed,
    Object? totalPrice = freezed,
    Object? measurements = freezed,
  }) {
    return _then(_value.copyWith(
      fancyColor: freezed == fancyColor
          ? _value.fancyColor
          : fancyColor // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      fancyIntensity: freezed == fancyIntensity
          ? _value.fancyIntensity
          : fancyIntensity // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      fancyOvertone: freezed == fancyOvertone
          ? _value.fancyOvertone
          : fancyOvertone // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      whiteColor: freezed == whiteColor
          ? _value.whiteColor
          : whiteColor // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      seiveSize: freezed == seiveSize
          ? _value.seiveSize
          : seiveSize // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      milimeter: freezed == milimeter
          ? _value.milimeter
          : milimeter // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      pointer: freezed == pointer
          ? _value.pointer
          : pointer // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      clarity: freezed == clarity
          ? _value.clarity
          : clarity // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      shape: freezed == shape
          ? _value.shape
          : shape // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      maxQuantity: freezed == maxQuantity
          ? _value.maxQuantity
          : maxQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      perDiamondCaret: freezed == perDiamondCaret
          ? _value.perDiamondCaret
          : perDiamondCaret // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCaret: freezed == pricePerCaret
          ? _value.pricePerCaret
          : pricePerCaret // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPrice: freezed == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      measurements: freezed == measurements
          ? _value.measurements
          : measurements // ignore: cast_nullable_to_non_nullable
              as List<Measurement>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MelleAttributesImplCopyWith<$Res>
    implements $MelleAttributesCopyWith<$Res> {
  factory _$$MelleAttributesImplCopyWith(_$MelleAttributesImpl value,
          $Res Function(_$MelleAttributesImpl) then) =
      __$$MelleAttributesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'fancy_color') List<String>? fancyColor,
      @JsonKey(name: 'fancy_intensity') List<String>? fancyIntensity,
      @JsonKey(name: 'fancy_overtone') List<String>? fancyOvertone,
      @JsonKey(name: 'white_color') List<String>? whiteColor,
      @JsonKey(name: 'seive_size') List<String>? seiveSize,
      @JsonKey(name: 'milimeter') List<String>? milimeter,
      @JsonKey(name: 'pointer') List<String>? pointer,
      @JsonKey(name: 'clarity') List<String>? clarity,
      @JsonKey(name: 'shape') List<String>? shape,
      @JsonKey(name: 'max_quantity') int? maxQuantity,
      @JsonKey(name: 'per_diamond_caret') double? perDiamondCaret,
      @JsonKey(name: 'price_per_caet') double? pricePerCaret,
      @JsonKey(name: 'total_price') double? totalPrice,
      @JsonKey(name: 'measurements') List<Measurement>? measurements});
}

/// @nodoc
class __$$MelleAttributesImplCopyWithImpl<$Res>
    extends _$MelleAttributesCopyWithImpl<$Res, _$MelleAttributesImpl>
    implements _$$MelleAttributesImplCopyWith<$Res> {
  __$$MelleAttributesImplCopyWithImpl(
      _$MelleAttributesImpl _value, $Res Function(_$MelleAttributesImpl) _then)
      : super(_value, _then);

  /// Create a copy of MelleAttributes
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fancyColor = freezed,
    Object? fancyIntensity = freezed,
    Object? fancyOvertone = freezed,
    Object? whiteColor = freezed,
    Object? seiveSize = freezed,
    Object? milimeter = freezed,
    Object? pointer = freezed,
    Object? clarity = freezed,
    Object? shape = freezed,
    Object? maxQuantity = freezed,
    Object? perDiamondCaret = freezed,
    Object? pricePerCaret = freezed,
    Object? totalPrice = freezed,
    Object? measurements = freezed,
  }) {
    return _then(_$MelleAttributesImpl(
      fancyColor: freezed == fancyColor
          ? _value._fancyColor
          : fancyColor // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      fancyIntensity: freezed == fancyIntensity
          ? _value._fancyIntensity
          : fancyIntensity // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      fancyOvertone: freezed == fancyOvertone
          ? _value._fancyOvertone
          : fancyOvertone // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      whiteColor: freezed == whiteColor
          ? _value._whiteColor
          : whiteColor // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      seiveSize: freezed == seiveSize
          ? _value._seiveSize
          : seiveSize // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      milimeter: freezed == milimeter
          ? _value._milimeter
          : milimeter // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      pointer: freezed == pointer
          ? _value._pointer
          : pointer // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      clarity: freezed == clarity
          ? _value._clarity
          : clarity // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      shape: freezed == shape
          ? _value._shape
          : shape // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      maxQuantity: freezed == maxQuantity
          ? _value.maxQuantity
          : maxQuantity // ignore: cast_nullable_to_non_nullable
              as int?,
      perDiamondCaret: freezed == perDiamondCaret
          ? _value.perDiamondCaret
          : perDiamondCaret // ignore: cast_nullable_to_non_nullable
              as double?,
      pricePerCaret: freezed == pricePerCaret
          ? _value.pricePerCaret
          : pricePerCaret // ignore: cast_nullable_to_non_nullable
              as double?,
      totalPrice: freezed == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      measurements: freezed == measurements
          ? _value._measurements
          : measurements // ignore: cast_nullable_to_non_nullable
              as List<Measurement>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MelleAttributesImpl implements _MelleAttributes {
  const _$MelleAttributesImpl(
      {@JsonKey(name: 'fancy_color') final List<String>? fancyColor,
      @JsonKey(name: 'fancy_intensity') final List<String>? fancyIntensity,
      @JsonKey(name: 'fancy_overtone') final List<String>? fancyOvertone,
      @JsonKey(name: 'white_color') final List<String>? whiteColor,
      @JsonKey(name: 'seive_size') final List<String>? seiveSize,
      @JsonKey(name: 'milimeter') final List<String>? milimeter,
      @JsonKey(name: 'pointer') final List<String>? pointer,
      @JsonKey(name: 'clarity') final List<String>? clarity,
      @JsonKey(name: 'shape') final List<String>? shape,
      @JsonKey(name: 'max_quantity') this.maxQuantity,
      @JsonKey(name: 'per_diamond_caret') this.perDiamondCaret,
      @JsonKey(name: 'price_per_caet') this.pricePerCaret,
      @JsonKey(name: 'total_price') this.totalPrice,
      @JsonKey(name: 'measurements') final List<Measurement>? measurements})
      : _fancyColor = fancyColor,
        _fancyIntensity = fancyIntensity,
        _fancyOvertone = fancyOvertone,
        _whiteColor = whiteColor,
        _seiveSize = seiveSize,
        _milimeter = milimeter,
        _pointer = pointer,
        _clarity = clarity,
        _shape = shape,
        _measurements = measurements;

  factory _$MelleAttributesImpl.fromJson(Map<String, dynamic> json) =>
      _$$MelleAttributesImplFromJson(json);

  final List<String>? _fancyColor;
  @override
  @JsonKey(name: 'fancy_color')
  List<String>? get fancyColor {
    final value = _fancyColor;
    if (value == null) return null;
    if (_fancyColor is EqualUnmodifiableListView) return _fancyColor;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _fancyIntensity;
  @override
  @JsonKey(name: 'fancy_intensity')
  List<String>? get fancyIntensity {
    final value = _fancyIntensity;
    if (value == null) return null;
    if (_fancyIntensity is EqualUnmodifiableListView) return _fancyIntensity;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _fancyOvertone;
  @override
  @JsonKey(name: 'fancy_overtone')
  List<String>? get fancyOvertone {
    final value = _fancyOvertone;
    if (value == null) return null;
    if (_fancyOvertone is EqualUnmodifiableListView) return _fancyOvertone;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _whiteColor;
  @override
  @JsonKey(name: 'white_color')
  List<String>? get whiteColor {
    final value = _whiteColor;
    if (value == null) return null;
    if (_whiteColor is EqualUnmodifiableListView) return _whiteColor;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _seiveSize;
  @override
  @JsonKey(name: 'seive_size')
  List<String>? get seiveSize {
    final value = _seiveSize;
    if (value == null) return null;
    if (_seiveSize is EqualUnmodifiableListView) return _seiveSize;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _milimeter;
  @override
  @JsonKey(name: 'milimeter')
  List<String>? get milimeter {
    final value = _milimeter;
    if (value == null) return null;
    if (_milimeter is EqualUnmodifiableListView) return _milimeter;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _pointer;
  @override
  @JsonKey(name: 'pointer')
  List<String>? get pointer {
    final value = _pointer;
    if (value == null) return null;
    if (_pointer is EqualUnmodifiableListView) return _pointer;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _clarity;
  @override
  @JsonKey(name: 'clarity')
  List<String>? get clarity {
    final value = _clarity;
    if (value == null) return null;
    if (_clarity is EqualUnmodifiableListView) return _clarity;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<String>? _shape;
  @override
  @JsonKey(name: 'shape')
  List<String>? get shape {
    final value = _shape;
    if (value == null) return null;
    if (_shape is EqualUnmodifiableListView) return _shape;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: 'max_quantity')
  final int? maxQuantity;
  @override
  @JsonKey(name: 'per_diamond_caret')
  final double? perDiamondCaret;
  @override
  @JsonKey(name: 'price_per_caet')
  final double? pricePerCaret;
  @override
  @JsonKey(name: 'total_price')
  final double? totalPrice;
  final List<Measurement>? _measurements;
  @override
  @JsonKey(name: 'measurements')
  List<Measurement>? get measurements {
    final value = _measurements;
    if (value == null) return null;
    if (_measurements is EqualUnmodifiableListView) return _measurements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'MelleAttributes(fancyColor: $fancyColor, fancyIntensity: $fancyIntensity, fancyOvertone: $fancyOvertone, whiteColor: $whiteColor, seiveSize: $seiveSize, milimeter: $milimeter, pointer: $pointer, clarity: $clarity, shape: $shape, maxQuantity: $maxQuantity, perDiamondCaret: $perDiamondCaret, pricePerCaret: $pricePerCaret, totalPrice: $totalPrice, measurements: $measurements)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MelleAttributesImpl &&
            const DeepCollectionEquality()
                .equals(other._fancyColor, _fancyColor) &&
            const DeepCollectionEquality()
                .equals(other._fancyIntensity, _fancyIntensity) &&
            const DeepCollectionEquality()
                .equals(other._fancyOvertone, _fancyOvertone) &&
            const DeepCollectionEquality()
                .equals(other._whiteColor, _whiteColor) &&
            const DeepCollectionEquality()
                .equals(other._seiveSize, _seiveSize) &&
            const DeepCollectionEquality()
                .equals(other._milimeter, _milimeter) &&
            const DeepCollectionEquality().equals(other._pointer, _pointer) &&
            const DeepCollectionEquality().equals(other._clarity, _clarity) &&
            const DeepCollectionEquality().equals(other._shape, _shape) &&
            (identical(other.maxQuantity, maxQuantity) ||
                other.maxQuantity == maxQuantity) &&
            (identical(other.perDiamondCaret, perDiamondCaret) ||
                other.perDiamondCaret == perDiamondCaret) &&
            (identical(other.pricePerCaret, pricePerCaret) ||
                other.pricePerCaret == pricePerCaret) &&
            (identical(other.totalPrice, totalPrice) ||
                other.totalPrice == totalPrice) &&
            const DeepCollectionEquality()
                .equals(other._measurements, _measurements));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_fancyColor),
      const DeepCollectionEquality().hash(_fancyIntensity),
      const DeepCollectionEquality().hash(_fancyOvertone),
      const DeepCollectionEquality().hash(_whiteColor),
      const DeepCollectionEquality().hash(_seiveSize),
      const DeepCollectionEquality().hash(_milimeter),
      const DeepCollectionEquality().hash(_pointer),
      const DeepCollectionEquality().hash(_clarity),
      const DeepCollectionEquality().hash(_shape),
      maxQuantity,
      perDiamondCaret,
      pricePerCaret,
      totalPrice,
      const DeepCollectionEquality().hash(_measurements));

  /// Create a copy of MelleAttributes
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MelleAttributesImplCopyWith<_$MelleAttributesImpl> get copyWith =>
      __$$MelleAttributesImplCopyWithImpl<_$MelleAttributesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MelleAttributesImplToJson(
      this,
    );
  }
}

abstract class _MelleAttributes implements MelleAttributes {
  const factory _MelleAttributes(
      {@JsonKey(name: 'fancy_color') final List<String>? fancyColor,
      @JsonKey(name: 'fancy_intensity') final List<String>? fancyIntensity,
      @JsonKey(name: 'fancy_overtone') final List<String>? fancyOvertone,
      @JsonKey(name: 'white_color') final List<String>? whiteColor,
      @JsonKey(name: 'seive_size') final List<String>? seiveSize,
      @JsonKey(name: 'milimeter') final List<String>? milimeter,
      @JsonKey(name: 'pointer') final List<String>? pointer,
      @JsonKey(name: 'clarity') final List<String>? clarity,
      @JsonKey(name: 'shape') final List<String>? shape,
      @JsonKey(name: 'max_quantity') final int? maxQuantity,
      @JsonKey(name: 'per_diamond_caret') final double? perDiamondCaret,
      @JsonKey(name: 'price_per_caet') final double? pricePerCaret,
      @JsonKey(name: 'total_price') final double? totalPrice,
      @JsonKey(name: 'measurements')
      final List<Measurement>? measurements}) = _$MelleAttributesImpl;

  factory _MelleAttributes.fromJson(Map<String, dynamic> json) =
      _$MelleAttributesImpl.fromJson;

  @override
  @JsonKey(name: 'fancy_color')
  List<String>? get fancyColor;
  @override
  @JsonKey(name: 'fancy_intensity')
  List<String>? get fancyIntensity;
  @override
  @JsonKey(name: 'fancy_overtone')
  List<String>? get fancyOvertone;
  @override
  @JsonKey(name: 'white_color')
  List<String>? get whiteColor;
  @override
  @JsonKey(name: 'seive_size')
  List<String>? get seiveSize;
  @override
  @JsonKey(name: 'milimeter')
  List<String>? get milimeter;
  @override
  @JsonKey(name: 'pointer')
  List<String>? get pointer;
  @override
  @JsonKey(name: 'clarity')
  List<String>? get clarity;
  @override
  @JsonKey(name: 'shape')
  List<String>? get shape;
  @override
  @JsonKey(name: 'max_quantity')
  int? get maxQuantity;
  @override
  @JsonKey(name: 'per_diamond_caret')
  double? get perDiamondCaret;
  @override
  @JsonKey(name: 'price_per_caet')
  double? get pricePerCaret;
  @override
  @JsonKey(name: 'total_price')
  double? get totalPrice;
  @override
  @JsonKey(name: 'measurements')
  List<Measurement>? get measurements;

  /// Create a copy of MelleAttributes
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MelleAttributesImplCopyWith<_$MelleAttributesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Measurement _$MeasurementFromJson(Map<String, dynamic> json) {
  return _Measurement.fromJson(json);
}

/// @nodoc
mixin _$Measurement {
  @JsonKey(name: 'sieve_size')
  String? get sieveSize => throw _privateConstructorUsedError;
  @JsonKey(name: 'milimeter')
  String? get milimeter => throw _privateConstructorUsedError;
  @JsonKey(name: 'pointer')
  String? get pointer => throw _privateConstructorUsedError;

  /// Serializes this Measurement to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MeasurementCopyWith<Measurement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MeasurementCopyWith<$Res> {
  factory $MeasurementCopyWith(
          Measurement value, $Res Function(Measurement) then) =
      _$MeasurementCopyWithImpl<$Res, Measurement>;
  @useResult
  $Res call(
      {@JsonKey(name: 'sieve_size') String? sieveSize,
      @JsonKey(name: 'milimeter') String? milimeter,
      @JsonKey(name: 'pointer') String? pointer});
}

/// @nodoc
class _$MeasurementCopyWithImpl<$Res, $Val extends Measurement>
    implements $MeasurementCopyWith<$Res> {
  _$MeasurementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sieveSize = freezed,
    Object? milimeter = freezed,
    Object? pointer = freezed,
  }) {
    return _then(_value.copyWith(
      sieveSize: freezed == sieveSize
          ? _value.sieveSize
          : sieveSize // ignore: cast_nullable_to_non_nullable
              as String?,
      milimeter: freezed == milimeter
          ? _value.milimeter
          : milimeter // ignore: cast_nullable_to_non_nullable
              as String?,
      pointer: freezed == pointer
          ? _value.pointer
          : pointer // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MeasurementImplCopyWith<$Res>
    implements $MeasurementCopyWith<$Res> {
  factory _$$MeasurementImplCopyWith(
          _$MeasurementImpl value, $Res Function(_$MeasurementImpl) then) =
      __$$MeasurementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'sieve_size') String? sieveSize,
      @JsonKey(name: 'milimeter') String? milimeter,
      @JsonKey(name: 'pointer') String? pointer});
}

/// @nodoc
class __$$MeasurementImplCopyWithImpl<$Res>
    extends _$MeasurementCopyWithImpl<$Res, _$MeasurementImpl>
    implements _$$MeasurementImplCopyWith<$Res> {
  __$$MeasurementImplCopyWithImpl(
      _$MeasurementImpl _value, $Res Function(_$MeasurementImpl) _then)
      : super(_value, _then);

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sieveSize = freezed,
    Object? milimeter = freezed,
    Object? pointer = freezed,
  }) {
    return _then(_$MeasurementImpl(
      sieveSize: freezed == sieveSize
          ? _value.sieveSize
          : sieveSize // ignore: cast_nullable_to_non_nullable
              as String?,
      milimeter: freezed == milimeter
          ? _value.milimeter
          : milimeter // ignore: cast_nullable_to_non_nullable
              as String?,
      pointer: freezed == pointer
          ? _value.pointer
          : pointer // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MeasurementImpl implements _Measurement {
  const _$MeasurementImpl(
      {@JsonKey(name: 'sieve_size') this.sieveSize,
      @JsonKey(name: 'milimeter') this.milimeter,
      @JsonKey(name: 'pointer') this.pointer});

  factory _$MeasurementImpl.fromJson(Map<String, dynamic> json) =>
      _$$MeasurementImplFromJson(json);

  @override
  @JsonKey(name: 'sieve_size')
  final String? sieveSize;
  @override
  @JsonKey(name: 'milimeter')
  final String? milimeter;
  @override
  @JsonKey(name: 'pointer')
  final String? pointer;

  @override
  String toString() {
    return 'Measurement(sieveSize: $sieveSize, milimeter: $milimeter, pointer: $pointer)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MeasurementImpl &&
            (identical(other.sieveSize, sieveSize) ||
                other.sieveSize == sieveSize) &&
            (identical(other.milimeter, milimeter) ||
                other.milimeter == milimeter) &&
            (identical(other.pointer, pointer) || other.pointer == pointer));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sieveSize, milimeter, pointer);

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MeasurementImplCopyWith<_$MeasurementImpl> get copyWith =>
      __$$MeasurementImplCopyWithImpl<_$MeasurementImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MeasurementImplToJson(
      this,
    );
  }
}

abstract class _Measurement implements Measurement {
  const factory _Measurement(
      {@JsonKey(name: 'sieve_size') final String? sieveSize,
      @JsonKey(name: 'milimeter') final String? milimeter,
      @JsonKey(name: 'pointer') final String? pointer}) = _$MeasurementImpl;

  factory _Measurement.fromJson(Map<String, dynamic> json) =
      _$MeasurementImpl.fromJson;

  @override
  @JsonKey(name: 'sieve_size')
  String? get sieveSize;
  @override
  @JsonKey(name: 'milimeter')
  String? get milimeter;
  @override
  @JsonKey(name: 'pointer')
  String? get pointer;

  /// Create a copy of Measurement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MeasurementImplCopyWith<_$MeasurementImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
