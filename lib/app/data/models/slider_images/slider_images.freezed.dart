// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slider_images.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SliderImages _$SliderImagesFromJson(Map<String, dynamic> json) {
  return _SliderImages.fromJson(json);
}

/// @nodoc
mixin _$SliderImages {
  @JsonKey(name: "id")
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "order")
  int? get order => throw _privateConstructorUsedError;
  @JsonKey(name: "name")
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: "description")
  String? get description => throw _privateConstructorUsedError;
  @JsonKey(name: "banner_image")
  String? get bannerImage => throw _privateConstructorUsedError;
  @JsonKey(name: "banner_image_url")
  String? get bannerImageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "position")
  String? get position => throw _privateConstructorUsedError;
  @JsonKey(name: "deep_link")
  String? get deepLink => throw _privateConstructorUsedError;
  @JsonKey(name: "type")
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: "payload")
  String? get payload => throw _privateConstructorUsedError;
  @JsonKey(name: "is_active")
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: "_deleted")
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: "createdAt")
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updatedAt")
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SliderImagesCopyWith<SliderImages> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SliderImagesCopyWith<$Res> {
  factory $SliderImagesCopyWith(
          SliderImages value, $Res Function(SliderImages) then) =
      _$SliderImagesCopyWithImpl<$Res, SliderImages>;
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "order") int? order,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "banner_image") String? bannerImage,
      @JsonKey(name: "banner_image_url") String? bannerImageUrl,
      @JsonKey(name: "position") String? position,
      @JsonKey(name: "deep_link") String? deepLink,
      @JsonKey(name: "type") String? type,
      @JsonKey(name: "payload") String? payload,
      @JsonKey(name: "is_active") bool? isActive,
      @JsonKey(name: "_deleted") bool? deleted,
      @JsonKey(name: "createdAt") DateTime? createdAt,
      @JsonKey(name: "updatedAt") DateTime? updatedAt});
}

/// @nodoc
class _$SliderImagesCopyWithImpl<$Res, $Val extends SliderImages>
    implements $SliderImagesCopyWith<$Res> {
  _$SliderImagesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? order = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? bannerImage = freezed,
    Object? bannerImageUrl = freezed,
    Object? position = freezed,
    Object? deepLink = freezed,
    Object? type = freezed,
    Object? payload = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      deepLink: freezed == deepLink
          ? _value.deepLink
          : deepLink // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SliderImagesImplCopyWith<$Res>
    implements $SliderImagesCopyWith<$Res> {
  factory _$$SliderImagesImplCopyWith(
          _$SliderImagesImpl value, $Res Function(_$SliderImagesImpl) then) =
      __$$SliderImagesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "id") String? id,
      @JsonKey(name: "order") int? order,
      @JsonKey(name: "name") String? name,
      @JsonKey(name: "description") String? description,
      @JsonKey(name: "banner_image") String? bannerImage,
      @JsonKey(name: "banner_image_url") String? bannerImageUrl,
      @JsonKey(name: "position") String? position,
      @JsonKey(name: "deep_link") String? deepLink,
      @JsonKey(name: "type") String? type,
      @JsonKey(name: "payload") String? payload,
      @JsonKey(name: "is_active") bool? isActive,
      @JsonKey(name: "_deleted") bool? deleted,
      @JsonKey(name: "createdAt") DateTime? createdAt,
      @JsonKey(name: "updatedAt") DateTime? updatedAt});
}

/// @nodoc
class __$$SliderImagesImplCopyWithImpl<$Res>
    extends _$SliderImagesCopyWithImpl<$Res, _$SliderImagesImpl>
    implements _$$SliderImagesImplCopyWith<$Res> {
  __$$SliderImagesImplCopyWithImpl(
      _$SliderImagesImpl _value, $Res Function(_$SliderImagesImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? order = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? bannerImage = freezed,
    Object? bannerImageUrl = freezed,
    Object? position = freezed,
    Object? deepLink = freezed,
    Object? type = freezed,
    Object? payload = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$SliderImagesImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImage: freezed == bannerImage
          ? _value.bannerImage
          : bannerImage // ignore: cast_nullable_to_non_nullable
              as String?,
      bannerImageUrl: freezed == bannerImageUrl
          ? _value.bannerImageUrl
          : bannerImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      deepLink: freezed == deepLink
          ? _value.deepLink
          : deepLink // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SliderImagesImpl implements _SliderImages {
  const _$SliderImagesImpl(
      {@JsonKey(name: "id") this.id,
      @JsonKey(name: "order") this.order,
      @JsonKey(name: "name") this.name,
      @JsonKey(name: "description") this.description,
      @JsonKey(name: "banner_image") this.bannerImage,
      @JsonKey(name: "banner_image_url") this.bannerImageUrl,
      @JsonKey(name: "position") this.position,
      @JsonKey(name: "deep_link") this.deepLink,
      @JsonKey(name: "type") this.type,
      @JsonKey(name: "payload") this.payload,
      @JsonKey(name: "is_active") this.isActive,
      @JsonKey(name: "_deleted") this.deleted,
      @JsonKey(name: "createdAt") this.createdAt,
      @JsonKey(name: "updatedAt") this.updatedAt});

  factory _$SliderImagesImpl.fromJson(Map<String, dynamic> json) =>
      _$$SliderImagesImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String? id;
  @override
  @JsonKey(name: "order")
  final int? order;
  @override
  @JsonKey(name: "name")
  final String? name;
  @override
  @JsonKey(name: "description")
  final String? description;
  @override
  @JsonKey(name: "banner_image")
  final String? bannerImage;
  @override
  @JsonKey(name: "banner_image_url")
  final String? bannerImageUrl;
  @override
  @JsonKey(name: "position")
  final String? position;
  @override
  @JsonKey(name: "deep_link")
  final String? deepLink;
  @override
  @JsonKey(name: "type")
  final String? type;
  @override
  @JsonKey(name: "payload")
  final String? payload;
  @override
  @JsonKey(name: "is_active")
  final bool? isActive;
  @override
  @JsonKey(name: "_deleted")
  final bool? deleted;
  @override
  @JsonKey(name: "createdAt")
  final DateTime? createdAt;
  @override
  @JsonKey(name: "updatedAt")
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'SliderImages(id: $id, order: $order, name: $name, description: $description, bannerImage: $bannerImage, bannerImageUrl: $bannerImageUrl, position: $position, deepLink: $deepLink, type: $type, payload: $payload, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SliderImagesImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.bannerImage, bannerImage) ||
                other.bannerImage == bannerImage) &&
            (identical(other.bannerImageUrl, bannerImageUrl) ||
                other.bannerImageUrl == bannerImageUrl) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.deepLink, deepLink) ||
                other.deepLink == deepLink) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      order,
      name,
      description,
      bannerImage,
      bannerImageUrl,
      position,
      deepLink,
      type,
      payload,
      isActive,
      deleted,
      createdAt,
      updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SliderImagesImplCopyWith<_$SliderImagesImpl> get copyWith =>
      __$$SliderImagesImplCopyWithImpl<_$SliderImagesImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SliderImagesImplToJson(
      this,
    );
  }
}

abstract class _SliderImages implements SliderImages {
  const factory _SliderImages(
          {@JsonKey(name: "id") final String? id,
          @JsonKey(name: "order") final int? order,
          @JsonKey(name: "name") final String? name,
          @JsonKey(name: "description") final String? description,
          @JsonKey(name: "banner_image") final String? bannerImage,
          @JsonKey(name: "banner_image_url") final String? bannerImageUrl,
          @JsonKey(name: "position") final String? position,
          @JsonKey(name: "deep_link") final String? deepLink,
          @JsonKey(name: "type") final String? type,
          @JsonKey(name: "payload") final String? payload,
          @JsonKey(name: "is_active") final bool? isActive,
          @JsonKey(name: "_deleted") final bool? deleted,
          @JsonKey(name: "createdAt") final DateTime? createdAt,
          @JsonKey(name: "updatedAt") final DateTime? updatedAt}) =
      _$SliderImagesImpl;

  factory _SliderImages.fromJson(Map<String, dynamic> json) =
      _$SliderImagesImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String? get id;
  @override
  @JsonKey(name: "order")
  int? get order;
  @override
  @JsonKey(name: "name")
  String? get name;
  @override
  @JsonKey(name: "description")
  String? get description;
  @override
  @JsonKey(name: "banner_image")
  String? get bannerImage;
  @override
  @JsonKey(name: "banner_image_url")
  String? get bannerImageUrl;
  @override
  @JsonKey(name: "position")
  String? get position;
  @override
  @JsonKey(name: "deep_link")
  String? get deepLink;
  @override
  @JsonKey(name: "type")
  String? get type;
  @override
  @JsonKey(name: "payload")
  String? get payload;
  @override
  @JsonKey(name: "is_active")
  bool? get isActive;
  @override
  @JsonKey(name: "_deleted")
  bool? get deleted;
  @override
  @JsonKey(name: "createdAt")
  DateTime? get createdAt;
  @override
  @JsonKey(name: "updatedAt")
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$SliderImagesImplCopyWith<_$SliderImagesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
