import 'package:freezed_annotation/freezed_annotation.dart';

part 'slider_images.freezed.dart';

part 'slider_images.g.dart';

@freezed
class SliderImages with _$SliderImages {
  const factory SliderImages({
    @<PERSON><PERSON><PERSON><PERSON>(name: "id") String? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: "order") int? order,
    @<PERSON><PERSON><PERSON><PERSON>(name: "name") String? name,
    @<PERSON><PERSON><PERSON><PERSON>(name: "description") String? description,
    @<PERSON>son<PERSON><PERSON>(name: "banner_image") String? bannerImage,
    @<PERSON><PERSON><PERSON><PERSON>(name: "banner_image_url") String? bannerImageUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: "position") String? position,
    @<PERSON><PERSON><PERSON><PERSON>(name: "deep_link") String? deepLink,
    @<PERSON><PERSON><PERSON><PERSON>(name: "type") String? type,
    @<PERSON><PERSON><PERSON><PERSON>(name: "payload") String? payload,
    @<PERSON><PERSON><PERSON><PERSON>(name: "is_active") bool? isActive,
    @<PERSON><PERSON><PERSON><PERSON>(name: "_deleted") bool? deleted,
    @<PERSON><PERSON><PERSON><PERSON>(name: "createdAt") DateTime? createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "updatedAt") DateTime? updatedAt,
  }) = _SliderImages;

  factory SliderImages.from<PERSON><PERSON>(Map<String, dynamic> json) =>
      _$SliderImagesFromJson(json);
}
