// import 'package:diamond_company_app/app/data/config/logger.dart';
// import 'package:diamond_company_app/app/data/local/db/app_isar.dart';
// import 'package:diamond_company_app/app/data/models/cart_diamonds/cart_diamond.dart';
// import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
// import 'package:diamond_company_app/app/data/models/diamond_type.dart';
// import 'package:isar/isar.dart';
//
// class CartDiamondHelper {
//   static final Isar _store = AppIsar.instance.store;
//
//   static List<CartDiamond> get({
//     int? offset,
//     int? limit,
//   }) =>
//       _store.cartDiamonds.where().sortByAddedOnDesc().findAll(
//             offset: offset,
//             limit: limit,
//           );
//
//   static int count() => _store.cartDiamonds.count();
//
//   static void add(DiamondEntity diamond) {
//     _store.write((isar) {
//       isar.cartDiamonds.put(CartDiamond(
//         id: AppIsar.instance.store.cartDiamonds.autoIncrement(),
//         diamondType: diamond.isLabGrown == 'Y'
//             ? DiamondType.labGrown
//             : DiamondType.natural,
//         diamond: diamond,
//         addedOn: DateTime.now().toLocal(),
//       ));
//     });
//   }
//
//   static void remove(DiamondEntity diamond) {
//     _store.write((isar) {
//       isar.cartDiamonds
//           .where()
//           .diamond((q) => q
//               .stockIdEqualTo(
//                 diamond.stockId,
//                 caseSensitive: false,
//               )
//               .and()
//               .certificateNumberEqualTo(
//                 diamond.certificateNumber,
//                 caseSensitive: false,
//               ))
//           .deleteAll();
//     });
//   }
//
//   static void removeAll() {
//     _store.write((isar) {
//       isar.cartDiamonds.clear();
//     });
//   }
//
//   static bool isAdded(DiamondEntity diamond) {
//     int count = _store.cartDiamonds
//         .where()
//         .diamond((q) => q
//             .stockIdEqualTo(
//               diamond.stockId,
//               caseSensitive: false,
//             )
//             .or()
//             .certificateNumberEqualTo(
//               diamond.certificateNumber,
//               caseSensitive: false,
//             ))
//         .count();
//
//     return count > 0;
//   }
// }
