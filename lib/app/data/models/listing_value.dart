import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/modules/listing/controllers/marketplace_controller.dart';
import 'package:diamond_company_app/app/ui/components/grid_stock_item.dart';
import 'package:diamond_company_app/app/ui/components/stock_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ListingValue<T> {
  T value;
  dynamic extra;

  ListingValue({
    required this.value,
    this.extra,
  });

  Widget render() {
    switch (value.runtimeType) {
      case const (DiamondEntity):
        final DiamondEntity diamond = value as DiamondEntity;

        return StockItem(
          diamond: diamond,
          isAddedToCart: CartItemHelper.isDiamondAdded(diamond).obs,
          border: CartItemHelper.isDiamondAdded(diamond)
              ? Border.all(
                  width: 1.w,
                  color: AppColors.k1A47E8,
                )
              : Border.all(
                  width: 0.5.w,
                  color: AppColors.k000000.withOpacity(0.2),
                ),
          width: Get.width,
          margin: EdgeInsets.zero,
          //height: 440.h,
          onTap: () {
            Get.find<MarketplaceController>()
                .navigateToDetailsFromList(diamond);
          },
        );
      default:
        return Text(value.toString());
    }
  }

  Widget renderGrid(int index) {
    switch (value.runtimeType) {
      case const (DiamondEntity):
        final DiamondEntity diamond = value as DiamondEntity;
        return GridStockItem(
          diamond: diamond,
          width: Get.width,
          //height: 880.h,
          index: index,
          onTap: () {
            Get.find<MarketplaceController>()
                .navigateToDetailsFromGrid(diamond);
          },
          isAddedToCart: CartItemHelper.isDiamondAdded(diamond).obs,
        );
      default:
        return Text(value.toString());
    }
  }
}
