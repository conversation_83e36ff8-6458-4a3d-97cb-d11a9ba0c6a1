import 'package:isar/isar.dart';

part 'jewellery.g.dart';

/// enum option
enum OptionType { metal, size, diamond, settngs, dimension, goldPurity, none }

@embedded
class Jewellery {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? subtitle;
  final String? description;
  final String? handle;
  final bool? isGiftcard;
  final String? status;
  final String? thumbnail;
  final int? weight;
  final int? length;
  final int? height;
  final int? width;
  final String? hsCode;
  final String? originCountry;
  final String? midCode;
  final String? material;
  final dynamic typeId;
  final bool? discountable;
  final dynamic externalId;
  final dynamic metadata;
  final Options? collection;
  final String? collectionId;
  final List<Image>? images;
  final List<Options>? options;
  final List<Profile>? profiles;
  final String? profileId;
  final List<dynamic>? tags;
  final dynamic type;
  final List<Variant>? variants;
  Variant? selectedVariant;

  Jewellery({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.subtitle,
    this.description,
    this.handle,
    this.isGiftcard,
    this.status,
    this.thumbnail,
    this.weight,
    this.length,
    this.height,
    this.width,
    this.hsCode,
    this.originCountry,
    this.midCode,
    this.material,
    this.typeId,
    this.discountable,
    this.externalId,
    this.metadata,
    this.collection,
    this.collectionId,
    this.images,
    this.options,
    this.profiles,
    this.profileId,
    this.tags,
    this.type,
    this.variants,
    this.selectedVariant,
  });

  Jewellery copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? subtitle,
    String? description,
    String? handle,
    bool? isGiftcard,
    String? status,
    String? thumbnail,
    int? weight,
    int? length,
    int? height,
    int? width,
    String? hsCode,
    String? originCountry,
    String? midCode,
    String? material,
    dynamic typeId,
    bool? discountable,
    dynamic externalId,
    dynamic metadata,
    Options? collection,
    String? collectionId,
    List<Image>? images,
    List<Options>? options,
    List<Profile>? profiles,
    String? profileId,
    List<dynamic>? tags,
    dynamic type,
    List<Variant>? variants,
    Variant? selectedVariant,
  }) =>
      Jewellery(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        title: title ?? this.title,
        subtitle: subtitle ?? this.subtitle,
        description: description ?? this.description,
        handle: handle ?? this.handle,
        isGiftcard: isGiftcard ?? this.isGiftcard,
        status: status ?? this.status,
        thumbnail: thumbnail ?? this.thumbnail,
        weight: weight ?? this.weight,
        length: length ?? this.length,
        height: height ?? this.height,
        width: width ?? this.width,
        hsCode: hsCode ?? this.hsCode,
        originCountry: originCountry ?? this.originCountry,
        midCode: midCode ?? this.midCode,
        material: material ?? this.material,
        typeId: typeId ?? this.typeId,
        discountable: discountable ?? this.discountable,
        externalId: externalId ?? this.externalId,
        metadata: metadata ?? this.metadata,
        collection: collection ?? this.collection,
        collectionId: collectionId ?? this.collectionId,
        images: images ?? this.images,
        options: options ?? this.options,
        profiles: profiles ?? this.profiles,
        profileId: profileId ?? this.profileId,
        tags: tags ?? this.tags,
        type: type ?? this.type,
        variants: variants ?? this.variants,
        selectedVariant: selectedVariant ?? this.selectedVariant,
      );

  factory Jewellery.fromJson(Map<String, dynamic> json) => Jewellery(
        id: json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        title:
            json['title'] == null || json['title'] == '' ? null : json['title'],
        subtitle: json['subtitle'] == null || json['subtitle'] == ''
            ? null
            : json['subtitle'],
        description: json['description'] == null || json['description'] == ''
            ? null
            : json['description'],
        handle: json['handle'] == null || json['handle'] == ''
            ? null
            : json['handle'],
        isGiftcard: json['is_giftcard'] == null || json['is_giftcard'] == ''
            ? null
            : bool.tryParse(json['is_giftcard'].toString()),
        status: json['status'] == null || json['status'] == ''
            ? null
            : json['status'],
        thumbnail: json['thumbnail'] == null || json['thumbnail'] == ''
            ? null
            : json['thumbnail'],
        weight: json['weight'] == null || json['weight'] == ''
            ? null
            : int.tryParse(json['weight'].toString()),
        length: json['length'] == null || json['length'] == ''
            ? null
            : int.tryParse(json['length'].toString()),
        height: json['height'] == null || json['height'] == ''
            ? null
            : int.tryParse(json['height'].toString()),
        width: json['width'] == null || json['width'] == ''
            ? null
            : int.tryParse(json['width'].toString()),
        hsCode: json['hs_code'] == null || json['hs_code'] == ''
            ? null
            : json['hs_code'],
        originCountry:
            json['origin_country'] == null || json['origin_country'] == ''
                ? null
                : json['origin_country'],
        midCode: json['mid_code'] == null || json['mid_code'] == ''
            ? null
            : json['mid_code'],
        material: json['material'] == null || json['material'] == ''
            ? null
            : json['material'],
        typeId: json['type_id'] == null || json['type_id'] == ''
            ? null
            : json['type_id'],
        discountable: json['discountable'] == null || json['discountable'] == ''
            ? null
            : bool.tryParse(json['discountable'].toString()),
        externalId: json['external_id'] == null || json['external_id'] == ''
            ? null
            : json['external_id'],
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
        collection: (json['collection'] == null || json['collection'] == "")
            ? null
            : Options.fromJson(json['collection']),
        collectionId:
            json['collection_id'] == null || json['collection_id'] == ''
                ? null
                : json['collection_id'],
        images: json['images'] == null || json['images'] == ''
            ? []
            : List<Image>.from(json['images']!.map((x) => Image.fromJson(x))),
        options: json['options'] == null || json['options'] == ''
            ? []
            : List<Options>.from(
                json['options']!.map((x) => Options.fromJson(x))),
        profiles: json['profiles'] == null || json['profiles'] == ''
            ? []
            : List<Profile>.from(
                json['profiles']!.map((x) => Profile.fromJson(x))),
        profileId: json['profile_id'] == null || json['profile_id'] == ''
            ? null
            : json['profile_id'],
        tags: json['tags'] == null || json['tags'] == ''
            ? []
            : List<dynamic>.from(json['tags']!.map((x) => x)),
        type: json['type'] == null || json['type'] == '' ? null : json['type'],
        variants: json['variants'] == null || json['variants'] == ''
            ? []
            : List<Variant>.from(
                json['variants']!.map((x) => Variant.fromJson(x))),
        selectedVariant:
            json['selected_variant'] == null || json['selected_variant'] == ''
                ? null
                : Variant.fromJson(json['selected_variant']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'title': title,
        'subtitle': subtitle,
        'description': description,
        'handle': handle,
        'is_giftcard': isGiftcard,
        'status': status,
        'thumbnail': thumbnail,
        'weight': weight,
        'length': length,
        'height': height,
        'width': width,
        'hs_code': hsCode,
        'origin_country': originCountry,
        'mid_code': midCode,
        'material': material,
        'type_id': typeId,
        'discountable': discountable,
        'external_id': externalId,
        'metadata': metadata,
        'collection': collection?.toJson(),
        'collection_id': collectionId,
        'images': images == null
            ? []
            : List<dynamic>.from(images!.map((x) => x.toJson())),
        'options': options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
        'profiles': profiles == null
            ? []
            : List<dynamic>.from(profiles!.map((x) => x.toJson())),
        'profile_id': profileId,
        'tags': tags == null ? [] : List<dynamic>.from(tags!.map((x) => x)),
        'type': type,
        'selected_variant': selectedVariant?.toJson(),
        'variants': variants == null
            ? []
            : List<dynamic>.from(variants!.map((x) => x.toJson())),
      };
}

@embedded
class Options {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? handle;
  final dynamic metadata;
  final String? productId;
  Value? selectedValue;
  final List<Value>? values;

  Options({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.handle,
    this.metadata,
    this.productId,
    this.values,
    this.selectedValue,
  });

  Options copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? handle,
    dynamic metadata,
    String? productId,
    List<Value>? values,
    Value? selectedValue,
  }) =>
      Options(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        title: title ?? this.title,
        handle: handle ?? this.handle,
        metadata: metadata ?? this.metadata,
        productId: productId ?? this.productId,
        values: values ?? this.values,
        selectedValue: selectedValue ?? this.selectedValue,
      );

  factory Options.fromJson(Map<String, dynamic> json) => Options(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        title:
            json['title'] == null || json['title'] == '' ? null : json['title'],
        handle: json['handle'] == null || json['handle'] == ''
            ? null
            : json['handle'],
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
        productId: json['product_id'] == null || json['product_id'] == ''
            ? null
            : json['product_id'],
        values: json['values'] == null || json['values'] == ''
            ? []
            : List<Value>.from(json['values']!.map((x) => Value.fromJson(x))),
        selectedValue:
            json['selected_value'] == null || json['selected_value'] == ''
                ? null
                : Value.fromJson(json['selected_value']),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'title': title,
        'handle': handle,
        'metadata': metadata,
        'product_id': productId,
        'selected_value': selectedValue?.toJson(),
        'values': values == null
            ? []
            : List<dynamic>.from(values!.map((x) => x.toJson())),
      };
}

@embedded
class Value {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? value;
  final String? optionId;
  final String? variantId;
  final dynamic metadata;

  Value({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.value,
    this.optionId,
    this.variantId,
    this.metadata,
  });

  Value copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? value,
    String? optionId,
    String? variantId,
    dynamic metadata,
  }) =>
      Value(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        value: value ?? this.value,
        optionId: optionId ?? this.optionId,
        variantId: variantId ?? this.variantId,
        metadata: metadata ?? this.metadata,
      );

  factory Value.fromJson(Map<String, dynamic> json) => Value(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        value:
            json['value'] == null || json['value'] == '' ? null : json['value'],
        optionId: json['option_id'] == null || json['option_id'] == ''
            ? null
            : json['option_id'],
        variantId: json['variant_id'] == null || json['variant_id'] == ''
            ? null
            : json['variant_id'],
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'value': value,
        'option_id': id,
        'variant_id': variantId,
        'metadata': metadata,
      };
}

@embedded
class Image {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? url;
  final dynamic metadata;

  Image({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.url,
    this.metadata,
  });

  Image copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? url,
    dynamic metadata,
  }) =>
      Image(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        url: url ?? this.url,
        metadata: metadata ?? this.metadata,
      );

  factory Image.fromJson(Map<String, dynamic> json) => Image(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        url: json['url'] == null || json['url'] == '' ? null : json['url'],
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'url': url,
        'metadata': metadata,
      };
}

@embedded
class Profile {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? name;
  final String? type;
  final dynamic metadata;

  Profile({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.name,
    this.type,
    this.metadata,
  });

  Profile copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? name,
    String? type,
    dynamic metadata,
  }) =>
      Profile(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        name: name ?? this.name,
        type: type ?? this.type,
        metadata: metadata ?? this.metadata,
      );

  factory Profile.fromJson(Map<String, dynamic> json) => Profile(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        name: json['name'] == null || json['name'] == '' ? null : json['name'],
        type: json['type'] == null || json['type'] == '' ? null : json['type'],
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'name': name,
        'type': type,
        'metadata': metadata,
      };
}

@embedded
class Variant {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? title;
  final String? productId;
  final String? sku;
  final String? barcode;
  final String? ean;
  final String? upc;
  final int? variantRank;
  final int? inventoryQuantity;
  final bool? allowBackorder;
  final bool? manageInventory;
  final String? hsCode;
  final String? originCountry;
  final String? midCode;
  final String? material;
  final int? weight;
  final int? length;
  final int? height;
  final int? width;
  final dynamic metadata;
  final List<Value>? options;
  final List<Price>? prices;
  final dynamic originalPrice;
  final dynamic calculatedPrice;
  final dynamic originalPriceInclTax;
  final dynamic calculatedPriceInclTax;
  final dynamic originalTax;
  final dynamic calculatedTax;
  final dynamic taxRates;
  String? selectedSize;
  String? selectedMetal;
  String? selectedPurity;
  String? thumbnail;
  int? quantity;

  Variant({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.productId,
    this.sku,
    this.barcode,
    this.ean,
    this.upc,
    this.variantRank,
    this.inventoryQuantity,
    this.allowBackorder,
    this.manageInventory,
    this.hsCode,
    this.originCountry,
    this.midCode,
    this.material,
    this.weight,
    this.length,
    this.height,
    this.width,
    this.metadata,
    this.options,
    this.prices,
    this.originalPrice,
    this.calculatedPrice,
    this.originalPriceInclTax,
    this.calculatedPriceInclTax,
    this.originalTax,
    this.calculatedTax,
    this.taxRates,
    this.selectedSize,
    this.selectedPurity,
    this.selectedMetal,
    this.thumbnail,
    this.quantity,
  });

  Variant copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? title,
    String? productId,
    String? sku,
    String? barcode,
    String? ean,
    String? upc,
    int? variantRank,
    int? inventoryQuantity,
    bool? allowBackorder,
    bool? manageInventory,
    String? hsCode,
    String? originCountry,
    String? midCode,
    String? material,
    int? weight,
    int? length,
    int? height,
    int? width,
    dynamic metadata,
    List<Value>? options,
    List<Price>? prices,
    dynamic originalPrice,
    dynamic calculatedPrice,
    dynamic originalPriceInclTax,
    dynamic calculatedPriceInclTax,
    dynamic originalTax,
    dynamic calculatedTax,
    dynamic taxRates,
    String? selectedSize,
    String? selectedMetal,
    String? selectedPurity,
    String? thumbnail,
    int? quantity,
  }) =>
      Variant(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        title: title ?? this.title,
        productId: productId ?? this.productId,
        sku: sku ?? this.sku,
        barcode: barcode ?? this.barcode,
        ean: ean ?? this.ean,
        upc: upc ?? this.upc,
        variantRank: variantRank ?? this.variantRank,
        inventoryQuantity: inventoryQuantity ?? this.inventoryQuantity,
        allowBackorder: allowBackorder ?? this.allowBackorder,
        manageInventory: manageInventory ?? this.manageInventory,
        hsCode: hsCode ?? this.hsCode,
        originCountry: originCountry ?? this.originCountry,
        midCode: midCode ?? this.midCode,
        material: material ?? this.material,
        weight: weight ?? this.weight,
        length: length ?? this.length,
        height: height ?? this.height,
        width: width ?? this.width,
        metadata: metadata ?? this.metadata,
        options: options ?? this.options,
        prices: prices ?? this.prices,
        originalPrice: originalPrice ?? this.originalPrice,
        calculatedPrice: calculatedPrice ?? this.calculatedPrice,
        originalPriceInclTax: originalPriceInclTax ?? this.originalPriceInclTax,
        calculatedPriceInclTax:
            calculatedPriceInclTax ?? this.calculatedPriceInclTax,
        originalTax: originalTax ?? this.originalTax,
        calculatedTax: calculatedTax ?? this.calculatedTax,
        taxRates: taxRates ?? this.taxRates,
        selectedSize: selectedSize ?? this.selectedSize,
        selectedPurity: selectedPurity ?? this.selectedPurity,
        selectedMetal: selectedMetal ?? this.selectedMetal,
        thumbnail: thumbnail ?? this.thumbnail,
        quantity: quantity ?? this.quantity,
      );

  factory Variant.fromJson(Map<String, dynamic> json) => Variant(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        title:
            json['title'] == null || json['title'] == '' ? null : json['title'],
        productId: json['product_id'] == null || json['product_id'] == ''
            ? null
            : json['product_id'],
        sku: json['sku'] == null || json['sku'] == '' ? null : json['sku'],
        barcode: json['barcode'] == null || json['barcode'] == ''
            ? null
            : json['barcode'],
        ean: json['ean'] == null || json['ean'] == '' ? null : json['ean'],
        upc: json['upc'] == null || json['upc'] == '' ? null : json['upc'],
        variantRank: json['variant_rank'] == null || json['variant_rank'] == ''
            ? null
            : int.tryParse(json['variant_rank'].toString()),
        inventoryQuantity: json['inventory_quantity'] == null ||
                json['inventory_quantity'] == ''
            ? null
            : int.tryParse(json['inventory_quantity'].toString()),
        allowBackorder:
            json['allow_backorder'] == null || json['allow_backorder'] == ''
                ? null
                : bool.tryParse(json['allow_backorder'].toString()),
        manageInventory:
            json['manage_inventory'] == null || json['manage_inventory'] == ''
                ? null
                : bool.tryParse(json['manage_inventory'].toString()),
        hsCode: json['hs_code'] == null || json['hs_code'] == ''
            ? null
            : json['hs_code'],
        originCountry:
            json['origin_country'] == null || json['origin_country'] == ''
                ? null
                : json['origin_country'],
        midCode: json['mid_code'] == null || json['mid_code'] == ''
            ? null
            : json['mid_code'],
        material: json['material'] == null || json['material'] == ''
            ? null
            : json['material'],
        weight: json['weight'] == null || json['weight'] == ''
            ? null
            : int.tryParse(json['weight'].toString()),
        length: json['length'] == null || json['length'] == ''
            ? null
            : int.tryParse(json['length'].toString()),
        height: json['height'] == null || json['height'] == ''
            ? null
            : int.tryParse(json['height'].toString()),
        width: json['width'] == null || json['width'] == ''
            ? null
            : int.tryParse(json['width'].toString()),
        metadata: json['metadata'] == null || json['metadata'] == ''
            ? null
            : json['metadata'],
        options: json['options'] == null || json['options'] == ''
            ? []
            : List<Value>.from(json['options']!.map((x) => Value.fromJson(x))),
        prices: json['prices'] == null || json['prices'] == ''
            ? []
            : List<Price>.from(json['prices']!.map((x) => Price.fromJson(x))),
        originalPrice:
            json['original_price'] == null || json['original_price'] == ''
                ? null
                : json['original_price'],
        calculatedPrice:
            json['calculated_price'] == null || json['calculated_price'] == ''
                ? null
                : json['calculated_price'],
        originalPriceInclTax: json['original_price_incl_tax'] == null ||
                json['original_price_incl_tax'] == ''
            ? null
            : json['original_price_incl_tax'],
        calculatedPriceInclTax: json['calculated_price_incl_tax'] == null ||
                json['calculated_price_incl_tax'] == ''
            ? null
            : json['calculated_price_incl_tax'],
        originalTax: json['original_tax'] == null || json['original_tax'] == ''
            ? null
            : json['original_tax'],
        calculatedTax:
            json['calculated_tax'] == null || json['calculated_tax'] == ''
                ? null
                : json['calculated_tax'],
        taxRates: json['tax_rates'] == null || json['tax_rates'] == ''
            ? null
            : json['tax_rates'],
        selectedSize:
            json['selected_size'] == null || json['selected_size'] == ''
                ? null
                : json['selected_size'],
        selectedPurity:
            json['selected_purity'] == null || json['selected_purity'] == ''
                ? null
                : json['selected_purity'],
        selectedMetal:
            json['selected_metal'] == null || json['selected_metal'] == ''
                ? null
                : json['selected_metal'],
        quantity: json['quantity'] == null || json['quantity'] == ''
            ? null
            : json['quantity'],
        thumbnail: json['thumbnail'] == null || json['thumbnail'] == ''
            ? null
            : json['thumbnail'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'title': title,
        'product_id': productId,
        'sku': sku,
        'barcode': barcode,
        'ean': ean,
        'upc': upc,
        'variant_rank': variantRank,
        'inventory_quantity': inventoryQuantity,
        'allow_backorder': allowBackorder,
        'manage_inventory': manageInventory,
        'hs_code': hsCode,
        'origin_country': originCountry,
        'mid_code': midCode,
        'material': material,
        'weight': weight,
        'length': length,
        'height': height,
        'width': width,
        'metadata': metadata,
        'options': options == null
            ? []
            : List<dynamic>.from(options!.map((x) => x.toJson())),
        'prices': prices == null
            ? []
            : List<dynamic>.from(prices!.map((x) => x.toJson())),
        'original_price': originalPrice,
        'calculated_price': calculatedPrice,
        'original_price_incl_tax': originalPriceInclTax,
        'calculated_price_incl_tax': calculatedPriceInclTax,
        'original_tax': originalTax,
        'calculated_tax': calculatedTax,
        'tax_rates': taxRates,
        'selected_size': selectedSize,
        'selected_purity': selectedPurity,
        'selected_metal': selectedMetal,
        'quantity': quantity,
        'thumbnail': thumbnail,
      };
}

@embedded
class Price {
  final String? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? currencyCode;
  final double? amount;
  final dynamic minQuantity;
  final dynamic maxQuantity;
  final dynamic priceListId;
  final dynamic regionId;
  final dynamic priceList;
  final String? variantId;

  Price({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.currencyCode,
    this.amount,
    this.minQuantity,
    this.maxQuantity,
    this.priceListId,
    this.regionId,
    this.priceList,
    this.variantId,
  });

  Price copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? currencyCode,
    double? amount,
    dynamic minQuantity,
    dynamic maxQuantity,
    dynamic priceListId,
    dynamic regionId,
    dynamic priceList,
    String? variantId,
  }) =>
      Price(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        currencyCode: currencyCode ?? this.currencyCode,
        amount: amount ?? this.amount,
        minQuantity: minQuantity ?? this.minQuantity,
        maxQuantity: maxQuantity ?? this.maxQuantity,
        priceListId: priceListId ?? this.priceListId,
        regionId: regionId ?? this.regionId,
        priceList: priceList ?? this.priceList,
        variantId: variantId ?? this.variantId,
      );

  factory Price.fromJson(Map<String, dynamic> json) => Price(
        id: json['id'] == null || json['id'] == '' ? null : json['id'],
        createdAt: json['created_at'] == null || json['created_at'] == ''
            ? null
            : DateTime.parse(json['created_at']),
        updatedAt: json['updated_at'] == null || json['updated_at'] == ''
            ? null
            : DateTime.parse(json['updated_at']),
        deletedAt: json['deleted_at'] == null || json['deleted_at'] == ''
            ? null
            : DateTime.parse(json['deleted_at']),
        currencyCode:
            json['currency_code'] == null || json['currency_code'] == ''
                ? null
                : json['currency_code'],
        amount: json['amount'] == null || json['amount'] == ''
            ? null
            : double.parse(json['amount'].toString()) / 100,
        minQuantity: json['min_quantity'] == null || json['min_quantity'] == ''
            ? null
            : json['min_quantity'],
        maxQuantity: json['max_quantity'] == null || json['max_quantity'] == ''
            ? null
            : json['max_quantity'],
        priceListId:
            json['price_list_id'] == null || json['price_list_id'] == ''
                ? null
                : json['price_list_id'],
        regionId: json['region_id'] == null || json['region_id'] == ''
            ? null
            : json['region_id'],
        priceList: json['price_list'] == null || json['price_list'] == ''
            ? null
            : json['price_list'],
        variantId: json['variant_id'] == null || json['variant_id'] == ''
            ? null
            : json['variant_id'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'currency_code': currencyCode,
        'amount': amount,
        'min_quantity': minQuantity,
        'max_quantity': maxQuantity,
        'price_list_id': priceListId,
        'region_id': regionId,
        'price_list': priceList,
        'variant_id': variantId,
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
