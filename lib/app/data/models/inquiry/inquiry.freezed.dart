// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'inquiry.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InquiryData _$InquiryDataFromJson(Map<String, dynamic> json) {
  return _InquiryData.fromJson(json);
}

/// @nodoc
mixin _$InquiryData {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'inquiry')
  Inquiry? get inquiry => throw _privateConstructorUsedError;
  @JsonKey(name: 'room_id')
  String? get roomId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId => throw _privateConstructorUsedError;
  @JsonKey(name: 'product_id')
  String? get productId => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_messageAt')
  DateTime? get lastMessageAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_unread')
  int? get userUnread => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_unread')
  int? get adminUnread => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_message')
  String? get lastMessage => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: '__v')
  int? get v => throw _privateConstructorUsedError;

  /// Serializes this InquiryData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InquiryDataCopyWith<InquiryData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InquiryDataCopyWith<$Res> {
  factory $InquiryDataCopyWith(
          InquiryData value, $Res Function(InquiryData) then) =
      _$InquiryDataCopyWithImpl<$Res, InquiryData>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'inquiry') Inquiry? inquiry,
      @JsonKey(name: 'room_id') String? roomId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'last_messageAt') DateTime? lastMessageAt,
      @JsonKey(name: 'user_unread') int? userUnread,
      @JsonKey(name: 'admin_unread') int? adminUnread,
      @JsonKey(name: 'last_message') String? lastMessage,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: '__v') int? v});

  $InquiryCopyWith<$Res>? get inquiry;
}

/// @nodoc
class _$InquiryDataCopyWithImpl<$Res, $Val extends InquiryData>
    implements $InquiryDataCopyWith<$Res> {
  _$InquiryDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? inquiry = freezed,
    Object? roomId = freezed,
    Object? stockId = freezed,
    Object? orderId = freezed,
    Object? buyRequestId = freezed,
    Object? productId = freezed,
    Object? lastMessageAt = freezed,
    Object? userUnread = freezed,
    Object? adminUnread = freezed,
    Object? lastMessage = freezed,
    Object? status = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? v = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      inquiry: freezed == inquiry
          ? _value.inquiry
          : inquiry // ignore: cast_nullable_to_non_nullable
              as Inquiry?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastMessageAt: freezed == lastMessageAt
          ? _value.lastMessageAt
          : lastMessageAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userUnread: freezed == userUnread
          ? _value.userUnread
          : userUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      adminUnread: freezed == adminUnread
          ? _value.adminUnread
          : adminUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      lastMessage: freezed == lastMessage
          ? _value.lastMessage
          : lastMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      v: freezed == v
          ? _value.v
          : v // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InquiryCopyWith<$Res>? get inquiry {
    if (_value.inquiry == null) {
      return null;
    }

    return $InquiryCopyWith<$Res>(_value.inquiry!, (value) {
      return _then(_value.copyWith(inquiry: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$InquiryDataImplCopyWith<$Res>
    implements $InquiryDataCopyWith<$Res> {
  factory _$$InquiryDataImplCopyWith(
          _$InquiryDataImpl value, $Res Function(_$InquiryDataImpl) then) =
      __$$InquiryDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'inquiry') Inquiry? inquiry,
      @JsonKey(name: 'room_id') String? roomId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'buy_request_id') String? buyRequestId,
      @JsonKey(name: 'product_id') String? productId,
      @JsonKey(name: 'last_messageAt') DateTime? lastMessageAt,
      @JsonKey(name: 'user_unread') int? userUnread,
      @JsonKey(name: 'admin_unread') int? adminUnread,
      @JsonKey(name: 'last_message') String? lastMessage,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: '__v') int? v});

  @override
  $InquiryCopyWith<$Res>? get inquiry;
}

/// @nodoc
class __$$InquiryDataImplCopyWithImpl<$Res>
    extends _$InquiryDataCopyWithImpl<$Res, _$InquiryDataImpl>
    implements _$$InquiryDataImplCopyWith<$Res> {
  __$$InquiryDataImplCopyWithImpl(
      _$InquiryDataImpl _value, $Res Function(_$InquiryDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? inquiry = freezed,
    Object? roomId = freezed,
    Object? stockId = freezed,
    Object? orderId = freezed,
    Object? buyRequestId = freezed,
    Object? productId = freezed,
    Object? lastMessageAt = freezed,
    Object? userUnread = freezed,
    Object? adminUnread = freezed,
    Object? lastMessage = freezed,
    Object? status = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? v = freezed,
  }) {
    return _then(_$InquiryDataImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      inquiry: freezed == inquiry
          ? _value.inquiry
          : inquiry // ignore: cast_nullable_to_non_nullable
              as Inquiry?,
      roomId: freezed == roomId
          ? _value.roomId
          : roomId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestId: freezed == buyRequestId
          ? _value.buyRequestId
          : buyRequestId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastMessageAt: freezed == lastMessageAt
          ? _value.lastMessageAt
          : lastMessageAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userUnread: freezed == userUnread
          ? _value.userUnread
          : userUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      adminUnread: freezed == adminUnread
          ? _value.adminUnread
          : adminUnread // ignore: cast_nullable_to_non_nullable
              as int?,
      lastMessage: freezed == lastMessage
          ? _value.lastMessage
          : lastMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      v: freezed == v
          ? _value.v
          : v // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InquiryDataImpl implements _InquiryData {
  const _$InquiryDataImpl(
      {@JsonKey(name: '_id') this.id,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'inquiry') this.inquiry,
      @JsonKey(name: 'room_id') this.roomId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'buy_request_id') this.buyRequestId,
      @JsonKey(name: 'product_id') this.productId,
      @JsonKey(name: 'last_messageAt') this.lastMessageAt,
      @JsonKey(name: 'user_unread') this.userUnread,
      @JsonKey(name: 'admin_unread') this.adminUnread,
      @JsonKey(name: 'last_message') this.lastMessage,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: '__v') this.v});

  factory _$InquiryDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$InquiryDataImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'inquiry')
  final Inquiry? inquiry;
  @override
  @JsonKey(name: 'room_id')
  final String? roomId;
  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'order_id')
  final String? orderId;
  @override
  @JsonKey(name: 'buy_request_id')
  final String? buyRequestId;
  @override
  @JsonKey(name: 'product_id')
  final String? productId;
  @override
  @JsonKey(name: 'last_messageAt')
  final DateTime? lastMessageAt;
  @override
  @JsonKey(name: 'user_unread')
  final int? userUnread;
  @override
  @JsonKey(name: 'admin_unread')
  final int? adminUnread;
  @override
  @JsonKey(name: 'last_message')
  final String? lastMessage;
  @override
  @JsonKey(name: 'status')
  final String? status;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: '__v')
  final int? v;

  @override
  String toString() {
    return 'InquiryData(id: $id, userId: $userId, inquiry: $inquiry, roomId: $roomId, stockId: $stockId, orderId: $orderId, buyRequestId: $buyRequestId, productId: $productId, lastMessageAt: $lastMessageAt, userUnread: $userUnread, adminUnread: $adminUnread, lastMessage: $lastMessage, status: $status, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, v: $v)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InquiryDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.inquiry, inquiry) || other.inquiry == inquiry) &&
            (identical(other.roomId, roomId) || other.roomId == roomId) &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.buyRequestId, buyRequestId) ||
                other.buyRequestId == buyRequestId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.lastMessageAt, lastMessageAt) ||
                other.lastMessageAt == lastMessageAt) &&
            (identical(other.userUnread, userUnread) ||
                other.userUnread == userUnread) &&
            (identical(other.adminUnread, adminUnread) ||
                other.adminUnread == adminUnread) &&
            (identical(other.lastMessage, lastMessage) ||
                other.lastMessage == lastMessage) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.v, v) || other.v == v));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      inquiry,
      roomId,
      stockId,
      orderId,
      buyRequestId,
      productId,
      lastMessageAt,
      userUnread,
      adminUnread,
      lastMessage,
      status,
      deleted,
      createdAt,
      updatedAt,
      v);

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InquiryDataImplCopyWith<_$InquiryDataImpl> get copyWith =>
      __$$InquiryDataImplCopyWithImpl<_$InquiryDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InquiryDataImplToJson(
      this,
    );
  }
}

abstract class _InquiryData implements InquiryData {
  const factory _InquiryData(
      {@JsonKey(name: '_id') final String? id,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'inquiry') final Inquiry? inquiry,
      @JsonKey(name: 'room_id') final String? roomId,
      @JsonKey(name: 'stock_id') final String? stockId,
      @JsonKey(name: 'order_id') final String? orderId,
      @JsonKey(name: 'buy_request_id') final String? buyRequestId,
      @JsonKey(name: 'product_id') final String? productId,
      @JsonKey(name: 'last_messageAt') final DateTime? lastMessageAt,
      @JsonKey(name: 'user_unread') final int? userUnread,
      @JsonKey(name: 'admin_unread') final int? adminUnread,
      @JsonKey(name: 'last_message') final String? lastMessage,
      @JsonKey(name: 'status') final String? status,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: '__v') final int? v}) = _$InquiryDataImpl;

  factory _InquiryData.fromJson(Map<String, dynamic> json) =
      _$InquiryDataImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'inquiry')
  Inquiry? get inquiry;
  @override
  @JsonKey(name: 'room_id')
  String? get roomId;
  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'order_id')
  String? get orderId;
  @override
  @JsonKey(name: 'buy_request_id')
  String? get buyRequestId;
  @override
  @JsonKey(name: 'product_id')
  String? get productId;
  @override
  @JsonKey(name: 'last_messageAt')
  DateTime? get lastMessageAt;
  @override
  @JsonKey(name: 'user_unread')
  int? get userUnread;
  @override
  @JsonKey(name: 'admin_unread')
  int? get adminUnread;
  @override
  @JsonKey(name: 'last_message')
  String? get lastMessage;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: '__v')
  int? get v;

  /// Create a copy of InquiryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InquiryDataImplCopyWith<_$InquiryDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Inquiry _$InquiryFromJson(Map<String, dynamic> json) {
  return _Inquiry.fromJson(json);
}

/// @nodoc
mixin _$Inquiry {
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'title')
  String? get title => throw _privateConstructorUsedError;
  @JsonKey(name: 'sub_title')
  String? get subTitle => throw _privateConstructorUsedError;
  @JsonKey(name: 'jewellery')
  Jewellery? get jewellery => throw _privateConstructorUsedError;
  @JsonKey(name: 'diamond')
  DiamondEntity? get diamond => throw _privateConstructorUsedError;

  /// Serializes this Inquiry to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Inquiry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InquiryCopyWith<Inquiry> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InquiryCopyWith<$Res> {
  factory $InquiryCopyWith(Inquiry value, $Res Function(Inquiry) then) =
      _$InquiryCopyWithImpl<$Res, Inquiry>;
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'title') String? title,
      @JsonKey(name: 'sub_title') String? subTitle,
      @JsonKey(name: 'jewellery') Jewellery? jewellery,
      @JsonKey(name: 'diamond') DiamondEntity? diamond});
}

/// @nodoc
class _$InquiryCopyWithImpl<$Res, $Val extends Inquiry>
    implements $InquiryCopyWith<$Res> {
  _$InquiryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Inquiry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? jewellery = freezed,
    Object? diamond = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jewellery: freezed == jewellery
          ? _value.jewellery
          : jewellery // ignore: cast_nullable_to_non_nullable
              as Jewellery?,
      diamond: freezed == diamond
          ? _value.diamond
          : diamond // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InquiryImplCopyWith<$Res> implements $InquiryCopyWith<$Res> {
  factory _$$InquiryImplCopyWith(
          _$InquiryImpl value, $Res Function(_$InquiryImpl) then) =
      __$$InquiryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'title') String? title,
      @JsonKey(name: 'sub_title') String? subTitle,
      @JsonKey(name: 'jewellery') Jewellery? jewellery,
      @JsonKey(name: 'diamond') DiamondEntity? diamond});
}

/// @nodoc
class __$$InquiryImplCopyWithImpl<$Res>
    extends _$InquiryCopyWithImpl<$Res, _$InquiryImpl>
    implements _$$InquiryImplCopyWith<$Res> {
  __$$InquiryImplCopyWithImpl(
      _$InquiryImpl _value, $Res Function(_$InquiryImpl) _then)
      : super(_value, _then);

  /// Create a copy of Inquiry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? title = freezed,
    Object? subTitle = freezed,
    Object? jewellery = freezed,
    Object? diamond = freezed,
  }) {
    return _then(_$InquiryImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      subTitle: freezed == subTitle
          ? _value.subTitle
          : subTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      jewellery: freezed == jewellery
          ? _value.jewellery
          : jewellery // ignore: cast_nullable_to_non_nullable
              as Jewellery?,
      diamond: freezed == diamond
          ? _value.diamond
          : diamond // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$InquiryImpl implements _Inquiry {
  const _$InquiryImpl(
      {@JsonKey(name: 'type') this.type,
      @JsonKey(name: 'title') this.title,
      @JsonKey(name: 'sub_title') this.subTitle,
      @JsonKey(name: 'jewellery') this.jewellery,
      @JsonKey(name: 'diamond') this.diamond});

  factory _$InquiryImpl.fromJson(Map<String, dynamic> json) =>
      _$$InquiryImplFromJson(json);

  @override
  @JsonKey(name: 'type')
  final String? type;
  @override
  @JsonKey(name: 'title')
  final String? title;
  @override
  @JsonKey(name: 'sub_title')
  final String? subTitle;
  @override
  @JsonKey(name: 'jewellery')
  final Jewellery? jewellery;
  @override
  @JsonKey(name: 'diamond')
  final DiamondEntity? diamond;

  @override
  String toString() {
    return 'Inquiry(type: $type, title: $title, subTitle: $subTitle, jewellery: $jewellery, diamond: $diamond)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InquiryImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subTitle, subTitle) ||
                other.subTitle == subTitle) &&
            (identical(other.jewellery, jewellery) ||
                other.jewellery == jewellery) &&
            (identical(other.diamond, diamond) || other.diamond == diamond));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, type, title, subTitle, jewellery, diamond);

  /// Create a copy of Inquiry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InquiryImplCopyWith<_$InquiryImpl> get copyWith =>
      __$$InquiryImplCopyWithImpl<_$InquiryImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InquiryImplToJson(
      this,
    );
  }
}

abstract class _Inquiry implements Inquiry {
  const factory _Inquiry(
      {@JsonKey(name: 'type') final String? type,
      @JsonKey(name: 'title') final String? title,
      @JsonKey(name: 'sub_title') final String? subTitle,
      @JsonKey(name: 'jewellery') final Jewellery? jewellery,
      @JsonKey(name: 'diamond') final DiamondEntity? diamond}) = _$InquiryImpl;

  factory _Inquiry.fromJson(Map<String, dynamic> json) = _$InquiryImpl.fromJson;

  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'title')
  String? get title;
  @override
  @JsonKey(name: 'sub_title')
  String? get subTitle;
  @override
  @JsonKey(name: 'jewellery')
  Jewellery? get jewellery;
  @override
  @JsonKey(name: 'diamond')
  DiamondEntity? get diamond;

  /// Create a copy of Inquiry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InquiryImplCopyWith<_$InquiryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
