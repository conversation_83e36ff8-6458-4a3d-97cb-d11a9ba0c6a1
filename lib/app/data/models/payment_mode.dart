import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:flutter/material.dart';

/// PaymentModeModel
class PaymentModeModel {
  /// PaymentTypeModel Constructor
  PaymentModeModel({
    required this.title,
    required this.image,
    required this.paymentType,
    this.isIOS,
  });

  /// title
  final String title;

  /// payment type
  final PaymentMode paymentType;

  /// image
  final Widget image;

  /// isIOS
  final bool? isIOS;
}
