// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'unified_order_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UnifiedOrderResult _$UnifiedOrderResultFromJson(Map<String, dynamic> json) {
  return _UnifiedOrderResult.fromJson(json);
}

/// @nodoc
mixin _$UnifiedOrderResult {
  @JsonKey(name: 'success_data')
  List<SuccessDatum>? get successData => throw _privateConstructorUsedError;
  @JsonKey(name: 'error_data')
  List<ErrorDatum>? get errorData => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UnifiedOrderResultCopyWith<UnifiedOrderResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnifiedOrderResultCopyWith<$Res> {
  factory $UnifiedOrderResultCopyWith(
          UnifiedOrderResult value, $Res Function(UnifiedOrderResult) then) =
      _$UnifiedOrderResultCopyWithImpl<$Res, UnifiedOrderResult>;
  @useResult
  $Res call(
      {@JsonKey(name: 'success_data') List<SuccessDatum>? successData,
      @JsonKey(name: 'error_data') List<ErrorDatum>? errorData});
}

/// @nodoc
class _$UnifiedOrderResultCopyWithImpl<$Res, $Val extends UnifiedOrderResult>
    implements $UnifiedOrderResultCopyWith<$Res> {
  _$UnifiedOrderResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? successData = freezed,
    Object? errorData = freezed,
  }) {
    return _then(_value.copyWith(
      successData: freezed == successData
          ? _value.successData
          : successData // ignore: cast_nullable_to_non_nullable
              as List<SuccessDatum>?,
      errorData: freezed == errorData
          ? _value.errorData
          : errorData // ignore: cast_nullable_to_non_nullable
              as List<ErrorDatum>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UnifiedOrderResultImplCopyWith<$Res>
    implements $UnifiedOrderResultCopyWith<$Res> {
  factory _$$UnifiedOrderResultImplCopyWith(_$UnifiedOrderResultImpl value,
          $Res Function(_$UnifiedOrderResultImpl) then) =
      __$$UnifiedOrderResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'success_data') List<SuccessDatum>? successData,
      @JsonKey(name: 'error_data') List<ErrorDatum>? errorData});
}

/// @nodoc
class __$$UnifiedOrderResultImplCopyWithImpl<$Res>
    extends _$UnifiedOrderResultCopyWithImpl<$Res, _$UnifiedOrderResultImpl>
    implements _$$UnifiedOrderResultImplCopyWith<$Res> {
  __$$UnifiedOrderResultImplCopyWithImpl(_$UnifiedOrderResultImpl _value,
      $Res Function(_$UnifiedOrderResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? successData = freezed,
    Object? errorData = freezed,
  }) {
    return _then(_$UnifiedOrderResultImpl(
      successData: freezed == successData
          ? _value._successData
          : successData // ignore: cast_nullable_to_non_nullable
              as List<SuccessDatum>?,
      errorData: freezed == errorData
          ? _value._errorData
          : errorData // ignore: cast_nullable_to_non_nullable
              as List<ErrorDatum>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnifiedOrderResultImpl implements _UnifiedOrderResult {
  const _$UnifiedOrderResultImpl(
      {@JsonKey(name: 'success_data') final List<SuccessDatum>? successData,
      @JsonKey(name: 'error_data') final List<ErrorDatum>? errorData})
      : _successData = successData,
        _errorData = errorData;

  factory _$UnifiedOrderResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$UnifiedOrderResultImplFromJson(json);

  final List<SuccessDatum>? _successData;
  @override
  @JsonKey(name: 'success_data')
  List<SuccessDatum>? get successData {
    final value = _successData;
    if (value == null) return null;
    if (_successData is EqualUnmodifiableListView) return _successData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ErrorDatum>? _errorData;
  @override
  @JsonKey(name: 'error_data')
  List<ErrorDatum>? get errorData {
    final value = _errorData;
    if (value == null) return null;
    if (_errorData is EqualUnmodifiableListView) return _errorData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'UnifiedOrderResult(successData: $successData, errorData: $errorData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnifiedOrderResultImpl &&
            const DeepCollectionEquality()
                .equals(other._successData, _successData) &&
            const DeepCollectionEquality()
                .equals(other._errorData, _errorData));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_successData),
      const DeepCollectionEquality().hash(_errorData));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UnifiedOrderResultImplCopyWith<_$UnifiedOrderResultImpl> get copyWith =>
      __$$UnifiedOrderResultImplCopyWithImpl<_$UnifiedOrderResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnifiedOrderResultImplToJson(
      this,
    );
  }
}

abstract class _UnifiedOrderResult implements UnifiedOrderResult {
  const factory _UnifiedOrderResult(
          {@JsonKey(name: 'success_data') final List<SuccessDatum>? successData,
          @JsonKey(name: 'error_data') final List<ErrorDatum>? errorData}) =
      _$UnifiedOrderResultImpl;

  factory _UnifiedOrderResult.fromJson(Map<String, dynamic> json) =
      _$UnifiedOrderResultImpl.fromJson;

  @override
  @JsonKey(name: 'success_data')
  List<SuccessDatum>? get successData;
  @override
  @JsonKey(name: 'error_data')
  List<ErrorDatum>? get errorData;
  @override
  @JsonKey(ignore: true)
  _$$UnifiedOrderResultImplCopyWith<_$UnifiedOrderResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ErrorDatum _$ErrorDatumFromJson(Map<String, dynamic> json) {
  return _ErrorDatum.fromJson(json);
}

/// @nodoc
mixin _$ErrorDatum {
  @JsonKey(name: 'type')
  UnifiedOrderResultType? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'error')
  dynamic get error => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ErrorDatumCopyWith<ErrorDatum> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorDatumCopyWith<$Res> {
  factory $ErrorDatumCopyWith(
          ErrorDatum value, $Res Function(ErrorDatum) then) =
      _$ErrorDatumCopyWithImpl<$Res, ErrorDatum>;
  @useResult
  $Res call(
      {@JsonKey(name: 'type') UnifiedOrderResultType? type,
      @JsonKey(name: 'error') dynamic error});
}

/// @nodoc
class _$ErrorDatumCopyWithImpl<$Res, $Val extends ErrorDatum>
    implements $ErrorDatumCopyWith<$Res> {
  _$ErrorDatumCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderResultType?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ErrorDatumImplCopyWith<$Res>
    implements $ErrorDatumCopyWith<$Res> {
  factory _$$ErrorDatumImplCopyWith(
          _$ErrorDatumImpl value, $Res Function(_$ErrorDatumImpl) then) =
      __$$ErrorDatumImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'type') UnifiedOrderResultType? type,
      @JsonKey(name: 'error') dynamic error});
}

/// @nodoc
class __$$ErrorDatumImplCopyWithImpl<$Res>
    extends _$ErrorDatumCopyWithImpl<$Res, _$ErrorDatumImpl>
    implements _$$ErrorDatumImplCopyWith<$Res> {
  __$$ErrorDatumImplCopyWithImpl(
      _$ErrorDatumImpl _value, $Res Function(_$ErrorDatumImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? error = freezed,
  }) {
    return _then(_$ErrorDatumImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderResultType?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ErrorDatumImpl implements _ErrorDatum {
  const _$ErrorDatumImpl(
      {@JsonKey(name: 'type') this.type, @JsonKey(name: 'error') this.error});

  factory _$ErrorDatumImpl.fromJson(Map<String, dynamic> json) =>
      _$$ErrorDatumImplFromJson(json);

  @override
  @JsonKey(name: 'type')
  final UnifiedOrderResultType? type;
  @override
  @JsonKey(name: 'error')
  final dynamic error;

  @override
  String toString() {
    return 'ErrorDatum(type: $type, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorDatumImpl &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other.error, error));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, type, const DeepCollectionEquality().hash(error));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorDatumImplCopyWith<_$ErrorDatumImpl> get copyWith =>
      __$$ErrorDatumImplCopyWithImpl<_$ErrorDatumImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ErrorDatumImplToJson(
      this,
    );
  }
}

abstract class _ErrorDatum implements ErrorDatum {
  const factory _ErrorDatum(
      {@JsonKey(name: 'type') final UnifiedOrderResultType? type,
      @JsonKey(name: 'error') final dynamic error}) = _$ErrorDatumImpl;

  factory _ErrorDatum.fromJson(Map<String, dynamic> json) =
      _$ErrorDatumImpl.fromJson;

  @override
  @JsonKey(name: 'type')
  UnifiedOrderResultType? get type;
  @override
  @JsonKey(name: 'error')
  dynamic get error;
  @override
  @JsonKey(ignore: true)
  _$$ErrorDatumImplCopyWith<_$ErrorDatumImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ErrorClass _$ErrorClassFromJson(Map<String, dynamic> json) {
  return _ErrorClass.fromJson(json);
}

/// @nodoc
mixin _$ErrorClass {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ErrorClassCopyWith<ErrorClass> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ErrorClassCopyWith<$Res> {
  factory $ErrorClassCopyWith(
          ErrorClass value, $Res Function(ErrorClass) then) =
      _$ErrorClassCopyWithImpl<$Res, ErrorClass>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'created_at') DateTime? createdAt,
      @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class _$ErrorClassCopyWithImpl<$Res, $Val extends ErrorClass>
    implements $ErrorClassCopyWith<$Res> {
  _$ErrorClassCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ErrorClassImplCopyWith<$Res>
    implements $ErrorClassCopyWith<$Res> {
  factory _$$ErrorClassImplCopyWith(
          _$ErrorClassImpl value, $Res Function(_$ErrorClassImpl) then) =
      __$$ErrorClassImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'created_at') DateTime? createdAt,
      @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class __$$ErrorClassImplCopyWithImpl<$Res>
    extends _$ErrorClassCopyWithImpl<$Res, _$ErrorClassImpl>
    implements _$$ErrorClassImplCopyWith<$Res> {
  __$$ErrorClassImplCopyWithImpl(
      _$ErrorClassImpl _value, $Res Function(_$ErrorClassImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ErrorClassImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ErrorClassImpl implements _ErrorClass {
  const _$ErrorClassImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'created_at') this.createdAt,
      @JsonKey(name: 'updated_at') this.updatedAt});

  factory _$ErrorClassImpl.fromJson(Map<String, dynamic> json) =>
      _$$ErrorClassImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ErrorClass(id: $id, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorClassImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, createdAt, updatedAt);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorClassImplCopyWith<_$ErrorClassImpl> get copyWith =>
      __$$ErrorClassImplCopyWithImpl<_$ErrorClassImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ErrorClassImplToJson(
      this,
    );
  }
}

abstract class _ErrorClass implements ErrorClass {
  const factory _ErrorClass(
          {@JsonKey(name: 'id') final String? id,
          @JsonKey(name: 'created_at') final DateTime? createdAt,
          @JsonKey(name: 'updated_at') final DateTime? updatedAt}) =
      _$ErrorClassImpl;

  factory _ErrorClass.fromJson(Map<String, dynamic> json) =
      _$ErrorClassImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt;
  @override
  @JsonKey(ignore: true)
  _$$ErrorClassImplCopyWith<_$ErrorClassImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SuccessDatum _$SuccessDatumFromJson(Map<String, dynamic> json) {
  return _SuccessDatum.fromJson(json);
}

/// @nodoc
mixin _$SuccessDatum {
  @JsonKey(name: 'type')
  UnifiedOrderResultType? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'data')
  Data? get data => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SuccessDatumCopyWith<SuccessDatum> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SuccessDatumCopyWith<$Res> {
  factory $SuccessDatumCopyWith(
          SuccessDatum value, $Res Function(SuccessDatum) then) =
      _$SuccessDatumCopyWithImpl<$Res, SuccessDatum>;
  @useResult
  $Res call(
      {@JsonKey(name: 'type') UnifiedOrderResultType? type,
      @JsonKey(name: 'data') Data? data});

  $DataCopyWith<$Res>? get data;
}

/// @nodoc
class _$SuccessDatumCopyWithImpl<$Res, $Val extends SuccessDatum>
    implements $SuccessDatumCopyWith<$Res> {
  _$SuccessDatumCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? data = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderResultType?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Data?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $DataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $DataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SuccessDatumImplCopyWith<$Res>
    implements $SuccessDatumCopyWith<$Res> {
  factory _$$SuccessDatumImplCopyWith(
          _$SuccessDatumImpl value, $Res Function(_$SuccessDatumImpl) then) =
      __$$SuccessDatumImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'type') UnifiedOrderResultType? type,
      @JsonKey(name: 'data') Data? data});

  @override
  $DataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$SuccessDatumImplCopyWithImpl<$Res>
    extends _$SuccessDatumCopyWithImpl<$Res, _$SuccessDatumImpl>
    implements _$$SuccessDatumImplCopyWith<$Res> {
  __$$SuccessDatumImplCopyWithImpl(
      _$SuccessDatumImpl _value, $Res Function(_$SuccessDatumImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? data = freezed,
  }) {
    return _then(_$SuccessDatumImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as UnifiedOrderResultType?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Data?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SuccessDatumImpl implements _SuccessDatum {
  const _$SuccessDatumImpl(
      {@JsonKey(name: 'type') this.type, @JsonKey(name: 'data') this.data});

  factory _$SuccessDatumImpl.fromJson(Map<String, dynamic> json) =>
      _$$SuccessDatumImplFromJson(json);

  @override
  @JsonKey(name: 'type')
  final UnifiedOrderResultType? type;
  @override
  @JsonKey(name: 'data')
  final Data? data;

  @override
  String toString() {
    return 'SuccessDatum(type: $type, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessDatumImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, data);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessDatumImplCopyWith<_$SuccessDatumImpl> get copyWith =>
      __$$SuccessDatumImplCopyWithImpl<_$SuccessDatumImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SuccessDatumImplToJson(
      this,
    );
  }
}

abstract class _SuccessDatum implements SuccessDatum {
  const factory _SuccessDatum(
      {@JsonKey(name: 'type') final UnifiedOrderResultType? type,
      @JsonKey(name: 'data') final Data? data}) = _$SuccessDatumImpl;

  factory _SuccessDatum.fromJson(Map<String, dynamic> json) =
      _$SuccessDatumImpl.fromJson;

  @override
  @JsonKey(name: 'type')
  UnifiedOrderResultType? get type;
  @override
  @JsonKey(name: 'data')
  Data? get data;
  @override
  @JsonKey(ignore: true)
  _$$SuccessDatumImplCopyWith<_$SuccessDatumImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Data _$DataFromJson(Map<String, dynamic> json) {
  return _Data.fromJson(json);
}

/// @nodoc
mixin _$Data {
  @JsonKey(name: 'buy_request')
  BuyRequest? get buyRequest => throw _privateConstructorUsedError;
  @JsonKey(name: 'jewellery')
  UserOrder? get jewellery => throw _privateConstructorUsedError;
  @JsonKey(name: 'melee')
  UserOrder? get melee => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $DataCopyWith<Data> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DataCopyWith<$Res> {
  factory $DataCopyWith(Data value, $Res Function(Data) then) =
      _$DataCopyWithImpl<$Res, Data>;
  @useResult
  $Res call(
      {@JsonKey(name: 'buy_request') BuyRequest? buyRequest,
      @JsonKey(name: 'jewellery') UserOrder? jewellery,
      @JsonKey(name: 'melee') UserOrder? melee});

  $BuyRequestCopyWith<$Res>? get buyRequest;
  $UserOrderCopyWith<$Res>? get jewellery;
  $UserOrderCopyWith<$Res>? get melee;
}

/// @nodoc
class _$DataCopyWithImpl<$Res, $Val extends Data>
    implements $DataCopyWith<$Res> {
  _$DataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyRequest = freezed,
    Object? jewellery = freezed,
    Object? melee = freezed,
  }) {
    return _then(_value.copyWith(
      buyRequest: freezed == buyRequest
          ? _value.buyRequest
          : buyRequest // ignore: cast_nullable_to_non_nullable
              as BuyRequest?,
      jewellery: freezed == jewellery
          ? _value.jewellery
          : jewellery // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      melee: freezed == melee
          ? _value.melee
          : melee // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $BuyRequestCopyWith<$Res>? get buyRequest {
    if (_value.buyRequest == null) {
      return null;
    }

    return $BuyRequestCopyWith<$Res>(_value.buyRequest!, (value) {
      return _then(_value.copyWith(buyRequest: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $UserOrderCopyWith<$Res>? get jewellery {
    if (_value.jewellery == null) {
      return null;
    }

    return $UserOrderCopyWith<$Res>(_value.jewellery!, (value) {
      return _then(_value.copyWith(jewellery: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $UserOrderCopyWith<$Res>? get melee {
    if (_value.melee == null) {
      return null;
    }

    return $UserOrderCopyWith<$Res>(_value.melee!, (value) {
      return _then(_value.copyWith(melee: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DataImplCopyWith<$Res> implements $DataCopyWith<$Res> {
  factory _$$DataImplCopyWith(
          _$DataImpl value, $Res Function(_$DataImpl) then) =
      __$$DataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'buy_request') BuyRequest? buyRequest,
      @JsonKey(name: 'jewellery') UserOrder? jewellery,
      @JsonKey(name: 'melee') UserOrder? melee});

  @override
  $BuyRequestCopyWith<$Res>? get buyRequest;
  @override
  $UserOrderCopyWith<$Res>? get jewellery;
  @override
  $UserOrderCopyWith<$Res>? get melee;
}

/// @nodoc
class __$$DataImplCopyWithImpl<$Res>
    extends _$DataCopyWithImpl<$Res, _$DataImpl>
    implements _$$DataImplCopyWith<$Res> {
  __$$DataImplCopyWithImpl(_$DataImpl _value, $Res Function(_$DataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyRequest = freezed,
    Object? jewellery = freezed,
    Object? melee = freezed,
  }) {
    return _then(_$DataImpl(
      buyRequest: freezed == buyRequest
          ? _value.buyRequest
          : buyRequest // ignore: cast_nullable_to_non_nullable
              as BuyRequest?,
      jewellery: freezed == jewellery
          ? _value.jewellery
          : jewellery // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      melee: freezed == melee
          ? _value.melee
          : melee // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DataImpl implements _Data {
  const _$DataImpl(
      {@JsonKey(name: 'buy_request') this.buyRequest,
      @JsonKey(name: 'jewellery') this.jewellery,
      @JsonKey(name: 'melee') this.melee});

  factory _$DataImpl.fromJson(Map<String, dynamic> json) =>
      _$$DataImplFromJson(json);

  @override
  @JsonKey(name: 'buy_request')
  final BuyRequest? buyRequest;
  @override
  @JsonKey(name: 'jewellery')
  final UserOrder? jewellery;
  @override
  @JsonKey(name: 'melee')
  final UserOrder? melee;

  @override
  String toString() {
    return 'Data(buyRequest: $buyRequest, jewellery: $jewellery, melee: $melee)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DataImpl &&
            (identical(other.buyRequest, buyRequest) ||
                other.buyRequest == buyRequest) &&
            (identical(other.jewellery, jewellery) ||
                other.jewellery == jewellery) &&
            (identical(other.melee, melee) || other.melee == melee));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, buyRequest, jewellery, melee);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      __$$DataImplCopyWithImpl<_$DataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DataImplToJson(
      this,
    );
  }
}

abstract class _Data implements Data {
  const factory _Data(
      {@JsonKey(name: 'buy_request') final BuyRequest? buyRequest,
      @JsonKey(name: 'jewellery') final UserOrder? jewellery,
      @JsonKey(name: 'melee') final UserOrder? melee}) = _$DataImpl;

  factory _Data.fromJson(Map<String, dynamic> json) = _$DataImpl.fromJson;

  @override
  @JsonKey(name: 'buy_request')
  BuyRequest? get buyRequest;
  @override
  @JsonKey(name: 'jewellery')
  UserOrder? get jewellery;
  @override
  @JsonKey(name: 'melee')
  UserOrder? get melee;
  @override
  @JsonKey(ignore: true)
  _$$DataImplCopyWith<_$DataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
