// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unified_order_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UnifiedOrderResultImpl _$$UnifiedOrderResultImplFromJson(
        Map<String, dynamic> json) =>
    _$UnifiedOrderResultImpl(
      successData: (json['success_data'] as List<dynamic>?)
          ?.map((e) => SuccessDatum.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorData: (json['error_data'] as List<dynamic>?)
          ?.map((e) => ErrorDatum.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$UnifiedOrderResultImplToJson(
        _$UnifiedOrderResultImpl instance) =>
    <String, dynamic>{
      'success_data': instance.successData,
      'error_data': instance.errorData,
    };

_$ErrorDatumImpl _$$ErrorDatumImplFromJson(Map<String, dynamic> json) =>
    _$ErrorDatumImpl(
      type: $enumDecodeNullable(_$UnifiedOrderResultTypeEnumMap, json['type']),
      error: json['error'],
    );

Map<String, dynamic> _$$ErrorDatumImplToJson(_$ErrorDatumImpl instance) =>
    <String, dynamic>{
      'type': _$UnifiedOrderResultTypeEnumMap[instance.type],
      'error': instance.error,
    };

const _$UnifiedOrderResultTypeEnumMap = {
  UnifiedOrderResultType.DIAMOND: 'DIAMONDS',
  UnifiedOrderResultType.JEWELLERY: 'JEWELLERY',
  UnifiedOrderResultType.MELEE: 'MELEE',
};

_$ErrorClassImpl _$$ErrorClassImplFromJson(Map<String, dynamic> json) =>
    _$ErrorClassImpl(
      id: json['id'] as String?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$ErrorClassImplToJson(_$ErrorClassImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

_$SuccessDatumImpl _$$SuccessDatumImplFromJson(Map<String, dynamic> json) =>
    _$SuccessDatumImpl(
      type: $enumDecodeNullable(_$UnifiedOrderResultTypeEnumMap, json['type']),
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$SuccessDatumImplToJson(_$SuccessDatumImpl instance) =>
    <String, dynamic>{
      'type': _$UnifiedOrderResultTypeEnumMap[instance.type],
      'data': instance.data,
    };

_$DataImpl _$$DataImplFromJson(Map<String, dynamic> json) => _$DataImpl(
      buyRequest: json['buy_request'] == null
          ? null
          : BuyRequest.fromJson(json['buy_request'] as Map<String, dynamic>),
      jewellery: json['jewellery'] == null
          ? null
          : UserOrder.fromJson(json['jewellery'] as Map<String, dynamic>),
      melee: json['melee'] == null
          ? null
          : UserOrder.fromJson(json['melee'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DataImplToJson(_$DataImpl instance) =>
    <String, dynamic>{
      'buy_request': instance.buyRequest,
      'jewellery': instance.jewellery,
      'melee': instance.melee,
    };
