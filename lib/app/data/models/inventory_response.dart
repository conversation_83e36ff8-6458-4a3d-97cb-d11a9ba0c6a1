import 'dart:convert';

import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';

InventoryResponse stockEntityFromJson(String str) =>
    InventoryResponse.fromJson(json.decode(str) as Map<String, dynamic>);

String stockEntityToJson(InventoryResponse data) => json.encode(data.toJson());

class InventoryResponse {
  List<DiamondEntity>? diamonds;
  final int? total;
  final int? limit;
  final int? page;
  final int? pages;

  InventoryResponse({
    this.diamonds,
    this.total,
    this.limit,
    this.page,
    this.pages,
  });

  factory InventoryResponse.fromJson(
    Map<String, dynamic> json, {
    List<dynamic>? ads,
  }) {
    final List<dynamic> docs = json["docs"] as List<dynamic>? ?? [];
    return InventoryResponse(
      diamonds: json["docs"] == null
          ? []
          : List<DiamondEntity>.from(
              docs.map<DiamondEntity>(
                (dynamic x) =>
                    DiamondEntity.fromJson(x as Map<String, dynamic>),
              ),
            ),
      total: json["total"] as int?,
      limit: json["limit"] as int?,
      page: json["page"] as int?,
      pages: json["pages"] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
        "docs": diamonds == null
            ? []
            : List<dynamic>.from(diamonds!.map((x) => x.toJson())),
        "total": total,
        "limit": limit,
        "page": page,
        "pages": pages,
      };
}
