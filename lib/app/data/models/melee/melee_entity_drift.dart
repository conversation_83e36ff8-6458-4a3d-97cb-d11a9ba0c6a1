import 'package:drift/drift.dart';

@DataClassName('MeleeEntityData')
class MeleeEntities extends Table {
  TextColumn get id => text().nullable()();
  TextColumn get shape => text().nullable()();
  TextColumn get growthType => text().nullable()();
  TextColumn get sieveSize => text().nullable()();
  TextColumn get mm => text().nullable()();
  TextColumn get pointer => text().nullable()();
  RealColumn get perDiamondCarat => real().nullable()();
  IntColumn get diamondQuantity => integer().withDefault(const Constant(1))();
  RealColumn get pricePerCarat => real().nullable()();
  RealColumn get totalPrice => real().nullable()();
  TextColumn get fancyColor => text().nullable()();
  TextColumn get fancyIntensity => text().nullable()();
  TextColumn get fancyOvertone => text().nullable()();
  TextColumn get whiteColor => text().nullable()();
  TextColumn get clarity => text().nullable()();
  TextColumn get notes => text().nullable()();
  TextColumn get colorType => text().withDefault(const Constant('white'))();

  @override
  Set<Column> get primaryKey => {id};
}
