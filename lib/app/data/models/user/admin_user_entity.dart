/// Admin user entity
class AdminUserEntity {
  /// Admin entity data
  AdminUserEntity({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.profileImage,
    this.buyRequestCount,
    this.marginPercentage,
    this.orderCount,
    this.vendorType,
    this.apiUrl,
    this.billingDetails,
    this.address,
    this.ftpUsername,
    this.ftpPassword,
    this.isTermsAccepted,
    this.isBlacklisted,
    this.companyName,
    this.error,
    this.lastSyncedAt,
    this.apiType,
    this.body,
    this.headers,
    this.deleted,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.role,
    this.token,
    this.isVerified,
    this.documentUrl,
    this.documentName,
    this.kycRejectReason,
    this.isKycUploaded,
  });

  /// From json
  factory AdminUserEntity.fromJson(Map<String, dynamic> json) =>
      AdminUserEntity(
        id: json['id'],
        firstName: json['first_name'],
        lastName: json['last_name'],
        email: json['email'],
        phone: json['phone'],
        profileImage: json['profile_image'],
        buyRequestCount: json['buy_request_count'],
        marginPercentage: json['margin_percentage'],
        orderCount: json['order_count'],
        vendorType: json['vendor_type'],
        apiUrl: json['api_url'],
        billingDetails: json['billing_details'] == null
            ? null
            : BillingDetails.fromJson(json['billing_details']),
        address:
            json['address'] == null ? null : Address.fromJson(json['address']),
        ftpUsername: json['ftp_username'],
        ftpPassword: json['ftp_password'],
        isTermsAccepted: json['is_terms_accepted'],
        isBlacklisted: json['is_blacklisted'],
        companyName: json['company_name'],
        error: json['error'],
        lastSyncedAt: json['last_synced_at'],
        apiType: json['api_type'],
        body: json['body'],
        headers: json['headers'],
        deleted: json['_deleted'],
        isActive: json['is_active'],
        createdAt: json['createdAt'] == null
            ? null
            : DateTime.parse(json['createdAt']),
        updatedAt: json['updatedAt'] == null
            ? null
            : DateTime.parse(json['updatedAt']),
        role: json['role'],
        token: json['token'],
        isVerified: json['is_verified'] ?? false,
        documentName: json['document_name'],
        documentUrl: json['document_url'],
        kycRejectReason: json['kyc_reject_reason'],
        isKycUploaded: json['is_kyc_uploaded'],
      );

  /// Id
  final String? id;

  /// First name
  final String? firstName;

  /// Last name
  final String? lastName;

  /// Email
  final String? email;

  /// Phone
  final String? phone;

  /// Profile image
  final String? profileImage;

  /// Buy request count
  final int? buyRequestCount;

  /// Margin percentage
  final num? marginPercentage;

  /// Order count
  final int? orderCount;

  /// Vendor type
  final String? vendorType;

  /// Api url`
  final String? apiUrl;

  /// Billing details
  final BillingDetails? billingDetails;

  /// Address
  final Address? address;

  /// Ftp username
  final String? ftpUsername;

  /// Ftp password
  final String? ftpPassword;

  /// Is terms accepted
  final bool? isTermsAccepted;

  /// Is blacklisted
  final bool? isBlacklisted;

  /// Company name
  final String? companyName;

  /// Error
  final String? error;

  /// Last synced at
  final dynamic lastSyncedAt;

  /// Api type
  final dynamic apiType;

  /// Body
  final dynamic body;

  /// Headers
  final dynamic headers;

  /// Deleted
  final bool? deleted;

  /// Is active
  final bool? isActive;

  /// Created at
  final DateTime? createdAt;

  /// Updated at
  final DateTime? updatedAt;

  /// Role
  final String? role;

  /// Token
  final String? token;

  /// Is verified
  final bool? isVerified;

  /// Is kyc uploaded
  final bool? isKycUploaded;

  /// Document name
  final String? documentName;

  /// kyc reject reason
  final String? kycRejectReason;

  /// Document url
  final String? documentUrl;

  /// Check if the user is a vendor
  bool get isTypeVendor => role == 'vendor';

  /// Is kyc in progress
  bool get isKycInProgress =>
      !(isVerified ?? false) &&
      documentName != null &&
      documentName!.isNotEmpty;

  /// Is kyc rejected
  bool get isKycRejected =>
      !(isVerified ?? false) &&
      (documentName == null || (documentName?.isEmpty ?? true));

  /// Can show kyc Re-submission
  bool get canShowKyc => isKycRejected || isKycInProgress;

  /// To json
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'first_name': firstName,
        'last_name': lastName,
        'email': email,
        'phone': phone,
        'profile_image': profileImage,
        'buy_request_count': buyRequestCount,
        'margin_percentage': marginPercentage,
        'order_count': orderCount,
        'vendor_type': vendorType,
        'api_url': apiUrl,
        'billing_details': billingDetails?.toJson(),
        'address': address?.toJson(),
        'ftp_username': ftpUsername,
        'ftp_password': ftpPassword,
        'is_terms_accepted': isTermsAccepted,
        'is_blacklisted': isBlacklisted,
        'company_name': companyName,
        'error': error,
        'last_synced_at': lastSyncedAt,
        'api_type': apiType,
        'body': body,
        'headers': headers,
        '_deleted': deleted,
        'is_active': isActive,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'role': role,
        'fcm_token': token,
        'is_verified': isVerified,
        'document_name': documentName,
        'document_url': documentUrl,
        'kyc_reject_reason': kycRejectReason,
        'is_kyc_uploaded': isKycUploaded,
      };
}

/// Address
class Address {
  /// Address
  Address({
    this.city,
    this.state,
    this.street,
    this.country,
    this.zipCode,
  });

  /// From json
  factory Address.fromJson(Map<String, dynamic> json) => Address(
        city: json['city'],
        state: json['state'],
        street: json['street'],
        country: json['country'],
        zipCode: json['zip_code'],
      );

  /// City
  final String? city;

  /// State
  final String? state;

  /// Street
  final String? street;

  /// Country
  final String? country;

  /// Zip code
  final String? zipCode;

  /// To json
  Map<String, dynamic> toJson() => <String, dynamic>{
        'city': city,
        'state': state,
        'street': street,
        'country': country,
        'zip_code': zipCode,
      };
}

/// Billing details
class BillingDetails {
  /// Billing details
  BillingDetails({
    this.ifsc,
    this.bankName,
    this.accountNumber,
    this.accountHolderName,
  });

  /// From json
  factory BillingDetails.fromJson(Map<String, dynamic> json) => BillingDetails(
        ifsc: json['ifsc'],
        bankName: json['bank_name'],
        accountNumber: json['account_number'],
        accountHolderName: json['account_holder_name'],
      );

  /// Ifsc
  final String? ifsc;

  /// Bank name
  final String? bankName;

  /// Account number
  final String? accountNumber;

  /// Account holder name
  final String? accountHolderName;

  /// To json
  Map<String, dynamic> toJson() => <String, dynamic>{
        'ifsc': ifsc,
        'bank_name': bankName,
        'account_number': accountNumber,
        'account_holder_name': accountHolderName,
      };
}
