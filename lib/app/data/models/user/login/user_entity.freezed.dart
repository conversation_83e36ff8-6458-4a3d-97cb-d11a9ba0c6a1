// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserEntity _$UserEntityFromJson(Map<String, dynamic> json) {
  return _UserEntity.fromJson(json);
}

/// @nodoc
mixin _$UserEntity {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'first_name')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'last_name')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'username')
  String? get username => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_password_created')
  bool? get isPasswordCreated => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'mobile')
  String? get mobile => throw _privateConstructorUsedError;
  @JsonKey(name: 'profile_image')
  dynamic get profileImage => throw _privateConstructorUsedError;
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_address_same')
  bool? get isAddressSame => throw _privateConstructorUsedError;
  @JsonKey(name: 'fax')
  String? get fax => throw _privateConstructorUsedError;
  @JsonKey(name: 'website')
  String? get website => throw _privateConstructorUsedError;
  @JsonKey(name: 'zip_code')
  String? get zipCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'state')
  String? get state => throw _privateConstructorUsedError;
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_count')
  int? get buyRequestCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_count')
  int? get orderCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'otp')
  String? get otp => throw _privateConstructorUsedError;
  @JsonKey(name: 'legal_registered_name')
  String? get legalRegisteredName => throw _privateConstructorUsedError;
  @JsonKey(name: 'company_operating_name')
  String? get companyOperatingName => throw _privateConstructorUsedError;
  @JsonKey(name: 'fed_tax_id')
  String? get fedTaxId => throw _privateConstructorUsedError;
  @JsonKey(name: 'resale_tax')
  String? get resaleTax => throw _privateConstructorUsedError;
  @JsonKey(name: 'jbt_id')
  String? get jbtId => throw _privateConstructorUsedError;
  @JsonKey(name: 'has_AMLprogram')
  bool? get hasAmLprogram => throw _privateConstructorUsedError;
  @JsonKey(name: 'how_they_heard')
  String? get howTheyHeard => throw _privateConstructorUsedError;
  @JsonKey(name: 'facebook_id')
  String? get facebookId => throw _privateConstructorUsedError;
  @JsonKey(name: 'instagram_id')
  String? get instagramId => throw _privateConstructorUsedError;
  @JsonKey(name: 'type_of_business')
  String? get typeOfBusiness => throw _privateConstructorUsedError;
  @JsonKey(name: 'business_start_date')
  DateTime? get businessStartDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'years_at_present_location')
  String? get yearsAtPresentLocation => throw _privateConstructorUsedError;
  @JsonKey(name: 'legal_organization_status')
  LegalOrganizationStatus? get legalOrganizationStatus =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'order_authorized_person')
  List<OrderAuthorizedPerson>? get orderAuthorizedPerson =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'document_name')
  String? get documentName => throw _privateConstructorUsedError;
  @JsonKey(name: 'document_url')
  String? get documentUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'trade_references')
  List<TradeReference>? get tradeReferences =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'business_entity_name')
  String? get businessEntityName => throw _privateConstructorUsedError;
  @JsonKey(name: 'signature_image_name')
  String? get signatureImageName => throw _privateConstructorUsedError;
  @JsonKey(name: 'signature_image_url')
  String? get signatureImageUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'sign_date')
  DateTime? get signDate => throw _privateConstructorUsedError;
  @JsonKey(name: 'print_name')
  String? get printName => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_phone_verified')
  bool? get isPhoneVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_email_verified')
  bool? get isEmailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_blocked')
  bool? get isBlocked => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_verified')
  bool? get isVerified => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'token')
  String? get token => throw _privateConstructorUsedError;
  @JsonKey(name: 'customer_profile_id')
  String? get customerProfileId => throw _privateConstructorUsedError;
  @JsonKey(name: 'kyc_reject_reason')
  String? get kycRejectReason => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserEntityCopyWith<UserEntity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserEntityCopyWith<$Res> {
  factory $UserEntityCopyWith(
          UserEntity value, $Res Function(UserEntity) then) =
      _$UserEntityCopyWithImpl<$Res, UserEntity>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'username') String? username,
      @JsonKey(name: 'is_password_created') bool? isPasswordCreated,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'profile_image') dynamic profileImage,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'is_address_same') bool? isAddressSame,
      @JsonKey(name: 'fax') String? fax,
      @JsonKey(name: 'website') String? website,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'buy_request_count') int? buyRequestCount,
      @JsonKey(name: 'order_count') int? orderCount,
      @JsonKey(name: 'otp') String? otp,
      @JsonKey(name: 'legal_registered_name') String? legalRegisteredName,
      @JsonKey(name: 'company_operating_name') String? companyOperatingName,
      @JsonKey(name: 'fed_tax_id') String? fedTaxId,
      @JsonKey(name: 'resale_tax') String? resaleTax,
      @JsonKey(name: 'jbt_id') String? jbtId,
      @JsonKey(name: 'has_AMLprogram') bool? hasAmLprogram,
      @JsonKey(name: 'how_they_heard') String? howTheyHeard,
      @JsonKey(name: 'facebook_id') String? facebookId,
      @JsonKey(name: 'instagram_id') String? instagramId,
      @JsonKey(name: 'type_of_business') String? typeOfBusiness,
      @JsonKey(name: 'business_start_date') DateTime? businessStartDate,
      @JsonKey(name: 'years_at_present_location')
      String? yearsAtPresentLocation,
      @JsonKey(name: 'legal_organization_status')
      LegalOrganizationStatus? legalOrganizationStatus,
      @JsonKey(name: 'order_authorized_person')
      List<OrderAuthorizedPerson>? orderAuthorizedPerson,
      @JsonKey(name: 'document_name') String? documentName,
      @JsonKey(name: 'document_url') String? documentUrl,
      @JsonKey(name: 'trade_references') List<TradeReference>? tradeReferences,
      @JsonKey(name: 'business_entity_name') String? businessEntityName,
      @JsonKey(name: 'signature_image_name') String? signatureImageName,
      @JsonKey(name: 'signature_image_url') String? signatureImageUrl,
      @JsonKey(name: 'sign_date') DateTime? signDate,
      @JsonKey(name: 'print_name') String? printName,
      @JsonKey(name: 'is_phone_verified') bool? isPhoneVerified,
      @JsonKey(name: 'is_email_verified') bool? isEmailVerified,
      @JsonKey(name: 'is_blocked') bool? isBlocked,
      @JsonKey(name: 'is_verified') bool? isVerified,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'token') String? token,
      @JsonKey(name: 'customer_profile_id') String? customerProfileId,
      @JsonKey(name: 'kyc_reject_reason') String? kycRejectReason});

  $IngAddressCopyWith<$Res>? get shippingAddress;
  $IngAddressCopyWith<$Res>? get billingAddress;
  $LegalOrganizationStatusCopyWith<$Res>? get legalOrganizationStatus;
}

/// @nodoc
class _$UserEntityCopyWithImpl<$Res, $Val extends UserEntity>
    implements $UserEntityCopyWith<$Res> {
  _$UserEntityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? username = freezed,
    Object? isPasswordCreated = freezed,
    Object? phone = freezed,
    Object? mobile = freezed,
    Object? profileImage = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? isAddressSame = freezed,
    Object? fax = freezed,
    Object? website = freezed,
    Object? zipCode = freezed,
    Object? state = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? buyRequestCount = freezed,
    Object? orderCount = freezed,
    Object? otp = freezed,
    Object? legalRegisteredName = freezed,
    Object? companyOperatingName = freezed,
    Object? fedTaxId = freezed,
    Object? resaleTax = freezed,
    Object? jbtId = freezed,
    Object? hasAmLprogram = freezed,
    Object? howTheyHeard = freezed,
    Object? facebookId = freezed,
    Object? instagramId = freezed,
    Object? typeOfBusiness = freezed,
    Object? businessStartDate = freezed,
    Object? yearsAtPresentLocation = freezed,
    Object? legalOrganizationStatus = freezed,
    Object? orderAuthorizedPerson = freezed,
    Object? documentName = freezed,
    Object? documentUrl = freezed,
    Object? tradeReferences = freezed,
    Object? businessEntityName = freezed,
    Object? signatureImageName = freezed,
    Object? signatureImageUrl = freezed,
    Object? signDate = freezed,
    Object? printName = freezed,
    Object? isPhoneVerified = freezed,
    Object? isEmailVerified = freezed,
    Object? isBlocked = freezed,
    Object? isVerified = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? token = freezed,
    Object? customerProfileId = freezed,
    Object? kycRejectReason = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isPasswordCreated: freezed == isPasswordCreated
          ? _value.isPasswordCreated
          : isPasswordCreated // ignore: cast_nullable_to_non_nullable
              as bool?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as dynamic,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      isAddressSame: freezed == isAddressSame
          ? _value.isAddressSame
          : isAddressSame // ignore: cast_nullable_to_non_nullable
              as bool?,
      fax: freezed == fax
          ? _value.fax
          : fax // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestCount: freezed == buyRequestCount
          ? _value.buyRequestCount
          : buyRequestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderCount: freezed == orderCount
          ? _value.orderCount
          : orderCount // ignore: cast_nullable_to_non_nullable
              as int?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      legalRegisteredName: freezed == legalRegisteredName
          ? _value.legalRegisteredName
          : legalRegisteredName // ignore: cast_nullable_to_non_nullable
              as String?,
      companyOperatingName: freezed == companyOperatingName
          ? _value.companyOperatingName
          : companyOperatingName // ignore: cast_nullable_to_non_nullable
              as String?,
      fedTaxId: freezed == fedTaxId
          ? _value.fedTaxId
          : fedTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      resaleTax: freezed == resaleTax
          ? _value.resaleTax
          : resaleTax // ignore: cast_nullable_to_non_nullable
              as String?,
      jbtId: freezed == jbtId
          ? _value.jbtId
          : jbtId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasAmLprogram: freezed == hasAmLprogram
          ? _value.hasAmLprogram
          : hasAmLprogram // ignore: cast_nullable_to_non_nullable
              as bool?,
      howTheyHeard: freezed == howTheyHeard
          ? _value.howTheyHeard
          : howTheyHeard // ignore: cast_nullable_to_non_nullable
              as String?,
      facebookId: freezed == facebookId
          ? _value.facebookId
          : facebookId // ignore: cast_nullable_to_non_nullable
              as String?,
      instagramId: freezed == instagramId
          ? _value.instagramId
          : instagramId // ignore: cast_nullable_to_non_nullable
              as String?,
      typeOfBusiness: freezed == typeOfBusiness
          ? _value.typeOfBusiness
          : typeOfBusiness // ignore: cast_nullable_to_non_nullable
              as String?,
      businessStartDate: freezed == businessStartDate
          ? _value.businessStartDate
          : businessStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      yearsAtPresentLocation: freezed == yearsAtPresentLocation
          ? _value.yearsAtPresentLocation
          : yearsAtPresentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      legalOrganizationStatus: freezed == legalOrganizationStatus
          ? _value.legalOrganizationStatus
          : legalOrganizationStatus // ignore: cast_nullable_to_non_nullable
              as LegalOrganizationStatus?,
      orderAuthorizedPerson: freezed == orderAuthorizedPerson
          ? _value.orderAuthorizedPerson
          : orderAuthorizedPerson // ignore: cast_nullable_to_non_nullable
              as List<OrderAuthorizedPerson>?,
      documentName: freezed == documentName
          ? _value.documentName
          : documentName // ignore: cast_nullable_to_non_nullable
              as String?,
      documentUrl: freezed == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeReferences: freezed == tradeReferences
          ? _value.tradeReferences
          : tradeReferences // ignore: cast_nullable_to_non_nullable
              as List<TradeReference>?,
      businessEntityName: freezed == businessEntityName
          ? _value.businessEntityName
          : businessEntityName // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageName: freezed == signatureImageName
          ? _value.signatureImageName
          : signatureImageName // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageUrl: freezed == signatureImageUrl
          ? _value.signatureImageUrl
          : signatureImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      signDate: freezed == signDate
          ? _value.signDate
          : signDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      printName: freezed == printName
          ? _value.printName
          : printName // ignore: cast_nullable_to_non_nullable
              as String?,
      isPhoneVerified: freezed == isPhoneVerified
          ? _value.isPhoneVerified
          : isPhoneVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailVerified: freezed == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isBlocked: freezed == isBlocked
          ? _value.isBlocked
          : isBlocked // ignore: cast_nullable_to_non_nullable
              as bool?,
      isVerified: freezed == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      customerProfileId: freezed == customerProfileId
          ? _value.customerProfileId
          : customerProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
      kycRejectReason: freezed == kycRejectReason
          ? _value.kycRejectReason
          : kycRejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get shippingAddress {
    if (_value.shippingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.shippingAddress!, (value) {
      return _then(_value.copyWith(shippingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $IngAddressCopyWith<$Res>? get billingAddress {
    if (_value.billingAddress == null) {
      return null;
    }

    return $IngAddressCopyWith<$Res>(_value.billingAddress!, (value) {
      return _then(_value.copyWith(billingAddress: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $LegalOrganizationStatusCopyWith<$Res>? get legalOrganizationStatus {
    if (_value.legalOrganizationStatus == null) {
      return null;
    }

    return $LegalOrganizationStatusCopyWith<$Res>(
        _value.legalOrganizationStatus!, (value) {
      return _then(_value.copyWith(legalOrganizationStatus: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserEntityImplCopyWith<$Res>
    implements $UserEntityCopyWith<$Res> {
  factory _$$UserEntityImplCopyWith(
          _$UserEntityImpl value, $Res Function(_$UserEntityImpl) then) =
      __$$UserEntityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'email') String? email,
      @JsonKey(name: 'username') String? username,
      @JsonKey(name: 'is_password_created') bool? isPasswordCreated,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'mobile') String? mobile,
      @JsonKey(name: 'profile_image') dynamic profileImage,
      @JsonKey(name: 'shipping_address') IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') IngAddress? billingAddress,
      @JsonKey(name: 'is_address_same') bool? isAddressSame,
      @JsonKey(name: 'fax') String? fax,
      @JsonKey(name: 'website') String? website,
      @JsonKey(name: 'zip_code') String? zipCode,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'buy_request_count') int? buyRequestCount,
      @JsonKey(name: 'order_count') int? orderCount,
      @JsonKey(name: 'otp') String? otp,
      @JsonKey(name: 'legal_registered_name') String? legalRegisteredName,
      @JsonKey(name: 'company_operating_name') String? companyOperatingName,
      @JsonKey(name: 'fed_tax_id') String? fedTaxId,
      @JsonKey(name: 'resale_tax') String? resaleTax,
      @JsonKey(name: 'jbt_id') String? jbtId,
      @JsonKey(name: 'has_AMLprogram') bool? hasAmLprogram,
      @JsonKey(name: 'how_they_heard') String? howTheyHeard,
      @JsonKey(name: 'facebook_id') String? facebookId,
      @JsonKey(name: 'instagram_id') String? instagramId,
      @JsonKey(name: 'type_of_business') String? typeOfBusiness,
      @JsonKey(name: 'business_start_date') DateTime? businessStartDate,
      @JsonKey(name: 'years_at_present_location')
      String? yearsAtPresentLocation,
      @JsonKey(name: 'legal_organization_status')
      LegalOrganizationStatus? legalOrganizationStatus,
      @JsonKey(name: 'order_authorized_person')
      List<OrderAuthorizedPerson>? orderAuthorizedPerson,
      @JsonKey(name: 'document_name') String? documentName,
      @JsonKey(name: 'document_url') String? documentUrl,
      @JsonKey(name: 'trade_references') List<TradeReference>? tradeReferences,
      @JsonKey(name: 'business_entity_name') String? businessEntityName,
      @JsonKey(name: 'signature_image_name') String? signatureImageName,
      @JsonKey(name: 'signature_image_url') String? signatureImageUrl,
      @JsonKey(name: 'sign_date') DateTime? signDate,
      @JsonKey(name: 'print_name') String? printName,
      @JsonKey(name: 'is_phone_verified') bool? isPhoneVerified,
      @JsonKey(name: 'is_email_verified') bool? isEmailVerified,
      @JsonKey(name: 'is_blocked') bool? isBlocked,
      @JsonKey(name: 'is_verified') bool? isVerified,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'token') String? token,
      @JsonKey(name: 'customer_profile_id') String? customerProfileId,
      @JsonKey(name: 'kyc_reject_reason') String? kycRejectReason});

  @override
  $IngAddressCopyWith<$Res>? get shippingAddress;
  @override
  $IngAddressCopyWith<$Res>? get billingAddress;
  @override
  $LegalOrganizationStatusCopyWith<$Res>? get legalOrganizationStatus;
}

/// @nodoc
class __$$UserEntityImplCopyWithImpl<$Res>
    extends _$UserEntityCopyWithImpl<$Res, _$UserEntityImpl>
    implements _$$UserEntityImplCopyWith<$Res> {
  __$$UserEntityImplCopyWithImpl(
      _$UserEntityImpl _value, $Res Function(_$UserEntityImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? username = freezed,
    Object? isPasswordCreated = freezed,
    Object? phone = freezed,
    Object? mobile = freezed,
    Object? profileImage = freezed,
    Object? shippingAddress = freezed,
    Object? billingAddress = freezed,
    Object? isAddressSame = freezed,
    Object? fax = freezed,
    Object? website = freezed,
    Object? zipCode = freezed,
    Object? state = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? buyRequestCount = freezed,
    Object? orderCount = freezed,
    Object? otp = freezed,
    Object? legalRegisteredName = freezed,
    Object? companyOperatingName = freezed,
    Object? fedTaxId = freezed,
    Object? resaleTax = freezed,
    Object? jbtId = freezed,
    Object? hasAmLprogram = freezed,
    Object? howTheyHeard = freezed,
    Object? facebookId = freezed,
    Object? instagramId = freezed,
    Object? typeOfBusiness = freezed,
    Object? businessStartDate = freezed,
    Object? yearsAtPresentLocation = freezed,
    Object? legalOrganizationStatus = freezed,
    Object? orderAuthorizedPerson = freezed,
    Object? documentName = freezed,
    Object? documentUrl = freezed,
    Object? tradeReferences = freezed,
    Object? businessEntityName = freezed,
    Object? signatureImageName = freezed,
    Object? signatureImageUrl = freezed,
    Object? signDate = freezed,
    Object? printName = freezed,
    Object? isPhoneVerified = freezed,
    Object? isEmailVerified = freezed,
    Object? isBlocked = freezed,
    Object? isVerified = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? token = freezed,
    Object? customerProfileId = freezed,
    Object? kycRejectReason = freezed,
  }) {
    return _then(_$UserEntityImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      username: freezed == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String?,
      isPasswordCreated: freezed == isPasswordCreated
          ? _value.isPasswordCreated
          : isPasswordCreated // ignore: cast_nullable_to_non_nullable
              as bool?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      mobile: freezed == mobile
          ? _value.mobile
          : mobile // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as dynamic,
      shippingAddress: freezed == shippingAddress
          ? _value.shippingAddress
          : shippingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      billingAddress: freezed == billingAddress
          ? _value.billingAddress
          : billingAddress // ignore: cast_nullable_to_non_nullable
              as IngAddress?,
      isAddressSame: freezed == isAddressSame
          ? _value.isAddressSame
          : isAddressSame // ignore: cast_nullable_to_non_nullable
              as bool?,
      fax: freezed == fax
          ? _value.fax
          : fax // ignore: cast_nullable_to_non_nullable
              as String?,
      website: freezed == website
          ? _value.website
          : website // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      buyRequestCount: freezed == buyRequestCount
          ? _value.buyRequestCount
          : buyRequestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      orderCount: freezed == orderCount
          ? _value.orderCount
          : orderCount // ignore: cast_nullable_to_non_nullable
              as int?,
      otp: freezed == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String?,
      legalRegisteredName: freezed == legalRegisteredName
          ? _value.legalRegisteredName
          : legalRegisteredName // ignore: cast_nullable_to_non_nullable
              as String?,
      companyOperatingName: freezed == companyOperatingName
          ? _value.companyOperatingName
          : companyOperatingName // ignore: cast_nullable_to_non_nullable
              as String?,
      fedTaxId: freezed == fedTaxId
          ? _value.fedTaxId
          : fedTaxId // ignore: cast_nullable_to_non_nullable
              as String?,
      resaleTax: freezed == resaleTax
          ? _value.resaleTax
          : resaleTax // ignore: cast_nullable_to_non_nullable
              as String?,
      jbtId: freezed == jbtId
          ? _value.jbtId
          : jbtId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasAmLprogram: freezed == hasAmLprogram
          ? _value.hasAmLprogram
          : hasAmLprogram // ignore: cast_nullable_to_non_nullable
              as bool?,
      howTheyHeard: freezed == howTheyHeard
          ? _value.howTheyHeard
          : howTheyHeard // ignore: cast_nullable_to_non_nullable
              as String?,
      facebookId: freezed == facebookId
          ? _value.facebookId
          : facebookId // ignore: cast_nullable_to_non_nullable
              as String?,
      instagramId: freezed == instagramId
          ? _value.instagramId
          : instagramId // ignore: cast_nullable_to_non_nullable
              as String?,
      typeOfBusiness: freezed == typeOfBusiness
          ? _value.typeOfBusiness
          : typeOfBusiness // ignore: cast_nullable_to_non_nullable
              as String?,
      businessStartDate: freezed == businessStartDate
          ? _value.businessStartDate
          : businessStartDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      yearsAtPresentLocation: freezed == yearsAtPresentLocation
          ? _value.yearsAtPresentLocation
          : yearsAtPresentLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      legalOrganizationStatus: freezed == legalOrganizationStatus
          ? _value.legalOrganizationStatus
          : legalOrganizationStatus // ignore: cast_nullable_to_non_nullable
              as LegalOrganizationStatus?,
      orderAuthorizedPerson: freezed == orderAuthorizedPerson
          ? _value.orderAuthorizedPerson
          : orderAuthorizedPerson // ignore: cast_nullable_to_non_nullable
              as List<OrderAuthorizedPerson>?,
      documentName: freezed == documentName
          ? _value.documentName
          : documentName // ignore: cast_nullable_to_non_nullable
              as String?,
      documentUrl: freezed == documentUrl
          ? _value.documentUrl
          : documentUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      tradeReferences: freezed == tradeReferences
          ? _value.tradeReferences
          : tradeReferences // ignore: cast_nullable_to_non_nullable
              as List<TradeReference>?,
      businessEntityName: freezed == businessEntityName
          ? _value.businessEntityName
          : businessEntityName // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageName: freezed == signatureImageName
          ? _value.signatureImageName
          : signatureImageName // ignore: cast_nullable_to_non_nullable
              as String?,
      signatureImageUrl: freezed == signatureImageUrl
          ? _value.signatureImageUrl
          : signatureImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      signDate: freezed == signDate
          ? _value.signDate
          : signDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      printName: freezed == printName
          ? _value.printName
          : printName // ignore: cast_nullable_to_non_nullable
              as String?,
      isPhoneVerified: freezed == isPhoneVerified
          ? _value.isPhoneVerified
          : isPhoneVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isEmailVerified: freezed == isEmailVerified
          ? _value.isEmailVerified
          : isEmailVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isBlocked: freezed == isBlocked
          ? _value.isBlocked
          : isBlocked // ignore: cast_nullable_to_non_nullable
              as bool?,
      isVerified: freezed == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      customerProfileId: freezed == customerProfileId
          ? _value.customerProfileId
          : customerProfileId // ignore: cast_nullable_to_non_nullable
              as String?,
      kycRejectReason: freezed == kycRejectReason
          ? _value.kycRejectReason
          : kycRejectReason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserEntityImpl implements _UserEntity {
  const _$UserEntityImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'first_name') this.firstName,
      @JsonKey(name: 'last_name') this.lastName,
      @JsonKey(name: 'email') this.email,
      @JsonKey(name: 'username') this.username,
      @JsonKey(name: 'is_password_created') this.isPasswordCreated,
      @JsonKey(name: 'phone') this.phone,
      @JsonKey(name: 'mobile') this.mobile,
      @JsonKey(name: 'profile_image') this.profileImage,
      @JsonKey(name: 'shipping_address') this.shippingAddress,
      @JsonKey(name: 'billing_address') this.billingAddress,
      @JsonKey(name: 'is_address_same') this.isAddressSame,
      @JsonKey(name: 'fax') this.fax,
      @JsonKey(name: 'website') this.website,
      @JsonKey(name: 'zip_code') this.zipCode,
      @JsonKey(name: 'state') this.state,
      @JsonKey(name: 'city') this.city,
      @JsonKey(name: 'country') this.country,
      @JsonKey(name: 'buy_request_count') this.buyRequestCount,
      @JsonKey(name: 'order_count') this.orderCount,
      @JsonKey(name: 'otp') this.otp,
      @JsonKey(name: 'legal_registered_name') this.legalRegisteredName,
      @JsonKey(name: 'company_operating_name') this.companyOperatingName,
      @JsonKey(name: 'fed_tax_id') this.fedTaxId,
      @JsonKey(name: 'resale_tax') this.resaleTax,
      @JsonKey(name: 'jbt_id') this.jbtId,
      @JsonKey(name: 'has_AMLprogram') this.hasAmLprogram,
      @JsonKey(name: 'how_they_heard') this.howTheyHeard,
      @JsonKey(name: 'facebook_id') this.facebookId,
      @JsonKey(name: 'instagram_id') this.instagramId,
      @JsonKey(name: 'type_of_business') this.typeOfBusiness,
      @JsonKey(name: 'business_start_date') this.businessStartDate,
      @JsonKey(name: 'years_at_present_location') this.yearsAtPresentLocation,
      @JsonKey(name: 'legal_organization_status') this.legalOrganizationStatus,
      @JsonKey(name: 'order_authorized_person') this.orderAuthorizedPerson,
      @JsonKey(name: 'document_name') this.documentName,
      @JsonKey(name: 'document_url') this.documentUrl,
      @JsonKey(name: 'trade_references') this.tradeReferences,
      @JsonKey(name: 'business_entity_name') this.businessEntityName,
      @JsonKey(name: 'signature_image_name') this.signatureImageName,
      @JsonKey(name: 'signature_image_url') this.signatureImageUrl,
      @JsonKey(name: 'sign_date') this.signDate,
      @JsonKey(name: 'print_name') this.printName,
      @JsonKey(name: 'is_phone_verified') this.isPhoneVerified,
      @JsonKey(name: 'is_email_verified') this.isEmailVerified,
      @JsonKey(name: 'is_blocked') this.isBlocked,
      @JsonKey(name: 'is_verified') this.isVerified,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'token') this.token,
      @JsonKey(name: 'customer_profile_id') this.customerProfileId,
      @JsonKey(name: 'kyc_reject_reason') this.kycRejectReason});

  factory _$UserEntityImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserEntityImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'first_name')
  final String? firstName;
  @override
  @JsonKey(name: 'last_name')
  final String? lastName;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'username')
  final String? username;
  @override
  @JsonKey(name: 'is_password_created')
  final bool? isPasswordCreated;
  @override
  @JsonKey(name: 'phone')
  final String? phone;
  @override
  @JsonKey(name: 'mobile')
  final String? mobile;
  @override
  @JsonKey(name: 'profile_image')
  final dynamic profileImage;
  @override
  @JsonKey(name: 'shipping_address')
  final IngAddress? shippingAddress;
  @override
  @JsonKey(name: 'billing_address')
  final IngAddress? billingAddress;
  @override
  @JsonKey(name: 'is_address_same')
  final bool? isAddressSame;
  @override
  @JsonKey(name: 'fax')
  final String? fax;
  @override
  @JsonKey(name: 'website')
  final String? website;
  @override
  @JsonKey(name: 'zip_code')
  final String? zipCode;
  @override
  @JsonKey(name: 'state')
  final String? state;
  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'country')
  final String? country;
  @override
  @JsonKey(name: 'buy_request_count')
  final int? buyRequestCount;
  @override
  @JsonKey(name: 'order_count')
  final int? orderCount;
  @override
  @JsonKey(name: 'otp')
  final String? otp;
  @override
  @JsonKey(name: 'legal_registered_name')
  final String? legalRegisteredName;
  @override
  @JsonKey(name: 'company_operating_name')
  final String? companyOperatingName;
  @override
  @JsonKey(name: 'fed_tax_id')
  final String? fedTaxId;
  @override
  @JsonKey(name: 'resale_tax')
  final String? resaleTax;
  @override
  @JsonKey(name: 'jbt_id')
  final String? jbtId;
  @override
  @JsonKey(name: 'has_AMLprogram')
  final bool? hasAmLprogram;
  @override
  @JsonKey(name: 'how_they_heard')
  final String? howTheyHeard;
  @override
  @JsonKey(name: 'facebook_id')
  final String? facebookId;
  @override
  @JsonKey(name: 'instagram_id')
  final String? instagramId;
  @override
  @JsonKey(name: 'type_of_business')
  final String? typeOfBusiness;
  @override
  @JsonKey(name: 'business_start_date')
  final DateTime? businessStartDate;
  @override
  @JsonKey(name: 'years_at_present_location')
  final String? yearsAtPresentLocation;
  @override
  @JsonKey(name: 'legal_organization_status')
  final LegalOrganizationStatus? legalOrganizationStatus;
  @override
  @JsonKey(name: 'order_authorized_person')
  final List<OrderAuthorizedPerson>? orderAuthorizedPerson;
  @override
  @JsonKey(name: 'document_name')
  final String? documentName;
  @override
  @JsonKey(name: 'document_url')
  final String? documentUrl;
  @override
  @JsonKey(name: 'trade_references')
  final List<TradeReference>? tradeReferences;
  @override
  @JsonKey(name: 'business_entity_name')
  final String? businessEntityName;
  @override
  @JsonKey(name: 'signature_image_name')
  final String? signatureImageName;
  @override
  @JsonKey(name: 'signature_image_url')
  final String? signatureImageUrl;
  @override
  @JsonKey(name: 'sign_date')
  final DateTime? signDate;
  @override
  @JsonKey(name: 'print_name')
  final String? printName;
  @override
  @JsonKey(name: 'is_phone_verified')
  final bool? isPhoneVerified;
  @override
  @JsonKey(name: 'is_email_verified')
  final bool? isEmailVerified;
  @override
  @JsonKey(name: 'is_blocked')
  final bool? isBlocked;
  @override
  @JsonKey(name: 'is_verified')
  final bool? isVerified;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'token')
  final String? token;
  @override
  @JsonKey(name: 'customer_profile_id')
  final String? customerProfileId;
  @override
  @JsonKey(name: 'kyc_reject_reason')
  final String? kycRejectReason;

  @override
  String toString() {
    return 'UserEntity(id: $id, firstName: $firstName, lastName: $lastName, email: $email, username: $username, isPasswordCreated: $isPasswordCreated, phone: $phone, mobile: $mobile, profileImage: $profileImage, shippingAddress: $shippingAddress, billingAddress: $billingAddress, isAddressSame: $isAddressSame, fax: $fax, website: $website, zipCode: $zipCode, state: $state, city: $city, country: $country, buyRequestCount: $buyRequestCount, orderCount: $orderCount, otp: $otp, legalRegisteredName: $legalRegisteredName, companyOperatingName: $companyOperatingName, fedTaxId: $fedTaxId, resaleTax: $resaleTax, jbtId: $jbtId, hasAmLprogram: $hasAmLprogram, howTheyHeard: $howTheyHeard, facebookId: $facebookId, instagramId: $instagramId, typeOfBusiness: $typeOfBusiness, businessStartDate: $businessStartDate, yearsAtPresentLocation: $yearsAtPresentLocation, legalOrganizationStatus: $legalOrganizationStatus, orderAuthorizedPerson: $orderAuthorizedPerson, documentName: $documentName, documentUrl: $documentUrl, tradeReferences: $tradeReferences, businessEntityName: $businessEntityName, signatureImageName: $signatureImageName, signatureImageUrl: $signatureImageUrl, signDate: $signDate, printName: $printName, isPhoneVerified: $isPhoneVerified, isEmailVerified: $isEmailVerified, isBlocked: $isBlocked, isVerified: $isVerified, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, token: $token, customerProfileId: $customerProfileId, kycRejectReason: $kycRejectReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserEntityImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.isPasswordCreated, isPasswordCreated) ||
                other.isPasswordCreated == isPasswordCreated) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.mobile, mobile) || other.mobile == mobile) &&
            const DeepCollectionEquality()
                .equals(other.profileImage, profileImage) &&
            (identical(other.shippingAddress, shippingAddress) ||
                other.shippingAddress == shippingAddress) &&
            (identical(other.billingAddress, billingAddress) ||
                other.billingAddress == billingAddress) &&
            (identical(other.isAddressSame, isAddressSame) ||
                other.isAddressSame == isAddressSame) &&
            (identical(other.fax, fax) || other.fax == fax) &&
            (identical(other.website, website) || other.website == website) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.buyRequestCount, buyRequestCount) ||
                other.buyRequestCount == buyRequestCount) &&
            (identical(other.orderCount, orderCount) ||
                other.orderCount == orderCount) &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.legalRegisteredName, legalRegisteredName) ||
                other.legalRegisteredName == legalRegisteredName) &&
            (identical(other.companyOperatingName, companyOperatingName) ||
                other.companyOperatingName == companyOperatingName) &&
            (identical(other.fedTaxId, fedTaxId) ||
                other.fedTaxId == fedTaxId) &&
            (identical(other.resaleTax, resaleTax) ||
                other.resaleTax == resaleTax) &&
            (identical(other.jbtId, jbtId) || other.jbtId == jbtId) &&
            (identical(other.hasAmLprogram, hasAmLprogram) ||
                other.hasAmLprogram == hasAmLprogram) &&
            (identical(other.howTheyHeard, howTheyHeard) ||
                other.howTheyHeard == howTheyHeard) &&
            (identical(other.facebookId, facebookId) ||
                other.facebookId == facebookId) &&
            (identical(other.instagramId, instagramId) ||
                other.instagramId == instagramId) &&
            (identical(other.typeOfBusiness, typeOfBusiness) ||
                other.typeOfBusiness == typeOfBusiness) &&
            (identical(other.businessStartDate, businessStartDate) ||
                other.businessStartDate == businessStartDate) &&
            (identical(other.yearsAtPresentLocation, yearsAtPresentLocation) ||
                other.yearsAtPresentLocation == yearsAtPresentLocation) &&
            (identical(other.legalOrganizationStatus, legalOrganizationStatus) ||
                other.legalOrganizationStatus == legalOrganizationStatus) &&
            const DeepCollectionEquality()
                .equals(other.orderAuthorizedPerson, orderAuthorizedPerson) &&
            (identical(other.documentName, documentName) ||
                other.documentName == documentName) &&
            (identical(other.documentUrl, documentUrl) ||
                other.documentUrl == documentUrl) &&
            const DeepCollectionEquality()
                .equals(other.tradeReferences, tradeReferences) &&
            (identical(other.businessEntityName, businessEntityName) ||
                other.businessEntityName == businessEntityName) &&
            (identical(other.signatureImageName, signatureImageName) ||
                other.signatureImageName == signatureImageName) &&
            (identical(other.signatureImageUrl, signatureImageUrl) ||
                other.signatureImageUrl == signatureImageUrl) &&
            (identical(other.signDate, signDate) ||
                other.signDate == signDate) &&
            (identical(other.printName, printName) ||
                other.printName == printName) &&
            (identical(other.isPhoneVerified, isPhoneVerified) ||
                other.isPhoneVerified == isPhoneVerified) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.isBlocked, isBlocked) ||
                other.isBlocked == isBlocked) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.customerProfileId, customerProfileId) ||
                other.customerProfileId == customerProfileId) &&
            (identical(other.kycRejectReason, kycRejectReason) || other.kycRejectReason == kycRejectReason));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        firstName,
        lastName,
        email,
        username,
        isPasswordCreated,
        phone,
        mobile,
        const DeepCollectionEquality().hash(profileImage),
        shippingAddress,
        billingAddress,
        isAddressSame,
        fax,
        website,
        zipCode,
        state,
        city,
        country,
        buyRequestCount,
        orderCount,
        otp,
        legalRegisteredName,
        companyOperatingName,
        fedTaxId,
        resaleTax,
        jbtId,
        hasAmLprogram,
        howTheyHeard,
        facebookId,
        instagramId,
        typeOfBusiness,
        businessStartDate,
        yearsAtPresentLocation,
        legalOrganizationStatus,
        const DeepCollectionEquality().hash(orderAuthorizedPerson),
        documentName,
        documentUrl,
        const DeepCollectionEquality().hash(tradeReferences),
        businessEntityName,
        signatureImageName,
        signatureImageUrl,
        signDate,
        printName,
        isPhoneVerified,
        isEmailVerified,
        isBlocked,
        isVerified,
        isActive,
        deleted,
        createdAt,
        updatedAt,
        token,
        customerProfileId,
        kycRejectReason
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserEntityImplCopyWith<_$UserEntityImpl> get copyWith =>
      __$$UserEntityImplCopyWithImpl<_$UserEntityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserEntityImplToJson(
      this,
    );
  }
}

abstract class _UserEntity implements UserEntity {
  const factory _UserEntity(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'first_name') final String? firstName,
      @JsonKey(name: 'last_name') final String? lastName,
      @JsonKey(name: 'email') final String? email,
      @JsonKey(name: 'username') final String? username,
      @JsonKey(name: 'is_password_created') final bool? isPasswordCreated,
      @JsonKey(name: 'phone') final String? phone,
      @JsonKey(name: 'mobile') final String? mobile,
      @JsonKey(name: 'profile_image') final dynamic profileImage,
      @JsonKey(name: 'shipping_address') final IngAddress? shippingAddress,
      @JsonKey(name: 'billing_address') final IngAddress? billingAddress,
      @JsonKey(name: 'is_address_same') final bool? isAddressSame,
      @JsonKey(name: 'fax') final String? fax,
      @JsonKey(name: 'website') final String? website,
      @JsonKey(name: 'zip_code') final String? zipCode,
      @JsonKey(name: 'state') final String? state,
      @JsonKey(name: 'city') final String? city,
      @JsonKey(name: 'country') final String? country,
      @JsonKey(name: 'buy_request_count') final int? buyRequestCount,
      @JsonKey(name: 'order_count') final int? orderCount,
      @JsonKey(name: 'otp') final String? otp,
      @JsonKey(name: 'legal_registered_name') final String? legalRegisteredName,
      @JsonKey(name: 'company_operating_name')
      final String? companyOperatingName,
      @JsonKey(name: 'fed_tax_id') final String? fedTaxId,
      @JsonKey(name: 'resale_tax') final String? resaleTax,
      @JsonKey(name: 'jbt_id') final String? jbtId,
      @JsonKey(name: 'has_AMLprogram') final bool? hasAmLprogram,
      @JsonKey(name: 'how_they_heard') final String? howTheyHeard,
      @JsonKey(name: 'facebook_id') final String? facebookId,
      @JsonKey(name: 'instagram_id') final String? instagramId,
      @JsonKey(name: 'type_of_business') final String? typeOfBusiness,
      @JsonKey(name: 'business_start_date') final DateTime? businessStartDate,
      @JsonKey(name: 'years_at_present_location')
      final String? yearsAtPresentLocation,
      @JsonKey(name: 'legal_organization_status')
      final LegalOrganizationStatus? legalOrganizationStatus,
      @JsonKey(name: 'order_authorized_person')
      final List<OrderAuthorizedPerson>? orderAuthorizedPerson,
      @JsonKey(name: 'document_name') final String? documentName,
      @JsonKey(name: 'document_url') final String? documentUrl,
      @JsonKey(name: 'trade_references')
      final List<TradeReference>? tradeReferences,
      @JsonKey(name: 'business_entity_name') final String? businessEntityName,
      @JsonKey(name: 'signature_image_name') final String? signatureImageName,
      @JsonKey(name: 'signature_image_url') final String? signatureImageUrl,
      @JsonKey(name: 'sign_date') final DateTime? signDate,
      @JsonKey(name: 'print_name') final String? printName,
      @JsonKey(name: 'is_phone_verified') final bool? isPhoneVerified,
      @JsonKey(name: 'is_email_verified') final bool? isEmailVerified,
      @JsonKey(name: 'is_blocked') final bool? isBlocked,
      @JsonKey(name: 'is_verified') final bool? isVerified,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'token') final String? token,
      @JsonKey(name: 'customer_profile_id') final String? customerProfileId,
      @JsonKey(name: 'kyc_reject_reason')
      final String? kycRejectReason}) = _$UserEntityImpl;

  factory _UserEntity.fromJson(Map<String, dynamic> json) =
      _$UserEntityImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'first_name')
  String? get firstName;
  @override
  @JsonKey(name: 'last_name')
  String? get lastName;
  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'username')
  String? get username;
  @override
  @JsonKey(name: 'is_password_created')
  bool? get isPasswordCreated;
  @override
  @JsonKey(name: 'phone')
  String? get phone;
  @override
  @JsonKey(name: 'mobile')
  String? get mobile;
  @override
  @JsonKey(name: 'profile_image')
  dynamic get profileImage;
  @override
  @JsonKey(name: 'shipping_address')
  IngAddress? get shippingAddress;
  @override
  @JsonKey(name: 'billing_address')
  IngAddress? get billingAddress;
  @override
  @JsonKey(name: 'is_address_same')
  bool? get isAddressSame;
  @override
  @JsonKey(name: 'fax')
  String? get fax;
  @override
  @JsonKey(name: 'website')
  String? get website;
  @override
  @JsonKey(name: 'zip_code')
  String? get zipCode;
  @override
  @JsonKey(name: 'state')
  String? get state;
  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'country')
  String? get country;
  @override
  @JsonKey(name: 'buy_request_count')
  int? get buyRequestCount;
  @override
  @JsonKey(name: 'order_count')
  int? get orderCount;
  @override
  @JsonKey(name: 'otp')
  String? get otp;
  @override
  @JsonKey(name: 'legal_registered_name')
  String? get legalRegisteredName;
  @override
  @JsonKey(name: 'company_operating_name')
  String? get companyOperatingName;
  @override
  @JsonKey(name: 'fed_tax_id')
  String? get fedTaxId;
  @override
  @JsonKey(name: 'resale_tax')
  String? get resaleTax;
  @override
  @JsonKey(name: 'jbt_id')
  String? get jbtId;
  @override
  @JsonKey(name: 'has_AMLprogram')
  bool? get hasAmLprogram;
  @override
  @JsonKey(name: 'how_they_heard')
  String? get howTheyHeard;
  @override
  @JsonKey(name: 'facebook_id')
  String? get facebookId;
  @override
  @JsonKey(name: 'instagram_id')
  String? get instagramId;
  @override
  @JsonKey(name: 'type_of_business')
  String? get typeOfBusiness;
  @override
  @JsonKey(name: 'business_start_date')
  DateTime? get businessStartDate;
  @override
  @JsonKey(name: 'years_at_present_location')
  String? get yearsAtPresentLocation;
  @override
  @JsonKey(name: 'legal_organization_status')
  LegalOrganizationStatus? get legalOrganizationStatus;
  @override
  @JsonKey(name: 'order_authorized_person')
  List<OrderAuthorizedPerson>? get orderAuthorizedPerson;
  @override
  @JsonKey(name: 'document_name')
  String? get documentName;
  @override
  @JsonKey(name: 'document_url')
  String? get documentUrl;
  @override
  @JsonKey(name: 'trade_references')
  List<TradeReference>? get tradeReferences;
  @override
  @JsonKey(name: 'business_entity_name')
  String? get businessEntityName;
  @override
  @JsonKey(name: 'signature_image_name')
  String? get signatureImageName;
  @override
  @JsonKey(name: 'signature_image_url')
  String? get signatureImageUrl;
  @override
  @JsonKey(name: 'sign_date')
  DateTime? get signDate;
  @override
  @JsonKey(name: 'print_name')
  String? get printName;
  @override
  @JsonKey(name: 'is_phone_verified')
  bool? get isPhoneVerified;
  @override
  @JsonKey(name: 'is_email_verified')
  bool? get isEmailVerified;
  @override
  @JsonKey(name: 'is_blocked')
  bool? get isBlocked;
  @override
  @JsonKey(name: 'is_verified')
  bool? get isVerified;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'token')
  String? get token;
  @override
  @JsonKey(name: 'customer_profile_id')
  String? get customerProfileId;
  @override
  @JsonKey(name: 'kyc_reject_reason')
  String? get kycRejectReason;
  @override
  @JsonKey(ignore: true)
  _$$UserEntityImplCopyWith<_$UserEntityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IngAddress _$IngAddressFromJson(Map<String, dynamic> json) {
  return _IngAddress.fromJson(json);
}

/// @nodoc
mixin _$IngAddress {
  @JsonKey(name: 'full_name')
  String? get fullName => throw _privateConstructorUsedError;
  @JsonKey(name: 'city')
  String? get city => throw _privateConstructorUsedError;
  @JsonKey(name: 'state')
  String? get state => throw _privateConstructorUsedError;
  @JsonKey(name: 'street')
  String? get street => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne => throw _privateConstructorUsedError;
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'zip_code')
  String? get zipCode => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $IngAddressCopyWith<IngAddress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IngAddressCopyWith<$Res> {
  factory $IngAddressCopyWith(
          IngAddress value, $Res Function(IngAddress) then) =
      _$IngAddressCopyWithImpl<$Res, IngAddress>;
  @useResult
  $Res call(
      {@JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode});
}

/// @nodoc
class _$IngAddressCopyWithImpl<$Res, $Val extends IngAddress>
    implements $IngAddressCopyWith<$Res> {
  _$IngAddressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fullName = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? street = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
  }) {
    return _then(_value.copyWith(
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IngAddressImplCopyWith<$Res>
    implements $IngAddressCopyWith<$Res> {
  factory _$$IngAddressImplCopyWith(
          _$IngAddressImpl value, $Res Function(_$IngAddressImpl) then) =
      __$$IngAddressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'full_name') String? fullName,
      @JsonKey(name: 'city') String? city,
      @JsonKey(name: 'state') String? state,
      @JsonKey(name: 'street') String? street,
      @JsonKey(name: 'address_line_one') String? addressLineOne,
      @JsonKey(name: 'address_line_two') String? addressLineTwo,
      @JsonKey(name: 'country') String? country,
      @JsonKey(name: 'zip_code') String? zipCode});
}

/// @nodoc
class __$$IngAddressImplCopyWithImpl<$Res>
    extends _$IngAddressCopyWithImpl<$Res, _$IngAddressImpl>
    implements _$$IngAddressImplCopyWith<$Res> {
  __$$IngAddressImplCopyWithImpl(
      _$IngAddressImpl _value, $Res Function(_$IngAddressImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fullName = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? street = freezed,
    Object? addressLineOne = freezed,
    Object? addressLineTwo = freezed,
    Object? country = freezed,
    Object? zipCode = freezed,
  }) {
    return _then(_$IngAddressImpl(
      fullName: freezed == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      street: freezed == street
          ? _value.street
          : street // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineOne: freezed == addressLineOne
          ? _value.addressLineOne
          : addressLineOne // ignore: cast_nullable_to_non_nullable
              as String?,
      addressLineTwo: freezed == addressLineTwo
          ? _value.addressLineTwo
          : addressLineTwo // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      zipCode: freezed == zipCode
          ? _value.zipCode
          : zipCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IngAddressImpl implements _IngAddress {
  const _$IngAddressImpl(
      {@JsonKey(name: 'full_name') this.fullName,
      @JsonKey(name: 'city') this.city,
      @JsonKey(name: 'state') this.state,
      @JsonKey(name: 'street') this.street,
      @JsonKey(name: 'address_line_one') this.addressLineOne,
      @JsonKey(name: 'address_line_two') this.addressLineTwo,
      @JsonKey(name: 'country') this.country,
      @JsonKey(name: 'zip_code') this.zipCode});

  factory _$IngAddressImpl.fromJson(Map<String, dynamic> json) =>
      _$$IngAddressImplFromJson(json);

  @override
  @JsonKey(name: 'full_name')
  final String? fullName;
  @override
  @JsonKey(name: 'city')
  final String? city;
  @override
  @JsonKey(name: 'state')
  final String? state;
  @override
  @JsonKey(name: 'street')
  final String? street;
  @override
  @JsonKey(name: 'address_line_one')
  final String? addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  final String? addressLineTwo;
  @override
  @JsonKey(name: 'country')
  final String? country;
  @override
  @JsonKey(name: 'zip_code')
  final String? zipCode;

  @override
  String toString() {
    return 'IngAddress(fullName: $fullName, city: $city, state: $state, street: $street, addressLineOne: $addressLineOne, addressLineTwo: $addressLineTwo, country: $country, zipCode: $zipCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IngAddressImpl &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.street, street) || other.street == street) &&
            (identical(other.addressLineOne, addressLineOne) ||
                other.addressLineOne == addressLineOne) &&
            (identical(other.addressLineTwo, addressLineTwo) ||
                other.addressLineTwo == addressLineTwo) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.zipCode, zipCode) || other.zipCode == zipCode));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fullName, city, state, street,
      addressLineOne, addressLineTwo, country, zipCode);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      __$$IngAddressImplCopyWithImpl<_$IngAddressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IngAddressImplToJson(
      this,
    );
  }
}

abstract class _IngAddress implements IngAddress {
  const factory _IngAddress(
      {@JsonKey(name: 'full_name') final String? fullName,
      @JsonKey(name: 'city') final String? city,
      @JsonKey(name: 'state') final String? state,
      @JsonKey(name: 'street') final String? street,
      @JsonKey(name: 'address_line_one') final String? addressLineOne,
      @JsonKey(name: 'address_line_two') final String? addressLineTwo,
      @JsonKey(name: 'country') final String? country,
      @JsonKey(name: 'zip_code') final String? zipCode}) = _$IngAddressImpl;

  factory _IngAddress.fromJson(Map<String, dynamic> json) =
      _$IngAddressImpl.fromJson;

  @override
  @JsonKey(name: 'full_name')
  String? get fullName;
  @override
  @JsonKey(name: 'city')
  String? get city;
  @override
  @JsonKey(name: 'state')
  String? get state;
  @override
  @JsonKey(name: 'street')
  String? get street;
  @override
  @JsonKey(name: 'address_line_one')
  String? get addressLineOne;
  @override
  @JsonKey(name: 'address_line_two')
  String? get addressLineTwo;
  @override
  @JsonKey(name: 'country')
  String? get country;
  @override
  @JsonKey(name: 'zip_code')
  String? get zipCode;
  @override
  @JsonKey(ignore: true)
  _$$IngAddressImplCopyWith<_$IngAddressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

LegalOrganizationStatus _$LegalOrganizationStatusFromJson(
    Map<String, dynamic> json) {
  return _LegalOrganizationStatus.fromJson(json);
}

/// @nodoc
mixin _$LegalOrganizationStatus {
  @JsonKey(name: 'type')
  String? get type => throw _privateConstructorUsedError;
  @JsonKey(name: 'registration_or_incorporation_state')
  String? get registrationOrIncorporationState =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'registration_or_incorporation_country')
  String? get registrationOrIncorporationCountry =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $LegalOrganizationStatusCopyWith<LegalOrganizationStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LegalOrganizationStatusCopyWith<$Res> {
  factory $LegalOrganizationStatusCopyWith(LegalOrganizationStatus value,
          $Res Function(LegalOrganizationStatus) then) =
      _$LegalOrganizationStatusCopyWithImpl<$Res, LegalOrganizationStatus>;
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'registration_or_incorporation_state')
      String? registrationOrIncorporationState,
      @JsonKey(name: 'registration_or_incorporation_country')
      String? registrationOrIncorporationCountry});
}

/// @nodoc
class _$LegalOrganizationStatusCopyWithImpl<$Res,
        $Val extends LegalOrganizationStatus>
    implements $LegalOrganizationStatusCopyWith<$Res> {
  _$LegalOrganizationStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? registrationOrIncorporationState = freezed,
    Object? registrationOrIncorporationCountry = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationOrIncorporationState: freezed ==
              registrationOrIncorporationState
          ? _value.registrationOrIncorporationState
          : registrationOrIncorporationState // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationOrIncorporationCountry: freezed ==
              registrationOrIncorporationCountry
          ? _value.registrationOrIncorporationCountry
          : registrationOrIncorporationCountry // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LegalOrganizationStatusImplCopyWith<$Res>
    implements $LegalOrganizationStatusCopyWith<$Res> {
  factory _$$LegalOrganizationStatusImplCopyWith(
          _$LegalOrganizationStatusImpl value,
          $Res Function(_$LegalOrganizationStatusImpl) then) =
      __$$LegalOrganizationStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'type') String? type,
      @JsonKey(name: 'registration_or_incorporation_state')
      String? registrationOrIncorporationState,
      @JsonKey(name: 'registration_or_incorporation_country')
      String? registrationOrIncorporationCountry});
}

/// @nodoc
class __$$LegalOrganizationStatusImplCopyWithImpl<$Res>
    extends _$LegalOrganizationStatusCopyWithImpl<$Res,
        _$LegalOrganizationStatusImpl>
    implements _$$LegalOrganizationStatusImplCopyWith<$Res> {
  __$$LegalOrganizationStatusImplCopyWithImpl(
      _$LegalOrganizationStatusImpl _value,
      $Res Function(_$LegalOrganizationStatusImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? registrationOrIncorporationState = freezed,
    Object? registrationOrIncorporationCountry = freezed,
  }) {
    return _then(_$LegalOrganizationStatusImpl(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationOrIncorporationState: freezed ==
              registrationOrIncorporationState
          ? _value.registrationOrIncorporationState
          : registrationOrIncorporationState // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationOrIncorporationCountry: freezed ==
              registrationOrIncorporationCountry
          ? _value.registrationOrIncorporationCountry
          : registrationOrIncorporationCountry // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LegalOrganizationStatusImpl implements _LegalOrganizationStatus {
  const _$LegalOrganizationStatusImpl(
      {@JsonKey(name: 'type') this.type,
      @JsonKey(name: 'registration_or_incorporation_state')
      this.registrationOrIncorporationState,
      @JsonKey(name: 'registration_or_incorporation_country')
      this.registrationOrIncorporationCountry});

  factory _$LegalOrganizationStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$LegalOrganizationStatusImplFromJson(json);

  @override
  @JsonKey(name: 'type')
  final String? type;
  @override
  @JsonKey(name: 'registration_or_incorporation_state')
  final String? registrationOrIncorporationState;
  @override
  @JsonKey(name: 'registration_or_incorporation_country')
  final String? registrationOrIncorporationCountry;

  @override
  String toString() {
    return 'LegalOrganizationStatus(type: $type, registrationOrIncorporationState: $registrationOrIncorporationState, registrationOrIncorporationCountry: $registrationOrIncorporationCountry)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LegalOrganizationStatusImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.registrationOrIncorporationState,
                    registrationOrIncorporationState) ||
                other.registrationOrIncorporationState ==
                    registrationOrIncorporationState) &&
            (identical(other.registrationOrIncorporationCountry,
                    registrationOrIncorporationCountry) ||
                other.registrationOrIncorporationCountry ==
                    registrationOrIncorporationCountry));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type,
      registrationOrIncorporationState, registrationOrIncorporationCountry);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LegalOrganizationStatusImplCopyWith<_$LegalOrganizationStatusImpl>
      get copyWith => __$$LegalOrganizationStatusImplCopyWithImpl<
          _$LegalOrganizationStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LegalOrganizationStatusImplToJson(
      this,
    );
  }
}

abstract class _LegalOrganizationStatus implements LegalOrganizationStatus {
  const factory _LegalOrganizationStatus(
          {@JsonKey(name: 'type') final String? type,
          @JsonKey(name: 'registration_or_incorporation_state')
          final String? registrationOrIncorporationState,
          @JsonKey(name: 'registration_or_incorporation_country')
          final String? registrationOrIncorporationCountry}) =
      _$LegalOrganizationStatusImpl;

  factory _LegalOrganizationStatus.fromJson(Map<String, dynamic> json) =
      _$LegalOrganizationStatusImpl.fromJson;

  @override
  @JsonKey(name: 'type')
  String? get type;
  @override
  @JsonKey(name: 'registration_or_incorporation_state')
  String? get registrationOrIncorporationState;
  @override
  @JsonKey(name: 'registration_or_incorporation_country')
  String? get registrationOrIncorporationCountry;
  @override
  @JsonKey(ignore: true)
  _$$LegalOrganizationStatusImplCopyWith<_$LegalOrganizationStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OrderAuthorizedPerson _$OrderAuthorizedPersonFromJson(
    Map<String, dynamic> json) {
  return _OrderAuthorizedPerson.fromJson(json);
}

/// @nodoc
mixin _$OrderAuthorizedPerson {
  @JsonKey(name: 'last_name')
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: 'mobile_no')
  String? get mobileNo => throw _privateConstructorUsedError;
  @JsonKey(name: 'first_name')
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: 'designation')
  String? get designation => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OrderAuthorizedPersonCopyWith<OrderAuthorizedPerson> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderAuthorizedPersonCopyWith<$Res> {
  factory $OrderAuthorizedPersonCopyWith(OrderAuthorizedPerson value,
          $Res Function(OrderAuthorizedPerson) then) =
      _$OrderAuthorizedPersonCopyWithImpl<$Res, OrderAuthorizedPerson>;
  @useResult
  $Res call(
      {@JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'mobile_no') String? mobileNo,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'designation') String? designation});
}

/// @nodoc
class _$OrderAuthorizedPersonCopyWithImpl<$Res,
        $Val extends OrderAuthorizedPerson>
    implements $OrderAuthorizedPersonCopyWith<$Res> {
  _$OrderAuthorizedPersonCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = freezed,
    Object? mobileNo = freezed,
    Object? firstName = freezed,
    Object? designation = freezed,
  }) {
    return _then(_value.copyWith(
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNo: freezed == mobileNo
          ? _value.mobileNo
          : mobileNo // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderAuthorizedPersonImplCopyWith<$Res>
    implements $OrderAuthorizedPersonCopyWith<$Res> {
  factory _$$OrderAuthorizedPersonImplCopyWith(
          _$OrderAuthorizedPersonImpl value,
          $Res Function(_$OrderAuthorizedPersonImpl) then) =
      __$$OrderAuthorizedPersonImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'last_name') String? lastName,
      @JsonKey(name: 'mobile_no') String? mobileNo,
      @JsonKey(name: 'first_name') String? firstName,
      @JsonKey(name: 'designation') String? designation});
}

/// @nodoc
class __$$OrderAuthorizedPersonImplCopyWithImpl<$Res>
    extends _$OrderAuthorizedPersonCopyWithImpl<$Res,
        _$OrderAuthorizedPersonImpl>
    implements _$$OrderAuthorizedPersonImplCopyWith<$Res> {
  __$$OrderAuthorizedPersonImplCopyWithImpl(_$OrderAuthorizedPersonImpl _value,
      $Res Function(_$OrderAuthorizedPersonImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastName = freezed,
    Object? mobileNo = freezed,
    Object? firstName = freezed,
    Object? designation = freezed,
  }) {
    return _then(_$OrderAuthorizedPersonImpl(
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileNo: freezed == mobileNo
          ? _value.mobileNo
          : mobileNo // ignore: cast_nullable_to_non_nullable
              as String?,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderAuthorizedPersonImpl implements _OrderAuthorizedPerson {
  const _$OrderAuthorizedPersonImpl(
      {@JsonKey(name: 'last_name') this.lastName,
      @JsonKey(name: 'mobile_no') this.mobileNo,
      @JsonKey(name: 'first_name') this.firstName,
      @JsonKey(name: 'designation') this.designation});

  factory _$OrderAuthorizedPersonImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderAuthorizedPersonImplFromJson(json);

  @override
  @JsonKey(name: 'last_name')
  final String? lastName;
  @override
  @JsonKey(name: 'mobile_no')
  final String? mobileNo;
  @override
  @JsonKey(name: 'first_name')
  final String? firstName;
  @override
  @JsonKey(name: 'designation')
  final String? designation;

  @override
  String toString() {
    return 'OrderAuthorizedPerson(lastName: $lastName, mobileNo: $mobileNo, firstName: $firstName, designation: $designation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderAuthorizedPersonImpl &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.mobileNo, mobileNo) ||
                other.mobileNo == mobileNo) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.designation, designation) ||
                other.designation == designation));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, lastName, mobileNo, firstName, designation);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderAuthorizedPersonImplCopyWith<_$OrderAuthorizedPersonImpl>
      get copyWith => __$$OrderAuthorizedPersonImplCopyWithImpl<
          _$OrderAuthorizedPersonImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderAuthorizedPersonImplToJson(
      this,
    );
  }
}

abstract class _OrderAuthorizedPerson implements OrderAuthorizedPerson {
  const factory _OrderAuthorizedPerson(
          {@JsonKey(name: 'last_name') final String? lastName,
          @JsonKey(name: 'mobile_no') final String? mobileNo,
          @JsonKey(name: 'first_name') final String? firstName,
          @JsonKey(name: 'designation') final String? designation}) =
      _$OrderAuthorizedPersonImpl;

  factory _OrderAuthorizedPerson.fromJson(Map<String, dynamic> json) =
      _$OrderAuthorizedPersonImpl.fromJson;

  @override
  @JsonKey(name: 'last_name')
  String? get lastName;
  @override
  @JsonKey(name: 'mobile_no')
  String? get mobileNo;
  @override
  @JsonKey(name: 'first_name')
  String? get firstName;
  @override
  @JsonKey(name: 'designation')
  String? get designation;
  @override
  @JsonKey(ignore: true)
  _$$OrderAuthorizedPersonImplCopyWith<_$OrderAuthorizedPersonImpl>
      get copyWith => throw _privateConstructorUsedError;
}

TradeReference _$TradeReferenceFromJson(Map<String, dynamic> json) {
  return _TradeReference.fromJson(json);
}

/// @nodoc
mixin _$TradeReference {
  @JsonKey(name: 'email')
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone')
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: 'company_name')
  String? get companyName => throw _privateConstructorUsedError;
  @JsonKey(name: 'contact_person_name')
  String? get contactPersonName => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TradeReferenceCopyWith<TradeReference> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TradeReferenceCopyWith<$Res> {
  factory $TradeReferenceCopyWith(
          TradeReference value, $Res Function(TradeReference) then) =
      _$TradeReferenceCopyWithImpl<$Res, TradeReference>;
  @useResult
  $Res call(
      {@JsonKey(name: 'email') String? email,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'company_name') String? companyName,
      @JsonKey(name: 'contact_person_name') String? contactPersonName});
}

/// @nodoc
class _$TradeReferenceCopyWithImpl<$Res, $Val extends TradeReference>
    implements $TradeReferenceCopyWith<$Res> {
  _$TradeReferenceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? phone = freezed,
    Object? companyName = freezed,
    Object? contactPersonName = freezed,
  }) {
    return _then(_value.copyWith(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPersonName: freezed == contactPersonName
          ? _value.contactPersonName
          : contactPersonName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TradeReferenceImplCopyWith<$Res>
    implements $TradeReferenceCopyWith<$Res> {
  factory _$$TradeReferenceImplCopyWith(_$TradeReferenceImpl value,
          $Res Function(_$TradeReferenceImpl) then) =
      __$$TradeReferenceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'email') String? email,
      @JsonKey(name: 'phone') String? phone,
      @JsonKey(name: 'company_name') String? companyName,
      @JsonKey(name: 'contact_person_name') String? contactPersonName});
}

/// @nodoc
class __$$TradeReferenceImplCopyWithImpl<$Res>
    extends _$TradeReferenceCopyWithImpl<$Res, _$TradeReferenceImpl>
    implements _$$TradeReferenceImplCopyWith<$Res> {
  __$$TradeReferenceImplCopyWithImpl(
      _$TradeReferenceImpl _value, $Res Function(_$TradeReferenceImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = freezed,
    Object? phone = freezed,
    Object? companyName = freezed,
    Object? contactPersonName = freezed,
  }) {
    return _then(_$TradeReferenceImpl(
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      companyName: freezed == companyName
          ? _value.companyName
          : companyName // ignore: cast_nullable_to_non_nullable
              as String?,
      contactPersonName: freezed == contactPersonName
          ? _value.contactPersonName
          : contactPersonName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TradeReferenceImpl implements _TradeReference {
  const _$TradeReferenceImpl(
      {@JsonKey(name: 'email') this.email,
      @JsonKey(name: 'phone') this.phone,
      @JsonKey(name: 'company_name') this.companyName,
      @JsonKey(name: 'contact_person_name') this.contactPersonName});

  factory _$TradeReferenceImpl.fromJson(Map<String, dynamic> json) =>
      _$$TradeReferenceImplFromJson(json);

  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'phone')
  final String? phone;
  @override
  @JsonKey(name: 'company_name')
  final String? companyName;
  @override
  @JsonKey(name: 'contact_person_name')
  final String? contactPersonName;

  @override
  String toString() {
    return 'TradeReference(email: $email, phone: $phone, companyName: $companyName, contactPersonName: $contactPersonName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TradeReferenceImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.contactPersonName, contactPersonName) ||
                other.contactPersonName == contactPersonName));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, email, phone, companyName, contactPersonName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TradeReferenceImplCopyWith<_$TradeReferenceImpl> get copyWith =>
      __$$TradeReferenceImplCopyWithImpl<_$TradeReferenceImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TradeReferenceImplToJson(
      this,
    );
  }
}

abstract class _TradeReference implements TradeReference {
  const factory _TradeReference(
      {@JsonKey(name: 'email') final String? email,
      @JsonKey(name: 'phone') final String? phone,
      @JsonKey(name: 'company_name') final String? companyName,
      @JsonKey(name: 'contact_person_name')
      final String? contactPersonName}) = _$TradeReferenceImpl;

  factory _TradeReference.fromJson(Map<String, dynamic> json) =
      _$TradeReferenceImpl.fromJson;

  @override
  @JsonKey(name: 'email')
  String? get email;
  @override
  @JsonKey(name: 'phone')
  String? get phone;
  @override
  @JsonKey(name: 'company_name')
  String? get companyName;
  @override
  @JsonKey(name: 'contact_person_name')
  String? get contactPersonName;
  @override
  @JsonKey(ignore: true)
  _$$TradeReferenceImplCopyWith<_$TradeReferenceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
