// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationModelImpl _$$NotificationModelImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationModelImpl(
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      notificationType: $enumDecodeNullable(
          _$NotificationTypeEnumMap, json['notification_type']),
      title: json['title'] as String?,
      message: json['message'],
      payload: json['payload'],
      isRead: json['is_read'] as bool?,
      deleted: json['_deleted'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$NotificationModelImplToJson(
        _$NotificationModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'notification_type': _$NotificationTypeEnumMap[instance.notificationType],
      'title': instance.title,
      'message': instance.message,
      'payload': instance.payload,
      'is_read': instance.isRead,
      '_deleted': instance.deleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$NotificationTypeEnumMap = {
  NotificationType.BUY_REQUEST_UPDATED: 'BUY_REQUEST_UPDATED',
  NotificationType.ORDER_CREATED: 'ORDER_CREATED',
  NotificationType.BUY_REQUEST_REJECTED: 'BUY_REQUEST_REJECTED',
  NotificationType.ORDER_CANCELED: 'ORDER_CANCELED',
  NotificationType.ORDER_DELIVERED: 'ORDER_DELIVERED',
  NotificationType.ORDER_SHIPPED: 'ORDER_SHIPPED',
  NotificationType.STOCK_UPDATED: 'STOCK_UPDATED',
  NotificationType.STOCK_PRICE_INCREASED: 'STOCK_PRICE_INCREASED',
  NotificationType.STOCK_PRICE_DECREASED: 'STOCK_PRICE_DECREASED',
  NotificationType.STOCK_AVAILABLE: 'STOCK_AVAILABLE',
  NotificationType.STOCK_HOLD: 'STOCK_HOLD',
  NotificationType.STOCK_SOLD: 'STOCK_SOLD',
  NotificationType.KYC_REJECTED: 'KYC_REJECTED',
  NotificationType.KYC_ACCEPTED: 'KYC_ACCEPTED',
  NotificationType.CREDIT_LIMIT_ADDED: 'CREDIT_LIMIT_ADDED',
  NotificationType.RAISE_INVOICE: 'RAISE_INVOICE',
  NotificationType.INVOICE_ACCEPTED: 'INVOICE_ACCEPTED',
  NotificationType.INVOICE_REJECTED: 'INVOICE_REJECTED',
  NotificationType.INVOICE_AVAILABLE: 'INVOICE_AVAILABLE',
  NotificationType.RETURN_ORDER_ACCEPTED: 'RETURN_ORDER_ACCEPTED',
  NotificationType.RETURN_ORDER_REJECTED: 'RETURN_ORDER_REJECTED',
  NotificationType.OFFER_ACCEPTED: 'OFFER_ACCEPTED',
  NotificationType.OFFER_REJECTED: 'OFFER_REJECTED',
  NotificationType.OFFER_CREATED: 'OFFER_REVISED',
  NotificationType.OFFER_REVISED: 'OFFER_CREATED',
  NotificationType.VENDOR_ORDER_SHIPPED: 'VENDOR_ORDER_SHIPPED',
  NotificationType.VENDOR_ORDER_PAID: 'VENDOR_ORDER_PAID',
  NotificationType.BUY_REQUEST_CREATED: 'BUY_REQUEST_CREATED',
};
