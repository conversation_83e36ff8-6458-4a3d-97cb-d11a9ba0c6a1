import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_model.freezed.dart';

part 'notification_model.g.dart';

@freezed
class NotificationModel with _$NotificationModel {
  const factory NotificationModel({
    @J<PERSON><PERSON>ey(name: 'id') String? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id') String? userId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'notification_type') NotificationType? notificationType,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') String? title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'message') dynamic message,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'payload') dynamic payload,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_read') bool? isRead,
    @<PERSON><PERSON><PERSON><PERSON>(name: '_deleted') bool? deleted,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'createdAt') DateTime? createdAt,
    @J<PERSON><PERSON><PERSON>(name: 'updatedAt') DateTime? updatedAt,
  }) = _NotificationModel;

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);
}

enum NotificationType {
  @JsonValue('BUY_REQUEST_UPDATED')
  BUY_REQUEST_UPDATED,
  @JsonValue('ORDER_CREATED')
  ORDER_CREATED,
  @JsonValue('BUY_REQUEST_REJECTED')
  BUY_REQUEST_REJECTED,
  @JsonValue('ORDER_CANCELED')
  ORDER_CANCELED,
  @JsonValue('ORDER_DELIVERED')
  ORDER_DELIVERED,
  @JsonValue('ORDER_SHIPPED')
  ORDER_SHIPPED,
  @JsonValue('STOCK_UPDATED')
  STOCK_UPDATED,
  @JsonValue('STOCK_PRICE_INCREASED')
  STOCK_PRICE_INCREASED,
  @JsonValue('STOCK_PRICE_DECREASED')
  STOCK_PRICE_DECREASED,
  @JsonValue('STOCK_AVAILABLE')
  STOCK_AVAILABLE,
  @JsonValue('STOCK_HOLD')
  STOCK_HOLD,
  @JsonValue('STOCK_SOLD')
  STOCK_SOLD,
  @JsonValue('KYC_REJECTED')
  KYC_REJECTED,
  @JsonValue('KYC_ACCEPTED')
  KYC_ACCEPTED,
  @JsonValue('CREDIT_LIMIT_ADDED')
  CREDIT_LIMIT_ADDED,
  @JsonValue('RAISE_INVOICE')
  RAISE_INVOICE,
  @JsonValue('INVOICE_ACCEPTED')
  INVOICE_ACCEPTED,
  @JsonValue('INVOICE_REJECTED')
  INVOICE_REJECTED,
  @JsonValue('INVOICE_AVAILABLE')
  INVOICE_AVAILABLE,
  @JsonValue('RETURN_ORDER_ACCEPTED')
  RETURN_ORDER_ACCEPTED,
  @JsonValue('RETURN_ORDER_REJECTED')
  RETURN_ORDER_REJECTED,
  @JsonValue('RETURN_ORDER_INITIATED')
  RETURN_ORDER_INITIATED,
  @JsonValue('OFFER_ACCEPTED')
  OFFER_ACCEPTED,
  @JsonValue('OFFER_REJECTED')
  OFFER_REJECTED,
  @JsonValue('OFFER_REVISED')
  OFFER_CREATED,
  @JsonValue('OFFER_CREATED')
  OFFER_REVISED,
  @JsonValue('VENDOR_ORDER_SHIPPED')
  VENDOR_ORDER_SHIPPED,
  @JsonValue('VENDOR_ORDER_PAID')
  VENDOR_ORDER_PAID,
  @JsonValue('VENDOR_ORDER_REJECTED')
  VENDOR_ORDER_REJECTED,
  @JsonValue('BUY_REQUEST_CREATED')
  BUY_REQUEST_CREATED,
  @JsonValue('INQUIRY_MESSAGE')
  INQUIRY_MESSAGE,
}

final notificationType = EnumValues({
  'BUY_REQUEST_UPDATED': NotificationType.BUY_REQUEST_UPDATED,
  'ORDER_CREATED': NotificationType.ORDER_CREATED,
  'BUY_REQUEST_REJECTED': NotificationType.BUY_REQUEST_REJECTED,
  'ORDER_CANCELED': NotificationType.ORDER_CANCELED,
  'ORDER_DELIVERED': NotificationType.ORDER_DELIVERED,
  'ORDER_SHIPPED': NotificationType.ORDER_SHIPPED,
  'STOCK_UPDATED': NotificationType.STOCK_UPDATED,
  'STOCK_PRICE_INCREASED': NotificationType.STOCK_PRICE_INCREASED,
  'STOCK_PRICE_DECREASED': NotificationType.STOCK_PRICE_DECREASED,
  'STOCK_AVAILABLE': NotificationType.STOCK_AVAILABLE,
  'STOCK_HOLD': NotificationType.STOCK_HOLD,
  'STOCK_SOLD': NotificationType.STOCK_SOLD,
  'KYC_REJECTED': NotificationType.KYC_REJECTED,
  'KYC_ACCEPTED': NotificationType.KYC_ACCEPTED,
  'CREDIT_LIMIT_ADDED': NotificationType.CREDIT_LIMIT_ADDED,
  'INVOICE_AVAILABLE': NotificationType.INVOICE_AVAILABLE,
  'INVOICE_ACCEPTED': NotificationType.INVOICE_ACCEPTED,
  'INVOICE_REJECTED': NotificationType.INVOICE_REJECTED,
  'RAISE_INVOICE': NotificationType.RAISE_INVOICE,
  'RETURN_ORDER_ACCEPTED': NotificationType.RETURN_ORDER_ACCEPTED,
  'RETURN_ORDER_REJECTED': NotificationType.RETURN_ORDER_REJECTED,
  'OFFER_ACCEPTED': NotificationType.OFFER_ACCEPTED,
  'OFFER_REJECTED': NotificationType.OFFER_REJECTED,
});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
