// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_metrics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DashboardMetrics _$DashboardMetricsFromJson(Map<String, dynamic> json) {
  return _DashboardMetrics.fromJson(json);
}

/// @nodoc
mixin _$DashboardMetrics {
  @JsonKey(name: 'show_offers_module')
  bool? get showOffersModule => throw _privateConstructorUsedError;
  @JsonKey(name: 'buy_request_count')
  int? get buyRequestCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'unread_notification_count')
  int? get unreadNotificationCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_pending_offers_count')
  int? get userPendingOffersCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'pending_offers_count')
  int? get pendingOffersCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'diamonds_count')
  int? get diamondsCount => throw _privateConstructorUsedError;
  @JsonKey(name: 'current_gold_rate')
  double? get currentGoldRate => throw _privateConstructorUsedError;
  @JsonKey(name: 'change_percent')
  double? get changePercent => throw _privateConstructorUsedError;
  @JsonKey(name: 'change_amount')
  double? get changeAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'calculated_change_amount')
  double? get calculatedChangeAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'gold_prices')
  GoldPrices? get goldPrices => throw _privateConstructorUsedError;

  /// Serializes this DashboardMetrics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardMetricsCopyWith<DashboardMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardMetricsCopyWith<$Res> {
  factory $DashboardMetricsCopyWith(
          DashboardMetrics value, $Res Function(DashboardMetrics) then) =
      _$DashboardMetricsCopyWithImpl<$Res, DashboardMetrics>;
  @useResult
  $Res call(
      {@JsonKey(name: 'show_offers_module') bool? showOffersModule,
      @JsonKey(name: 'buy_request_count') int? buyRequestCount,
      @JsonKey(name: 'unread_notification_count') int? unreadNotificationCount,
      @JsonKey(name: 'user_pending_offers_count') int? userPendingOffersCount,
      @JsonKey(name: 'pending_offers_count') int? pendingOffersCount,
      @JsonKey(name: 'diamonds_count') int? diamondsCount,
      @JsonKey(name: 'current_gold_rate') double? currentGoldRate,
      @JsonKey(name: 'change_percent') double? changePercent,
      @JsonKey(name: 'change_amount') double? changeAmount,
      @JsonKey(name: 'calculated_change_amount') double? calculatedChangeAmount,
      @JsonKey(name: 'gold_prices') GoldPrices? goldPrices});

  $GoldPricesCopyWith<$Res>? get goldPrices;
}

/// @nodoc
class _$DashboardMetricsCopyWithImpl<$Res, $Val extends DashboardMetrics>
    implements $DashboardMetricsCopyWith<$Res> {
  _$DashboardMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showOffersModule = freezed,
    Object? buyRequestCount = freezed,
    Object? unreadNotificationCount = freezed,
    Object? userPendingOffersCount = freezed,
    Object? pendingOffersCount = freezed,
    Object? diamondsCount = freezed,
    Object? currentGoldRate = freezed,
    Object? changePercent = freezed,
    Object? changeAmount = freezed,
    Object? calculatedChangeAmount = freezed,
    Object? goldPrices = freezed,
  }) {
    return _then(_value.copyWith(
      showOffersModule: freezed == showOffersModule
          ? _value.showOffersModule
          : showOffersModule // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyRequestCount: freezed == buyRequestCount
          ? _value.buyRequestCount
          : buyRequestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      unreadNotificationCount: freezed == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int?,
      userPendingOffersCount: freezed == userPendingOffersCount
          ? _value.userPendingOffersCount
          : userPendingOffersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pendingOffersCount: freezed == pendingOffersCount
          ? _value.pendingOffersCount
          : pendingOffersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      diamondsCount: freezed == diamondsCount
          ? _value.diamondsCount
          : diamondsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      currentGoldRate: freezed == currentGoldRate
          ? _value.currentGoldRate
          : currentGoldRate // ignore: cast_nullable_to_non_nullable
              as double?,
      changePercent: freezed == changePercent
          ? _value.changePercent
          : changePercent // ignore: cast_nullable_to_non_nullable
              as double?,
      changeAmount: freezed == changeAmount
          ? _value.changeAmount
          : changeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      calculatedChangeAmount: freezed == calculatedChangeAmount
          ? _value.calculatedChangeAmount
          : calculatedChangeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      goldPrices: freezed == goldPrices
          ? _value.goldPrices
          : goldPrices // ignore: cast_nullable_to_non_nullable
              as GoldPrices?,
    ) as $Val);
  }

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GoldPricesCopyWith<$Res>? get goldPrices {
    if (_value.goldPrices == null) {
      return null;
    }

    return $GoldPricesCopyWith<$Res>(_value.goldPrices!, (value) {
      return _then(_value.copyWith(goldPrices: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DashboardMetricsImplCopyWith<$Res>
    implements $DashboardMetricsCopyWith<$Res> {
  factory _$$DashboardMetricsImplCopyWith(_$DashboardMetricsImpl value,
          $Res Function(_$DashboardMetricsImpl) then) =
      __$$DashboardMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'show_offers_module') bool? showOffersModule,
      @JsonKey(name: 'buy_request_count') int? buyRequestCount,
      @JsonKey(name: 'unread_notification_count') int? unreadNotificationCount,
      @JsonKey(name: 'user_pending_offers_count') int? userPendingOffersCount,
      @JsonKey(name: 'pending_offers_count') int? pendingOffersCount,
      @JsonKey(name: 'diamonds_count') int? diamondsCount,
      @JsonKey(name: 'current_gold_rate') double? currentGoldRate,
      @JsonKey(name: 'change_percent') double? changePercent,
      @JsonKey(name: 'change_amount') double? changeAmount,
      @JsonKey(name: 'calculated_change_amount') double? calculatedChangeAmount,
      @JsonKey(name: 'gold_prices') GoldPrices? goldPrices});

  @override
  $GoldPricesCopyWith<$Res>? get goldPrices;
}

/// @nodoc
class __$$DashboardMetricsImplCopyWithImpl<$Res>
    extends _$DashboardMetricsCopyWithImpl<$Res, _$DashboardMetricsImpl>
    implements _$$DashboardMetricsImplCopyWith<$Res> {
  __$$DashboardMetricsImplCopyWithImpl(_$DashboardMetricsImpl _value,
      $Res Function(_$DashboardMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showOffersModule = freezed,
    Object? buyRequestCount = freezed,
    Object? unreadNotificationCount = freezed,
    Object? userPendingOffersCount = freezed,
    Object? pendingOffersCount = freezed,
    Object? diamondsCount = freezed,
    Object? currentGoldRate = freezed,
    Object? changePercent = freezed,
    Object? changeAmount = freezed,
    Object? calculatedChangeAmount = freezed,
    Object? goldPrices = freezed,
  }) {
    return _then(_$DashboardMetricsImpl(
      showOffersModule: freezed == showOffersModule
          ? _value.showOffersModule
          : showOffersModule // ignore: cast_nullable_to_non_nullable
              as bool?,
      buyRequestCount: freezed == buyRequestCount
          ? _value.buyRequestCount
          : buyRequestCount // ignore: cast_nullable_to_non_nullable
              as int?,
      unreadNotificationCount: freezed == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int?,
      userPendingOffersCount: freezed == userPendingOffersCount
          ? _value.userPendingOffersCount
          : userPendingOffersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      pendingOffersCount: freezed == pendingOffersCount
          ? _value.pendingOffersCount
          : pendingOffersCount // ignore: cast_nullable_to_non_nullable
              as int?,
      diamondsCount: freezed == diamondsCount
          ? _value.diamondsCount
          : diamondsCount // ignore: cast_nullable_to_non_nullable
              as int?,
      currentGoldRate: freezed == currentGoldRate
          ? _value.currentGoldRate
          : currentGoldRate // ignore: cast_nullable_to_non_nullable
              as double?,
      changePercent: freezed == changePercent
          ? _value.changePercent
          : changePercent // ignore: cast_nullable_to_non_nullable
              as double?,
      changeAmount: freezed == changeAmount
          ? _value.changeAmount
          : changeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      calculatedChangeAmount: freezed == calculatedChangeAmount
          ? _value.calculatedChangeAmount
          : calculatedChangeAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      goldPrices: freezed == goldPrices
          ? _value.goldPrices
          : goldPrices // ignore: cast_nullable_to_non_nullable
              as GoldPrices?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DashboardMetricsImpl implements _DashboardMetrics {
  const _$DashboardMetricsImpl(
      {@JsonKey(name: 'show_offers_module') this.showOffersModule,
      @JsonKey(name: 'buy_request_count') this.buyRequestCount,
      @JsonKey(name: 'unread_notification_count') this.unreadNotificationCount,
      @JsonKey(name: 'user_pending_offers_count') this.userPendingOffersCount,
      @JsonKey(name: 'pending_offers_count') this.pendingOffersCount,
      @JsonKey(name: 'diamonds_count') this.diamondsCount,
      @JsonKey(name: 'current_gold_rate') this.currentGoldRate,
      @JsonKey(name: 'change_percent') this.changePercent,
      @JsonKey(name: 'change_amount') this.changeAmount,
      @JsonKey(name: 'calculated_change_amount') this.calculatedChangeAmount,
      @JsonKey(name: 'gold_prices') this.goldPrices});

  factory _$DashboardMetricsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardMetricsImplFromJson(json);

  @override
  @JsonKey(name: 'show_offers_module')
  final bool? showOffersModule;
  @override
  @JsonKey(name: 'buy_request_count')
  final int? buyRequestCount;
  @override
  @JsonKey(name: 'unread_notification_count')
  final int? unreadNotificationCount;
  @override
  @JsonKey(name: 'user_pending_offers_count')
  final int? userPendingOffersCount;
  @override
  @JsonKey(name: 'pending_offers_count')
  final int? pendingOffersCount;
  @override
  @JsonKey(name: 'diamonds_count')
  final int? diamondsCount;
  @override
  @JsonKey(name: 'current_gold_rate')
  final double? currentGoldRate;
  @override
  @JsonKey(name: 'change_percent')
  final double? changePercent;
  @override
  @JsonKey(name: 'change_amount')
  final double? changeAmount;
  @override
  @JsonKey(name: 'calculated_change_amount')
  final double? calculatedChangeAmount;
  @override
  @JsonKey(name: 'gold_prices')
  final GoldPrices? goldPrices;

  @override
  String toString() {
    return 'DashboardMetrics(showOffersModule: $showOffersModule, buyRequestCount: $buyRequestCount, unreadNotificationCount: $unreadNotificationCount, userPendingOffersCount: $userPendingOffersCount, pendingOffersCount: $pendingOffersCount, diamondsCount: $diamondsCount, currentGoldRate: $currentGoldRate, changePercent: $changePercent, changeAmount: $changeAmount, calculatedChangeAmount: $calculatedChangeAmount, goldPrices: $goldPrices)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardMetricsImpl &&
            (identical(other.showOffersModule, showOffersModule) ||
                other.showOffersModule == showOffersModule) &&
            (identical(other.buyRequestCount, buyRequestCount) ||
                other.buyRequestCount == buyRequestCount) &&
            (identical(
                    other.unreadNotificationCount, unreadNotificationCount) ||
                other.unreadNotificationCount == unreadNotificationCount) &&
            (identical(other.userPendingOffersCount, userPendingOffersCount) ||
                other.userPendingOffersCount == userPendingOffersCount) &&
            (identical(other.pendingOffersCount, pendingOffersCount) ||
                other.pendingOffersCount == pendingOffersCount) &&
            (identical(other.diamondsCount, diamondsCount) ||
                other.diamondsCount == diamondsCount) &&
            (identical(other.currentGoldRate, currentGoldRate) ||
                other.currentGoldRate == currentGoldRate) &&
            (identical(other.changePercent, changePercent) ||
                other.changePercent == changePercent) &&
            (identical(other.changeAmount, changeAmount) ||
                other.changeAmount == changeAmount) &&
            (identical(other.calculatedChangeAmount, calculatedChangeAmount) ||
                other.calculatedChangeAmount == calculatedChangeAmount) &&
            (identical(other.goldPrices, goldPrices) ||
                other.goldPrices == goldPrices));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showOffersModule,
      buyRequestCount,
      unreadNotificationCount,
      userPendingOffersCount,
      pendingOffersCount,
      diamondsCount,
      currentGoldRate,
      changePercent,
      changeAmount,
      calculatedChangeAmount,
      goldPrices);

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardMetricsImplCopyWith<_$DashboardMetricsImpl> get copyWith =>
      __$$DashboardMetricsImplCopyWithImpl<_$DashboardMetricsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardMetricsImplToJson(
      this,
    );
  }
}

abstract class _DashboardMetrics implements DashboardMetrics {
  const factory _DashboardMetrics(
          {@JsonKey(name: 'show_offers_module') final bool? showOffersModule,
          @JsonKey(name: 'buy_request_count') final int? buyRequestCount,
          @JsonKey(name: 'unread_notification_count')
          final int? unreadNotificationCount,
          @JsonKey(name: 'user_pending_offers_count')
          final int? userPendingOffersCount,
          @JsonKey(name: 'pending_offers_count') final int? pendingOffersCount,
          @JsonKey(name: 'diamonds_count') final int? diamondsCount,
          @JsonKey(name: 'current_gold_rate') final double? currentGoldRate,
          @JsonKey(name: 'change_percent') final double? changePercent,
          @JsonKey(name: 'change_amount') final double? changeAmount,
          @JsonKey(name: 'calculated_change_amount')
          final double? calculatedChangeAmount,
          @JsonKey(name: 'gold_prices') final GoldPrices? goldPrices}) =
      _$DashboardMetricsImpl;

  factory _DashboardMetrics.fromJson(Map<String, dynamic> json) =
      _$DashboardMetricsImpl.fromJson;

  @override
  @JsonKey(name: 'show_offers_module')
  bool? get showOffersModule;
  @override
  @JsonKey(name: 'buy_request_count')
  int? get buyRequestCount;
  @override
  @JsonKey(name: 'unread_notification_count')
  int? get unreadNotificationCount;
  @override
  @JsonKey(name: 'user_pending_offers_count')
  int? get userPendingOffersCount;
  @override
  @JsonKey(name: 'pending_offers_count')
  int? get pendingOffersCount;
  @override
  @JsonKey(name: 'diamonds_count')
  int? get diamondsCount;
  @override
  @JsonKey(name: 'current_gold_rate')
  double? get currentGoldRate;
  @override
  @JsonKey(name: 'change_percent')
  double? get changePercent;
  @override
  @JsonKey(name: 'change_amount')
  double? get changeAmount;
  @override
  @JsonKey(name: 'calculated_change_amount')
  double? get calculatedChangeAmount;
  @override
  @JsonKey(name: 'gold_prices')
  GoldPrices? get goldPrices;

  /// Create a copy of DashboardMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardMetricsImplCopyWith<_$DashboardMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GoldPrices _$GoldPricesFromJson(Map<String, dynamic> json) {
  return _GoldPrices.fromJson(json);
}

/// @nodoc
mixin _$GoldPrices {
  @JsonKey(name: 'curr')
  String? get curr => throw _privateConstructorUsedError;
  @JsonKey(name: 'xauPrice')
  double? get xauPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'xagPrice')
  double? get xagPrice => throw _privateConstructorUsedError;
  @JsonKey(name: 'chgXau')
  double? get chgXau => throw _privateConstructorUsedError;
  @JsonKey(name: 'chgXag')
  double? get chgXag => throw _privateConstructorUsedError;
  @JsonKey(name: 'pcXau')
  double? get pcXau => throw _privateConstructorUsedError;
  @JsonKey(name: 'pcXag')
  double? get pcXag => throw _privateConstructorUsedError;
  @JsonKey(name: 'xauClose')
  double? get xauClose => throw _privateConstructorUsedError;
  @JsonKey(name: 'xagClose')
  double? get xagClose => throw _privateConstructorUsedError;

  /// Serializes this GoldPrices to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GoldPrices
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GoldPricesCopyWith<GoldPrices> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GoldPricesCopyWith<$Res> {
  factory $GoldPricesCopyWith(
          GoldPrices value, $Res Function(GoldPrices) then) =
      _$GoldPricesCopyWithImpl<$Res, GoldPrices>;
  @useResult
  $Res call(
      {@JsonKey(name: 'curr') String? curr,
      @JsonKey(name: 'xauPrice') double? xauPrice,
      @JsonKey(name: 'xagPrice') double? xagPrice,
      @JsonKey(name: 'chgXau') double? chgXau,
      @JsonKey(name: 'chgXag') double? chgXag,
      @JsonKey(name: 'pcXau') double? pcXau,
      @JsonKey(name: 'pcXag') double? pcXag,
      @JsonKey(name: 'xauClose') double? xauClose,
      @JsonKey(name: 'xagClose') double? xagClose});
}

/// @nodoc
class _$GoldPricesCopyWithImpl<$Res, $Val extends GoldPrices>
    implements $GoldPricesCopyWith<$Res> {
  _$GoldPricesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GoldPrices
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? curr = freezed,
    Object? xauPrice = freezed,
    Object? xagPrice = freezed,
    Object? chgXau = freezed,
    Object? chgXag = freezed,
    Object? pcXau = freezed,
    Object? pcXag = freezed,
    Object? xauClose = freezed,
    Object? xagClose = freezed,
  }) {
    return _then(_value.copyWith(
      curr: freezed == curr
          ? _value.curr
          : curr // ignore: cast_nullable_to_non_nullable
              as String?,
      xauPrice: freezed == xauPrice
          ? _value.xauPrice
          : xauPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      xagPrice: freezed == xagPrice
          ? _value.xagPrice
          : xagPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      chgXau: freezed == chgXau
          ? _value.chgXau
          : chgXau // ignore: cast_nullable_to_non_nullable
              as double?,
      chgXag: freezed == chgXag
          ? _value.chgXag
          : chgXag // ignore: cast_nullable_to_non_nullable
              as double?,
      pcXau: freezed == pcXau
          ? _value.pcXau
          : pcXau // ignore: cast_nullable_to_non_nullable
              as double?,
      pcXag: freezed == pcXag
          ? _value.pcXag
          : pcXag // ignore: cast_nullable_to_non_nullable
              as double?,
      xauClose: freezed == xauClose
          ? _value.xauClose
          : xauClose // ignore: cast_nullable_to_non_nullable
              as double?,
      xagClose: freezed == xagClose
          ? _value.xagClose
          : xagClose // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GoldPricesImplCopyWith<$Res>
    implements $GoldPricesCopyWith<$Res> {
  factory _$$GoldPricesImplCopyWith(
          _$GoldPricesImpl value, $Res Function(_$GoldPricesImpl) then) =
      __$$GoldPricesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'curr') String? curr,
      @JsonKey(name: 'xauPrice') double? xauPrice,
      @JsonKey(name: 'xagPrice') double? xagPrice,
      @JsonKey(name: 'chgXau') double? chgXau,
      @JsonKey(name: 'chgXag') double? chgXag,
      @JsonKey(name: 'pcXau') double? pcXau,
      @JsonKey(name: 'pcXag') double? pcXag,
      @JsonKey(name: 'xauClose') double? xauClose,
      @JsonKey(name: 'xagClose') double? xagClose});
}

/// @nodoc
class __$$GoldPricesImplCopyWithImpl<$Res>
    extends _$GoldPricesCopyWithImpl<$Res, _$GoldPricesImpl>
    implements _$$GoldPricesImplCopyWith<$Res> {
  __$$GoldPricesImplCopyWithImpl(
      _$GoldPricesImpl _value, $Res Function(_$GoldPricesImpl) _then)
      : super(_value, _then);

  /// Create a copy of GoldPrices
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? curr = freezed,
    Object? xauPrice = freezed,
    Object? xagPrice = freezed,
    Object? chgXau = freezed,
    Object? chgXag = freezed,
    Object? pcXau = freezed,
    Object? pcXag = freezed,
    Object? xauClose = freezed,
    Object? xagClose = freezed,
  }) {
    return _then(_$GoldPricesImpl(
      curr: freezed == curr
          ? _value.curr
          : curr // ignore: cast_nullable_to_non_nullable
              as String?,
      xauPrice: freezed == xauPrice
          ? _value.xauPrice
          : xauPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      xagPrice: freezed == xagPrice
          ? _value.xagPrice
          : xagPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      chgXau: freezed == chgXau
          ? _value.chgXau
          : chgXau // ignore: cast_nullable_to_non_nullable
              as double?,
      chgXag: freezed == chgXag
          ? _value.chgXag
          : chgXag // ignore: cast_nullable_to_non_nullable
              as double?,
      pcXau: freezed == pcXau
          ? _value.pcXau
          : pcXau // ignore: cast_nullable_to_non_nullable
              as double?,
      pcXag: freezed == pcXag
          ? _value.pcXag
          : pcXag // ignore: cast_nullable_to_non_nullable
              as double?,
      xauClose: freezed == xauClose
          ? _value.xauClose
          : xauClose // ignore: cast_nullable_to_non_nullable
              as double?,
      xagClose: freezed == xagClose
          ? _value.xagClose
          : xagClose // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GoldPricesImpl implements _GoldPrices {
  const _$GoldPricesImpl(
      {@JsonKey(name: 'curr') this.curr,
      @JsonKey(name: 'xauPrice') this.xauPrice,
      @JsonKey(name: 'xagPrice') this.xagPrice,
      @JsonKey(name: 'chgXau') this.chgXau,
      @JsonKey(name: 'chgXag') this.chgXag,
      @JsonKey(name: 'pcXau') this.pcXau,
      @JsonKey(name: 'pcXag') this.pcXag,
      @JsonKey(name: 'xauClose') this.xauClose,
      @JsonKey(name: 'xagClose') this.xagClose});

  factory _$GoldPricesImpl.fromJson(Map<String, dynamic> json) =>
      _$$GoldPricesImplFromJson(json);

  @override
  @JsonKey(name: 'curr')
  final String? curr;
  @override
  @JsonKey(name: 'xauPrice')
  final double? xauPrice;
  @override
  @JsonKey(name: 'xagPrice')
  final double? xagPrice;
  @override
  @JsonKey(name: 'chgXau')
  final double? chgXau;
  @override
  @JsonKey(name: 'chgXag')
  final double? chgXag;
  @override
  @JsonKey(name: 'pcXau')
  final double? pcXau;
  @override
  @JsonKey(name: 'pcXag')
  final double? pcXag;
  @override
  @JsonKey(name: 'xauClose')
  final double? xauClose;
  @override
  @JsonKey(name: 'xagClose')
  final double? xagClose;

  @override
  String toString() {
    return 'GoldPrices(curr: $curr, xauPrice: $xauPrice, xagPrice: $xagPrice, chgXau: $chgXau, chgXag: $chgXag, pcXau: $pcXau, pcXag: $pcXag, xauClose: $xauClose, xagClose: $xagClose)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GoldPricesImpl &&
            (identical(other.curr, curr) || other.curr == curr) &&
            (identical(other.xauPrice, xauPrice) ||
                other.xauPrice == xauPrice) &&
            (identical(other.xagPrice, xagPrice) ||
                other.xagPrice == xagPrice) &&
            (identical(other.chgXau, chgXau) || other.chgXau == chgXau) &&
            (identical(other.chgXag, chgXag) || other.chgXag == chgXag) &&
            (identical(other.pcXau, pcXau) || other.pcXau == pcXau) &&
            (identical(other.pcXag, pcXag) || other.pcXag == pcXag) &&
            (identical(other.xauClose, xauClose) ||
                other.xauClose == xauClose) &&
            (identical(other.xagClose, xagClose) ||
                other.xagClose == xagClose));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, curr, xauPrice, xagPrice, chgXau,
      chgXag, pcXau, pcXag, xauClose, xagClose);

  /// Create a copy of GoldPrices
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GoldPricesImplCopyWith<_$GoldPricesImpl> get copyWith =>
      __$$GoldPricesImplCopyWithImpl<_$GoldPricesImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GoldPricesImplToJson(
      this,
    );
  }
}

abstract class _GoldPrices implements GoldPrices {
  const factory _GoldPrices(
      {@JsonKey(name: 'curr') final String? curr,
      @JsonKey(name: 'xauPrice') final double? xauPrice,
      @JsonKey(name: 'xagPrice') final double? xagPrice,
      @JsonKey(name: 'chgXau') final double? chgXau,
      @JsonKey(name: 'chgXag') final double? chgXag,
      @JsonKey(name: 'pcXau') final double? pcXau,
      @JsonKey(name: 'pcXag') final double? pcXag,
      @JsonKey(name: 'xauClose') final double? xauClose,
      @JsonKey(name: 'xagClose') final double? xagClose}) = _$GoldPricesImpl;

  factory _GoldPrices.fromJson(Map<String, dynamic> json) =
      _$GoldPricesImpl.fromJson;

  @override
  @JsonKey(name: 'curr')
  String? get curr;
  @override
  @JsonKey(name: 'xauPrice')
  double? get xauPrice;
  @override
  @JsonKey(name: 'xagPrice')
  double? get xagPrice;
  @override
  @JsonKey(name: 'chgXau')
  double? get chgXau;
  @override
  @JsonKey(name: 'chgXag')
  double? get chgXag;
  @override
  @JsonKey(name: 'pcXau')
  double? get pcXau;
  @override
  @JsonKey(name: 'pcXag')
  double? get pcXag;
  @override
  @JsonKey(name: 'xauClose')
  double? get xauClose;
  @override
  @JsonKey(name: 'xagClose')
  double? get xagClose;

  /// Create a copy of GoldPrices
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GoldPricesImplCopyWith<_$GoldPricesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
