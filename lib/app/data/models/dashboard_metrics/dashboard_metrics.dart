import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_metrics.freezed.dart';
part 'dashboard_metrics.g.dart';

@freezed
class DashboardMetrics with _$DashboardMetrics {
  const factory DashboardMetrics({
    @J<PERSON><PERSON><PERSON>(name: 'buy_request_count') int? buyRequestCount,
    @J<PERSON><PERSON><PERSON>(name: 'unread_notification_count') int? unreadNotificationCount,
    @Json<PERSON>ey(name: 'user_pending_offers_count') int? userPendingOffersCount,
    @Json<PERSON><PERSON>(name: 'pending_offers_count') int? pendingOffersCount,
    @J<PERSON><PERSON><PERSON>(name: 'diamonds_count') int? diamondsCount,
    @Json<PERSON>ey(name: 'current_gold_rate') double? currentGoldRate,
    @Json<PERSON><PERSON>(name: 'change_percent') double? changePercent,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'change_amount') double? changeAmount,
    @<PERSON>son<PERSON>ey(name: 'calculated_change_amount') double? calculatedChangeAmount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'gold_prices') GoldPrices? goldPrices,
  }) = _DashboardMetrics;

  factory DashboardMetrics.fromJson(Map<String, dynamic> json) =>
      _$DashboardMetricsFromJson(json);
}

@freezed
class GoldPrices with _$GoldPrices {
  const factory GoldPrices({
    @JsonKey(name: 'curr') String? curr,
    @JsonKey(name: 'xauPrice') double? xauPrice,
    @JsonKey(name: 'xagPrice') double? xagPrice,
    @JsonKey(name: 'chgXau') double? chgXau,
    @JsonKey(name: 'chgXag') double? chgXag,
    @JsonKey(name: 'pcXau') double? pcXau,
    @JsonKey(name: 'pcXag') double? pcXag,
    @JsonKey(name: 'xauClose') double? xauClose,
    @JsonKey(name: 'xagClose') double? xagClose,
  }) = _GoldPrices;

  factory GoldPrices.fromJson(Map<String, dynamic> json) =>
      _$GoldPricesFromJson(json);
}
