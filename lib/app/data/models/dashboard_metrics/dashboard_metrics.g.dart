// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_metrics.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DashboardMetricsImpl _$$DashboardMetricsImplFromJson(
        Map<String, dynamic> json) =>
    _$DashboardMetricsImpl(
      showOffersModule: json['show_offers_module'] as bool?,
      buyRequestCount: (json['buy_request_count'] as num?)?.toInt(),
      unreadNotificationCount:
          (json['unread_notification_count'] as num?)?.toInt(),
      userPendingOffersCount:
          (json['user_pending_offers_count'] as num?)?.toInt(),
      pendingOffersCount: (json['pending_offers_count'] as num?)?.toInt(),
      diamondsCount: (json['diamonds_count'] as num?)?.toInt(),
      currentGoldRate: (json['current_gold_rate'] as num?)?.toDouble(),
      changePercent: (json['change_percent'] as num?)?.toDouble(),
      changeAmount: (json['change_amount'] as num?)?.toDouble(),
      calculatedChangeAmount:
          (json['calculated_change_amount'] as num?)?.toDouble(),
      goldPrices: json['gold_prices'] == null
          ? null
          : GoldPrices.fromJson(json['gold_prices'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$DashboardMetricsImplToJson(
        _$DashboardMetricsImpl instance) =>
    <String, dynamic>{
      'show_offers_module': instance.showOffersModule,
      'buy_request_count': instance.buyRequestCount,
      'unread_notification_count': instance.unreadNotificationCount,
      'user_pending_offers_count': instance.userPendingOffersCount,
      'pending_offers_count': instance.pendingOffersCount,
      'diamonds_count': instance.diamondsCount,
      'current_gold_rate': instance.currentGoldRate,
      'change_percent': instance.changePercent,
      'change_amount': instance.changeAmount,
      'calculated_change_amount': instance.calculatedChangeAmount,
      'gold_prices': instance.goldPrices,
    };

_$GoldPricesImpl _$$GoldPricesImplFromJson(Map<String, dynamic> json) =>
    _$GoldPricesImpl(
      curr: json['curr'] as String?,
      xauPrice: (json['xauPrice'] as num?)?.toDouble(),
      xagPrice: (json['xagPrice'] as num?)?.toDouble(),
      chgXau: (json['chgXau'] as num?)?.toDouble(),
      chgXag: (json['chgXag'] as num?)?.toDouble(),
      pcXau: (json['pcXau'] as num?)?.toDouble(),
      pcXag: (json['pcXag'] as num?)?.toDouble(),
      xauClose: (json['xauClose'] as num?)?.toDouble(),
      xagClose: (json['xagClose'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$GoldPricesImplToJson(_$GoldPricesImpl instance) =>
    <String, dynamic>{
      'curr': instance.curr,
      'xauPrice': instance.xauPrice,
      'xagPrice': instance.xagPrice,
      'chgXau': instance.chgXau,
      'chgXag': instance.chgXag,
      'pcXau': instance.pcXau,
      'pcXag': instance.pcXag,
      'xauClose': instance.xauClose,
      'xagClose': instance.xagClose,
    };
