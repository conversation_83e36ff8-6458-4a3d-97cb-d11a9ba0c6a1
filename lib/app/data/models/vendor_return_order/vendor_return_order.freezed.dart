// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vendor_return_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VendorReturnOrder _$VendorReturnOrderFromJson(Map<String, dynamic> json) {
  return _VendorReturnOrder.fromJson(json);
}

/// @nodoc
mixin _$VendorReturnOrder {
  @JsonKey(name: 'id')
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'order_id')
  String? get orderId => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_id')
  String? get vendorId => throw _privateConstructorUsedError;
  @JsonKey(name: 'admin_id')
  dynamic get adminId => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock_id')
  String? get stockId => throw _privateConstructorUsedError;
  @JsonKey(name: 'status')
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'refund_amount')
  double? get refundAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor_refund_amount')
  double? get vendorRefundAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'received_amount')
  int? get receivedAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_accepted')
  bool? get isAccepted => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken => throw _privateConstructorUsedError;
  @JsonKey(name: 'reason')
  String? get reason => throw _privateConstructorUsedError;
  @JsonKey(name: 'reject_reason')
  dynamic get rejectReason => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  bool? get isActive => throw _privateConstructorUsedError;
  @JsonKey(name: '_deleted')
  bool? get deleted => throw _privateConstructorUsedError;
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'order')
  UserOrder? get order => throw _privateConstructorUsedError;
  @JsonKey(name: 'user')
  UserEntity? get user => throw _privateConstructorUsedError;
  @JsonKey(name: 'vendor')
  AdminUserEntity? get vendor => throw _privateConstructorUsedError;
  @JsonKey(name: 'stock')
  DiamondEntity? get stock => throw _privateConstructorUsedError;

  /// Serializes this VendorReturnOrder to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VendorReturnOrderCopyWith<VendorReturnOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VendorReturnOrderCopyWith<$Res> {
  factory $VendorReturnOrderCopyWith(
          VendorReturnOrder value, $Res Function(VendorReturnOrder) then) =
      _$VendorReturnOrderCopyWithImpl<$Res, VendorReturnOrder>;
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'admin_id') dynamic adminId,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'refund_amount') double? refundAmount,
      @JsonKey(name: 'vendor_refund_amount') double? vendorRefundAmount,
      @JsonKey(name: 'received_amount') int? receivedAmount,
      @JsonKey(name: 'is_accepted') bool? isAccepted,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'reject_reason') dynamic rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'user') UserEntity? user,
      @JsonKey(name: 'vendor') AdminUserEntity? vendor,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  $UserOrderCopyWith<$Res>? get order;
  $UserEntityCopyWith<$Res>? get user;
}

/// @nodoc
class _$VendorReturnOrderCopyWithImpl<$Res, $Val extends VendorReturnOrder>
    implements $VendorReturnOrderCopyWith<$Res> {
  _$VendorReturnOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? adminId = freezed,
    Object? userId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? refundAmount = freezed,
    Object? vendorRefundAmount = freezed,
    Object? receivedAmount = freezed,
    Object? isAccepted = freezed,
    Object? isActionTaken = freezed,
    Object? reason = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? order = freezed,
    Object? user = freezed,
    Object? vendor = freezed,
    Object? stock = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      vendorRefundAmount: freezed == vendorRefundAmount
          ? _value.vendorRefundAmount
          : vendorRefundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivedAmount: freezed == receivedAmount
          ? _value.receivedAmount
          : receivedAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      isAccepted: freezed == isAccepted
          ? _value.isAccepted
          : isAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserEntity?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as AdminUserEntity?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ) as $Val);
  }

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserOrderCopyWith<$Res>? get order {
    if (_value.order == null) {
      return null;
    }

    return $UserOrderCopyWith<$Res>(_value.order!, (value) {
      return _then(_value.copyWith(order: value) as $Val);
    });
  }

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserEntityCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserEntityCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VendorReturnOrderImplCopyWith<$Res>
    implements $VendorReturnOrderCopyWith<$Res> {
  factory _$$VendorReturnOrderImplCopyWith(_$VendorReturnOrderImpl value,
          $Res Function(_$VendorReturnOrderImpl) then) =
      __$$VendorReturnOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'id') String? id,
      @JsonKey(name: 'order_id') String? orderId,
      @JsonKey(name: 'vendor_id') String? vendorId,
      @JsonKey(name: 'admin_id') dynamic adminId,
      @JsonKey(name: 'user_id') String? userId,
      @JsonKey(name: 'stock_id') String? stockId,
      @JsonKey(name: 'status') String? status,
      @JsonKey(name: 'refund_amount') double? refundAmount,
      @JsonKey(name: 'vendor_refund_amount') double? vendorRefundAmount,
      @JsonKey(name: 'received_amount') int? receivedAmount,
      @JsonKey(name: 'is_accepted') bool? isAccepted,
      @JsonKey(name: 'is_action_taken') bool? isActionTaken,
      @JsonKey(name: 'reason') String? reason,
      @JsonKey(name: 'reject_reason') dynamic rejectReason,
      @JsonKey(name: 'is_active') bool? isActive,
      @JsonKey(name: '_deleted') bool? deleted,
      @JsonKey(name: 'createdAt') DateTime? createdAt,
      @JsonKey(name: 'updatedAt') DateTime? updatedAt,
      @JsonKey(name: 'order') UserOrder? order,
      @JsonKey(name: 'user') UserEntity? user,
      @JsonKey(name: 'vendor') AdminUserEntity? vendor,
      @JsonKey(name: 'stock') DiamondEntity? stock});

  @override
  $UserOrderCopyWith<$Res>? get order;
  @override
  $UserEntityCopyWith<$Res>? get user;
}

/// @nodoc
class __$$VendorReturnOrderImplCopyWithImpl<$Res>
    extends _$VendorReturnOrderCopyWithImpl<$Res, _$VendorReturnOrderImpl>
    implements _$$VendorReturnOrderImplCopyWith<$Res> {
  __$$VendorReturnOrderImplCopyWithImpl(_$VendorReturnOrderImpl _value,
      $Res Function(_$VendorReturnOrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? orderId = freezed,
    Object? vendorId = freezed,
    Object? adminId = freezed,
    Object? userId = freezed,
    Object? stockId = freezed,
    Object? status = freezed,
    Object? refundAmount = freezed,
    Object? vendorRefundAmount = freezed,
    Object? receivedAmount = freezed,
    Object? isAccepted = freezed,
    Object? isActionTaken = freezed,
    Object? reason = freezed,
    Object? rejectReason = freezed,
    Object? isActive = freezed,
    Object? deleted = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? order = freezed,
    Object? user = freezed,
    Object? vendor = freezed,
    Object? stock = freezed,
  }) {
    return _then(_$VendorReturnOrderImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      orderId: freezed == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String?,
      vendorId: freezed == vendorId
          ? _value.vendorId
          : vendorId // ignore: cast_nullable_to_non_nullable
              as String?,
      adminId: freezed == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      stockId: freezed == stockId
          ? _value.stockId
          : stockId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      refundAmount: freezed == refundAmount
          ? _value.refundAmount
          : refundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      vendorRefundAmount: freezed == vendorRefundAmount
          ? _value.vendorRefundAmount
          : vendorRefundAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      receivedAmount: freezed == receivedAmount
          ? _value.receivedAmount
          : receivedAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      isAccepted: freezed == isAccepted
          ? _value.isAccepted
          : isAccepted // ignore: cast_nullable_to_non_nullable
              as bool?,
      isActionTaken: freezed == isActionTaken
          ? _value.isActionTaken
          : isActionTaken // ignore: cast_nullable_to_non_nullable
              as bool?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      rejectReason: freezed == rejectReason
          ? _value.rejectReason
          : rejectReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
      deleted: freezed == deleted
          ? _value.deleted
          : deleted // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      order: freezed == order
          ? _value.order
          : order // ignore: cast_nullable_to_non_nullable
              as UserOrder?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserEntity?,
      vendor: freezed == vendor
          ? _value.vendor
          : vendor // ignore: cast_nullable_to_non_nullable
              as AdminUserEntity?,
      stock: freezed == stock
          ? _value.stock
          : stock // ignore: cast_nullable_to_non_nullable
              as DiamondEntity?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VendorReturnOrderImpl implements _VendorReturnOrder {
  const _$VendorReturnOrderImpl(
      {@JsonKey(name: 'id') this.id,
      @JsonKey(name: 'order_id') this.orderId,
      @JsonKey(name: 'vendor_id') this.vendorId,
      @JsonKey(name: 'admin_id') this.adminId,
      @JsonKey(name: 'user_id') this.userId,
      @JsonKey(name: 'stock_id') this.stockId,
      @JsonKey(name: 'status') this.status,
      @JsonKey(name: 'refund_amount') this.refundAmount,
      @JsonKey(name: 'vendor_refund_amount') this.vendorRefundAmount,
      @JsonKey(name: 'received_amount') this.receivedAmount,
      @JsonKey(name: 'is_accepted') this.isAccepted,
      @JsonKey(name: 'is_action_taken') this.isActionTaken,
      @JsonKey(name: 'reason') this.reason,
      @JsonKey(name: 'reject_reason') this.rejectReason,
      @JsonKey(name: 'is_active') this.isActive,
      @JsonKey(name: '_deleted') this.deleted,
      @JsonKey(name: 'createdAt') this.createdAt,
      @JsonKey(name: 'updatedAt') this.updatedAt,
      @JsonKey(name: 'order') this.order,
      @JsonKey(name: 'user') this.user,
      @JsonKey(name: 'vendor') this.vendor,
      @JsonKey(name: 'stock') this.stock});

  factory _$VendorReturnOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$VendorReturnOrderImplFromJson(json);

  @override
  @JsonKey(name: 'id')
  final String? id;
  @override
  @JsonKey(name: 'order_id')
  final String? orderId;
  @override
  @JsonKey(name: 'vendor_id')
  final String? vendorId;
  @override
  @JsonKey(name: 'admin_id')
  final dynamic adminId;
  @override
  @JsonKey(name: 'user_id')
  final String? userId;
  @override
  @JsonKey(name: 'stock_id')
  final String? stockId;
  @override
  @JsonKey(name: 'status')
  final String? status;
  @override
  @JsonKey(name: 'refund_amount')
  final double? refundAmount;
  @override
  @JsonKey(name: 'vendor_refund_amount')
  final double? vendorRefundAmount;
  @override
  @JsonKey(name: 'received_amount')
  final int? receivedAmount;
  @override
  @JsonKey(name: 'is_accepted')
  final bool? isAccepted;
  @override
  @JsonKey(name: 'is_action_taken')
  final bool? isActionTaken;
  @override
  @JsonKey(name: 'reason')
  final String? reason;
  @override
  @JsonKey(name: 'reject_reason')
  final dynamic rejectReason;
  @override
  @JsonKey(name: 'is_active')
  final bool? isActive;
  @override
  @JsonKey(name: '_deleted')
  final bool? deleted;
  @override
  @JsonKey(name: 'createdAt')
  final DateTime? createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'order')
  final UserOrder? order;
  @override
  @JsonKey(name: 'user')
  final UserEntity? user;
  @override
  @JsonKey(name: 'vendor')
  final AdminUserEntity? vendor;
  @override
  @JsonKey(name: 'stock')
  final DiamondEntity? stock;

  @override
  String toString() {
    return 'VendorReturnOrder(id: $id, orderId: $orderId, vendorId: $vendorId, adminId: $adminId, userId: $userId, stockId: $stockId, status: $status, refundAmount: $refundAmount, vendorRefundAmount: $vendorRefundAmount, receivedAmount: $receivedAmount, isAccepted: $isAccepted, isActionTaken: $isActionTaken, reason: $reason, rejectReason: $rejectReason, isActive: $isActive, deleted: $deleted, createdAt: $createdAt, updatedAt: $updatedAt, order: $order, user: $user, vendor: $vendor, stock: $stock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VendorReturnOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.vendorId, vendorId) ||
                other.vendorId == vendorId) &&
            const DeepCollectionEquality().equals(other.adminId, adminId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.stockId, stockId) || other.stockId == stockId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.refundAmount, refundAmount) ||
                other.refundAmount == refundAmount) &&
            (identical(other.vendorRefundAmount, vendorRefundAmount) ||
                other.vendorRefundAmount == vendorRefundAmount) &&
            (identical(other.receivedAmount, receivedAmount) ||
                other.receivedAmount == receivedAmount) &&
            (identical(other.isAccepted, isAccepted) ||
                other.isAccepted == isAccepted) &&
            (identical(other.isActionTaken, isActionTaken) ||
                other.isActionTaken == isActionTaken) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            const DeepCollectionEquality()
                .equals(other.rejectReason, rejectReason) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.deleted, deleted) || other.deleted == deleted) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.order, order) || other.order == order) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.vendor, vendor) || other.vendor == vendor) &&
            (identical(other.stock, stock) || other.stock == stock));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        orderId,
        vendorId,
        const DeepCollectionEquality().hash(adminId),
        userId,
        stockId,
        status,
        refundAmount,
        vendorRefundAmount,
        receivedAmount,
        isAccepted,
        isActionTaken,
        reason,
        const DeepCollectionEquality().hash(rejectReason),
        isActive,
        deleted,
        createdAt,
        updatedAt,
        order,
        user,
        vendor,
        stock
      ]);

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VendorReturnOrderImplCopyWith<_$VendorReturnOrderImpl> get copyWith =>
      __$$VendorReturnOrderImplCopyWithImpl<_$VendorReturnOrderImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VendorReturnOrderImplToJson(
      this,
    );
  }
}

abstract class _VendorReturnOrder implements VendorReturnOrder {
  const factory _VendorReturnOrder(
      {@JsonKey(name: 'id') final String? id,
      @JsonKey(name: 'order_id') final String? orderId,
      @JsonKey(name: 'vendor_id') final String? vendorId,
      @JsonKey(name: 'admin_id') final dynamic adminId,
      @JsonKey(name: 'user_id') final String? userId,
      @JsonKey(name: 'stock_id') final String? stockId,
      @JsonKey(name: 'status') final String? status,
      @JsonKey(name: 'refund_amount') final double? refundAmount,
      @JsonKey(name: 'vendor_refund_amount') final double? vendorRefundAmount,
      @JsonKey(name: 'received_amount') final int? receivedAmount,
      @JsonKey(name: 'is_accepted') final bool? isAccepted,
      @JsonKey(name: 'is_action_taken') final bool? isActionTaken,
      @JsonKey(name: 'reason') final String? reason,
      @JsonKey(name: 'reject_reason') final dynamic rejectReason,
      @JsonKey(name: 'is_active') final bool? isActive,
      @JsonKey(name: '_deleted') final bool? deleted,
      @JsonKey(name: 'createdAt') final DateTime? createdAt,
      @JsonKey(name: 'updatedAt') final DateTime? updatedAt,
      @JsonKey(name: 'order') final UserOrder? order,
      @JsonKey(name: 'user') final UserEntity? user,
      @JsonKey(name: 'vendor') final AdminUserEntity? vendor,
      @JsonKey(name: 'stock')
      final DiamondEntity? stock}) = _$VendorReturnOrderImpl;

  factory _VendorReturnOrder.fromJson(Map<String, dynamic> json) =
      _$VendorReturnOrderImpl.fromJson;

  @override
  @JsonKey(name: 'id')
  String? get id;
  @override
  @JsonKey(name: 'order_id')
  String? get orderId;
  @override
  @JsonKey(name: 'vendor_id')
  String? get vendorId;
  @override
  @JsonKey(name: 'admin_id')
  dynamic get adminId;
  @override
  @JsonKey(name: 'user_id')
  String? get userId;
  @override
  @JsonKey(name: 'stock_id')
  String? get stockId;
  @override
  @JsonKey(name: 'status')
  String? get status;
  @override
  @JsonKey(name: 'refund_amount')
  double? get refundAmount;
  @override
  @JsonKey(name: 'vendor_refund_amount')
  double? get vendorRefundAmount;
  @override
  @JsonKey(name: 'received_amount')
  int? get receivedAmount;
  @override
  @JsonKey(name: 'is_accepted')
  bool? get isAccepted;
  @override
  @JsonKey(name: 'is_action_taken')
  bool? get isActionTaken;
  @override
  @JsonKey(name: 'reason')
  String? get reason;
  @override
  @JsonKey(name: 'reject_reason')
  dynamic get rejectReason;
  @override
  @JsonKey(name: 'is_active')
  bool? get isActive;
  @override
  @JsonKey(name: '_deleted')
  bool? get deleted;
  @override
  @JsonKey(name: 'createdAt')
  DateTime? get createdAt;
  @override
  @JsonKey(name: 'updatedAt')
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'order')
  UserOrder? get order;
  @override
  @JsonKey(name: 'user')
  UserEntity? get user;
  @override
  @JsonKey(name: 'vendor')
  AdminUserEntity? get vendor;
  @override
  @JsonKey(name: 'stock')
  DiamondEntity? get stock;

  /// Create a copy of VendorReturnOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VendorReturnOrderImplCopyWith<_$VendorReturnOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
