// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vendor_return_order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VendorReturnOrderImpl _$$VendorReturnOrderImplFromJson(
        Map<String, dynamic> json) =>
    _$VendorReturnOrderImpl(
      id: json['id'] as String?,
      orderId: json['order_id'] as String?,
      vendorId: json['vendor_id'] as String?,
      adminId: json['admin_id'],
      userId: json['user_id'] as String?,
      stockId: json['stock_id'] as String?,
      status: json['status'] as String?,
      refundAmount: (json['refund_amount'] as num?)?.toDouble(),
      vendorRefundAmount: (json['vendor_refund_amount'] as num?)?.toDouble(),
      receivedAmount: (json['received_amount'] as num?)?.toInt(),
      isAccepted: json['is_accepted'] as bool?,
      isActionTaken: json['is_action_taken'] as bool?,
      reason: json['reason'] as String?,
      rejectReason: json['reject_reason'],
      isActive: json['is_active'] as bool?,
      deleted: json['_deleted'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      order: json['order'] == null
          ? null
          : UserOrder.fromJson(json['order'] as Map<String, dynamic>),
      user: json['user'] == null
          ? null
          : UserEntity.fromJson(json['user'] as Map<String, dynamic>),
      vendor: json['vendor'] == null
          ? null
          : AdminUserEntity.fromJson(json['vendor'] as Map<String, dynamic>),
      stock: json['stock'] == null
          ? null
          : DiamondEntity.fromJson(json['stock'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$VendorReturnOrderImplToJson(
        _$VendorReturnOrderImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'order_id': instance.orderId,
      'vendor_id': instance.vendorId,
      'admin_id': instance.adminId,
      'user_id': instance.userId,
      'stock_id': instance.stockId,
      'status': instance.status,
      'refund_amount': instance.refundAmount,
      'vendor_refund_amount': instance.vendorRefundAmount,
      'received_amount': instance.receivedAmount,
      'is_accepted': instance.isAccepted,
      'is_action_taken': instance.isActionTaken,
      'reason': instance.reason,
      'reject_reason': instance.rejectReason,
      'is_active': instance.isActive,
      '_deleted': instance.deleted,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'order': instance.order,
      'user': instance.user,
      'vendor': instance.vendor,
      'stock': instance.stock,
    };
