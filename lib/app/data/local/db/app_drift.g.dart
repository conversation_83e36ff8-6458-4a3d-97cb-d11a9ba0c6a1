// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_drift.dart';

// ignore_for_file: type=lint
class $CartItemsTable extends CartItems
    with TableInfo<$CartItemsTable, CartItem> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CartItemsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  @override
  late final GeneratedColumnWithTypeConverter<CartItemType, String> type =
      GeneratedColumn<String>('type', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<CartItemType>($CartItemsTable.$convertertype);
  @override
  late final GeneratedColumnWithTypeConverter<DiamondEntity?, String> diamond =
      GeneratedColumn<String>('diamond', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<DiamondEntity?>($CartItemsTable.$converterdiamondn);
  static const VerificationMeta _diamondIdMeta =
      const VerificationMeta('diamondId');
  @override
  late final GeneratedColumn<String> diamondId = GeneratedColumn<String>(
      'diamond_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<Jewellery?, String> jewellery =
      GeneratedColumn<String>('jewellery', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<Jewellery?>($CartItemsTable.$converterjewelleryn);
  static const VerificationMeta _jewelleryIdMeta =
      const VerificationMeta('jewelleryId');
  @override
  late final GeneratedColumn<String> jewelleryId = GeneratedColumn<String>(
      'jewellery_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _meleeIdMeta =
      const VerificationMeta('meleeId');
  @override
  late final GeneratedColumn<String> meleeId = GeneratedColumn<String>(
      'melee_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<MeleeEntity?, String> melee =
      GeneratedColumn<String>('melee', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<MeleeEntity?>($CartItemsTable.$convertermeleen);
  static const VerificationMeta _addedOnMeta =
      const VerificationMeta('addedOn');
  @override
  late final GeneratedColumn<DateTime> addedOn = GeneratedColumn<DateTime>(
      'added_on', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _availableMeta =
      const VerificationMeta('available');
  @override
  late final GeneratedColumn<bool> available = GeneratedColumn<bool>(
      'available', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("available" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _quantityMeta =
      const VerificationMeta('quantity');
  @override
  late final GeneratedColumn<int> quantity = GeneratedColumn<int>(
      'quantity', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(1));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        type,
        diamond,
        diamondId,
        jewellery,
        jewelleryId,
        meleeId,
        melee,
        addedOn,
        available,
        quantity
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'cart_items';
  @override
  VerificationContext validateIntegrity(Insertable<CartItem> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('diamond_id')) {
      context.handle(_diamondIdMeta,
          diamondId.isAcceptableOrUnknown(data['diamond_id']!, _diamondIdMeta));
    }
    if (data.containsKey('jewellery_id')) {
      context.handle(
          _jewelleryIdMeta,
          jewelleryId.isAcceptableOrUnknown(
              data['jewellery_id']!, _jewelleryIdMeta));
    }
    if (data.containsKey('melee_id')) {
      context.handle(_meleeIdMeta,
          meleeId.isAcceptableOrUnknown(data['melee_id']!, _meleeIdMeta));
    }
    if (data.containsKey('added_on')) {
      context.handle(_addedOnMeta,
          addedOn.isAcceptableOrUnknown(data['added_on']!, _addedOnMeta));
    }
    if (data.containsKey('available')) {
      context.handle(_availableMeta,
          available.isAcceptableOrUnknown(data['available']!, _availableMeta));
    }
    if (data.containsKey('quantity')) {
      context.handle(_quantityMeta,
          quantity.isAcceptableOrUnknown(data['quantity']!, _quantityMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  CartItem map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CartItem(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      type: $CartItemsTable.$convertertype.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!),
      diamond: $CartItemsTable.$converterdiamondn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}diamond'])),
      diamondId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}diamond_id']),
      jewellery: $CartItemsTable.$converterjewelleryn.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}jewellery'])),
      jewelleryId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}jewellery_id']),
      meleeId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}melee_id']),
      melee: $CartItemsTable.$convertermeleen.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}melee'])),
      addedOn: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}added_on']),
      available: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}available'])!,
      quantity: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}quantity'])!,
    );
  }

  @override
  $CartItemsTable createAlias(String alias) {
    return $CartItemsTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<CartItemType, String, String> $convertertype =
      const EnumNameConverter<CartItemType>(CartItemType.values);
  static TypeConverter<DiamondEntity, String> $converterdiamond =
      const DiamondEntityConverter();
  static TypeConverter<DiamondEntity?, String?> $converterdiamondn =
      NullAwareTypeConverter.wrap($converterdiamond);
  static TypeConverter<Jewellery, String> $converterjewellery =
      const JewelleryConverter();
  static TypeConverter<Jewellery?, String?> $converterjewelleryn =
      NullAwareTypeConverter.wrap($converterjewellery);
  static TypeConverter<MeleeEntity, String> $convertermelee =
      const MeleeEntityConverter();
  static TypeConverter<MeleeEntity?, String?> $convertermeleen =
      NullAwareTypeConverter.wrap($convertermelee);
}

class CartItem extends DataClass implements Insertable<CartItem> {
  final int id;
  final CartItemType type;
  final DiamondEntity? diamond;

  /// diamond id
  final String? diamondId;
  final Jewellery? jewellery;

  /// jewellery id
  final String? jewelleryId;

  /// melee id
  final String? meleeId;
  final MeleeEntity? melee;
  final DateTime? addedOn;
  final bool available;
  final int quantity;
  const CartItem(
      {required this.id,
      required this.type,
      this.diamond,
      this.diamondId,
      this.jewellery,
      this.jewelleryId,
      this.meleeId,
      this.melee,
      this.addedOn,
      required this.available,
      required this.quantity});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    {
      map['type'] =
          Variable<String>($CartItemsTable.$convertertype.toSql(type));
    }
    if (!nullToAbsent || diamond != null) {
      map['diamond'] =
          Variable<String>($CartItemsTable.$converterdiamondn.toSql(diamond));
    }
    if (!nullToAbsent || diamondId != null) {
      map['diamond_id'] = Variable<String>(diamondId);
    }
    if (!nullToAbsent || jewellery != null) {
      map['jewellery'] = Variable<String>(
          $CartItemsTable.$converterjewelleryn.toSql(jewellery));
    }
    if (!nullToAbsent || jewelleryId != null) {
      map['jewellery_id'] = Variable<String>(jewelleryId);
    }
    if (!nullToAbsent || meleeId != null) {
      map['melee_id'] = Variable<String>(meleeId);
    }
    if (!nullToAbsent || melee != null) {
      map['melee'] =
          Variable<String>($CartItemsTable.$convertermeleen.toSql(melee));
    }
    if (!nullToAbsent || addedOn != null) {
      map['added_on'] = Variable<DateTime>(addedOn);
    }
    map['available'] = Variable<bool>(available);
    map['quantity'] = Variable<int>(quantity);
    return map;
  }

  CartItemsCompanion toCompanion(bool nullToAbsent) {
    return CartItemsCompanion(
      id: Value(id),
      type: Value(type),
      diamond: diamond == null && nullToAbsent
          ? const Value.absent()
          : Value(diamond),
      diamondId: diamondId == null && nullToAbsent
          ? const Value.absent()
          : Value(diamondId),
      jewellery: jewellery == null && nullToAbsent
          ? const Value.absent()
          : Value(jewellery),
      jewelleryId: jewelleryId == null && nullToAbsent
          ? const Value.absent()
          : Value(jewelleryId),
      meleeId: meleeId == null && nullToAbsent
          ? const Value.absent()
          : Value(meleeId),
      melee:
          melee == null && nullToAbsent ? const Value.absent() : Value(melee),
      addedOn: addedOn == null && nullToAbsent
          ? const Value.absent()
          : Value(addedOn),
      available: Value(available),
      quantity: Value(quantity),
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CartItem(
      id: serializer.fromJson<int>(json['id']),
      type: $CartItemsTable.$convertertype
          .fromJson(serializer.fromJson<String>(json['type'])),
      diamond: serializer.fromJson<DiamondEntity?>(json['diamond']),
      diamondId: serializer.fromJson<String?>(json['diamondId']),
      jewellery: serializer.fromJson<Jewellery?>(json['jewellery']),
      jewelleryId: serializer.fromJson<String?>(json['jewelleryId']),
      meleeId: serializer.fromJson<String?>(json['meleeId']),
      melee: serializer.fromJson<MeleeEntity?>(json['melee']),
      addedOn: serializer.fromJson<DateTime?>(json['addedOn']),
      available: serializer.fromJson<bool>(json['available']),
      quantity: serializer.fromJson<int>(json['quantity']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'type': serializer
          .toJson<String>($CartItemsTable.$convertertype.toJson(type)),
      'diamond': serializer.toJson<DiamondEntity?>(diamond),
      'diamondId': serializer.toJson<String?>(diamondId),
      'jewellery': serializer.toJson<Jewellery?>(jewellery),
      'jewelleryId': serializer.toJson<String?>(jewelleryId),
      'meleeId': serializer.toJson<String?>(meleeId),
      'melee': serializer.toJson<MeleeEntity?>(melee),
      'addedOn': serializer.toJson<DateTime?>(addedOn),
      'available': serializer.toJson<bool>(available),
      'quantity': serializer.toJson<int>(quantity),
    };
  }

  CartItem copyWith(
          {int? id,
          CartItemType? type,
          Value<DiamondEntity?> diamond = const Value.absent(),
          Value<String?> diamondId = const Value.absent(),
          Value<Jewellery?> jewellery = const Value.absent(),
          Value<String?> jewelleryId = const Value.absent(),
          Value<String?> meleeId = const Value.absent(),
          Value<MeleeEntity?> melee = const Value.absent(),
          Value<DateTime?> addedOn = const Value.absent(),
          bool? available,
          int? quantity}) =>
      CartItem(
        id: id ?? this.id,
        type: type ?? this.type,
        diamond: diamond.present ? diamond.value : this.diamond,
        diamondId: diamondId.present ? diamondId.value : this.diamondId,
        jewellery: jewellery.present ? jewellery.value : this.jewellery,
        jewelleryId: jewelleryId.present ? jewelleryId.value : this.jewelleryId,
        meleeId: meleeId.present ? meleeId.value : this.meleeId,
        melee: melee.present ? melee.value : this.melee,
        addedOn: addedOn.present ? addedOn.value : this.addedOn,
        available: available ?? this.available,
        quantity: quantity ?? this.quantity,
      );
  CartItem copyWithCompanion(CartItemsCompanion data) {
    return CartItem(
      id: data.id.present ? data.id.value : this.id,
      type: data.type.present ? data.type.value : this.type,
      diamond: data.diamond.present ? data.diamond.value : this.diamond,
      diamondId: data.diamondId.present ? data.diamondId.value : this.diamondId,
      jewellery: data.jewellery.present ? data.jewellery.value : this.jewellery,
      jewelleryId:
          data.jewelleryId.present ? data.jewelleryId.value : this.jewelleryId,
      meleeId: data.meleeId.present ? data.meleeId.value : this.meleeId,
      melee: data.melee.present ? data.melee.value : this.melee,
      addedOn: data.addedOn.present ? data.addedOn.value : this.addedOn,
      available: data.available.present ? data.available.value : this.available,
      quantity: data.quantity.present ? data.quantity.value : this.quantity,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CartItem(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('diamond: $diamond, ')
          ..write('diamondId: $diamondId, ')
          ..write('jewellery: $jewellery, ')
          ..write('jewelleryId: $jewelleryId, ')
          ..write('meleeId: $meleeId, ')
          ..write('melee: $melee, ')
          ..write('addedOn: $addedOn, ')
          ..write('available: $available, ')
          ..write('quantity: $quantity')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, type, diamond, diamondId, jewellery,
      jewelleryId, meleeId, melee, addedOn, available, quantity);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CartItem &&
          other.id == this.id &&
          other.type == this.type &&
          other.diamond == this.diamond &&
          other.diamondId == this.diamondId &&
          other.jewellery == this.jewellery &&
          other.jewelleryId == this.jewelleryId &&
          other.meleeId == this.meleeId &&
          other.melee == this.melee &&
          other.addedOn == this.addedOn &&
          other.available == this.available &&
          other.quantity == this.quantity);
}

class CartItemsCompanion extends UpdateCompanion<CartItem> {
  final Value<int> id;
  final Value<CartItemType> type;
  final Value<DiamondEntity?> diamond;
  final Value<String?> diamondId;
  final Value<Jewellery?> jewellery;
  final Value<String?> jewelleryId;
  final Value<String?> meleeId;
  final Value<MeleeEntity?> melee;
  final Value<DateTime?> addedOn;
  final Value<bool> available;
  final Value<int> quantity;
  const CartItemsCompanion({
    this.id = const Value.absent(),
    this.type = const Value.absent(),
    this.diamond = const Value.absent(),
    this.diamondId = const Value.absent(),
    this.jewellery = const Value.absent(),
    this.jewelleryId = const Value.absent(),
    this.meleeId = const Value.absent(),
    this.melee = const Value.absent(),
    this.addedOn = const Value.absent(),
    this.available = const Value.absent(),
    this.quantity = const Value.absent(),
  });
  CartItemsCompanion.insert({
    this.id = const Value.absent(),
    required CartItemType type,
    this.diamond = const Value.absent(),
    this.diamondId = const Value.absent(),
    this.jewellery = const Value.absent(),
    this.jewelleryId = const Value.absent(),
    this.meleeId = const Value.absent(),
    this.melee = const Value.absent(),
    this.addedOn = const Value.absent(),
    this.available = const Value.absent(),
    this.quantity = const Value.absent(),
  }) : type = Value(type);
  static Insertable<CartItem> custom({
    Expression<int>? id,
    Expression<String>? type,
    Expression<String>? diamond,
    Expression<String>? diamondId,
    Expression<String>? jewellery,
    Expression<String>? jewelleryId,
    Expression<String>? meleeId,
    Expression<String>? melee,
    Expression<DateTime>? addedOn,
    Expression<bool>? available,
    Expression<int>? quantity,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (type != null) 'type': type,
      if (diamond != null) 'diamond': diamond,
      if (diamondId != null) 'diamond_id': diamondId,
      if (jewellery != null) 'jewellery': jewellery,
      if (jewelleryId != null) 'jewellery_id': jewelleryId,
      if (meleeId != null) 'melee_id': meleeId,
      if (melee != null) 'melee': melee,
      if (addedOn != null) 'added_on': addedOn,
      if (available != null) 'available': available,
      if (quantity != null) 'quantity': quantity,
    });
  }

  CartItemsCompanion copyWith(
      {Value<int>? id,
      Value<CartItemType>? type,
      Value<DiamondEntity?>? diamond,
      Value<String?>? diamondId,
      Value<Jewellery?>? jewellery,
      Value<String?>? jewelleryId,
      Value<String?>? meleeId,
      Value<MeleeEntity?>? melee,
      Value<DateTime?>? addedOn,
      Value<bool>? available,
      Value<int>? quantity}) {
    return CartItemsCompanion(
      id: id ?? this.id,
      type: type ?? this.type,
      diamond: diamond ?? this.diamond,
      diamondId: diamondId ?? this.diamondId,
      jewellery: jewellery ?? this.jewellery,
      jewelleryId: jewelleryId ?? this.jewelleryId,
      meleeId: meleeId ?? this.meleeId,
      melee: melee ?? this.melee,
      addedOn: addedOn ?? this.addedOn,
      available: available ?? this.available,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (type.present) {
      map['type'] =
          Variable<String>($CartItemsTable.$convertertype.toSql(type.value));
    }
    if (diamond.present) {
      map['diamond'] = Variable<String>(
          $CartItemsTable.$converterdiamondn.toSql(diamond.value));
    }
    if (diamondId.present) {
      map['diamond_id'] = Variable<String>(diamondId.value);
    }
    if (jewellery.present) {
      map['jewellery'] = Variable<String>(
          $CartItemsTable.$converterjewelleryn.toSql(jewellery.value));
    }
    if (jewelleryId.present) {
      map['jewellery_id'] = Variable<String>(jewelleryId.value);
    }
    if (meleeId.present) {
      map['melee_id'] = Variable<String>(meleeId.value);
    }
    if (melee.present) {
      map['melee'] =
          Variable<String>($CartItemsTable.$convertermeleen.toSql(melee.value));
    }
    if (addedOn.present) {
      map['added_on'] = Variable<DateTime>(addedOn.value);
    }
    if (available.present) {
      map['available'] = Variable<bool>(available.value);
    }
    if (quantity.present) {
      map['quantity'] = Variable<int>(quantity.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CartItemsCompanion(')
          ..write('id: $id, ')
          ..write('type: $type, ')
          ..write('diamond: $diamond, ')
          ..write('diamondId: $diamondId, ')
          ..write('jewellery: $jewellery, ')
          ..write('jewelleryId: $jewelleryId, ')
          ..write('meleeId: $meleeId, ')
          ..write('melee: $melee, ')
          ..write('addedOn: $addedOn, ')
          ..write('available: $available, ')
          ..write('quantity: $quantity')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $CartItemsTable cartItems = $CartItemsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [cartItems];
}

typedef $$CartItemsTableCreateCompanionBuilder = CartItemsCompanion Function({
  Value<int> id,
  required CartItemType type,
  Value<DiamondEntity?> diamond,
  Value<String?> diamondId,
  Value<Jewellery?> jewellery,
  Value<String?> jewelleryId,
  Value<String?> meleeId,
  Value<MeleeEntity?> melee,
  Value<DateTime?> addedOn,
  Value<bool> available,
  Value<int> quantity,
});
typedef $$CartItemsTableUpdateCompanionBuilder = CartItemsCompanion Function({
  Value<int> id,
  Value<CartItemType> type,
  Value<DiamondEntity?> diamond,
  Value<String?> diamondId,
  Value<Jewellery?> jewellery,
  Value<String?> jewelleryId,
  Value<String?> meleeId,
  Value<MeleeEntity?> melee,
  Value<DateTime?> addedOn,
  Value<bool> available,
  Value<int> quantity,
});

class $$CartItemsTableFilterComposer
    extends Composer<_$AppDatabase, $CartItemsTable> {
  $$CartItemsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<CartItemType, CartItemType, String> get type =>
      $composableBuilder(
          column: $table.type,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnWithTypeConverterFilters<DiamondEntity?, DiamondEntity, String>
      get diamond => $composableBuilder(
          column: $table.diamond,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get diamondId => $composableBuilder(
      column: $table.diamondId, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<Jewellery?, Jewellery, String> get jewellery =>
      $composableBuilder(
          column: $table.jewellery,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get jewelleryId => $composableBuilder(
      column: $table.jewelleryId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get meleeId => $composableBuilder(
      column: $table.meleeId, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<MeleeEntity?, MeleeEntity, String> get melee =>
      $composableBuilder(
          column: $table.melee,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get addedOn => $composableBuilder(
      column: $table.addedOn, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get available => $composableBuilder(
      column: $table.available, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get quantity => $composableBuilder(
      column: $table.quantity, builder: (column) => ColumnFilters(column));
}

class $$CartItemsTableOrderingComposer
    extends Composer<_$AppDatabase, $CartItemsTable> {
  $$CartItemsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get diamond => $composableBuilder(
      column: $table.diamond, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get diamondId => $composableBuilder(
      column: $table.diamondId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get jewellery => $composableBuilder(
      column: $table.jewellery, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get jewelleryId => $composableBuilder(
      column: $table.jewelleryId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get meleeId => $composableBuilder(
      column: $table.meleeId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get melee => $composableBuilder(
      column: $table.melee, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get addedOn => $composableBuilder(
      column: $table.addedOn, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get available => $composableBuilder(
      column: $table.available, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get quantity => $composableBuilder(
      column: $table.quantity, builder: (column) => ColumnOrderings(column));
}

class $$CartItemsTableAnnotationComposer
    extends Composer<_$AppDatabase, $CartItemsTable> {
  $$CartItemsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumnWithTypeConverter<CartItemType, String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumnWithTypeConverter<DiamondEntity?, String> get diamond =>
      $composableBuilder(column: $table.diamond, builder: (column) => column);

  GeneratedColumn<String> get diamondId =>
      $composableBuilder(column: $table.diamondId, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Jewellery?, String> get jewellery =>
      $composableBuilder(column: $table.jewellery, builder: (column) => column);

  GeneratedColumn<String> get jewelleryId => $composableBuilder(
      column: $table.jewelleryId, builder: (column) => column);

  GeneratedColumn<String> get meleeId =>
      $composableBuilder(column: $table.meleeId, builder: (column) => column);

  GeneratedColumnWithTypeConverter<MeleeEntity?, String> get melee =>
      $composableBuilder(column: $table.melee, builder: (column) => column);

  GeneratedColumn<DateTime> get addedOn =>
      $composableBuilder(column: $table.addedOn, builder: (column) => column);

  GeneratedColumn<bool> get available =>
      $composableBuilder(column: $table.available, builder: (column) => column);

  GeneratedColumn<int> get quantity =>
      $composableBuilder(column: $table.quantity, builder: (column) => column);
}

class $$CartItemsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $CartItemsTable,
    CartItem,
    $$CartItemsTableFilterComposer,
    $$CartItemsTableOrderingComposer,
    $$CartItemsTableAnnotationComposer,
    $$CartItemsTableCreateCompanionBuilder,
    $$CartItemsTableUpdateCompanionBuilder,
    (CartItem, BaseReferences<_$AppDatabase, $CartItemsTable, CartItem>),
    CartItem,
    PrefetchHooks Function()> {
  $$CartItemsTableTableManager(_$AppDatabase db, $CartItemsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$CartItemsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$CartItemsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CartItemsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<CartItemType> type = const Value.absent(),
            Value<DiamondEntity?> diamond = const Value.absent(),
            Value<String?> diamondId = const Value.absent(),
            Value<Jewellery?> jewellery = const Value.absent(),
            Value<String?> jewelleryId = const Value.absent(),
            Value<String?> meleeId = const Value.absent(),
            Value<MeleeEntity?> melee = const Value.absent(),
            Value<DateTime?> addedOn = const Value.absent(),
            Value<bool> available = const Value.absent(),
            Value<int> quantity = const Value.absent(),
          }) =>
              CartItemsCompanion(
            id: id,
            type: type,
            diamond: diamond,
            diamondId: diamondId,
            jewellery: jewellery,
            jewelleryId: jewelleryId,
            meleeId: meleeId,
            melee: melee,
            addedOn: addedOn,
            available: available,
            quantity: quantity,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required CartItemType type,
            Value<DiamondEntity?> diamond = const Value.absent(),
            Value<String?> diamondId = const Value.absent(),
            Value<Jewellery?> jewellery = const Value.absent(),
            Value<String?> jewelleryId = const Value.absent(),
            Value<String?> meleeId = const Value.absent(),
            Value<MeleeEntity?> melee = const Value.absent(),
            Value<DateTime?> addedOn = const Value.absent(),
            Value<bool> available = const Value.absent(),
            Value<int> quantity = const Value.absent(),
          }) =>
              CartItemsCompanion.insert(
            id: id,
            type: type,
            diamond: diamond,
            diamondId: diamondId,
            jewellery: jewellery,
            jewelleryId: jewelleryId,
            meleeId: meleeId,
            melee: melee,
            addedOn: addedOn,
            available: available,
            quantity: quantity,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$CartItemsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $CartItemsTable,
    CartItem,
    $$CartItemsTableFilterComposer,
    $$CartItemsTableOrderingComposer,
    $$CartItemsTableAnnotationComposer,
    $$CartItemsTableCreateCompanionBuilder,
    $$CartItemsTableUpdateCompanionBuilder,
    (CartItem, BaseReferences<_$AppDatabase, $CartItemsTable, CartItem>),
    CartItem,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$CartItemsTableTableManager get cartItems =>
      $$CartItemsTableTableManager(_db, _db.cartItems);
}
