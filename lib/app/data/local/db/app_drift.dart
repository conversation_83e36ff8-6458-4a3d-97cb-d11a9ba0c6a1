import 'dart:convert';

import 'package:diamond_company_app/app/data/local/db/universal_drift.dart';
import 'package:drift/drift.dart';

import '../../models/diamond/diamond_entity.dart';
import '../../models/jewellery/jewellery.dart' hide Value;
import '../../models/melee/melee_entity.dart';

part '../../models/cart_item/cart_item.dart';
part 'app_drift.g.dart';

@DriftDatabase(tables: [CartItems])
class AppDatabase extends _$AppDatabase {
  // After generating code, this class needs to define a `schemaVersion` getter
  // and a constructor telling drift where the database should be stored.
  // These are described in the getting started guide: https://drift.simonbinder.eu/setup/
  AppDatabase([QueryExecutor? executor])
      : super(executor ?? _openConnection(databaseName: 'my_database'));

  @override
  int get schemaVersion => 1;

  static QueryExecutor _openConnection({required String databaseName}) {
    try {
      return UniversalDriftDatabase().initDatabase(databaseName);
    } catch (e) {
      rethrow;
    }
  }
}
