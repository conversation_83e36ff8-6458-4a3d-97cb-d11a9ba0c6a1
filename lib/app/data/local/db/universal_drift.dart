import 'package:drift/drift.dart';

import 'drift_locator.dart'
    if (dart.library.html) 'web_database.dart'
    if (dart.library.io) 'native_database.dart';

/// This is an abstract class that defines the contract for the database
abstract class UniversalDriftDatabase {
  /// This method is used to get the platform-specific Drift database instance.
  factory UniversalDriftDatabase() => getPlatformDriftDatabase();

  /// This method is used to initialize the database connection.
  QueryExecutor initDatabase(String databaseName);
}
