import 'dart:convert';

import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/notification_model/notification_model.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/download_invoice_dialog.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Handle onMessage, onLaunch and onResume events
void notificationAction(RemoteMessage? remoteMessage) {
  //Todo(deepss1): Set Firebase notification callback
  logD(remoteMessage?.data.toString() ?? 'No Data');
  if (remoteMessage != null) {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      navigateToDetailsScreen(remoteMessage);
    });
  }
  // navigateToDetailsScreen(remoteMessage);
}

/// navigate to details screen
Future<void> navigateToDetailsScreen(RemoteMessage? remoteMessage) async {
  if (remoteMessage?.data['payload'] == null) {
    return;
  }

  final Map<String, dynamic> data =
      jsonDecode(remoteMessage?.data['payload'] ?? '');

  if (data['notification'] == null) {
    return;
  }

  final NotificationModel notification =
      NotificationModel.fromJson(data['notification']);

  var payload = notification.payload;

  if ([
    NotificationType.BUY_REQUEST_REJECTED,
    NotificationType.BUY_REQUEST_UPDATED
  ].contains(notification.notificationType)) {
    if (payload['buy_request_id'] != null) {
      await Get.toNamed(
        Routes.BUY_REQUEST_DETAILS,
        arguments: BuyRequest(id: payload['buy_request_id']),
      );
    }
  } else if ([
    NotificationType.ORDER_CANCELED,
    NotificationType.ORDER_CREATED,
    NotificationType.ORDER_DELIVERED,
    NotificationType.ORDER_SHIPPED,
  ].contains(notification.notificationType)) {
    if (payload['order_id'] != null) {
      await Get.toNamed(
        Routes.MY_ORDER_DETAIL,
        arguments: UserOrder(id: payload['order_id']),
      );
    }
  } else if ([
    NotificationType.RETURN_ORDER_ACCEPTED,
    NotificationType.RETURN_ORDER_REJECTED,
  ].contains(notification.notificationType)) {
    if (payload['order_id'] != null) {
      await Get.toNamed(
        Routes.RETURN_ORDER,
        arguments: UserOrder(id: payload['order_id']),
      );
    }
  } else if ([
    NotificationType.STOCK_UPDATED,
    NotificationType.STOCK_AVAILABLE,
    NotificationType.STOCK_HOLD,
    NotificationType.STOCK_SOLD,
    NotificationType.STOCK_PRICE_DECREASED,
    NotificationType.STOCK_PRICE_INCREASED
  ].contains(notification.notificationType)) {
    if (payload['stock_id'] != null) {
      logI('stock_id');
      logI(payload['stock_id']);
      await Get.toNamed(
        Routes.DIAMOND_DETAILS,
        arguments: DiamondArgs(
          id: payload['stock_id'],
          diamond: DiamondEntity(id: payload['stock_id']),
        ),
      );
    }
  } else if (NotificationType.INVOICE_AVAILABLE ==
      notification.notificationType) {
    if (payload['invoice_url'] != null) {
      // await Get.toNamed<void>(
      //   Routes.MY_ORDER_DETAIL,
      //   arguments: UserOrder(id: payload['order_id']),
      // );
      showDownloadInvoiceDialog(
        showInvoiceOnly: true,
        invoiceUrl: payload['invoice_url'],
      );
    }
  } else if ([
    NotificationType.OFFER_ACCEPTED,
    NotificationType.OFFER_REJECTED,
    NotificationType.OFFER_REVISED,
  ].contains(notification.notificationType)) {
    if (payload['offer_id'] != null) {
      await Get.toNamed(Routes.OFFER_DETAILS,
          arguments: StockOffer(id: payload['offer_id']));
    }
  } else if ([NotificationType.KYC_ACCEPTED, NotificationType.KYC_REJECTED]
      .contains(notification.notificationType)) {
    // if (payload['offer_id'] != null) {
    //   await Get.toNamed(Routes.STOCK_OFFERS);
    // }
  }
}

/// Handle local notification
void localNotificationAction(Map<String, dynamic>? remoteMessageData) {
  //Todo(deepss1): Set local notification callback
  // if (remoteMessageData != null) {
  //   WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
  //     navigateToDetailsScreen(
  //         RemoteMessage.fromMap(<String, dynamic>{'data': remoteMessageData}));
  //   });
  // }
}
