import 'package:diamond_company_app/app/data/config/env_config.dart';

/// Api Constants
class ApiConstants {
  /// prod url
  static String kBaseUrl = EnvConfig.baseUrl;

  /// prod url inquiry
  static String kSocketUrl = EnvConfig.socketUrl;

  /// medusa base url
  static String medusaBaseUrl = EnvConfig.medusaUrl;

  /// share link base url
  static String shareBaseUrl = 'https://web.diamondcompany.com/#';

  /// vendor panel base url prod
  static String vendorBaseUrlProd = 'https://vendor.diamondcompany.com';

  /// vendor panel base url dev
  static String vendorBaseUrlDev =
      'https://diamond-company.panel.dharmatech.in';

  /// dev url
  /// static const String kBaseUrl = 'https://diamond-company.api.dharmatech.in';

  /// dev url inquiry
  /// static const String kSocketUrl = 'https://diamond-company.chat.dharmatech.in/';

  /// local ip
  /// static const String kBaseUrl = 'http://*************:3002';
  /// static const String kSocketUrl = 'https://diamond-company.chat.dharmatech.in/';

  static const String kUrlPrefix = '/api/v1';

  /// login route
  static const String login = '$kUrlPrefix/user/login';

  /// Vendor login
  static const String vendorLogin = '$kUrlPrefix/admin/login/';

  /// Register vendor
  static const String registerVendor = '$kUrlPrefix/admin/vendor';

  /// Update vendor
  static const String updateVendor = '$kUrlPrefix/auth/admin/vendor';

  /// Dashboard data
  static const String vendorKpi = '$kUrlPrefix/auth/admin/kpi';

  /// Dashboard vendor-sales-reporting
  static const String vendorSalesReporting =
      '$kUrlPrefix/auth/admin/vendor-sales-reporting';

  /// Update offer
  static const String vendorUpdateOffer = '$kUrlPrefix/auth/admin/update-offer';

  /// Get user data
  static const String getUserData = '$kUrlPrefix/auth/admin/vendor/details';

  /// upload vendor stock file
  static const String uploadStockFile = '/file/upload/stock/file';

  /// add vendor stock
  static const String addStock = '$kUrlPrefix/auth/admin/stock';

  /// delete vendor stock
  static const String deleteStock = '$kUrlPrefix/auth/admin/stock';

  /// check registration status
  static const String registrationStatus =
      '$kUrlPrefix/user/registration-status';

  /// send verification route
  static const String sendVerification =
      '$kUrlPrefix/user/send-verification-otp';

  /// send verification route
  static const String sendVerificationVendor =
      '$kUrlPrefix/admin/vendor/send-verification-otp';

  /// send verification route
  static const String sendOTP = '$kUrlPrefix/user/send-otp';

  /// resend verification route
  static const String resendOTP = '$kUrlPrefix/user/resend-otp';

  /// verify otp
  static const String verifyOTP = '$kUrlPrefix/user/verify-otp';

  /// register route
  static const String register = '$kUrlPrefix/user/register';

  ///  buy request
  static const String buyRequest = '$kUrlPrefix/auth/buy-request';

  ///  unified order
  static const String unifiedOrder = '$kUrlPrefix/auth/user/unified-order';

  ///  accept buy request
  static const String acceptBuyRequest =
      '$kUrlPrefix/auth/user/accept-buy-request';

  ///  decline buy request
  static const String declineBuyRequest =
      '$kUrlPrefix/auth/user/decline-buy-request';

  ///  user credit history
  static const String userCreditHistory =
      '$kUrlPrefix/auth/user/credit-history';

  ///  user available credit
  static const String userAvailableCredit =
      '$kUrlPrefix/auth/user/available-credit';

  ///  user shipment
  static const String userShipment = '$kUrlPrefix/auth/user/shipments';

  ///  buy request details
  static const String buyRequestDetails =
      '$kUrlPrefix/auth/buy-request-details';

  ///  user order details
  static const String userOrderDetails = '$kUrlPrefix/auth/user/order-details';

  ///  user unified order details
  static const String userUnifiedOrderDetails =
      '$kUrlPrefix/auth/user/unified-order-details';

  /// upload user document
  static const String uploadUserDocument = '/file/upload/user/document';

  /// upload vendor order invoice
  static const String uploadVendorOrderInvoiceDocument =
      '/file/upload/vendor-order-invoice/file';

  /// upload vendor order invoice
  static const String vendorOrderInvoice =
      '$kUrlPrefix/auth/admin/vendor-order-invoice';

  /// Vendor document upload
  static const String vendorDocumentUpload = '/file/upload/vendor/document';

  /// upload user signature
  static const String uploadUserSignature = '/file/upload/user/signature';

  /// user verify phone
  static const String userVerifyPhone = '$kUrlPrefix/user/verify-phone';

  /// user verify email
  static const String userVerifyEmail = '$kUrlPrefix/user/verify-email';

  /// vendor verify email
  static const String vendorVerifyEmail =
      '$kUrlPrefix/admin/vendor/verify-email';

  /// reset password
  static const String userResetPassword = '$kUrlPrefix/user/reset-password';

  /// reset password route
  static const String vendorResetPassword =
      '$kUrlPrefix/admin/vendor/reset-password';

  /// change password
  static const String userChangePassword =
      '$kUrlPrefix/auth/user/change-password';

  /// user order
  static const String userOrder = '$kUrlPrefix/auth/user/order';

  /// update profile
  static const String updateProfile = '$kUrlPrefix/auth/user/update';

  /// update details
  static const String userDetails = '$kUrlPrefix/auth/user/details';

  /// user stock status
  static const String userStockStatus = '$kUrlPrefix/auth/user/stock-status';

  /// banner route
  static const String bannerImages = '$kUrlPrefix/user/banner';

  /// kpi route
  static const String kpi = '$kUrlPrefix/auth/user/kpi';

  /// wishlist
  static const String wishlist = '$kUrlPrefix/auth/user/wishlist';

  /// notification
  static const String notification = '$kUrlPrefix/auth/user/notification';

  /// melle
  static const String melleData = '$kUrlPrefix/admin/melle';

  /// jewellery showcase
  static const String jewelleryShowcase =
      '$kUrlPrefix/auth/user/jewellery-showcase';

  /// add remove like jewellery showcase
  static const String addRemoveLikeJewelleryShowcase =
      '$kUrlPrefix/auth/user/add-remove-like-jewellery';

  /// list jewellery tags
  static const String listJewelleryTags =
      '$kUrlPrefix/auth/user/jewellery-showcase-tags';

  /// add seen jewellery showcase
  static const String addSeenJewelleryShowcase =
      '$kUrlPrefix/auth/user/add-seen-jewellery';

  /// melle-price-per-carat
  static const String mellePricePerCarat =
      '$kUrlPrefix/auth/user/melle-price-per-carat';

  /// Vendor notification
  static const String vendorNotification =
      '$kUrlPrefix/auth/vendor/notification';

  /// create customer profile
  static const String createCustomerProfile =
      '$kUrlPrefix/auth/user/create-customer-profile';

  /// authorize credit card
  static const String authorizeCreditCard =
      '$kUrlPrefix/auth/user/authorize-credit-card';

  /// capture authorized amount
  static const String captureAuthorizedAmount =
      '$kUrlPrefix/auth/user/capture-authorized-amount';

  /// charge credit card
  static const String chargeCreditCard =
      '$kUrlPrefix/auth/user/charge-credit-card';

  /// updated buy request count
  static const String updatedBuyRequestCount =
      '$kUrlPrefix/auth/user/updated-buy-request-count';

  /// updated buy request count
  static const String deleteAccount = '$kUrlPrefix/auth/user/delete';

  /// notification unread count
  static const String notificationUnreadCount =
      '$kUrlPrefix/auth/user/notification-unread-count';

  /// Vendor notification un-read count
  static const String vendorNotificationUnreadCount =
      '$kUrlPrefix/auth/vendor/notification-unread-count';

  /// readNotification
  static const String readNotification =
      '$kUrlPrefix/auth/user/notification-read';

  /// readNotification
  static const String readVendorNotification =
      '$kUrlPrefix/auth/vendor/notification-read';

  /// return order
  static const String returnOrder = '$kUrlPrefix/auth/user/return-order';

  /// vendor return order
  static const String vendorReturnOrder = '$kUrlPrefix/auth/admin/return-order';

  /// admin return order
  static const String adminReturnOrder = '$kUrlPrefix/auth/admin/return-order';

////
  /// Get User inquiry
  static const String getUserInquiry = 'api/v1/auth/user/chat-rooms';

  /// create inquiry
  static const String createUserInquiry =
      '$kUrlPrefix/auth/user/create-inquiry';

  /// create offer
  static const String createOffer = '$kUrlPrefix/auth/user/stock-offer';

  /// update offer
  static const String updateOffer = '$kUrlPrefix/auth/user/update-offer';

  /// stock offer
  static const String listStockOffer = '$kUrlPrefix/auth/user/stock-offer';

  /// place order
  static const String placeOrder = '$kUrlPrefix/auth/user/place-order';

  /// offer trails
  static const String offerTrails = '$kUrlPrefix/auth/user/offer-trails';

  /// Vendor offers trails
  static const String vendorOfferTrails = '$kUrlPrefix/auth/admin/offer-trails';

  /// offer details
  static const String offerDetails = '$kUrlPrefix/auth/user/offer-details';

  /// vendor order
  static const String vendorOrder = '$kUrlPrefix/auth/admin/vendor-order';

  /// check email or phone available
  static const String checkEmailOrPhoneAvailable =
      '$kUrlPrefix/user/email-phone-available';

  /// static page details
  static const String staticPageDetails =
      '$kUrlPrefix/user/static-page-details';

  /// crm details
  static const String crmDetails = '$kUrlPrefix/user/crm';

  /// medusa list products
  static const String listProducts = 'store/products';

  /// medusa list product categories
  static const String listProductCategories = 'store/product-categories';

  /// alias config providers
  static const String aliasConfig = '$kUrlPrefix/admin/alias-config';

  /// list cart
  static const String listCart = '$kUrlPrefix/auth/user/list-cart';

  /// add to cart
  static const String addToCart = '$kUrlPrefix/auth/user/add-to-cart';

  /// update cart quantity
  static const String updateCartItem = '$kUrlPrefix/auth/user/update-cart-item';

  /// remove from cart
  static const String removeFromCart = '$kUrlPrefix/auth/user/remove-from-cart';

  /// remove all from cart
  static const String removeAllFromCart =
      '$kUrlPrefix/auth/user/remove-all-from-cart';

  /// share link
  static const String shareLink = '$kUrlPrefix/auth/user/share-links';
}
