/// Environment configuration
class EnvConfig {
  /// Base URL
  static String baseUrl = const String.fromEnvironment('base_url');

  /// Socket URL
  static String socketUrl = const String.fromEnvironment('socket_url');

  /// Medusa URL
  static String medusaUrl = const String.fromEnvironment('medusa_url');

  /// Environment
  static String env = const String.fromEnvironment('env');

  /// apple pay config
  static String applePayConfig =
      const String.fromEnvironment('apple_pay_config');

  /// Is production
  static bool get isProd => env == 'prod';

  /// Is development
  static bool get isDev => env == 'dev';
}
