import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// [AppImages] A class that contains all the image paths used in the app.
class AppImages {
  static const String _path = 'assets/images';

  /// Diamond shape images
  static const String diamPath = '$_path/diamond';

  /// Diamond selected shape images
  static const String diamSelPath = '$_path/diamond/selected';

  /// path
  static const String logo = '$_path/logo.svg';

  /// visibility_off icon svg
  static const String visibilityOffPath = '$_path/visibility_off_icon.svg';

  /// delete icon svg
  static const String deletePath = '$_path/delete_icon.svg';

  /// my order icon svg
  static const String myOrderPath = '$_path/my_order_icon.svg';

  /// inquires icon svg
  static const String inquiresPath = '$_path/inquires_icon.svg';

  /// buy requests icon svg
  static const String buyRequestsPath = '$_path/buy_requests_icon.svg';

  /// my address svg
  static const String myAddressPath = '$_path/my_address.svg';

  /// change password icon svg
  static const String changePasswordPath = '$_path/change_password_icon.svg';

  /// language icon svg
  static const String languagePath = '$_path/language_icon.svg';

  /// shipping policy icon svg
  static const String shippingPolicyPath = '$_path/shipping_policy_icon.svg';

  /// return policy icon svg
  static const String returnPolicyPath = '$_path/return_policy_icon.svg';

  /// credit card
  static const String creditCardPath = '$_path/credit_card_logo.svg';

  /// apple pay
  static const String applePayPath = '$_path/apple_pay_logo.svg';

  /// legal policy icon svg
  static const String legalPolicyPath = '$_path/legal_policy_icon.svg';

  /// help support icon svg
  static const String helpSupportPath = '$_path/help_support_icon.svg';

  /// delete account icon svg
  static const String deleteAccountPath = '$_path/delete_account_icon.svg';

  /// (profile flow) account mail icon svg
  static const String accountMailPath = '$_path/account_mail_icon.svg';

  /// instagram icon svg
  static const String instagramPath = '$_path/instagram_icon.svg';

  /// facebook icon svg
  static const String facebookPath = '$_path/facebook_icon.svg';

  /// edit icon svg
  static const String editPath = '$_path/edit_icon.svg';

  /// (profile flow) account other info path icon svg
  static const String accountOtherInfoPath =
      '$_path/account_other_info_icon.svg';

  /// location icon svg
  static const String locationPath = '$_path/location_icon.svg';

  /// logout icon svg
  static const String logoutPath = '$_path/logout_icon.svg';

  /// mail icon svg
  static const String mailPath = '$_path/mail_icon.svg';

  /// company logo icon svg
  static const String companyLogoPath = '$_path/company_logo.svg';

  /// calender icon svg
  static const String calenderIconPath = '$_path/calender_icon.svg';

  /// buy request svg
  static const String buyRequestPath = '$_path/buy_request.svg';

  /// buy svg
  static const String orderPath = '$_path/order.svg';

  /// drawer svg
  static const String drawerPath = '$_path/drawer.svg';

  /// bell svg
  static const String bellPath = '$_path/bell.svg';

  /// person svg
  static const String personPath = '$_path/person.svg';

  /// diamond placeholder svg
  static const String diamondPlaceholderPath = '$_path/diamond_placeholder.svg';

  /// diamond placeholder png
  static const String diamondPlaceholderPngPath =
      '$_path/diamond_placeholder.png';

  /// person icon path
  static const String personIconPath = '$_path/person_icon.svg';

  /// my address icon path
  static const String myAddressIconPath = '$_path/my_address_icon.svg';

  /// favourite svg
  static const String favouritePath = '$_path/favourite.svg';

  /// my cart svg
  static const String myCartPath = '$_path/my_cart.svg';

  /// my cart svg
  static const String myCartOutlinePath = '$_path/my_cart_outline.svg';

  /// diamond image
  static const String diamondImagePath = '$_path/diamond_image.png';

  /// person image
  static const String personImagePath = '$_path/person.png';

  /// support icon
  static const String supportIconPath = '$_path/support_icon.png';

  /// master card icon
  static const String masterCardIconPath = '$_path/mastercard_icon.png';

  /// google pay icon
  static const String googlePayIconPath = '$_path/google_pay_icon.png';

  /// apple pay icon
  static const String applePayIconPath = '$_path/apple_pay_icon.png';

  /// credit limit icon
  static const String creditLimitIconPath = '$_path/credit_limit_icon.png';

  /// Add icon
  static const String arrow = '$_path/arrow_up.png';

  /// calculator diamond icon
  static const String calculationDiamondPath = '$_path/calculation_diamond.png';

  /// blue color support icon
  static const String blueSupportIconPath = '$_path/blue_support_icon.png';

  /// verify image
  static const String verifyIconPath = '$_path/verify_icon.png';

  /// fill heart icon
  static const String fillHeartIconPath = '$_path/fill_heart.svg';

  /// buy request create bottom sheet icon
  static const String buyRequestCreateBottomSheetPath =
      '$_path/buy_request_create_bottomsheet.svg';

  /// thank you bottom sheet icon
  static const String thankYouBottomSheetPath =
      '$_path/thankyou_bottomsheet.svg';

  /// try again bottom sheet icon
  static const String tryAgainBottomSheetPath =
      '$_path/try_again_bottomsheet.svg';

  /// diamond icon
  static const String diamondIconPath = '$_path/diamond_icon.svg';

  /// empty wishlist icon
  static const String emptyWishlistPath = '$_path/empty_wishlist.svg';

  /// empty notification icon
  static const String emptyNotificationPath = '$_path/empty_notification.svg';

  /// empty diamond icon
  static const String emptyDiamondIconPath = '$_path/empty_diamond.svg';

  /// diamond company svg
  static const String diamondCompanyPath = '$_path/diamond_company.png';

  /// diamond company svg
  // static const String giaPath = '$_path/gia_logo.png';

  /// diamond company svg
  static const String calculationIconPath = '$_path/calculation_icon.svg';

  /// black heart svg
  static const String blackHeartPath = '$_path/black_heart.svg';

  /// cart icon path
  static const String cartIconPath = '$_path/cart_icon.svg';

  /// dashboard buy request path
  static const String dashboardBuyRequestPath =
      '$_path/dashboard_buyrequest.svg';

  /// dashboard icon path
  static const String dashboardIconPath = '$_path/dashboard_icon.svg';

  /// dashboard order path
  static const String dashboardOrderPath = '$_path/dashboard_myorders.svg';

  /// dashboard inquiries path
  static const String inquiriesPath = '$_path/inquiries_icon.svg';

  ///  inquiries path
  static const String inquiriePath = '$_path/inquiries.svg';

  /// dashboard market place path
  static const String marketPlacePath = '$_path/market_place.svg';

  /// back icon path
  static const String backIconPath = '$_path/back_icon.svg';

  /// user icon path
  static const String userIconPath = '$_path/user_icon.svg';

  /// arrow forward path
  static const String arrowForwardPath = '$_path/arrow_forward.svg';

  /// phone path
  static const String phonePath = '$_path/phone.svg';

  /// email path
  static const String emailPath = '$_path/email.svg';

  /// filter path
  static const String sortPath = '$_path/sort.svg';

  /// cart path
  static const String cartPath = '$_path/cart.svg';

  /// cart blue path
  static const String cartBluePath = '$_path/cart_blue.svg';

  /// filter path
  static const String filterIconPath = '$_path/filter_icon.png';

  /// Vendor stock path
  static const String vendorStock = '$_path/vendor.png';

  /// phone path
  static const String phonePurplePath = '$_path/phone_purple.svg';

  /// skype path
  static const String skypePath = '$_path/skype.svg';

  /// whatsapp path
  static const String whatsappPath = '$_path/whatsapp.svg';

  /// whishlist path
  static const String whishlistPath = '$_path/whishlist.svg';

  /// arrow down path
  static const String arrowDownPath = '$_path/arrow_down.svg';

  /// check icon path
  static const String checkIconPath = '$_path/check_icon.svg';

  /// info icon path
  static const String infoIconPath = '$_path/info_icon.svg';

  /// info icon path
  static const String neutralIconPath = '$_path/neutral.svg';

  /// info icon path
  static const String warningIconPath = '$_path/warning_icon.svg';

  /// download invoice path
  static const String downloadInvoicePath = '$_path/download_invoice.svg';

  /// arrow up path
  static const String arrowUpPath = '$_path/arrow_up.svg';

  /// calc icon path
  static const String calcIconPath = '$_path/calc_icon.png';

  /// goldd path
  static const String goldPath = '$_path/gold.png';

  /// search Inquiries path
  static const String searchInquiriesPath = '$_path/search_inquiries.svg';

  /// attach path
  static const String attachPath = '$_path/attach.svg';

  /// floating person logo
  static const String floatingPersonImagePath = '$_path/person_image.png';

  /// send path
  static const String sendPath = '$_path/send.svg';

  /// image path
  static const String imagePath = '$_path/image.svg';

  /// video path
  static const String videoPath = '$_path/video.svg';

  /// audio path
  static const String audioPath = '$_path/audio.svg';

  /// update bg path
  static const String updateBgPath = '$_path/update_bg.png';

  /// gradient bg path
  static const String gradientTileBgPath = '$_path/gradient_tile_bg.png';

  /// diamond update path
  static const String diamondUpdatePath = '$_path/diamond_update.svg';

  /// empty buy request path
  static const String emptyBuyRequestPath = '$_path/empty_buy_request.svg';

  /// empty cart path
  static const String emptyCartPath = '$_path/empty_cart.svg';

  /// empty marketplace path
  static const String emptyMarketplacePath = '$_path/empty_marketplace.svg';

  /// empty order path
  static const String emptyOrderPath = '$_path/empty_order.svg';

  /// empty inquiries path
  static const String emptyInquiriesPath = '$_path/no_inquiries.svg';

  /// empty wifi path
  static const String noWifiPath = '$_path/no_wifi.svg';

  /// empty offer
  static const String emptyOfferPath = '$_path/empty_offer.svg';

  /// check circle path
  static const String checkCirclePath = '$_path/check_circle.svg';

  /// my offer path
  static const String myOfferPath = '$_path/my_offer.svg';

  /// asc path
  static const String ascPath = '$_path/asc.svg';

  /// desc path
  static const String descPath = '$_path/desc.svg';

  /// check
  static const String checkPath = '$_path/check.svg';

  /// cross
  static const String crossPath = '$_path/cross.svg';

  /// info
  static const String infoPath = '$_path/info.svg';

  /// message
  static const String messagePath = '$_path/message.svg';

  /// warning
  static const String warningPath = '$_path/warning.svg';

  /// buy req dashboard path
  static const String buyReqDashboardPath = '$_path/buy_req_dashboard.svg';

  /// favourites dashboard path
  static const String favouritesDashboardPath =
      '$_path/favourites_dashboard.svg';

  /// my cart dashboard path
  static const String myCartDashboardPath = '$_path/my_cart_dashboard.svg';

  /// my offer dashboard path
  static const String myOfferDashboardPath = '$_path/my_offer_dashboard.svg';

  /// cart green path
  static const String cartGreenPath = '$_path/cart_green.svg';

  /// cart orange path
  static const String cartOrangePath = '$_path/cart_orange.svg';

  /// diamond jewellery path
  static const String diamondJewelleryPath = '$_path/diamond_jewellery.svg';

  /// diamond white path
  static const String diamondWhitePath = '$_path/diamond_white.svg';

  /// coming soon jewellery path
  static const String comingSoonJewelleryPath =
      '$_path/jewellery_coming_soon.jpg';

  /// melee path
  static const String meleePath = '$_path/melee.svg';

  /// fancy melee path
  static const String fancyMeleePath = '$_path/fancy_melee.svg';

  /// lab grown path
  static const String labGrownPath = '$_path/lab_grown.svg';

  /// my offers path
  static const String myOffersPath = '$_path/my_offers.svg';

  /// my orders web
  static const String myOrderWebPath = '$_path/my_orders_web.svg';

  /// benefits path
  static const String benefitsPath = '$_path/benefits.svg';

  /// ring path
  static const String ringPath = '$_path/ring.svg';

  /// diamond black path
  static const String diamondBlackPath = '$_path/diamond_black.svg';

  /// gold black path
  static const String goldBlackPath = '$_path/gold_black.svg';

  /// dimensions path
  static const String dimensionsPath = '$_path/dimensions.svg';

  /// diamond melee path
  static const String diamondMeleePath = '$_path/diamond_melee.svg';

  /// diamond cart path
  static const String diamondCartPath = '$_path/diamond_cart.svg';

  /// jewellery cart path
  static const String jewelleryCartPath = '$_path/jewellery_cart.svg';

  /// melee cart path
  static const String meleeCartPath = '$_path/melee_cart.svg';

  /// diamond order path
  static const String diamondOrderPath = '$_path/diamond_order.svg';

  /// diamond Cart
  static SvgPicture get diamondCart =>
      SvgPicture.asset(AppImages.diamondCartPath);

  /// diamond order
  static SvgPicture get diamondOrder =>
      SvgPicture.asset(AppImages.diamondOrderPath);

  /// jewellery Cart
  static SvgPicture get jewelleryCart =>
      SvgPicture.asset(AppImages.jewelleryCartPath);

  /// melee Cart
  static SvgPicture get meleeCart => SvgPicture.asset(AppImages.meleeCartPath);

  /// ring
  static SvgPicture get ring => SvgPicture.asset(AppImages.ringPath);

  /// diamond melee
  static SvgPicture get diamondMelee =>
      SvgPicture.asset(AppImages.diamondMeleePath);

  /// diamond black
  static SvgPicture get diamondBlack =>
      SvgPicture.asset(AppImages.diamondBlackPath);

  /// gold black
  static SvgPicture get goldBlack => SvgPicture.asset(AppImages.goldBlackPath);

  /// dimensions
  static SvgPicture get dimensions =>
      SvgPicture.asset(AppImages.dimensionsPath);

  /// cart green dashboard
  static SvgPicture get cartGreen => SvgPicture.asset(AppImages.cartGreenPath);

  /// benefits dashboard
  static SvgPicture get benefits => SvgPicture.asset(AppImages.benefitsPath);

  /// cart orange dashboard
  static SvgPicture get cartOrange =>
      SvgPicture.asset(AppImages.cartOrangePath);

  /// fancy melee dashboard
  static SvgPicture get fancyMelee =>
      SvgPicture.asset(AppImages.fancyMeleePath);

  /// lab grown dashboard
  static SvgPicture get labGrown => SvgPicture.asset(AppImages.labGrownPath);

  /// gradient tile bg
  static AssetImage get gradientTileBg =>
      const AssetImage(AppImages.gradientTileBgPath);

  /// gold dashboard
  static Image get gold => Image.asset(AppImages.goldPath);

  /// coming soon jewellery
  static Image get comingSoonJewellery => Image.asset(
        AppImages.comingSoonJewelleryPath,
        fit: BoxFit.cover,
      );

  /// diamond jewellery dashboard
  static SvgPicture get diamondJewellery =>
      SvgPicture.asset(AppImages.diamondJewelleryPath);

  /// diamond white dashboard
  static SvgPicture get diamondWhite =>
      SvgPicture.asset(AppImages.diamondWhitePath);

  /// melee dashboard
  static SvgPicture get melee => SvgPicture.asset(AppImages.meleePath);

  /// myOffers dashboard
  static SvgPicture get myOffers => SvgPicture.asset(AppImages.myOffersPath);

  /// my orders dashboard
  static SvgPicture get myOrdersWeb =>
      SvgPicture.asset(AppImages.myOrderWebPath);

  /// my offer dashboard
  static SvgPicture get myOfferDashboard =>
      SvgPicture.asset(AppImages.myOfferDashboardPath);

  /// my cart dashboard
  static SvgPicture get myCartDashboard =>
      SvgPicture.asset(AppImages.myCartDashboardPath);

  /// favourites dashboard
  static SvgPicture get favouritesDashboard =>
      SvgPicture.asset(AppImages.favouritesDashboardPath);

  /// buy req dashboard
  static SvgPicture get buyReqDashboard =>
      SvgPicture.asset(AppImages.buyReqDashboardPath);

  /// asc
  static SvgPicture get asc => SvgPicture.asset(
        AppImages.ascPath,
        color: AppColors.kffffff,
      );

  /// desc
  static SvgPicture get desc => SvgPicture.asset(
        AppImages.descPath,
        color: AppColors.kffffff,
      );

  /// diamond update
  static SvgPicture get diamondUpdate =>
      SvgPicture.asset(AppImages.diamondUpdatePath);

  /// filter
  static SvgPicture get sort => SvgPicture.asset(AppImages.sortPath);

  /// person
  static SvgPicture get personSvg => SvgPicture.asset(AppImages.personIconPath);

  /// my address
  static SvgPicture get myAddressIconSvg =>
      SvgPicture.asset(AppImages.myAddressIconPath);

  /// empty BuyRequest
  static SvgPicture get emptyBuyRequest =>
      SvgPicture.asset(AppImages.emptyBuyRequestPath);

  /// empty Cart
  static SvgPicture get emptyCart => SvgPicture.asset(AppImages.emptyCartPath);

  /// credit card svg
  static SvgPicture get creditCardSvg =>
      SvgPicture.asset(AppImages.creditCardPath);

  /// apple pay svg
  static SvgPicture get applePaySvg => SvgPicture.asset(AppImages.applePayPath);

  /// cart
  static SvgPicture get cart => SvgPicture.asset(AppImages.cartPath);

  /// cart blue
  static SvgPicture get cartBlue => SvgPicture.asset(AppImages.cartBluePath);

  /// empty Marketplace
  static SvgPicture get emptyMarketplace =>
      SvgPicture.asset(AppImages.emptyMarketplacePath);

  /// empty Order
  static SvgPicture get emptyOrder =>
      SvgPicture.asset(AppImages.emptyOrderPath);

  /// empty Inquiries
  static SvgPicture get emptyInquiries =>
      SvgPicture.asset(AppImages.emptyInquiriesPath);

  /// empty offer
  static SvgPicture get emptyOffer =>
      SvgPicture.asset(AppImages.emptyOfferPath);

  /// empty offer
  static SvgPicture get checkCircle =>
      SvgPicture.asset(AppImages.checkCirclePath);

  /// my offer
  static SvgPicture get myOffer => SvgPicture.asset(
        AppImages.myOfferPath,
        color: AppColors.k101C28,
      );

  /// no Wifi
  static SvgPicture get noWifi => SvgPicture.asset(AppImages.noWifiPath);

  /// update bg
  static Image get updateBg => Image.asset(AppImages.updateBgPath);

  /// attach
  static SvgPicture get attach => SvgPicture.asset(AppImages.attachPath);

  /// send
  static SvgPicture get send => SvgPicture.asset(AppImages.sendPath);

  /// image
  static SvgPicture get image => SvgPicture.asset(AppImages.imagePath);

  /// video
  static SvgPicture get video => SvgPicture.asset(AppImages.videoPath);

  /// audio
  static SvgPicture get audio => SvgPicture.asset(AppImages.audioPath);

  /// search Inquiries icon
  static SvgPicture get searchInquiries =>
      SvgPicture.asset(AppImages.searchInquiriesPath);

  /// calc icon
  static Image get calcIcon => Image.asset(
        AppImages.calcIconPath,
        color: AppColors.k101C28,
      );

  /// arrow up
  static SvgPicture get arrowUp => SvgPicture.asset(AppImages.arrowUpPath);

  /// app logo
  static SvgPicture get appLogo => SvgPicture.asset(AppImages.logo);

  /// download invoice
  static SvgPicture get downloadInvoice =>
      SvgPicture.asset(AppImages.downloadInvoicePath);

  /// app logo
  static SvgPicture get mailIconLogo => SvgPicture.asset(AppImages.mailPath);

  /// app logo
  static SvgPicture get companyIconLogo =>
      SvgPicture.asset(AppImages.companyLogoPath);

  /// check icon
  static SvgPicture get checkIcon => SvgPicture.asset(AppImages.checkIconPath);

  /// info icon
  static SvgPicture get infoIcon => SvgPicture.asset(AppImages.infoIconPath);

  /// neutral icon
  static SvgPicture get neutralIcon =>
      SvgPicture.asset(AppImages.neutralIconPath);

  /// warning icon
  static SvgPicture get warningIcon =>
      SvgPicture.asset(AppImages.warningIconPath);

  /// arrow down
  static SvgPicture get arrowDown => SvgPicture.asset(AppImages.arrowDownPath);

  /// whishlist
  static SvgPicture get whishlist => SvgPicture.asset(AppImages.whishlistPath);

  /// app logo
  static SvgPicture get phonePurple => SvgPicture.asset(
        AppImages.phonePurplePath,
        height: 55.h,
        width: 55.w,
      );

  /// app logo
  static SvgPicture get skype => SvgPicture.asset(
        AppImages.skypePath,
        height: 62.h,
        width: 62.w,
      );

  /// app logo
  static SvgPicture get whatsapp => SvgPicture.asset(
        AppImages.whatsappPath,
        height: 75.h,
        width: 75.w,
      );

  /// calender logo
  static SvgPicture get calenderLogo =>
      SvgPicture.asset(AppImages.calenderIconPath);

  /// delete logo
  static SvgPicture get deleteLogo => SvgPicture.asset(AppImages.deletePath);

  /// my order logo
  static SvgPicture get myOrderLogo => SvgPicture.asset(AppImages.myOrderPath);

  /// inquires logo
  static SvgPicture get inquiresLogo =>
      SvgPicture.asset(AppImages.inquiriesPath);

  /// buy requests logo
  static SvgPicture get buyRequestsLogo =>
      SvgPicture.asset(AppImages.buyRequestsPath);

  /// my address logo
  static SvgPicture get myAddressLogo =>
      SvgPicture.asset(AppImages.myAddressPath);

  /// change password logo
  static SvgPicture get changePasswordLogo =>
      SvgPicture.asset(AppImages.changePasswordPath);

  /// language logo
  static SvgPicture get languageLogo => SvgPicture.asset(
        AppImages.languagePath,
        height: 50.h,
      );

  /// shipping policy logo
  static SvgPicture get shippingPolicyLogo =>
      SvgPicture.asset(AppImages.shippingPolicyPath);

  /// return policy logo
  static SvgPicture get returnPolicyLogo =>
      SvgPicture.asset(AppImages.returnPolicyPath);

  /// legal policy logo
  static SvgPicture get legalPolicyLogo =>
      SvgPicture.asset(AppImages.legalPolicyPath);

  /// help & support logo
  static SvgPicture get helpSupportLogo =>
      SvgPicture.asset(AppImages.helpSupportPath);

  /// delete account logo
  static SvgPicture get deleteAccountLogo =>
      SvgPicture.asset(AppImages.deleteAccountPath);

  /// (profile flow) account screen mail icon logo
  static SvgPicture get accountMailIconLogo =>
      SvgPicture.asset(AppImages.accountMailPath);

  /// instagram logo
  static SvgPicture get instagramLogo =>
      SvgPicture.asset(AppImages.instagramPath);

  /// facebook logo
  static SvgPicture get facebookLogo =>
      SvgPicture.asset(AppImages.facebookPath);

  /// edit logo
  static SvgPicture get editLogo => SvgPicture.asset(AppImages.editPath);

  /// (profile flow) account screen other info logo
  static SvgPicture get accountOtherInfoLogo =>
      SvgPicture.asset(AppImages.accountOtherInfoPath);

  /// location logo
  static SvgPicture get locationLogo =>
      SvgPicture.asset(AppImages.locationPath);

  /// logout logo
  static SvgPicture get logoutIcon => SvgPicture.asset(AppImages.logoutPath);

  /// app logo
  static Image get filterIcon => Image.asset(AppImages.filterIconPath);

  /// back icon
  static SvgPicture get backIcon => SvgPicture.asset(
        AppImages.backIconPath,
        color: AppColors.k101C28,
      );

  /// back icon
  static SvgPicture get backIconWhite => SvgPicture.asset(
        AppImages.backIconPath,
        color: AppColors.kBEFFFC,
      );

  /// back icon
  static SvgPicture get whiteBackIcon => SvgPicture.asset(
        AppImages.backIconPath,
        color: AppColors.kffffff,
      );

  /// back icon
  static SvgPicture get arrowForward =>
      SvgPicture.asset(AppImages.arrowForwardPath);

  /// Arrow forward white
  static SvgPicture get arrowForwardWhite => SvgPicture.asset(
        AppImages.arrowForwardPath,
        color: AppColors.kffffff,
      );

  /// user icon
  static SvgPicture get userIcon => SvgPicture.asset(AppImages.userIconPath);

  /// phone icon
  static SvgPicture get phoneIcon => SvgPicture.asset(AppImages.phonePath);

  /// email icon
  static SvgPicture get emailIcon => SvgPicture.asset(AppImages.emailPath);

  /// cart icon
  static SvgPicture get cartIcon => SvgPicture.asset(AppImages.cartIconPath);

  /// dashboard buy request
  static SvgPicture get dashboardBuyRequest =>
      SvgPicture.asset(AppImages.dashboardBuyRequestPath);

  /// dashboard icon
  static SvgPicture get dashboardIcon =>
      SvgPicture.asset(AppImages.dashboardIconPath);

  /// buy request
  static SvgPicture get buyRequest =>
      SvgPicture.asset(AppImages.buyRequestPath);

  /// dashboard buy request
  static SvgPicture get dashboardOrder => SvgPicture.asset(
        AppImages.dashboardOrderPath,
        color: AppColors.kE24545.withOpacity(0.7),
      );

  /// inquiries icon
  static SvgPicture get inquiries => SvgPicture.asset(AppImages.inquiriePath);

  /// inquiries icon
  static SvgPicture get calculationIcon =>
      SvgPicture.asset(AppImages.calculationIconPath);

  /// market place icon
  static SvgPicture get marketPlace =>
      SvgPicture.asset(AppImages.marketPlacePath);

  /// order
  static SvgPicture get order => SvgPicture.asset(orderPath);

  /// favourite
  static SvgPicture get favourite => SvgPicture.asset(favouritePath);

  /// my cart
  static SvgPicture get myCart => SvgPicture.asset(myCartPath);

  /// my cart outline
  static SvgPicture get myCartOutline => SvgPicture.asset(
        myCartOutlinePath,
        color: AppColors.k333333,
      );

  /// drawer icon
  static SvgPicture get drawer => SvgPicture.asset(
        drawerPath,
        color: AppColors.k333333,
      );

  /// bell icon
  static SvgPicture get bell => SvgPicture.asset(
        bellPath,
        color: AppColors.k333333,
      );

  /// person icon
  static SvgPicture get person => SvgPicture.asset(personPath);

  /// diamond placeholder icon
  static SvgPicture get diamondPlaceholderSvg =>
      SvgPicture.asset(diamondPlaceholderPath);

  /// diamond image
  static Image get diamondImage => Image.asset(
        diamondImagePath,
        fit: BoxFit.cover,
      );

  /// fill heart icon
  static SvgPicture get fillHeartSvg => SvgPicture.asset(fillHeartIconPath);

  /// buyRequestCreateBottomSheetPath Svg
  static SvgPicture get buyRequestCreateBottomSheetSvg =>
      SvgPicture.asset(buyRequestCreateBottomSheetPath);

  /// thankYouBottomSheetPath Svg
  static SvgPicture get thankYouBottomSheetSvg =>
      SvgPicture.asset(thankYouBottomSheetPath);

  /// tryAgainBottomSheetPath Svg
  static SvgPicture get tryAgainBottomSheetSvg =>
      SvgPicture.asset(tryAgainBottomSheetPath);

  /// diamond icon
  static SvgPicture get diamondIcon => SvgPicture.asset(
        diamondIconPath,
        color: AppColors.k101C28,
      );

  /// empty diamond svg
  static SvgPicture get emptyDiamondSvg => SvgPicture.asset(
        emptyDiamondIconPath,
      );

  /// empty wishlist svg
  static SvgPicture get emptyWishlistSvg => SvgPicture.asset(
        emptyWishlistPath,
      );

  /// empty notification svg
  static SvgPicture get emptyNotificationSvg => SvgPicture.asset(
        emptyNotificationPath,
      );

  /// black heart
  static SvgPicture get blackHeart => SvgPicture.asset(
        blackHeartPath,
        height: 67.h,
        width: 67.w,
      );

  /// diamond company
  static Image get diamondCompany => Image.asset(
        diamondCompanyPath,
        color: AppColors.k333333,
      );

  /// gia logo
// static Image get gia => Image.asset(
//       giaPath,
//       fit: BoxFit.cover,
//     );
}
