import 'package:diamond_company_app/app/chat_service/socket_initialize.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/biometric_auth.dart';
import 'package:diamond_company_app/app/data/local/locale_provider.dart';
import 'package:diamond_company_app/app/data/local/theme_provider.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:diamond_company_app/app/data/remote/notifications/firebase_notifications.dart';
import 'package:diamond_company_app/app/data/remote/notifications/notification_actions.dart';
import 'package:diamond_company_app/app/utils/address_utils.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/*late String kDevelopmentApiBaseUrl;
late String kProductionApiBaseUrl;*/

/// Initialize all core functionalities
Future<void> initializeCoreApp({
  required String? developmentApiBaseUrl,
  required String? productionApiBaseUrl,
  bool firebaseApp = true,
  bool setupLocalNotifications = false,
  bool encryption = false,
}) async {
  //Activate logger
  initLogger();

  //Firebase products initializations
  if (firebaseApp) {
    await Firebase.initializeApp();
    notificationActions(
      action: notificationAction,
      localNotification: setupLocalNotifications,
      localNotificationAction: localNotificationAction,
    );
    logI(' TOKEN ====================== ${await pushNotificationToken()}');
  }
  /*if (developmentApiBaseUrl != null) {
    kDevelopmentApiBaseUrl = developmentApiBaseUrl;
  }
  if (productionApiBaseUrl != null) {
    kProductionApiBaseUrl = productionApiBaseUrl;
  }*/

  // Initialize the BiometricAuth singleton
  if (!kIsWeb) {
    await BiometricAuth.instance.init();
  }
  await GetStorage.init();
  UserProvider.loadUser();
  LocaleProvider.loadCurrentLocale();
  await ThemeProvider.getThemeModeFromStore();

  AppSocket.initSocket();
  await LocationJSON.init();

  if (productionApiBaseUrl != null && developmentApiBaseUrl != null) {
    APIService.initializeAPIService(
      encryptData: encryption,
      devBaseUrl: developmentApiBaseUrl,
      prodBaseUrl: productionApiBaseUrl,
    );
  }
}
