part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart
// ignore_for_file: type=lint

abstract class Routes {
  Routes._();

  static const HOME = _Paths.HOME;
  static const SIGN_IN = _Paths.SIGN_IN;
  static const LOGIN = _Paths.LOGIN;
  static const FORGOT_PASSWORD = _Paths.FORGOT_PASSWORD;
  static const VERIFICATION_PHONE = _Paths.VERIFICATION_PHONE;
  static const RESET_PASSWORD = _Paths.RESET_PASSWORD;
  static const NEW_COMPANY_REGISTRATION = _Paths.NEW_COMPANY_REGISTRATION;
  static const DASHBOARD = _Paths.DASHBOARD;
  static const BUY_REQUEST = _Paths.BUY_REQUEST;
  static const MY_ORDER_DETAIL = _Paths.MY_ORDER_DETAIL;
  static const MY_ORDER = _Paths.MY_ORDER;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const WHISHLIST = _Paths.WHISHLIST;
  static const LEGAL_STATUS_ORGANIZATION = _Paths.LEGAL_STATUS_ORGANIZATION;
  static const USA_PATRIOT_ACT = _Paths.USA_PATRIOT_ACT;
  static const TRADE_REFERENCES = _Paths.TRADE_REFERENCES;
  static const CREDIT_AGREEMENT = _Paths.CREDIT_AGREEMENT;
  static const REGISTER_PHONE = _Paths.REGISTER_PHONE;
  static const REGISTER_EMAIL = _Paths.REGISTER_EMAIL;
  static const REGISTER_CREATE_PASSWORD = _Paths.REGISTER_CREATE_PASSWORD;
  static const CART = _Paths.CART;
  static const RETURN_ORDER = _Paths.RETURN_ORDER;
  static const DIAMOND_DETAILS = _Paths.DIAMOND_DETAILS;
  static const CART_FIRST_ADDRESS = _Paths.CART_FIRST_ADDRESS;
  static const CART_SECOND_ADDRESS = _Paths.CART_SECOND_ADDRESS;
  static const PROFILE = _Paths.PROFILE;
  static const CHANGE_PASSWORD = _Paths.CHANGE_PASSWORD;
  static const MY_ACCOUNT = _Paths.MY_ACCOUNT;
  static const GET_FILTER = _Paths.GET_FILTER;
  static const GET_FILTER_UPDATED = _Paths.GET_FILTER_UPDATED;
  static const LISTING = _Paths.LISTING;
  static const INQUIRIES = _Paths.INQUIRIES;
  static const INQUIRY_MESSAGES = _Paths.INQUIRY_MESSAGES;
  static const PRIVACY_POLICY = _Paths.PRIVACY_POLICY;
  static const COMPULARY_UPDATE = _Paths.COMPULARY_UPDATE;
  static const MY_ADDRESS = _Paths.MY_ADDRESS;
  static const EDIT_ADDRESS = _Paths.EDIT_ADDRESS;
  static const BUY_REQUEST_DETAILS = _Paths.BUY_REQUEST_DETAILS;
  static const CONNECTIVITY_SERVICE = _Paths.CONNECTIVITY_SERVICE;
  static const CREDIT_CARD_PAYMENT = _Paths.CREDIT_CARD_PAYMENT;
  static const VIEW_ALL = _Paths.VIEW_ALL;
  static const STOCK_OFFERS = _Paths.STOCK_OFFERS;
  static const OFFER_DETAILS = _Paths.OFFER_DETAILS;
  static const CREATE_VENDOR = _Paths.CREATE_VENDOR;
  static const VENDOR_LOGIN = _Paths.VENDOR_LOGIN;
  static const VENDOR_DASHBOARD = _Paths.VENDOR_DASHBOARD;
  static const VENDOR_STOCK_OFFERES = _Paths.VENDOR_STOCK_OFFERES;
  static const VENDER_OFFER_DETAILS = _Paths.VENDER_OFFER_DETAILS;
  static const VENDOR_STOCK_OFFER_DETAILS = _Paths.VENDOR_STOCK_OFFER_DETAILS;
  static const JEWELLERY_CATEGORY = _Paths.JEWELLERY_CATEGORY;
  static const JEWELLERY_SUB_CATEGORY = _Paths.JEWELLERY_SUB_CATEGORY;
  static const JEWELLERIES = _Paths.JEWELLERIES;
  static const JEWELLERY_DETAILS = _Paths.JEWELLERY_DETAILS;
  static const CART_UPDATED = _Paths.CART_UPDATED;
  static const MELEE = _Paths.MELEE;
  static const VENDOR_ACCOUNT = _Paths.VENDOR_ACCOUNT;
  static const VENDOR_NOTIFICATION = _Paths.VENDOR_NOTIFICATION;
  static const VENDOR_INVENTORY = _Paths.VENDOR_INVENTORY;
  static const VENDOR_DIAMOND_DETAILS = _Paths.VENDOR_DIAMOND_DETAILS;
  static const HOLD_REQUEST = _Paths.HOLD_REQUEST;
  static const VENDOR_ORDER = _Paths.VENDOR_ORDER;
  static const VENDOR_RETURN_ORDER = _Paths.VENDOR_RETURN_ORDER;
  static const RETURNED_DIAMONDS = _Paths.RETURNED_DIAMONDS;
  static const ADD_VENDOR_INVENTORY = _Paths.ADD_VENDOR_INVENTORY;
  static const BIOMETRIC = _Paths.BIOMETRIC;
  static const JEWELLERY_SHOWCASE = _Paths.JEWELLERY_SHOWCASE;
  static const JEWELLERY_SHOWCASE_GRID = _Paths.JEWELLERY_SHOWCASE_GRID;
  static const WEB_REGISTRATION = _Paths.WEB_REGISTRATION;
}

abstract class _Paths {
  static const HOME = '/home';
  static const SIGN_IN = '/sign-in';
  static const LOGIN = '/login';
  static const FORGOT_PASSWORD = '/forgot-password';
  static const VERIFICATION_PHONE = '/verification-phone';
  static const RESET_PASSWORD = '/reset-password';
  static const NEW_COMPANY_REGISTRATION = '/new-company-registration';
  static const DASHBOARD = '/dashboard';
  static const BUY_REQUEST = '/buy-request';
  static const MY_ORDER_DETAIL = '/my-order-detail';
  static const MY_ORDER = '/my-order';
  static const NOTIFICATION = '/notification';
  static const WHISHLIST = '/whishlist';
  static const LEGAL_STATUS_ORGANIZATION = '/legal-status-organization';
  static const USA_PATRIOT_ACT = '/usa-patriot-act';
  static const TRADE_REFERENCES = '/trade-references';
  static const CREDIT_AGREEMENT = '/credit-agreement';
  static const REGISTER_PHONE = '/register-phone';
  static const REGISTER_EMAIL = '/register-email';
  static const REGISTER_CREATE_PASSWORD = '/register-create-password';
  static const CART = '/cart';
  static const RETURN_ORDER = '/return-order';
  static const DIAMOND_DETAILS = '/diamond-details';
  static const CART_FIRST_ADDRESS = '/cart-first-address';
  static const CART_SECOND_ADDRESS = '/cart-second-address';
  static const PROFILE = '/profile';
  static const CHANGE_PASSWORD = '/change-password';
  static const MY_ACCOUNT = '/my-account';
  static const GET_FILTER = '/get-filter';
  static const GET_FILTER_UPDATED = '/get-filter-updated';
  static const LISTING = '/listing';
  static const INQUIRIES = '/inquiries';
  static const INQUIRY_MESSAGES = '/inquiry-messages';
  static const PRIVACY_POLICY = '/privacy-policy';
  static const COMPULARY_UPDATE = '/compulary-update';
  static const MY_ADDRESS = '/my-address';
  static const EDIT_ADDRESS = '/edit-address';
  static const BUY_REQUEST_DETAILS = '/buy-request-details';
  static const CONNECTIVITY_SERVICE = '/connectivity-service';
  static const CREDIT_CARD_PAYMENT = '/credit-card-payment';
  static const VIEW_ALL = '/view-all';
  static const STOCK_OFFERS = '/stock-offers';
  static const OFFER_DETAILS = '/offer-details';
  static const CREATE_VENDOR = '/create-vendor';
  static const VENDOR_LOGIN = '/vendor-login';
  static const VENDOR_DASHBOARD = '/vendor-dashboard';
  static const VENDOR_STOCK_OFFERES = '/vendor-stock-offeres';
  static const VENDER_OFFER_DETAILS = '/vender-offer-details';
  static const VENDOR_STOCK_OFFER_DETAILS = '/vendor-stock-offer-details';
  static const JEWELLERY_CATEGORY = '/jewellery-category';
  static const JEWELLERY_SUB_CATEGORY = '/jewellery-sub-category';
  static const JEWELLERIES = '/jewelleries';
  static const JEWELLERY_DETAILS = '/jewellery-details';
  static const CART_UPDATED = '/cart-updated';
  static const MELEE = '/melee';
  static const VENDOR_ACCOUNT = '/vendor-account';
  static const VENDOR_NOTIFICATION = '/vendor-notification';
  static const VENDOR_INVENTORY = '/vendor-inventory_temp';
  static const VENDOR_DIAMOND_DETAILS = '/vendor-diamond-details';
  static const HOLD_REQUEST = '/hold-request';
  static const VENDOR_ORDER = '/vendor-order';
  static const VENDOR_RETURN_ORDER = '/vendor-return-order';
  static const RETURNED_DIAMONDS = '/returned-diamonds';
  static const ADD_VENDOR_INVENTORY = '/add-vendor-inventory_temp';
  static const BIOMETRIC = '/biometric';
  static const JEWELLERY_SHOWCASE = '/jewellery-showcase';
  static const JEWELLERY_SHOWCASE_GRID = '/jewellery-showcase-grid';
  static const WEB_REGISTRATION = '/web-registration';
  static const APP_LINK_DIAMOND = '/d';
  static const APP_LINK_JEWELLERY = '/j';
}
