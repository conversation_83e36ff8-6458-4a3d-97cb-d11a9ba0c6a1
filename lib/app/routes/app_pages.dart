import 'package:get/get.dart';

import '../data/local/user_provider.dart';
import '../modules/biometric/bindings/biometric_binding.dart';
import '../modules/biometric/views/biometric_view.dart';
import '../modules/buy_request/bindings/buy_request_binding.dart';
import '../modules/buy_request/views/buy_request_view.dart';
import '../modules/buy_request_details/bindings/buy_request_details_binding.dart';
import '../modules/buy_request_details/views/buy_request_details_view.dart';
import '../modules/cart/bindings/cart_binding.dart';
import '../modules/cart/views/cart_view.dart';
import '../modules/cart_first_address/bindings/cart_first_address_binding.dart';
import '../modules/cart_first_address/views/cart_first_address_view.dart';
import '../modules/cart_second_address/bindings/cart_second_address_binding.dart';
import '../modules/cart_second_address/views/cart_second_address_view.dart';
import '../modules/cart_updated/bindings/cart_updated_binding.dart';
import '../modules/cart_updated/views/cart_updated_view.dart';
import '../modules/change_password/bindings/change_password_binding.dart';
import '../modules/change_password/views/change_password_view.dart';
import '../modules/compulary_update/bindings/compulary_update_binding.dart';
import '../modules/compulary_update/views/compulary_update_view.dart';
import '../modules/connectivity_service/bindings/connectivity_service_binding.dart';
import '../modules/connectivity_service/views/connectivity_service_view.dart';
import '../modules/credit_agreement/bindings/credit_agreement_binding.dart';
import '../modules/credit_agreement/views/credit_agreement_view.dart';
import '../modules/credit_card_payment/bindings/credit_card_payment_binding.dart';
import '../modules/credit_card_payment/views/credit_card_payment_view.dart';
import '../modules/dashboard/bindings/dashboard_binding.dart';
import '../modules/dashboard/views/dashboard_view.dart';
import '../modules/diamond_details/bindings/diamond_details_binding.dart';
import '../modules/diamond_details/controllers/diamond_args.dart';
import '../modules/diamond_details/views/diamond_details_view.dart';
import '../modules/edit_address/bindings/edit_address_binding.dart';
import '../modules/edit_address/views/edit_address_view.dart';
import '../modules/forgot_password/bindings/forgot_password_binding.dart';
import '../modules/forgot_password/views/forgot_password_view.dart';
import '../modules/get_filter/bindings/get_filter_binding.dart';
import '../modules/get_filter/views/get_filter_view.dart';
import '../modules/get_filter/views/get_filter_view_updated.dart';
import '../modules/inquiries/bindings/inquiries_binding.dart';
import '../modules/inquiries/views/inquiries_view.dart';
import '../modules/inquiry_messages/bindings/inquiry_messages_binding.dart';
import '../modules/inquiry_messages/views/inquiry_messages_view.dart';
import '../modules/jewelleries/bindings/jewelleries_binding.dart';
import '../modules/jewelleries/views/jewelleries_view.dart';
import '../modules/jewellery_category/bindings/jewellery_category_binding.dart';
import '../modules/jewellery_category/views/jewellery_category_view.dart';
import '../modules/jewellery_details/bindings/jewellery_details_binding.dart';
import '../modules/jewellery_details/views/jewellery_details_view.dart';
import '../modules/jewellery_showcase/bindings/jewellery_showcase_binding.dart';
import '../modules/jewellery_showcase/bindings/jewellery_showcase_grid_binding.dart';
import '../modules/jewellery_showcase/views/jewellery_showcase_grid_view.dart';
import '../modules/jewellery_showcase/views/jewellery_showcase_view.dart';
import '../modules/jewellery_sub_category/bindings/jewellery_sub_category_binding.dart';
import '../modules/jewellery_sub_category/views/jewellery_sub_category_view.dart';
import '../modules/legal_status_organization/bindings/legal_status_organization_binding.dart';
import '../modules/legal_status_organization/views/legal_status_organization_view.dart';
import '../modules/listing/bindings/marketplace_binding.dart';
import '../modules/listing/views/marketplace_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/melee/bindings/melee_binding.dart';
import '../modules/melee/views/melee_view.dart';
import '../modules/my_account/bindings/my_account_binding.dart';
import '../modules/my_account/views/my_account_view.dart';
import '../modules/my_address/bindings/my_address_binding.dart';
import '../modules/my_address/views/my_address_view.dart';
import '../modules/my_order/bindings/my_order_binding.dart';
import '../modules/my_order/views/my_order_view.dart';
import '../modules/my_order_detail/bindings/my_order_detail_binding.dart';
import '../modules/my_order_detail/views/my_order_detail_view.dart';
import '../modules/new_company_registration/bindings/new_company_registration_binding.dart';
import '../modules/new_company_registration/views/new_company_registration_view.dart';
import '../modules/notification/bindings/notification_binding.dart';
import '../modules/notification/views/notification_view.dart';
import '../modules/offer_details/bindings/offer_details_binding.dart';
import '../modules/offer_details/views/offer_details_view.dart';
import '../modules/privacy_policy/bindings/privacy_policy_binding.dart';
import '../modules/privacy_policy/views/privacy_policy_view.dart';
import '../modules/profile/bindings/profile_binding.dart';
import '../modules/profile/views/profile_view.dart';
import '../modules/register_create_password/bindings/register_create_password_binding.dart';
import '../modules/register_create_password/views/register_create_password_view.dart';
import '../modules/register_email/bindings/register_email_binding.dart';
import '../modules/register_email/views/register_email_view.dart';
import '../modules/register_phone/bindings/register_phone_binding.dart';
import '../modules/register_phone/views/register_phone_view.dart';
import '../modules/reset_password/bindings/reset_password_binding.dart';
import '../modules/reset_password/views/reset_password_view.dart';
import '../modules/return_order/bindings/return_order_binding.dart';
import '../modules/return_order/views/return_order_view.dart';
import '../modules/returned_diamonds/bindings/returned_diamonds_binding.dart';
import '../modules/returned_diamonds/views/returned_diamonds_view.dart';
import '../modules/stock_offers/bindings/stock_offers_binding.dart';
import '../modules/stock_offers/views/stock_offers_view.dart';
import '../modules/trade_references/bindings/trade_references_binding.dart';
import '../modules/trade_references/views/trade_references_view.dart';
import '../modules/usa_patriot_act/bindings/usa_patriot_act_binding.dart';
import '../modules/usa_patriot_act/views/usa_patriot_act_view.dart';
import '../modules/vendor/add_vendor_inventory/bindings/add_vendor_inventory_binding.dart';
import '../modules/vendor/add_vendor_inventory/views/add_vendor_inventory_view.dart';
import '../modules/vendor/create_vendor/bindings/create_vendor_binding.dart';
import '../modules/vendor/create_vendor/views/create_vendor_view.dart';
import '../modules/vendor/hold_request/bindings/hold_request_binding.dart';
import '../modules/vendor/hold_request/views/hold_request_view.dart';
import '../modules/vendor/vendor_account/bindings/vendor_account_binding.dart';
import '../modules/vendor/vendor_account/views/vendor_account_view.dart';
import '../modules/vendor/vendor_dashboard/bindings/vendor_dashboard_binding.dart';
import '../modules/vendor/vendor_dashboard/views/vendor_dashboard_view.dart';
import '../modules/vendor/vendor_diamond_details/bindings/vendor_diamond_details_binding.dart';
import '../modules/vendor/vendor_diamond_details/views/vendor_diamond_details_view.dart';
import '../modules/vendor/vendor_inventory/bindings/vendor_inventory_binding.dart';
import '../modules/vendor/vendor_inventory/views/vendor_inventory_view.dart';
import '../modules/vendor/vendor_login/bindings/vendor_login_binding.dart';
import '../modules/vendor/vendor_login/views/vendor_login_view.dart';
import '../modules/vendor/vendor_notification/bindings/vendor_notification_binding.dart';
import '../modules/vendor/vendor_notification/views/vendor_notification_view.dart';
import '../modules/vendor/vendor_order/bindings/vendor_order_binding.dart';
import '../modules/vendor/vendor_order/views/vendor_order_view.dart';
import '../modules/vendor/vendor_return_order/bindings/vendor_return_order_binding.dart';
import '../modules/vendor/vendor_return_order/views/vendor_return_order_view.dart';
import '../modules/vendor/vendor_stock_offer_details/bindings/vendor_stock_offer_details_binding.dart';
import '../modules/vendor/vendor_stock_offer_details/views/vendor_stock_offer_details_view.dart';
import '../modules/vendor/vendor_stock_offeres/bindings/vendor_stock_offeres_binding.dart';
import '../modules/vendor/vendor_stock_offeres/views/vendor_stock_offeres_view.dart';
import '../modules/verification_phone/bindings/verification_phone_binding.dart';
import '../modules/verification_phone/views/verification_phone_view.dart';
import '../modules/view_all/bindings/view_all_binding.dart';
import '../modules/view_all/views/view_all_view.dart';
import '../modules/web_registration/bindings/web_registration_binding.dart';
import '../modules/web_registration/views/web_registration_view.dart';
import '../modules/whishlist/bindings/whishlist_binding.dart';
import '../modules/whishlist/views/whishlist_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static String INITIAL = UserProvider.initialPage;

  static final routes = [
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.FORGOT_PASSWORD,
      page: () => const ForgotPasswordView(),
      binding: ForgotPasswordBinding(),
    ),
    GetPage(
      name: _Paths.VERIFICATION_PHONE,
      page: () => VerificationPhoneView(),
      binding: VerificationPhoneBinding(),
    ),
    GetPage(
      name: _Paths.DASHBOARD,
      page: () => DashboardView(),
      binding: DashboardBinding(),
    ),
    GetPage(
      name: _Paths.APP_LINK_DIAMOND,
      page: () => DashboardView(),
      binding: DashboardBinding(),
    ),
    GetPage(
      name: _Paths.APP_LINK_JEWELLERY,
      page: () => DashboardView(),
      binding: DashboardBinding(),
    ),
    GetPage(
      name: _Paths.RESET_PASSWORD,
      page: () => const ResetPasswordView(),
      binding: ResetPasswordBinding(),
    ),
    GetPage(
      name: _Paths.NEW_COMPANY_REGISTRATION,
      page: () => NewCompanyRegistrationView(),
      binding: NewCompanyRegistrationBinding(),
    ),
    GetPage(
      name: _Paths.BUY_REQUEST,
      page: () => const BuyRequestView(),
      binding: BuyRequestBinding(),
    ),
    GetPage(
      name: _Paths.MY_ORDER_DETAIL,
      page: () => const MyOrderDetailView(),
      binding: MyOrderDetailBinding(),
    ),
    GetPage(
      name: _Paths.MY_ORDER,
      page: () => const MyOrderView(),
      binding: MyOrderBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.WHISHLIST,
      page: () => const WhishlistView(),
      binding: WhishlistBinding(),
    ),
    GetPage(
      name: _Paths.LEGAL_STATUS_ORGANIZATION,
      page: () => const LegalStatusOrganizationView(),
      binding: LegalStatusOrganizationBinding(),
    ),
    GetPage(
      name: _Paths.USA_PATRIOT_ACT,
      page: () => const UsaPatriotActView(),
      binding: UsaPatriotActBinding(),
    ),
    GetPage(
      name: _Paths.TRADE_REFERENCES,
      page: () => const TradeReferencesView(),
      binding: TradeReferencesBinding(),
    ),
    GetPage(
      name: _Paths.CREDIT_AGREEMENT,
      page: () => const CreditAgreementView(),
      binding: CreditAgreementBinding(),
    ),
    GetPage(
      name: _Paths.REGISTER_PHONE,
      page: () => const RegisterPhoneView(),
      binding: RegisterPhoneBinding(),
    ),
    GetPage(
      name: _Paths.REGISTER_EMAIL,
      page: () => const RegisterEmailView(),
      binding: RegisterEmailBinding(),
    ),
    GetPage(
      name: _Paths.REGISTER_CREATE_PASSWORD,
      page: () => const RegisterCreatePasswordView(),
      binding: RegisterCreatePasswordBinding(),
    ),
    GetPage(
      name: _Paths.CART,
      page: () => const CartView(),
      binding: CartBinding(),
    ),
    GetPage(
      name: _Paths.RETURN_ORDER,
      page: () => const ReturnOrderView(),
      binding: ReturnOrderBinding(),
    ),
    GetPage(
      name: _Paths.DIAMOND_DETAILS,
      page: () => DiamondDetailsView(
        viewTag: 'view_diamond_${(Get.arguments as DiamondArgs).id}',
      ),
      binding: DiamondDetailsBinding(),
    ),
    GetPage(
      name: _Paths.CART_FIRST_ADDRESS,
      page: () => const CartFirstAddressView(),
      binding: CartFirstAddressBinding(),
    ),
    GetPage(
      name: _Paths.CART_SECOND_ADDRESS,
      page: () => const CartSecondAddressView(),
      binding: CartSecondAddressBinding(),
    ),
    GetPage(
      name: _Paths.GET_FILTER_UPDATED,
      page: () => GetFilterViewUpdated(
        viewTag: 'filter_${DateTime.timestamp().millisecondsSinceEpoch}',
      ),
      binding: GetFilterBinding(),
    ),
    GetPage(
      name: _Paths.GET_FILTER,
      page: () => GetFilterView(
        viewTag: 'filter_${DateTime.timestamp().millisecondsSinceEpoch}',
      ),
      binding: GetFilterBinding(),
    ),
    GetPage(
      name: _Paths.LISTING,
      page: () => MarketplaceView(
        viewTag: 'marketplace_${DateTime.timestamp().millisecondsSinceEpoch}',
      ),
      binding: MarketplaceBinding(),
    ),
    GetPage(
      name: _Paths.INQUIRIES,
      page: () => const InquiriesView(),
      binding: InquiriesBinding(),
    ),
    GetPage(
      name: _Paths.INQUIRY_MESSAGES,
      page: () => const InquiryMessagesView(),
      binding: InquiryMessagesBinding(),
    ),
    GetPage(
      name: _Paths.PRIVACY_POLICY,
      page: () => const PrivacyPolicyView(),
      binding: PrivacyPolicyBinding(),
    ),
    GetPage(
      name: _Paths.COMPULARY_UPDATE,
      page: () => const CompularyUpdateView(),
      binding: CompularyUpdateBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE,
      page: () => ProfileView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: _Paths.CHANGE_PASSWORD,
      page: () => const ChangePasswordView(),
      binding: ChangePasswordBinding(),
    ),
    GetPage(
      name: _Paths.MY_ACCOUNT,
      page: () => MyAccountView(),
      binding: MyAccountBinding(),
    ),
    GetPage(
      name: _Paths.MY_ADDRESS,
      page: () => const MyAddressView(),
      binding: MyAddressBinding(),
    ),
    GetPage(
      name: _Paths.EDIT_ADDRESS,
      page: () => const EditAddressView(),
      binding: EditAddressBinding(),
    ),
    GetPage(
      name: _Paths.BUY_REQUEST_DETAILS,
      page: () => const BuyRequestDetailsView(),
      binding: BuyRequestDetailsBinding(),
    ),
    GetPage(
      name: _Paths.CONNECTIVITY_SERVICE,
      page: () => const ConnectivityServiceView(),
      binding: ConnectivityServiceBinding(),
    ),
    GetPage(
      name: _Paths.CREDIT_CARD_PAYMENT,
      page: () => const CreditCardPaymentView(),
      binding: CreditCardPaymentBinding(),
    ),
    GetPage(
      name: _Paths.VIEW_ALL,
      page: () => const ViewAllView(),
      binding: ViewAllBinding(),
    ),
    GetPage(
      name: _Paths.STOCK_OFFERS,
      page: () => const StockOffersView(),
      binding: StockOffersBinding(),
    ),
    GetPage(
      name: _Paths.OFFER_DETAILS,
      page: () => const OfferDetailsView(),
      binding: OfferDetailsBinding(),
    ),
    GetPage(
      name: _Paths.CREATE_VENDOR,
      page: () => const CreateVendorView(),
      binding: CreateVendorBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_LOGIN,
      page: () => const VendorLoginView(),
      binding: VendorLoginBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_DASHBOARD,
      page: () => const VendorDashboardView(),
      binding: VendorDashboardBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_STOCK_OFFERES,
      page: () => VendorStockOffersView(),
      binding: VendorStockOffersBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_STOCK_OFFER_DETAILS,
      page: () => const VendorStockOfferDetailsView(),
      binding: VendorStockOfferDetailsBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERY_CATEGORY,
      page: () => const JewelleryCategoryView(),
      binding: JewelleryCategoryBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERY_SUB_CATEGORY,
      page: () => const JewellerySubCategoryView(),
      binding: JewellerySubCategoryBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERIES,
      page: () => const JewelleriesView(),
      binding: JewelleriesBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERY_DETAILS,
      page: () => JewelleryDetailsView(
        viewTag: 'jewellery_${DateTime.timestamp().millisecondsSinceEpoch}',
      ),
      binding: JewelleryDetailsBinding(),
    ),
    GetPage(
      name: _Paths.CART_UPDATED,
      page: () => const CartUpdatedView(),
      binding: CartUpdatedBinding(),
    ),
    GetPage(
      name: _Paths.MELEE,
      page: () => const MeleeView(),
      binding: MeleeBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_ACCOUNT,
      page: () => const VendorAccountView(),
      binding: VendorAccountBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_NOTIFICATION,
      page: () => const VendorNotificationView(),
      binding: VendorNotificationBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_INVENTORY,
      page: () => const VendorInventoryView(),
      binding: VendorInventoryBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_DIAMOND_DETAILS,
      page: () => VendorDiamondDetailsView(
        viewTag: 'view_diamond_${(Get.arguments as DiamondArgs).id}',
      ),
      binding: VendorDiamondDetailsBinding(),
    ),
    GetPage(
      name: _Paths.HOLD_REQUEST,
      page: () => const HoldRequestView(),
      binding: HoldRequestBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_ORDER,
      page: () => const VendorOrderView(),
      binding: VendorOrderBinding(),
    ),
    GetPage(
      name: _Paths.VENDOR_RETURN_ORDER,
      page: () => const VendorReturnOrderView(),
      binding: VendorReturnOrderBinding(),
    ),
    GetPage(
      name: _Paths.RETURNED_DIAMONDS,
      page: () => const ReturnedDiamondsView(),
      binding: ReturnedDiamondsBinding(),
    ),
    GetPage(
      name: _Paths.ADD_VENDOR_INVENTORY,
      page: () => const AddVendorInventoryView(),
      binding: AddVendorInventoryBinding(),
    ),
    GetPage(
      name: _Paths.BIOMETRIC,
      page: () => const BiometricView(),
      binding: BiometricBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERY_SHOWCASE,
      page: () => const JewelleryShowcaseView(),
      binding: JewelleryShowcaseBinding(),
    ),
    GetPage(
      name: _Paths.JEWELLERY_SHOWCASE_GRID,
      page: () => const JewelleryShowcaseGridView(),
      binding: JewelleryShowcaseGridBinding(),
    ),
    GetPage(
      name: _Paths.WEB_REGISTRATION,
      page: () => WebRegistrationView(),
      binding: WebRegistrationBinding(),
    ),
  ];
}
