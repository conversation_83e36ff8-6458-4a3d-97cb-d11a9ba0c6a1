import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/static_pages/static_pages.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// stock provider
class StaticPagesProvider {
  /// get user details
  static Future<StaticPages?> getStaticPage(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.staticPageDetails,
        params: map,
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return StaticPages.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }
}
