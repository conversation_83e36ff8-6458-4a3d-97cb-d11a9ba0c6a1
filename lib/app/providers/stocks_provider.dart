import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// stock provider
class StockProvider {
  /// get user details
  static Future<List<DiamondEntity>> getStockStatus(
      Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.post(
          path: ApiConstants.userStockStatus,
          data: FormData.fromMap(map, ListFormat.multiCompatible));
      if ((response?.isOk ?? false) && response?.data != null) {
        return List.from((response?.data!['data'] as List)
            .map((e) => DiamondEntity.fromJson(e)));
      }
      return [];
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// list return orders
  static Future<List<ReturnOrder>> listReturnOrders({
    int? skip,
    int? limit,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.returnOrder,
        params: {
          'skip': skip ?? 0,
          'limit': limit ?? 10,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return List.from((response?.data!['data'] as List)
            .map((e) => ReturnOrder.fromJson(e)));
      }
      return [];
    } on DioException catch (e) {
      rethrow;
    }
  }
}
