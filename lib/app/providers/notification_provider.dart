import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/notification_model/notification_model.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// BannerSliderImages
class NotificationProvider {
  /// user order Details
  static Future<List<NotificationModel>> notificationList({
    required int skip,
    required int limit,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.notification,
        params: {
          'skip': skip,
          'limit': limit,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        final List<dynamic>? data = response?.data?['data'];
        if (data != null) {
          return data.map((e) => NotificationModel.fromJson(e)).toList();
        }
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// notification unread count
  static Future<int> notificationUnReadCount() async {
    try {
      final Response<Map<String, dynamic>?>? response =
          await APIService.get(path: ApiConstants.notificationUnreadCount);
      if ((response?.isOk ?? false) && response?.data != null) {
        return response?.data?['data'] ?? 0;
      }
      return 0;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// read notification
  static Future<void> readNotification({
    String? notificationId,
    bool? readAll,
  }) async {
    try {
      await APIService.put(
        path: ApiConstants.readNotification,
        params: {
          if (notificationId != null) 'id': notificationId,
          if (readAll != null) 'read_all': readAll
        },
      );
    } on DioException catch (_) {
      rethrow;
    }
  }
}
