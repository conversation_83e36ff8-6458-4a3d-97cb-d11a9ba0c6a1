import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/jewellery_showcase/jewellery_showcase.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// jewellery showcase provider
class JewelleryShowcaseProvider {
  /// jewellery showcase
  static Future<List<JewelleryShowcase>> getJewelleryShowcaseVideos({
    required int skip,
    required int limit,
    bool? isLiked,
    String? tag,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.jewelleryShowcase,
        params: {
          'skip': skip,
          'limit': limit,
          if (isLiked != null) 'is_liked': isLiked,
          if (tag != null) 'tag': tag,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        final List<dynamic>? data = response?.data?['data'];
        if (data != null) {
          return data.map((e) => JewelleryShowcase.fromJson(e)).toList();
        }
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// add remove like jewellery showcase
  static Future<bool> addRemoveLikeJewellery({
    required String id,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: ApiConstants.addRemoveLikeJewelleryShowcase,
        params: {
          'id': id,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// list jewellery tags
  static Future<List<String>> listJewelleryTags() async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.listJewelleryTags,
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return response?.data?['data'] != null
            ? List<String>.from(response?.data?['data'])
            : [];
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// add Seen JewelleryShowcase
  static Future<void> addSeenJewellery(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: ApiConstants.addSeenJewelleryShowcase,
        data: FormData.fromMap(map, ListFormat.multiCompatible),
      );
      if ((response?.isOk ?? false) && response?.data != null) {}
    } on DioException catch (_) {
      rethrow;
    }
  }
}
