import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/alias_configs/alias_configs.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// alias configs provider

class AliasConfigsProvider {
  /// alias configs
  static Future<AliasConfigs?> getAliasConfigs() async {
    try {
      final Response<Map<String, dynamic>?>? response =
          await APIService.get(path: ApiConstants.aliasConfig);
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] != null) {
          return AliasConfigs.fromJson(response?.data?['data']);
        }
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }
}
