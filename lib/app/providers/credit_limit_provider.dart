import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/credit_limit/credit_limit.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// credit limit provider
class CreditLimitProvider {
  /// register user
  static Future<bool> addCreditLimit(Map<String, dynamic> map) async {
    try {
      // Register user
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.userCreditHistory,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// user available credit limit
  static Future<double> userAvailableCreditLimit() async {
    try {
      // Register user
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.userAvailableCredit,
          params: {'user_id': UserProvider.currentUser?.id});
      if ((response?.isOk ?? false) && response?.data != null) {
        return double.parse((response?.data?['data'] ?? 0).toString());
      }
      return 0;
    } on DioException catch (e) {
      rethrow;
    }
  }
}
