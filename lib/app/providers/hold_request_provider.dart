import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/hold_request/hold_request.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// Hold request provider
class HoldRequestProvider {
  /// Get hold request
  static Future<List<HoldRequest>> getHoldRequest({
    required String status,
    required int skipLimit,
    required int page,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path:
            '${ApiConstants.kUrlPrefix}/auth/vendor-buy-request?skip=$skipLimit&limit=$page&status=$status',
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return List.from(response?.data?['data'])
            .map((e) => HoldRequest.fromJson(e))
            .toList();
      }
      return [];
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// vendor stock approval
  static Future<void> vendorStockApproval(Map<String, dynamic> map) async {
    try {
      // Register user
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: '${ApiConstants.kUrlPrefix}/auth/vendor-stock-approval',
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return;
      }
      return;
    } on DioException catch (e) {
      logE('Error in vendor stock approval: $e');
      rethrow;
    }
  }
}
