import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/notification_model/notification_model.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/models/user/admin_user_entity.dart';
import 'package:diamond_company_app/app/data/models/vendor_dashboard/vendor_kpidata_model.dart';
import 'package:diamond_company_app/app/data/models/vendor_order/vendor_order.dart';
import 'package:diamond_company_app/app/data/models/vendor_return_order/vendor_return_order.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// Vendor provider
class VendorProvider {
  /// send verification otp
  static Future<int?> sendVerificationOtpVendor({
    String? email,
    String? phone,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: ApiConstants.sendVerificationVendor,
        data: FormData.fromMap(<String, dynamic>{
          if (email != null) 'email': email.toString().toLowerCase().trim(),
          if (phone != null) 'phone': phone,
        }),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return response?.data?['otp'];
      }
      return null;
    } on DioException catch (e) {
      logE(e);
      rethrow;
    }
  }

  /// reset password
  static Future<dynamic> resetVendorPassword(
      {required String otp,
      required String email,
      required String newPassword}) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: ApiConstants.vendorResetPassword,
        data: FormData.fromMap(
          <String, dynamic>{
            'email': email.toString().toLowerCase().trim(),
            'otp': otp,
            'new_password': newPassword,
          },
        ),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return newPassword;
      }
    } on DioException catch (e) {
      logE(e);
      rethrow;
    }
  }

  /// email verification
  static Future<Response<Map<String, dynamic>?>?> vendorEmailVerification(
      Map<String, dynamic> map) async {
    try {
      // Register user
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: ApiConstants.vendorVerifyEmail,
        data: FormData.fromMap(map),
      );
      return response;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// Get kpi data
  static Future<KpiData?> getKpiData() async {
    try {
      KpiData? kpiData;
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.vendorKpi,
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        kpiData = KpiData.fromJson(response!.data!['data']);
      }
      return kpiData ?? KpiData();
    } on DioException catch (e) {
      logE('Error in getKpiData: $e');
      rethrow;
    }
  }

  /// Get hold request
  static Future<StockOffer?> getHoldRequest({
    required String status,
    required int skipLimit,
    required int page,
  }) async {
    try {
      StockOffer? holdRequestData;
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path:
            '${ApiConstants.kUrlPrefix}/auth/vendor-buy-request?skip=$skipLimit&limit=$page&status=$status',
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        holdRequestData = StockOffer.fromJson(response!.data!);
      }
      return holdRequestData ?? const StockOffer();
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// Get stock offer
  static Future<List<StockOffer>?> getStockOffers({
    required String status,
    required int skipLimit,
    required int page,
    bool isResponseRequired = false,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path:
            '${ApiConstants.kUrlPrefix}/auth/admin/stock-offer?skip=$skipLimit&limit=$page&status=$status&is_response_required=$isResponseRequired',
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return List<StockOffer>.from(
          (response?.data!['data'] as List).map(
            (e) => StockOffer.fromJson(e),
          ),
        );
      }
      return <StockOffer>[];
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// Update offer
  static Future<StockOffer?> updateOffer(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
          path: ApiConstants.vendorUpdateOffer,
          data: FormData.fromMap(map, ListFormat.multiCompatible));
      if ((response?.isOk ?? false) && response?.data != null) {
        return StockOffer.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// vendor Order
  static Future<List<VendorOrder>> vendorOrder({
    required String status,
    required int skipLimit,
    required int page,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: '${ApiConstants.vendorOrder}',
        params: {'skip': skipLimit, 'limit': page, 'status': status},
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return List<VendorOrder>.from(
          response?.data!['data'].map(
            (dynamic item) => VendorOrder.fromJson(item),
          ),
        );
      }
      return [];
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// vendor Order status change
  static Future<void> vendorOrderStatusChange(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: '${ApiConstants.vendorOrder}',
        data: FormData.fromMap(map, ListFormat.multiCompatible),
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return;
      }
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// vendor Order status change
  static Future<Map<String, dynamic>?> uploadVendorOrderInvoice(
      Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
        path: '${ApiConstants.vendorOrderInvoice}',
        data: FormData.fromMap(map, ListFormat.multiCompatible),
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return response?.data?['data'];
      }
      return null;
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// vendor return Order
  static Future<List<VendorReturnOrder>> vendorReturnOrder({
    required String status,
    required int skipLimit,
    required int page,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: '${ApiConstants.vendorReturnOrder}',
        params: {'skip': skipLimit, 'limit': page, 'list_vendor_stocks': true},
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return List<VendorReturnOrder>.from(
          response?.data!['data'].map(
            (dynamic item) => VendorReturnOrder.fromJson(item),
          ),
        );
      }
      return [];
    } on DioException catch (e) {
      logE('Error in get hold request data: $e');
      rethrow;
    }
  }

  /// Get offer details
  static Future<List<StockOffer>?> getOfferTrails({
    required String offerId,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.vendorOfferTrails,
        params: <String, dynamic>{'offer_id': offerId},
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        return List<StockOffer>.from(
          response?.data!['data'].map(
            (dynamic item) => StockOffer.fromJson(item),
          ),
        );
      }
      return <StockOffer>[];
    } on DioException catch (e) {
      logE('Error in getKpiData: $e');
      rethrow;
    }
  }

  /// Get user data
  static Future<AdminUserEntity?> getUserData() async {
    try {
      AdminUserEntity? userEntity;
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.getUserData,
      );

      if ((response?.isOk ?? false) && response?.data != null) {
        userEntity = AdminUserEntity.fromJson(response!.data!['data']);
      }
      return userEntity ?? AdminUserEntity();
    } on DioException catch (e) {
      logE('Error in get get user data admin: $e');
      rethrow;
    }
  }

  /// Vendor Notification
  static Future<List<NotificationModel>> notifications({
    required int skip,
    required int limit,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.vendorNotification,
        params: <String, dynamic>{
          'skip': skip,
          'limit': limit,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        final List<dynamic>? data = response?.data?['data'];
        if (data != null) {
          return data.map((e) => NotificationModel.fromJson(e)).toList();
        }
      }
      return <NotificationModel>[];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// Unread count
  static Future<int> notificationUnReadCount() async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.vendorNotificationUnreadCount);
      if ((response?.isOk ?? false) && response?.data != null) {
        return response?.data?['data'] ?? 0;
      }
      return 0;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// Read notification
  static Future<void> readNotification({
    String? notificationId,
    bool? readAll,
  }) async {
    try {
      await APIService.put(
        path: ApiConstants.readVendorNotification,
        params: <String, dynamic>{
          if (notificationId != null) 'id': notificationId,
          if (readAll != null) 'read_all': readAll
        },
      );
    } on DioException catch (_) {
      rethrow;
    }
  }
}
