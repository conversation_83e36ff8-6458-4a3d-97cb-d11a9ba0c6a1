import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

import '../data/models/cart_item/server_cart.dart';

/// cart provider
class CartProvider {
  /// list cart items
  static Future<List<ServerCart>> listCartItems() async {
    try {
      // add to cart
      final Response<Map<String, dynamic>?>? response =
          await APIService.get(path: ApiConstants.listCart);
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] == null) {
          return [];
        }
        return List<ServerCart>.from(
          response?.data?['data'].map((e) => ServerCart.fromJson(e)).toList(),
        );
      }
      return [];
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// add to cart
  static Future<bool> addToCart(Map<String, dynamic> map) async {
    try {
      // add to cart
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.addToCart,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// add to cart
  static Future<bool> updateCartItem(Map<String, dynamic> map) async {
    try {
      // add to cart
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.updateCartItem,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// remove from cart
  static Future<bool> removeFromCart(Map<String, dynamic> map) async {
    try {
      // add to cart
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.removeFromCart,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// remove all from cart
  static Future<bool> removeAllFromCart(Map<String, dynamic> map) async {
    try {
      // add to cart
      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.removeAllFromCart,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return true;
      }
      return false;
    } on DioException catch (e) {
      return false;
      //rethrow;
    }
  }
}
