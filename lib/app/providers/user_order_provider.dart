import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:dio/dio.dart';

import '../data/config/api_constants.dart';
import '../data/remote/api_service/init_api_service.dart';

/// UserOrderProvider
class UserOrderProvider {
  /// User Order
  static Future<List<UserOrder>> userOrder({
    required int skip,
    required int limit,
    String? orderStatus,
    String? paymentStatus,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response =
          await APIService.get(path: ApiConstants.userOrder, params: {
        'skip': skip,
        'limit': limit,
        if (orderStatus != null) 'order_status': orderStatus.toUpperCase(),
        if (paymentStatus != null)
          'payment_status': paymentStatus.toUpperCase(),
      });
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] != null) {
          return List<dynamic>.from(response?.data?['data'] as List)
              .map((e) => UserOrder.fromJson(e))
              .toList();
        }
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// User Unified Order
  static Future<List<UserOrder>> userUnifiedOrder({
    required int skip,
    required int limit,
    String? orderStatus,
    String? paymentStatus,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response =
          await APIService.get(path: ApiConstants.unifiedOrder, params: {
        'skip': skip,
        'limit': limit,
        if (orderStatus != null) 'order_status': orderStatus.toUpperCase(),
        if (paymentStatus != null)
          'payment_status': paymentStatus.toUpperCase(),
      });
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] != null) {
          return List<dynamic>.from(response?.data?['data'] as List)
              .map((e) => UserOrder.fromJson(e))
              .toList();
        }
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// user order Details
  static Future<UserOrder?> userOrderDetails(String id) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.userOrderDetails, params: {'id': id});
      if ((response?.isOk ?? false) && response?.data != null) {
        return UserOrder.fromJson(response!.data?['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// user unified order Details
  static Future<UserOrder?> userUnifiedOrderDetails(String id) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.userUnifiedOrderDetails, params: {'id': id});
      if ((response?.isOk ?? false) && response?.data != null) {
        return UserOrder.fromJson(response!.data?['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// user order Details
  static Future<void> returnOrder(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.post(
          path: ApiConstants.returnOrder, data: FormData.fromMap(map));
      if ((response?.isOk ?? false) && response?.data != null) {
        return;
      }
    } on DioException catch (_) {
      rethrow;
    }
  }
}
