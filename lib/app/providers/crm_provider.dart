import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/crm_model/crm_model.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// inquiries provider
class CrmProvider {
  /// user order Details
  static Future<CRMModel?> getCRMDetails({bool? isVendor}) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.crmDetails,
          params: {'is_vendor': isVendor?? UserProvider.isTypeVendor});
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] != null) {
          return CRMModel.fromJson(response?.data?['data']);
        }
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }
}
