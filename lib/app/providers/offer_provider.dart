import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// offer provide
class OfferProvider {
  /// create offer
  static Future<StockOffer?> createOffer(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.post(
          path: ApiConstants.createOffer,
          data: FormData.fromMap(map, ListFormat.multiCompatible));
      if ((response?.isOk ?? false) && response?.data != null) {
        return StockOffer.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// update offer
  static Future<StockOffer?> updateOffer(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.put(
          path: ApiConstants.updateOffer,
          data: FormData.fromMap(map, ListFormat.multiCompatible));
      if ((response?.isOk ?? false) && response?.data != null) {
        return StockOffer.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// stock offer
  static Future<List<StockOffer>> listStockOffers({
    int skip = 0,
    int limit = 10,
    String? status,
    bool? isCompleted,
    bool? isResponseRequired,
  }) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.listStockOffer,
        params: {
          if (isCompleted ?? false) 'skip': skip,
          if (isCompleted ?? false) 'limit': limit,
          if (status != null) 'status': status,
          if (isCompleted != null) 'is_completed': isCompleted,
          if (isResponseRequired != null)
            'is_response_required': isResponseRequired,
        },
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return List.from((response?.data!['data'] as List)
            .map((e) => StockOffer.fromJson(e)));
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// place order
  static Future<UserOrder?> placeOrder(Map<String, dynamic> map) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.post(
          path: ApiConstants.placeOrder,
          data: FormData.fromMap(map, ListFormat.multiCompatible));
      if ((response?.isOk ?? false) && response?.data != null) {
        return UserOrder.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// offer trails
  static Future<List<StockOffer>> offerTrails({required String offerId}) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.offerTrails, params: {'offer_id': offerId});
      if ((response?.isOk ?? false) && response?.data != null) {
        return List.from(
            response?.data!['data'].map((item) => StockOffer.fromJson(item)));
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }

  /// offer details
  static Future<StockOffer?> offerDetails({required String offerId}) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
          path: ApiConstants.offerDetails, params: {'offer_id': offerId});
      if ((response?.isOk ?? false) && response?.data != null) {
        return StockOffer.fromJson(response?.data!['data']);
      }
      return null;
    } on DioException catch (_) {
      rethrow;
    }
  }
}
