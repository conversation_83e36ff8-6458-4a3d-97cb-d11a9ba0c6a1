import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/models/inquiry/inquiry.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:dio/dio.dart';

/// inquiries provider
class InquiriesProvider {
  /// user order Details
  static Future<List<InquiryData>> listInquiries() async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.getUserInquiry,
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        if (response?.data?['data'] != null) {
          return List.from((response?.data?['data'] as List)
              .map((e) => InquiryData.fromJson(e)));
        }
      }
      return [];
    } on DioException catch (_) {
      rethrow;
    }
  }
}
