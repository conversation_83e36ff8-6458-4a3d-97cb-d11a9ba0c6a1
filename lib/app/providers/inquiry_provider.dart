import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/local_store.dart';
import 'package:diamond_company_app/app/data/models/inquiry/inquiry.dart';
import 'package:dio/dio.dart';

import '../data/remote/api_service/init_api_service.dart';

class InquiryProvider {
  static Future<List<InquiryData>> getInquiryList(
      {Map<String, dynamic>? params}) async {
    try {
      final Response<Map<String, dynamic>?>? response = await APIService.get(
        path: ApiConstants.getUserInquiry,
        baseUrl: ApiConstants.kSocketUrl,
        params: {'user_id': LocalStore.userId(), ...params ?? {}},
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return (response?.data?['data'] as List<dynamic>?)
                ?.map((dynamic e) =>
                    InquiryData.fromJson(e as Map<String, dynamic>))
                .toList() ??
            [];
      }
      return [];
    } on DioException catch (e) {
      rethrow;
    }
  }

  /// create user inquiry
  static Future<void> createUserInquiry(
      {required Map<String, dynamic> map}) async {
    try {
      logI('createUserInquiry');
      logI(map);

      final Response<Map<String, dynamic>?>? response = await APIService.post(
        path: ApiConstants.createUserInquiry,
        data: FormData.fromMap(map),
      );
      if ((response?.isOk ?? false) && response?.data != null) {
        return;
      }
      return;
    } on DioException catch (e) {
      rethrow;
    }
  }
}
