import 'dart:async';

import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/modules/my_account/controllers/my_account_controller.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/year_picker.dart';
import 'package:diamond_company_app/app/utils/address_utils.dart';
import 'package:diamond_company_app/app/utils/debouncer.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:diamond_company_app/app/utils/state_utils.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';

/// NewCompanyRegistrationController
class NewCompanyRegistrationController extends GetxController {
  /// UserEntity
  Rx<UserEntity> userEntity = const UserEntity().obs;

  /// isUpdate
  RxBool isEdit = false.obs;

  /// isFromBuyRequest
  RxBool isFromBuyRequest = false.obs;

  /// isForChangePhysical
  RxBool isForChangePhysical = false.obs;

  /// isForChangeMailing
  RxBool isForChangeMailing = false.obs;

  final Debouncer debouncer = Debouncer(milliseconds: 500);

  /// Options for Anti money laundering
  List<String> antiMoneyLaundering = <String>[
    LocaleKeys.new_company_registration_yes.tr,
    LocaleKeys.new_company_registration_no.tr
  ];

  /// On Init
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      // userEntity(Get.arguments['companyDetails']);
      isEdit(Get.arguments['isEdit']);
      isFromBuyRequest(Get.arguments['isFromBuyRequest']);
    }
    loadJson();
    if (isEdit()) {
      selectBusinessTypeList(
          AuthProvider.userEntity().typeOfBusiness?.split(','));

      if (AuthProvider.userEntity().hasAmLprogram == true) {
        selectProgramIndex(0);
      } else if (AuthProvider.userEntity().hasAmLprogram == false) {
        selectProgramIndex(1);
      }

      isCheck(AuthProvider.userEntity().isAddressSame ?? false);
    }
  }

  @override
  void onReady() {
    super.onReady();

    if (fbKey.currentState?.validate(focusOnInvalid: false) ?? false) {
      isNextDisabled(false);
    } else {
      isNextDisabled(true);
    }
  }

  Future<void> setIntialFiled() async {
    selectedCountryData = filterCoutryFromJson(Country.parse(
            AuthProvider.userEntity().billingAddress?.country ?? '')) ??
        {};
    selectedMailingAddressCountryData = filterCoutryFromJson(Country.parse(
          AuthProvider.userEntity().shippingAddress?.country ?? '',
        )) ??
        {};
    setPhysicalStateList();
    setMailingState();
  }

  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// CompanyExpansionTile
  RxBool companyExpansionTile = true.obs;

  /// PhysicalAddressExpansionTile
  RxBool physicalAddressExpansionTile = true.obs;

  /// MailingAddressExpansionTile
  RxBool mailingAddressExpansionTile = true.obs;

  /// ContactInfoExpansionTile
  RxBool contactInfoExpansionTile = true.obs;

  /// IsCheck
  RxBool isCheck = false.obs;

  /// IsCheck
  RxBool isLoading = false.obs;

  /// isNextDisabled
  RxBool isNextDisabled = true.obs;

  /// isPhoneAvailable
  RxBool isPhoneAvailable = true.obs;

  /// isEmailAvailable
  RxBool isEmailAvailable = true.obs;

  /// phone Prefix
  RxString phoneCountryCode = ''.obs;

  /// SelectedVal
  RxString mobileCountryCode = ''.obs;

  RxString city = ''.obs;
  RxString mailingCity = ''.obs;

  List<String> stateList = [];

  /// me
  List<String> mailingAddressStateList = [];

  /// selected Country code for phone
  void selectPhoneCountryCode(Country country) {
    phoneCountryCode(country.phoneCode);
  }

  /// selected Country code for mobile
  void selectMobileCountryCode(Country country) {
    mobileCountryCode(country.phoneCode);
  }

  /// selected Country
  void selectedCountryForPhysicalAddress(Country country) {
    selectCountryForPhysicalAddress.value = country.name;
    countryPhoneCode(country.phoneCode);
  }

  /// select Country For Mailing Address
  void selectedCountryForMailingAddress(Country country) {
    selectCountryForMailingAddress.value = country.name;
  }

  /// SelectProgramIndex
  RxInt selectProgramIndex = (-1).obs;

  /// SelectBusinessIndex
  RxList<String> selectBusinessTypeList = <String>[].obs;

  /// ErrorMessage
  RxString errorMessage = ''.obs;

  /// select country
  RxString selectCountryForPhysicalAddress = ''.obs;

  /// select country for mailing address
  RxString selectCountryForMailingAddress = ''.obs;

  /// country code
  RxString countryPhoneCode = ''.obs;

  /// selected state
  RxString selectedState = ''.obs;

  /// me selected state for mailing address
  RxString selectedStateForMailing = ''.obs;

  /// BusinessTypeList
  RxList<String> businessTypeList = <String>[
    'Retailer',
    'Wholesaler',
    'Etailer',
  ].obs;

  /// selected year
  int? selectedYear = DateTime.now().year;

  /// yearPresentLocation
  String yearPresentLocation = '1';

  void setPhysicalStateList() {
    final List<dynamic> state = selectedCountryData['state'] ?? [];
    if (selectedCountryData.isNotEmpty && state.isNotEmpty) {
      stateList =
          state.map((dynamic e) => e['name'].toString()).toSet().toList();

      stateList.sort((a, b) => a.compareTo(b));

      if (isForChangePhysical()) {
        logWTF(stateList.firstOrNull);
        selectedState(stateList.firstOrNull ?? '');
      } else if (isEdit() || isFromBuyRequest()) {
        selectedState(AuthProvider.userEntity().billingAddress?.state ?? '');
      } else {
        selectedState(stateList.firstOrNull ?? '');
      }

      physicalCityList(isForChangePhysical()
          ? getCityListFromState(
                '',
                '',
                selectedState: selectedState(),
              ) ??
              []
          : isEdit() || isFromBuyRequest()
              ? getCityListFromState(
                    isPhysicalUs
                        ? AuthProvider.userEntity().billingAddress?.zipCode
                        : '',
                    isPhysicalUs
                        ? RegistrationFormFields.physical_adress_state
                        : '',
                    selectedState: isPhysicalUs
                        ? null
                        : AuthProvider.userEntity().billingAddress?.state,
                  ) ??
                  []
              : getCityListFromState(
                    '',
                    '',
                    selectedState: selectedState(),
                  ) ??
                  []);

      physicalCityList.sort((a, b) => a.compareTo(b));

      if (isForChangePhysical()) {
        city(physicalCityList.firstOrNull ?? '');
      } else if (isEdit() || isFromBuyRequest()) {
        city(AuthProvider.userEntity().billingAddress?.city ?? '');
      } else {
        city(physicalCityList.firstOrNull ?? '');
      }
    } else {
      stateList = [];
      physicalCityList([]);
      selectedState('');
    }
    if (isPhysicalUs && isForChangePhysical()) {
      physicalCityList([]);
    }
    update();
  }

  void setMailingState() {
    final List<dynamic> state =
        selectedMailingAddressCountryData['state'] ?? [];

    if (selectedMailingAddressCountryData.isNotEmpty && state.isNotEmpty) {
      final List<dynamic> _stateList =
          selectedMailingAddressCountryData['state'];
      mailingAddressStateList =
          _stateList.map((dynamic e) => e['name'].toString()).toSet().toList();

      mailingAddressStateList.sort((a, b) => a.compareTo(b));

      if (isForChangeMailing()) {
        selectedStateForMailing(mailingAddressStateList.firstOrNull ?? '');
      } else if (isEdit() || isFromBuyRequest()) {
        selectedStateForMailing(
            AuthProvider.userEntity().shippingAddress?.state ?? '');
      } else {
        selectedStateForMailing(mailingAddressStateList.firstOrNull ?? '');
      }
      mailingAddCityList(isForChangeMailing()
          ? (getCityListFromState(
                '',
                '',
                selectedState: selectedStateForMailing(),
                isForMailing: true,
              ) ??
              [])
          : isEdit() || isFromBuyRequest()
              ? (getCityListFromState(
                    isMailingUs
                        ? AuthProvider.userEntity().shippingAddress?.zipCode
                        : '',
                    isMailingUs
                        ? RegistrationFormFields.mailing_adress_state
                        : '',
                    selectedState: isMailingUs
                        ? null
                        : AuthProvider.userEntity().shippingAddress?.state,
                    isForMailing: true,
                  ) ??
                  [])
              : getCityListFromState(
                    '',
                    '',
                    selectedState: selectedStateForMailing(),
                    isForMailing: true,
                  ) ??
                  []);

      mailingAddressStateList.sort((a, b) => a.compareTo(b));

      if (isForChangeMailing()) {
        mailingCity(mailingAddCityList.firstOrNull ?? '');
      } else if (isEdit() || isFromBuyRequest()) {
        mailingCity(AuthProvider.userEntity().shippingAddress?.city ?? '');
      } else {
        mailingCity(mailingAddCityList.firstOrNull ?? '');
      }
    } else {
      mailingAddressStateList = [];
      mailingAddCityList([]);
      selectedStateForMailing('');
    }

    if (isMailingUs && isForChangeMailing()) {
      mailingAddCityList([]);
    }
    update();
  }

  List<String>? getCityListFromState(String? zip, String key,
      {String? selectedState, bool isForMailing = false}) {
    try {
      if (selectedState == null) {
        String? state;
        if (isForMailing) {
          state = isMailingUs
              ? getStateFromZip(int.tryParse(zip ?? '0') ?? 0)
              : null;
        } else {
          state = isPhysicalUs
              ? getStateFromZip(int.tryParse(zip ?? '0') ?? 0)
              : null;
        }

        if (state?.isEmpty ?? false) {
          if (isForMailing) {
            mailingCity('');
            mailingAddCityList(<String>[]);
          } else {
            physicalCityList(<String>[]);
            city('');
          }
        }

        fbKey.currentState?.fields[key]?.didChange(state ?? '');
        final List<dynamic> _stateList = isForMailing
            ? selectedMailingAddressCountryData['state']
            : selectedCountryData['state'];

        final dynamic _selectedState = _stateList.firstWhereOrNull(
            (dynamic e) =>
                e['name'].toString().toLowerCase() ==
                (state?.toLowerCase() ?? selectedState?.toLowerCase()));
        final List<dynamic> _cityList = _selectedState['city'];
        final List<String> cityList =
            _cityList.map((e) => e['name'].toString()).toSet().toList();
        return cityList;
      } else {
        final List<dynamic> _stateList = isForMailing
            ? selectedMailingAddressCountryData['state']
            : selectedCountryData['state'];
        final dynamic _selectedState = _stateList.firstWhereOrNull(
            (dynamic e) =>
                e['name'].toString().toLowerCase() ==
                selectedState.toLowerCase());
        final List<dynamic> _cityList = _selectedState['city'];
        final List<String> cityList =
            _cityList.map((e) => e['name'].toString()).toSet().toList();
        return cityList;
      }
    } catch (_) {}
    return null;
  }

  Map<String, dynamic>? filterCoutryFromJson(Country country) {
    // if (country.name.toLowerCase() == 'united states') {

    final Map<String, dynamic>? countryData = decodedList.firstWhereOrNull(
        (dynamic e) =>
            e['name'].toString().toLowerCase() == country.name.toLowerCase());

    return countryData == null ? null : countryData;
  }

  /// show year picker
  void showYearPicker(BuildContext context) {
    Get.dialog(
      barrierColor: Colors.black.withOpacity(0.7),
      YearPickerDialog(
        initialYear: selectedYear ?? DateTime.now().year,
        firstYear: 1900,
        lastYear: DateTime.now().year,
        onYearSelected: (int year) {
          selectedYear = year;
          fbKey.currentState?.fields[RegistrationFormFields.businessStartDate]
              ?.didChange(selectedYear.toString());
        },
      ),
    );
  }

  /// Toggle Function for Company Expansion Tile
  void companyExpansionToggle(value) {
    companyExpansionTile.value = value;
  }

  /// Toggle Function for Physical Address Expansion Tile
  void physicalAddressExpansionToggle(value) {
    physicalAddressExpansionTile.value = value;
  }

  /// Toggle Function for Mailing Address Expansion Tile
  void mailingAddressExpansionToggle(value) {
    mailingAddressExpansionTile.value = value;
  }

  /// Toggle Function for Contact Info Expansion Tile
  void contactInfoExpansionToggle(value) {
    contactInfoExpansionTile.value = value;
  }

  /// isSame as Physical Address
  void isSameAsPhysicalAddress(value) {
    isCheck.value = value;
  }

  /// json data
  List<dynamic> decodedList = [];

  Map<String, dynamic> selectedCountryData = {};
  Map<String, dynamic> selectedMailingAddressCountryData = {};

  RxList<String> physicalCityList = <String>[].obs;
  RxList<String> mailingAddCityList = <String>[].obs;

  /// load json
  Future<void> loadJson() async {
    try {
      unawaited(EasyLoading.show());

      decodedList = LocationJSON.locationJSONList;
      if (isEdit() || isFromBuyRequest()) {
        await setIntialFiled();
      }
    } on Exception catch (e, _) {
      unawaited(EasyLoading.dismiss());
    } finally {
      unawaited(EasyLoading.dismiss());
      update();
    }
  }

  /// on submit
  Future<void> onSubmit() async {
    if (fbKey.currentState?.saveAndValidate() ?? false) {
      if (selectBusinessTypeList.isNotEmpty) {
        errorMessage.value = '';
        await isEmailOrPhoneAvailable();
        if (!(isEmailAvailable() && isPhoneAvailable())) {
          return;
        }
        final UserEntity registrationData = UserEntity(
          firstName: fbKey
              .currentState?.fields[RegistrationFormFields.firstName]?.value,
          lastName: fbKey
              .currentState?.fields[RegistrationFormFields.lastName]?.value,
          legalRegisteredName: fbKey.currentState
              ?.fields[RegistrationFormFields.companyLegalName]?.value,
          companyOperatingName: fbKey
              .currentState?.fields[RegistrationFormFields.tradeStyle]?.value,
          fedTaxId: fbKey
              .currentState?.fields[RegistrationFormFields.fedTaxId]?.value,
          resaleTax: fbKey
              .currentState?.fields[RegistrationFormFields.resaleTax]?.value,
          jbtId:
              fbKey.currentState?.fields[RegistrationFormFields.jbtId]?.value,
          businessStartDate: DateTime(selectedYear ?? DateTime.now().year),
          typeOfBusiness: selectBusinessTypeList().join(','),
          yearsAtPresentLocation: fbKey.currentState
              ?.fields[RegistrationFormFields.yearsAtPresentLocation]?.value,
          billingAddress: IngAddress(
            fullName:
                fbKey.currentState?.fields['Physical address full name']?.value,
            city: !isPhysicalUs
                ? city()
                : fbKey
                    .currentState
                    ?.fields[RegistrationFormFields.physical_adress_city]
                    ?.value,
            country: fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_country]?.value,
            state: fbKey
                    .currentState
                    ?.fields[RegistrationFormFields.physical_adress_state]
                    ?.value ??
                selectedState(),
            street: fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_street]?.value,
            addressLineOne: fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_street]?.value,
            addressLineTwo: fbKey
                .currentState
                ?.fields[RegistrationFormFields.physical_adress_line_two]
                ?.value,
            zipCode: fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_zip_code]?.value
                .toString(),
          ),
          isAddressSame: isCheck.value,
          shippingAddress: isCheck()
              ? IngAddress(
                  fullName: fbKey.currentState
                      ?.fields['Physical address full name']?.value,
                  city: fbKey
                          .currentState
                          ?.fields[RegistrationFormFields.physical_adress_city]
                          ?.value ??
                      city(),
                  country: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.physical_adress_country]
                      ?.value,
                  state: fbKey
                          .currentState
                          ?.fields[RegistrationFormFields.physical_adress_state]
                          ?.value ??
                      selectedState(),
                  street: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.physical_adress_street]
                      ?.value,
                  addressLineOne: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.physical_adress_street]
                      ?.value,
                  addressLineTwo: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.physical_adress_line_two]
                      ?.value,
                  zipCode: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.physical_adress_zip_code]
                      ?.value
                      .toString(),
                )
              : IngAddress(
                  fullName: fbKey
                      .currentState?.fields['Mailing address full name']?.value,
                  city: fbKey
                          .currentState
                          ?.fields[RegistrationFormFields.mailing_adress_city]
                          ?.value ??
                      mailingCity(),
                  country: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.mailing_adress_country]
                      ?.value,
                  state: fbKey
                          .currentState
                          ?.fields[RegistrationFormFields.mailing_adress_state]
                          ?.value ??
                      selectedStateForMailing(),
                  street: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.mailing_adress_street]
                      ?.value,
                  addressLineOne: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.mailing_adress_street]
                      ?.value,
                  addressLineTwo: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.mailing_adress_street]
                      ?.value,
                  zipCode: fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.mailing_adress_zip_code]
                      ?.value
                      .toString(),
                ),
          phone: getPhoneForSubmit(),
          mobile: getMobileForSubmit(),
          fax: fbKey.currentState?.fields[RegistrationFormFields.fax]?.value,
          website:
              fbKey.currentState?.fields[RegistrationFormFields.website]?.value,
          email: fbKey.currentState?.fields[RegistrationFormFields.email]?.value
              .toString()
              .toLowerCase()
              .trim(),
          facebookId: fbKey
              .currentState?.fields[RegistrationFormFields.facebookId]?.value,
          instagramId: fbKey
              .currentState?.fields[RegistrationFormFields.instagramId]?.value,
        );

        unawaited(Get.toNamed(
          Routes.LEGAL_STATUS_ORGANIZATION,
          arguments: {'registrationData': registrationData},
        ));
      } else {
        errorMessage.value = LocaleKeys.custom_error_business_type_is_empty.tr;
      }
    } else {
      appSnackbar(
        message: 'Please provide all required information',
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// get state from address
  Future<void> getAddressDetails({
    required String address,
    required bool isPhysicalAddress,
  }) async {
    try {
      final String address =
          '${fbKey.currentState?.fields[isPhysicalAddress ? RegistrationFormFields.physical_adress_street : RegistrationFormFields.mailing_adress_street]?.value} '
          '${fbKey.currentState?.fields[isPhysicalAddress ? RegistrationFormFields.physical_adress_line_two : RegistrationFormFields.mailing_adress_line_two]?.value}';

      if (address.isEmpty) {
        return;
      }
      // Geocoding the address to get latitude and longitude
      final List<Location> locations = await locationFromAddress(address);

      if (locations.isNotEmpty) {
        // Using the first location found
        final Location location = locations.first;

        // Reverse geocoding to get address details
        final List<Placemark> placeMarks = await placemarkFromCoordinates(
          location.latitude,
          location.longitude,
        );

        if (placeMarks.firstOrNull != null) {
          logI('City: ${placeMarks.firstOrNull?.locality}');
          logI('State: ${placeMarks.firstOrNull?.administrativeArea}');
          logI('Country: ${placeMarks.firstOrNull?.country}');
          logI('Postal Code: ${placeMarks.firstOrNull?.postalCode}');

          if (isPhysicalAddress) {
            setPhysicalAddress(placeMarks);
          } else {
            setMailingAddress(placeMarks);
          }
        }
      }
    } on Exception catch (e) {
      logE(e);
    }
  }

  /// set physicalAddress
  void setPhysicalAddress(List<Placemark> placeMarks) {
    if (placeMarks.isNotEmpty) {
      final Placemark place = placeMarks.first;

      // Extracting address details
      final String country = place.country ?? '';
      if (country.isNotEmpty) {
        fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_country]
            ?.didChange(country);
        selectedCountryData =
            filterCoutryFromJson(Country.parse(country)) ?? {};
        isForChangePhysical(true);
        setPhysicalStateList();
      }
      final String state = place.administrativeArea ?? '';
      if (state.isNotEmpty) {
        fbKey.currentState?.fields[RegistrationFormFields.physical_adress_state]
            ?.didChange(state);
        // selectedState(state);
        if (stateList.isNotEmpty) {
          int index = stateList.indexWhere(
            (element) => element.toLowerCase() == state.toLowerCase(),
          );
          if (index != -1) {
            selectedState(stateList[index]);
          } else {
            selectedState(stateList.firstOrNull);
          }
        }
        physicalCityList(
            getCityListFromState('', '', selectedState: state) ?? []);
        physicalCityList.refresh();
      }
      final String city = place.locality ?? '';

      this.city('');
      if (city.isNotEmpty) {
        fbKey.currentState?.fields[RegistrationFormFields.physical_adress_city]
            ?.didChange(city);
      }
      if (physicalCityList.isNotEmpty) {
        final int index = physicalCityList.indexWhere(
            (element) => element.toLowerCase() == city.toLowerCase());
        if (index != -1) {
          this.city(physicalCityList[index]);
        } else {
          this.city(physicalCityList.firstOrNull ?? '');
        }
      }

      final String postalCode = place.postalCode ?? '';
      if (postalCode.isNotEmpty) {
        fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_zip_code]
            ?.didChange(postalCode);
      }
    }
  }

  /// set mailing address
  void setMailingAddress(List<Placemark> placeMarks) {
    if (placeMarks.isNotEmpty) {
      final Placemark place = placeMarks.first;

      // Extracting address details
      final String country = place.country ?? '';
      if (country.isNotEmpty) {
        fbKey
            .currentState?.fields[RegistrationFormFields.mailing_adress_country]
            ?.didChange(country);
        selectedMailingAddressCountryData =
            filterCoutryFromJson(Country.parse(country)) ?? {};
        isForChangeMailing(true);
        setMailingState();
      }
      final String state = place.administrativeArea ?? '';
      if (state.isNotEmpty) {
        fbKey.currentState?.fields[RegistrationFormFields.mailing_adress_state]
            ?.didChange(state);
        // selectedStateForMailing(state);
        if (mailingAddressStateList.isNotEmpty) {
          int index = mailingAddressStateList.indexWhere(
            (element) => element.toLowerCase() == state.toLowerCase(),
          );
          if (index != -1) {
            selectedStateForMailing(mailingAddressStateList[index]);
          } else {
            selectedStateForMailing(mailingAddressStateList.firstOrNull);
          }
        }
        mailingAddCityList(getCityListFromState('', '',
                selectedState: state, isForMailing: true) ??
            []);
      }
      final String city = place.locality ?? '';
      mailingCity('');
      if (city.isNotEmpty) {
        fbKey.currentState?.fields[RegistrationFormFields.mailing_adress_city]
            ?.didChange(city);
      }
      if (mailingAddCityList.isNotEmpty) {
        final int index = mailingAddCityList.indexWhere(
            (String element) => element.toLowerCase() == city.toLowerCase());
        if (index != -1) {
          mailingCity(mailingAddCityList[index]);
        } else {
          mailingCity(mailingAddCityList.firstOrNull ?? '');
        }
      }

      final String postalCode = place.postalCode ?? '';
      if (postalCode.isNotEmpty) {
        fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_zip_code]
            ?.didChange(postalCode);
      }
    }
  }

  bool get isPhysicalUs => isForChangePhysical()
      ? selectCountryForPhysicalAddress.toString().toLowerCase() ==
          'united states'
      : isEdit() || isFromBuyRequest()
          ? AuthProvider.userEntity().billingAddress?.country?.toLowerCase() ==
              'united states'
          : selectCountryForPhysicalAddress.toString().toLowerCase() ==
              'united states';

  bool get isMailingUs => isForChangeMailing()
      ? selectCountryForMailingAddress.toString().toLowerCase() ==
          'united states'
      : isEdit() || isFromBuyRequest()
          ? AuthProvider.userEntity().shippingAddress?.country?.toLowerCase() ==
              'united states'
          : selectCountryForMailingAddress.toString().toLowerCase() ==
              'united states';

  /// Update Profile Data
  Future<void> updateProfileData() async {
    try {
      isLoading(true);
      if (fbKey.currentState?.saveAndValidate() ?? false) {
        if (isFromBuyRequest() || selectBusinessTypeList.isNotEmpty) {
          errorMessage.value = '';

          if (!isFromBuyRequest()) {
            final bool isPhoneChanged =
                UserProvider.currentUser?.phone != getPhoneForSubmit();
            final bool isEmailChanged = UserProvider.currentUser?.email !=
                fbKey.currentState?.fields[RegistrationFormFields.email]?.value
                    .toString()
                    .trim();
            await isEmailOrPhoneAvailable(
              isPhoneChanged: isPhoneChanged,
              isEmailChanged: isEmailChanged,
            );
            if (!(isEmailAvailable() && isPhoneAvailable())) {
              return;
            }
          }

          final Map<String, dynamic> data = {
            'shipping_address':
                isCheck() ? getBillingAddress() : getShippingAddress(),
            'billing_address': getBillingAddress(),
            'is_address_same': isCheck.value,
            'city': fbKey
                    .currentState
                    ?.fields[RegistrationFormFields.physical_adress_city]
                    ?.value ??
                city(),
            'country': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_country]?.value,
            'state': fbKey
                    .currentState
                    ?.fields[RegistrationFormFields.physical_adress_state]
                    ?.value ??
                selectedState(),
            'zip_code': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_zip_code]?.value
                .toString(),
          };

          if (!isFromBuyRequest()) {
            data.addAll({
              'first_name': fbKey.currentState
                  ?.fields[RegistrationFormFields.firstName]?.value,
              'last_name': fbKey
                  .currentState?.fields[RegistrationFormFields.lastName]?.value,
              'legal_registered_name': fbKey.currentState
                  ?.fields[RegistrationFormFields.companyLegalName]?.value,
              'company_operating_name': fbKey.currentState
                  ?.fields[RegistrationFormFields.tradeStyle]?.value,
              'fed_tax_id': fbKey
                  .currentState?.fields[RegistrationFormFields.fedTaxId]?.value,
              'resale_tax': fbKey.currentState
                  ?.fields[RegistrationFormFields.resaleTax]?.value,
              'jbt_id': fbKey
                  .currentState?.fields[RegistrationFormFields.jbtId]?.value,
              'business_start_date': fbKey.currentState
                  ?.fields[RegistrationFormFields.businessStartDate]?.value,
              'type_of_business': selectBusinessTypeList().join(','),
              'has_AMLprogram': selectProgramIndex() == 0,
              'years_at_present_location': fbKey.currentState
                  ?.fields[RegistrationFormFields.yearsAtPresentLocation]?.value
                  .toString(),
              // 'billing_address': getBillingAddress(),
              // 'is_address_same': isCheck.value,
              // 'shipping_address': isCheck()
              //     ? getBillingAddress()
              //     : getShippingAddress(),
              // 'city': fbKey
              //         .currentState
              //         ?.fields[RegistrationFormFields.physical_adress_city]
              //         ?.value ??
              //     city(),
              // 'country': fbKey
              //     .currentState
              //     ?.fields[RegistrationFormFields.physical_adress_country]
              //     ?.value,
              // 'state': fbKey
              //         .currentState
              //         ?.fields[RegistrationFormFields.physical_adress_state]
              //         ?.value ??
              //     selectedState(),
              // 'zip_code': fbKey
              //     .currentState
              //     ?.fields[RegistrationFormFields.physical_adress_zip_code]
              //     ?.value
              //     .toString(),
              'email': fbKey
                  .currentState?.fields[RegistrationFormFields.email]?.value
                  .toString()
                  .trim(),
              'phone': getPhoneForSubmit(),
              'mobile': getMobileForSubmit(),
              'fax':
                  fbKey.currentState?.fields[RegistrationFormFields.fax]?.value,
              'website': fbKey
                  .currentState?.fields[RegistrationFormFields.website]?.value,
              'facebook_id': fbKey.currentState
                  ?.fields[RegistrationFormFields.facebookId]?.value,
              'instagram_id': fbKey.currentState
                  ?.fields[RegistrationFormFields.instagramId]?.value,
            });
          }

          logI('update data');
          logI(data);
          final bool response = await AuthProvider.updateProfile(data);
          if (response) {
            Get.isRegistered()
                ? Get.find<MyAccountController>().update()
                : Get.put(MyAccountController()).update();
            appSnackbar(
              message: 'Profile updated successfully',
              snackBarState: SnackBarState.SUCCESS,
            );
            logI('Successfully Save');
            Get.back();
          }
        } else {
          appSnackbar(
            message: 'Something is missing',
            snackBarState: SnackBarState.INFO,
          );
          errorMessage.value =
              LocaleKeys.custom_error_business_type_is_empty.tr;
        }
      } else {
        appSnackbar(
          message: 'Please provide all required information',
          snackBarState: SnackBarState.DANGER,
        );
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            'Please provide all required information',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  /// get shipping address
  Map<String, dynamic> getShippingAddress() => {
        'full_name':
            fbKey.currentState?.fields['Mailing address full name']?.value,
        'city': fbKey.currentState
                ?.fields[RegistrationFormFields.mailing_adress_city]?.value ??
            mailingCity(),
        'country': fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_country]?.value,
        'state': fbKey.currentState
                ?.fields[RegistrationFormFields.mailing_adress_state]?.value ??
            selectedStateForMailing(),
        'street': fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_street]?.value,
        'address_line_one': fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_street]?.value,
        'address_line_two': fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_line_two]?.value,
        'zip_code': fbKey.currentState
            ?.fields[RegistrationFormFields.mailing_adress_zip_code]?.value
            .toString(),
      };

  /// get billing address
  Map<String, dynamic> getBillingAddress() => {
        'full_name':
            fbKey.currentState?.fields['Physical address full name']?.value,
        'city': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_city]?.value ??
            city(),
        'country': fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_country]?.value,
        'state': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_state]?.value ??
            selectedState(),
        'street': fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_street]?.value,
        'address_line_one': fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_street]?.value,
        'address_line_two': fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_line_two]?.value,
        'zip_code': fbKey.currentState
            ?.fields[RegistrationFormFields.physical_adress_zip_code]?.value
            .toString(),
      };

  /// get phone
  String getPhoneForSubmit() =>
      '${phoneCountryCodePrefix()}-${'${fbKey.currentState?.fields[RegistrationFormFields.phone]?.value}'}';

  /// get mobile
  String? getMobileForSubmit() {
    if (fbKey.currentState?.fields[RegistrationFormFields.mobile]?.value
            .toString()
            .trim()
            .isNotEmpty ??
        false) {
      return '${mobileCountryCodePrefix()}-${'${fbKey.currentState?.fields[RegistrationFormFields.mobile]?.value}'}';
    }
    return null;
  }

  /// mobile code prefix
  String mobileCountryCodePrefix() {
    if (isEdit()) {
      if (mobileCountryCode.isEmpty) {
        if (countryPhoneCode().isEmpty) {
          return AuthProvider.userEntity().mobile?.split('-')[0].toString() ??
              '+1';
        } else {
          return '+${countryPhoneCode.toString()}';
        }
      } else {
        return '+${mobileCountryCode.toString()}';
      }
    } else {
      if (mobileCountryCode.isEmpty) {
        if (countryPhoneCode().isEmpty) {
          return '+1';
        } else {
          return '+${countryPhoneCode.toString()}';
        }
      } else {
        return '+${mobileCountryCode.toString()}';
      }
    }
  }

  /// prefix country code
  String phoneCountryCodePrefix() {
    if (isEdit()) {
      if (phoneCountryCode().isEmpty) {
        if (countryPhoneCode.isEmpty) {
          return AuthProvider.userEntity().phone?.split('-')[0].toString() ??
              '';
        } else {
          return '+${countryPhoneCode.toString()}';
        }
      } else {
        return '+${phoneCountryCode.toString()}';
      }
    } else {
      if (phoneCountryCode().isEmpty) {
        if (countryPhoneCode.isEmpty) {
          return '+1';
        } else {
          return '+${countryPhoneCode.toString()}';
        }
      } else {
        return '+${phoneCountryCode.toString()}';
      }
    }
  }

  /// initial phone number
  String initialPhone() {
    if (isEdit()) {
      if (AuthProvider.userEntity().phone?.contains('-') ?? false) {
        return AuthProvider.userEntity().phone?.split('-')[1].toString() ?? '';
      }
      return AuthProvider.userEntity().phone?.toString() ?? '';
    }
    return '';
  }

  /// initial mobile number
  String initialMobile() {
    if (isEdit()) {
      if (AuthProvider.userEntity().mobile?.contains('-') ?? false) {
        return AuthProvider.userEntity().mobile?.split('-')[1].toString() ?? '';
      }
      //return AuthProvider.userEntity().mobile.toString();
    }
    return '';
  }

  /// call api to check
  Future<void> isEmailOrPhoneAvailable(
      {bool isPhoneChanged = true, bool isEmailChanged = true}) async {
    if (isPhoneChanged) {
      await checkEmailOrPhoneAvailable(phone: getPhoneForSubmit());
    }
    if (isEmailChanged) {
      await checkEmailOrPhoneAvailable(
          email: fbKey.currentState?.fields[RegistrationFormFields.email]?.value
              .toString()
              .trim());
    }
    if (isEmailAvailable() && isPhoneAvailable()) {
      isNextDisabled(false);
    } else {
      isNextDisabled(true);
    }
  }

  /// check email or phone available
  Future<void> checkEmailOrPhoneAvailable({
    String? email,
    String? phone,
  }) async {
    try {
      isLoading(true);
      final bool status = await AuthProvider.checkEmailOrPhoneAvailable({
        if (email != null) 'email': email.toString().toLowerCase().trim(),
        if (phone != null) 'phone': phone.toString().trim(),
      });
      if (email != null) {
        isEmailAvailable(status);
        if (!isEmailAvailable()) {
          fbKey.currentState?.fields[RegistrationFormFields.email]
              ?.invalidate('Email not available.');
        }
      } else if (phone != null) {
        isPhoneAvailable(status);
        if (!isPhoneAvailable()) {
          fbKey.currentState?.fields[RegistrationFormFields.phone]
              ?.invalidate('Phone not available.');
        }
      }

      isLoading(false);
    } on DioException catch (_) {
      isLoading(false);
    }
  }
}
