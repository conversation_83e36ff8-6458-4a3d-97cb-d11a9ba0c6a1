import 'dart:math';

import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/ui/components/common_country_picker.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/ui/components/fedtax_id_input_formatter.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../data/config/app_colors.dart';
import '../../../data/config/design_config.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/new_company_registration_controller.dart';

/// NewCompanyRegistrationView
class NewCompanyRegistrationView
    extends GetView<NewCompanyRegistrationController> {
  /// NewCompanyRegistrationView
  const NewCompanyRegistrationView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: false,
        bottomNavigationBar: Padding(
          padding: EdgeInsets.only(
            left: min(60.w, 15),
            right: min(60.w, 15),
            bottom: MediaQuery.of(context).viewInsets.bottom +
                MediaQuery.of(context).padding.bottom +
                60.h,
          ),
          child: _buildBottomBar(),
        ),
        appBar: _buildCommonAppbar(),
        body: _buildBody(context),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => Obx(
        () => IgnorePointer(
          ignoring: controller.isLoading(),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(
                left: min(60.w, 15),
                right: min(60.w, 15),
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: FormBuilder(
                key: controller.fbKey,
                onChanged: () {
                  if (controller.fbKey.currentState
                          ?.validate(focusOnInvalid: false) ??
                      false) {
                    controller.isNextDisabled(false);
                  } else {
                    controller.isNextDisabled(true);
                  }
                },

                /// autovalidateMode: AutovalidateMode.onUserInteraction,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    60.verticalSpace,

                    /// Description Text
                    _buildDescriptionText(),

                    /// Company Expansion Tile
                    if (!controller.isFromBuyRequest())
                      _buildCompanyExpansionTile(context),

                    /// Physical Address Expansion Tile
                    _buildPhysicalAddressExpansionTile(context),

                    /// Mailing Address Expansion Tile
                    _buildMailingAddressExpansionTile(context),

                    /// Contact Info Expansion Tile
                    if (!controller.isFromBuyRequest())
                      _buildContactInfoExpansionTile(context),
                    100.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
        ),
      );

  /// Common Button
  Widget _buildBottomBar() => Obx(
        () => AppButton.text(
          isLoading: controller.isLoading(),
          buttonText: controller.isEdit() || controller.isFromBuyRequest()
              ? LocaleKeys.edit_address_save.tr
              : LocaleKeys.new_company_registration_next.tr,
          onPressed: () {
            controller.isEdit() || controller.isFromBuyRequest()
                ? controller.updateProfileData()
                : controller.onSubmit();
          },
          buttonSize: Size(Get.width, 150.h),
          isDisabled: controller.isNextDisabled(),
          backgroundColor: AppColors.k101C28,
          borderRadius: 24.r,
          borderColor: AppColors.k101C28,
          buttonTextStyle: TextStyle(
            fontSize: 45.sp,
            fontWeight: FontWeight.w700,
            color: controller.isNextDisabled()
                ? AppColors.k101C28.withOpacity(0.5)
                : AppColors.kBEFFFC,
          ),
        ),
      );

  /// Contact Info Expansion Tile
  Widget _buildContactInfoExpansionTile(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Text(
            LocaleKeys.new_company_registration_contact_info.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w900,
              color: AppColors.k101C28,
              letterSpacing: 5.sp,
            ),
          ),
          maintainState: true,
          onExpansionChanged: (bool value) =>
              controller.contactInfoExpansionToggle(value),
          dense: true,
          initiallyExpanded: controller.contactInfoExpansionTile(),
          tilePadding: EdgeInsets.zero,
          trailing: Container(
            height: 100.h,
            width: min(100.w, 50),
            decoration: const BoxDecoration(
              color: AppColors.kEAEFF4,
              shape: BoxShape.circle,
            ),
            child: Obx(
              () => Icon(
                controller.contactInfoExpansionTile.value
                    ? Icons.expand_less_sharp
                    : Icons.expand_more_sharp,
                color: AppColors.k101C28,
              ),
            ),
          ),
          children: <Widget>[
            60.verticalSpace,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: _buildTextFormField(
                    initalValue: controller.isEdit()
                        ? AuthProvider.userEntity().firstName ?? ''
                        : '',
                    name: RegistrationFormFields.firstName,
                    keyboardType: TextInputType.text,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'\s')), // Deny any whitespace characters
                    ],
                    autofillHints: <String>[CustomAutofillHints.firstName],
                    labelText:
                        LocaleKeys.new_company_registration_first_name.tr,
                    validation: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_first_name_is_empty.tr;
                      }
                      return null;
                    },
                  ),
                ),
                40.horizontalSpace,
                Expanded(
                  child: _buildTextFormField(
                    initalValue: controller.isEdit()
                        ? AuthProvider.userEntity().lastName ?? ''
                        : '',
                    name: RegistrationFormFields.lastName,
                    keyboardType: TextInputType.text,
                    inputFormatters: [
                      FilteringTextInputFormatter.deny(
                          RegExp(r'\s')), // Deny any whitespace characters
                    ],
                    autofillHints: <String>[CustomAutofillHints.lastName],
                    labelText: LocaleKeys.new_company_registration_last_name.tr,
                    validation: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_last_name_is_empty.tr;
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            60.verticalSpace,
            Obx(
              () => _buildTextFormField(
                initalValue: controller.initialPhone(),
                name: RegistrationFormFields.phone,
                keyboardType: TextInputType.number,
                autofillHints: <String>[CustomAutofillHints.phone],
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.digitsOnly,
                ],
                labelText: LocaleKeys.new_company_registration_phone.tr,
                prefix: InkWell(
                  onTap: () {
                    commonShowCountryPicker(
                      context,
                      countryFilter: const ['US', 'IN'],
                      onSelect: (Country country) {
                        controller.selectPhoneCountryCode(country);
                      },
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: AppColors.k70777E,
                          width: 1.w,
                        ),
                      ),
                    ),
                    margin: REdgeInsets.only(right: 30, left: 10),
                    padding: REdgeInsets.symmetric(horizontal: 30),
                    child: Text(
                      controller.phoneCountryCodePrefix(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                isRequired: true,
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if (trimmedValue?.isEmpty ?? true) {
                    return LocaleKeys.validation_phone_is_empty.tr;
                  } else if (!GetUtils.isPhoneNumber(trimmedValue ?? '')) {
                    return LocaleKeys.validation_invalid_phone_number.tr;
                  }
                  return null;
                },
              ),
            ),
            76.verticalSpace,
            Obx(
              () => _buildTextFormField(
                initalValue: controller.initialMobile(),
                // initalValue: controller.isEdit()
                //     ? '${AuthProvider.userEntity().mobile ?.split('-')[0].toString()}  ${AuthProvider.userEntity().mobile?.split('-')[1].toString()}' ??
                //         ''
                //     : '',
                name: RegistrationFormFields.mobile,
                keyboardType: TextInputType.phone,
                autofillHints: const <String>[AutofillHints.telephoneNumber],
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.digitsOnly,
                ],
                labelText: LocaleKeys.new_company_registration_telephone.tr,
                prefix: InkWell(
                  onTap: () {
                    commonShowCountryPicker(
                      context,
                      countryFilter: const ['US', 'IN'],
                      onSelect: (Country country) {
                        controller.selectMobileCountryCode(country);
                      },
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: AppColors.k70777E,
                          width: 1.w,
                        ),
                      ),
                    ),
                    margin: REdgeInsets.only(right: 30, left: 10),
                    padding: REdgeInsets.symmetric(horizontal: 30),
                    child: Text(
                      controller.mobileCountryCodePrefix(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if ((trimmedValue?.length ?? 0) > 15) {
                    return LocaleKeys.validation_invalid_phone_number.tr;
                  }
                  return null;
                },
              ),
            ),
            76.verticalSpace,
            _buildTextFormField(
              initalValue: controller.isEdit()
                  ? AuthProvider.userEntity().fax ?? ''
                  : '',
              name: RegistrationFormFields.fax,
              keyboardType: TextInputType.text,
              autofillHints: <String>[CustomAutofillHints.fax],
              labelText: LocaleKeys.new_company_registration_fax.tr,
              validation: (String? value) {
                final String? trimmedValue = value?.trim();
                if (trimmedValue?.isNotEmpty ?? false) {
                  return null;
                }
                return null;
              },
            ),
            76.verticalSpace,
            _buildTextFormField(
              initalValue: controller.isEdit()
                  ? AuthProvider.userEntity().website ?? ''
                  : '',
              name: RegistrationFormFields.website,
              labelText: LocaleKeys.new_company_registration_website.tr,
              keyboardType: TextInputType.url,
              autofillHints: const <String>[AutofillHints.url],
              validation: (String? value) {
                final String? trimmedValue = value?.trim();
                if (trimmedValue?.isNotEmpty ?? false) {
                  return null;
                }
                return null;
              },
            ),
            76.verticalSpace,
            _buildTextFormField(
              initalValue: controller.isEdit()
                  ? AuthProvider.userEntity().email ?? ''
                  : '',
              name: RegistrationFormFields.email,
              labelText: LocaleKeys.new_company_registration_email.tr,
              keyboardType: TextInputType.emailAddress,
              autofillHints: const <String>[AutofillHints.email],
              isRequired: true,
              validation: (String? value) {
                final String? trimmedValue = value?.trim();
                if (trimmedValue?.isEmpty ?? true) {
                  return LocaleKeys.validation_email_is_empty.tr;
                } else if (!trimmedValue!.isEmail) {
                  return LocaleKeys.validation_email_is_invalid.tr;
                }
                return null;
              },
            ),
            76.verticalSpace,
            _buildTextFormField(
              initalValue: controller.isEdit()
                  ? AuthProvider.userEntity().facebookId ?? ''
                  : '',
              name: RegistrationFormFields.facebookId,
              keyboardType: TextInputType.text,
              autofillHints: <String>[CustomAutofillHints.facebookProfile],
              labelText:
                  LocaleKeys.new_company_registration_facebook_profile_link.tr,
              validation: (String? value) {
                if (value?.trim().isNotEmpty ?? false) {
                  return null;
                }
                return null;
              },
            ),
            76.verticalSpace,
            _buildTextFormField(
              initalValue: controller.isEdit()
                  ? AuthProvider.userEntity().instagramId ?? ''
                  : '',
              name: RegistrationFormFields.instagramId,
              keyboardType: TextInputType.url,
              autofillHints: <String>[CustomAutofillHints.instagramProfile],
              labelText:
                  LocaleKeys.new_company_registration_instagram_profile_link.tr,
              validation: (String? value) {
                if (value?.trim().isNotEmpty ?? false) {
                  return null;
                }
                return null;
              },
            ),
          ],
        ),
      );

  /// Mailing Address Expansion Tile
  Widget _buildMailingAddressExpansionTile(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: GetBuilder<NewCompanyRegistrationController>(
          builder: (_) => ExpansionTile(
            title: Text(
              LocaleKeys.new_company_registration_mailing_address.tr,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
                letterSpacing: 5.sp,
              ),
            ),
            maintainState: true,
            dense: true,
            initiallyExpanded: controller.mailingAddressExpansionTile(),
            onExpansionChanged: (bool value) =>
                controller.mailingAddressExpansionToggle(value),
            tilePadding: EdgeInsets.zero,
            trailing: Container(
              height: 100.h,
              width: min(100.w, 50),
              decoration: const BoxDecoration(
                color: AppColors.kEAEFF4,
                shape: BoxShape.circle,
              ),
              child: Obx(
                () => Icon(
                  controller.mailingAddressExpansionTile.value
                      ? Icons.expand_less_sharp
                      : Icons.expand_more_sharp,
                  color: AppColors.k101C28,
                ),
              ),
            ),
            children: <Widget>[
              35.verticalSpace,
              Obx(
                () => CheckboxListTile(
                  dense: true,
                  contentPadding: EdgeInsets.zero,
                  controlAffinity: ListTileControlAffinity.leading,
                  checkColor: Colors.white,
                  activeColor: AppColors.k101C28,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  visualDensity: VisualDensity.compact,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  side: BorderSide(
                    color: AppColors.k101C28,
                    width: min(3.w, 2),
                  ),
                  title: Text(
                    LocaleKeys
                        .new_company_registration_same_as_physical_address.tr,
                    style: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  value: controller.isCheck.value,
                  onChanged: (bool? value) =>
                      controller.isSameAsPhysicalAddress(value),
                ),
              ),
              Obx(
                () => AnimatedSize(
                  curve: Curves.easeIn,
                  duration: const Duration(milliseconds: 300),
                  child: Visibility(
                    visible: !controller.isCheck(),
                    child: Column(
                      children: <Widget>[
                        60.verticalSpace,
                        _buildTextFormField(
                          initalValue: controller.isEdit() ||
                                  controller.isFromBuyRequest()
                              ? AuthProvider.userEntity()
                                      .shippingAddress
                                      ?.fullName ??
                                  ''
                              : '',
                          name: 'Mailing address full name',
                          keyboardType: TextInputType.name,
                          autofillHints: <String>[CustomAutofillHints.fullName],
                          isRequired: true,
                          labelText:
                              LocaleKeys.new_company_registration_full_name.tr,
                          maxLines: 3,
                          validation: (String? value) {
                            final String? trimmedValue = value?.trim();
                            if (trimmedValue?.isEmpty ?? true) {
                              return LocaleKeys
                                  .validation_please_enter_your_name.tr;
                            }
                            return null;
                          },
                        ),
                        60.verticalSpace,
                        _buildTextFormField(
                          initalValue: controller.isEdit() ||
                                  controller.isFromBuyRequest()
                              ? AuthProvider.userEntity()
                                      .shippingAddress
                                      ?.street ??
                                  ''
                              : '',
                          name: RegistrationFormFields.mailing_adress_street,
                          isRequired: true,
                          onChange: (String? value) => controller.debouncer.run(
                              () => controller.getAddressDetails(
                                  address: value ?? '',
                                  isPhysicalAddress: false)),
                          keyboardType: TextInputType.streetAddress,
                          autofillHints: <String>[
                            AutofillHints.fullStreetAddress
                          ],
                          labelText: LocaleKeys
                              .new_company_registration_address_line.tr,
                          maxLines: 3,
                          validation: (String? value) {
                            final String? trimmedValue = value?.trim();
                            if (trimmedValue?.isEmpty ?? true) {
                              return LocaleKeys.validation_address1_is_empty.tr;
                            }
                            return null;
                          },
                        ),
                        76.verticalSpace,
                        _buildTextFormField(
                          initalValue: controller.isEdit() ||
                                  controller.isFromBuyRequest()
                              ? AuthProvider.userEntity()
                                      .shippingAddress
                                      ?.addressLineTwo ??
                                  ''
                              : '',
                          name: RegistrationFormFields.mailing_adress_line_two,
                          keyboardType: TextInputType.streetAddress,
                          onChange: (String? value) => controller.debouncer.run(
                              () => controller.getAddressDetails(
                                  address: value ?? '',
                                  isPhysicalAddress: false)),
                          autofillHints: <String>[
                            CustomAutofillHints.addressLineTwo
                          ],
                          labelText: LocaleKeys
                              .new_company_registration_address_line_two.tr,
                          maxLines: 3,
                        ),
                        60.verticalSpace,
                        _buildTextFormField(
                          initalValue: controller.isEdit() ||
                                  controller.isFromBuyRequest()
                              ? AuthProvider.userEntity()
                                      .shippingAddress
                                      ?.country ??
                                  ''
                              : controller.selectCountryForMailingAddress(),
                          onTap: () {
                            commonShowCountryPicker(
                              context,
                              onSelect: (Country country) {
                                clearMailingAddField();
                                controller
                                  ..selectedMailingAddressCountryData =
                                      controller
                                              .filterCoutryFromJson(country) ??
                                          {}
                                  ..selectedCountryForMailingAddress(country)
                                  ..isForChangeMailing(true)
                                  ..isForChangeMailing.refresh()
                                  ..update()
                                  ..setMailingState();
                                controller
                                    .fbKey
                                    .currentState
                                    ?.fields[RegistrationFormFields
                                        .mailing_adress_country]
                                    ?.didChange(country.name);
                                controller.update();
                              },
                            );
                          },
                          readOnly: true,
                          name: RegistrationFormFields.mailing_adress_country,
                          labelText:
                              LocaleKeys.new_company_registration_country.tr,
                          isRequired: true,
                          keyboardType: TextInputType.text,
                          autofillHints: const <String>[
                            AutofillHints.countryName
                          ],
                          validation: (String? value) {
                            if (controller.isCheck()) {
                              return null;
                            }
                            final String? trimmedValue = value?.trim();
                            if (trimmedValue?.isEmpty ?? true) {
                              return LocaleKeys.validation_country_is_empty.tr;
                            }
                            return null;
                          },
                        ),
                        76.verticalSpace,
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Expanded(
                              child: _buildTextFormField(
                                initalValue: controller.isEdit() ||
                                        controller.isFromBuyRequest()
                                    ? AuthProvider.userEntity()
                                            .shippingAddress
                                            ?.zipCode ??
                                        ''
                                    : '',
                                onSubmit: (String? zip) {
                                  if (zip?.isNotEmpty ?? false) {
                                    controller.mailingAddCityList(
                                        controller.getCityListFromState(
                                                zip,
                                                RegistrationFormFields
                                                    .mailing_adress_state,
                                                isForMailing: true) ??
                                            []);
                                    controller.mailingCity(controller
                                            .mailingAddCityList.firstOrNull ??
                                        '');
                                    controller.update();
                                  }
                                  // controller.mailingAddCityList =
                                  //     getCityListFromState(
                                  //         zip,
                                  //         RegistrationFormFields
                                  //             .mailing_adress_state);
                                },
                                onChange: (zip) {
                                  if ((zip?.isNotEmpty ?? false) &&
                                      controller.mailingAddCityList.isEmpty) {
                                    controller.mailingAddCityList(
                                        controller.getCityListFromState(
                                                zip,
                                                RegistrationFormFields
                                                    .mailing_adress_state,
                                                isForMailing: true) ??
                                            []);
                                    controller.mailingCity(controller
                                            .mailingAddCityList.firstOrNull ??
                                        '');
                                    controller.update();
                                  }
                                  // controller.mailingAddCityList =
                                  //     getCityListFromState(
                                  //         zip,
                                  //         RegistrationFormFields
                                  //             .mailing_adress_state);
                                },
                                name: RegistrationFormFields
                                    .mailing_adress_zip_code,
                                keyboardType: TextInputType.number,
                                autofillHints: const <String>[
                                  AutofillHints.postalCode
                                ],
                                labelText:
                                    LocaleKeys.new_company_registration_zip.tr,
                                isRequired: true,
                                validation: (String? value) {
                                  logI('validation');
                                  logI(controller.isCheck());
                                  if (controller.isCheck()) {
                                    return null;
                                  }
                                  final String? trimmedValue = value?.trim();
                                  if (trimmedValue?.isEmpty ?? true) {
                                    return LocaleKeys
                                        .validation_zip_is_empty.tr;
                                  } else if (trimmedValue!.length > 10) {
                                    return LocaleKeys
                                        .validation_zip_code_should_be_less_than_10_digits
                                        .tr;
                                  }
                                  return null;
                                },
                              ),
                            ),
                            40.horizontalSpace,
                            Obx(
                              () => Expanded(
                                child: controller.isMailingUs ||
                                        controller
                                            .mailingAddressStateList.isEmpty ||
                                        controller
                                            .selectedStateForMailing()
                                            .isEmpty
                                    ? _buildTextFormField(
                                        initalValue: controller
                                            .selectedStateForMailing(),
                                        name: RegistrationFormFields
                                            .mailing_adress_state,
                                        readOnly: controller.isMailingUs ||
                                            controller.mailingAddressStateList
                                                .isEmpty,
                                        labelText: LocaleKeys
                                            .new_company_registration_state.tr,
                                        isRequired: true,
                                        keyboardType: TextInputType.text,
                                        autofillHints: const <String>[
                                          AutofillHints.addressState
                                        ],
                                        validation: controller.isMailingUs ||
                                                controller
                                                    .mailingAddressStateList
                                                    .isEmpty
                                            ? (value) {
                                                return null;
                                              }
                                            : (String? value) {
                                                if (controller.isCheck()) {
                                                  return null;
                                                }
                                                final String? trimmedValue =
                                                    value?.trim();
                                                if (trimmedValue?.isEmpty ??
                                                    true) {
                                                  return LocaleKeys
                                                      .validation_state_is_empty
                                                      .tr;
                                                }
                                                return null;
                                              },
                                      )
                                    : controller.mailingAddressStateList.isEmpty
                                        ? const SizedBox.shrink()
                                        : DropDownFormField(
                                            isSearchable: true,
                                            hintText:
                                                '${LocaleKeys.new_company_registration_search_state.tr}...',
                                            onChanged: (value) {
                                              controller
                                                  .selectedStateForMailing(
                                                      value);
                                              controller.mailingAddCityList(
                                                  controller
                                                          .getCityListFromState(
                                                              '', '',
                                                              selectedState:
                                                                  value,
                                                              isForMailing:
                                                                  true) ??
                                                      []);
                                              controller.mailingCity(controller
                                                      .mailingAddCityList
                                                      .firstOrNull ??
                                                  '');
                                            },
                                            style: AppFontStyles.skolaSans(
                                              color: AppColors.k101C28,
                                              fontSize: 45.sp,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            items: controller
                                                .mailingAddressStateList,
                                            selectedValue: controller
                                                    .selectedStateForMailing()
                                                    .isEmpty
                                                ? null
                                                : controller
                                                    .selectedStateForMailing(),
                                            menuItemStyle:
                                                AppFontStyles.skolaSans(
                                              fontSize: 45.sp,
                                              color: AppColors.k101C28,
                                              fontWeight: FontWeight.w400,
                                            ),
                                            decoration: InputDecoration(
                                              contentPadding: REdgeInsets.only(
                                                left: 48,
                                                bottom: 30,
                                                right: 48,
                                              ),
                                              constraints: BoxConstraints(
                                                minHeight: 150.h,
                                              ),
                                              label: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: <Widget>[
                                                  Text(
                                                    LocaleKeys
                                                        .new_company_registration_state
                                                        .tr,
                                                  ),
                                                  Text(
                                                    ' *',
                                                    style:
                                                        AppFontStyles.skolaSans(
                                                      fontSize: 35.sp,
                                                      fontWeight:
                                                          FontWeight.w900,
                                                      color: Colors.red,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              labelStyle:
                                                  AppFontStyles.skolaSans(
                                                color: AppColors.k70777E,
                                                fontSize: 45.sp,
                                                fontWeight: FontWeight.w400,
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(24.r),
                                                borderSide: BorderSide(
                                                  width: 1.w,
                                                  color: AppColors.k70777E,
                                                ),
                                              ),
                                              errorBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(24.r),
                                                borderSide: BorderSide(
                                                  width: 1.w,
                                                  color: AppColors.k70777E,
                                                ),
                                              ),
                                              errorStyle: const TextStyle(
                                                  color: Colors.red),
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(24.r),
                                                borderSide: BorderSide(
                                                  width: 1.w,
                                                  color: AppColors.k70777E,
                                                ),
                                              ),
                                            ),
                                          ),
                              ),
                            ),
                          ],
                        ),
                        76.verticalSpace,
                        Obx(
                          () => controller.mailingAddCityList.isNotEmpty ||
                                  controller.mailingCity().isNotEmpty
                              ? DropDownFormField(
                                  isSearchable: true,
                                  hintText:
                                      '${LocaleKeys.new_company_registration_search_city.tr}...',
                                  onChanged: (dynamic value) {
                                    controller.mailingCity(value);
                                    controller.mailingCity.refresh();
                                  },
                                  items: controller.mailingAddCityList,
                                  selectedValue: controller.mailingCity(),
                                  menuItemStyle: AppFontStyles.skolaSans(
                                    fontSize: 45.sp,
                                    color: AppColors.k101C28,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  style: AppFontStyles.skolaSans(
                                    color: AppColors.k101C28,
                                    fontSize: 45.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration(
                                    contentPadding: REdgeInsets.only(
                                      left: 48,
                                      bottom: 30,
                                      right: 48,
                                    ),
                                    constraints: BoxConstraints(
                                      minHeight: 150.h,
                                    ),
                                    label: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: <Widget>[
                                        Text(
                                          LocaleKeys
                                              .new_company_registration_city.tr,
                                        ),
                                        Text(
                                          ' *',
                                          style: AppFontStyles.skolaSans(
                                            fontSize: 35.sp,
                                            fontWeight: FontWeight.w900,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                    labelStyle: AppFontStyles.skolaSans(
                                      color: AppColors.k70777E,
                                      fontSize: 45.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                    errorStyle:
                                        const TextStyle(color: Colors.red),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                  ),
                                )
                              : _buildTextFormField(
                                  initalValue: controller.mailingCity(),
                                  name: RegistrationFormFields
                                      .mailing_adress_city,
                                  keyboardType: TextInputType.text,
                                  autofillHints: const <String>[
                                    AutofillHints.addressCity
                                  ],
                                  labelText: LocaleKeys
                                      .new_company_registration_city.tr,
                                  isRequired: true,
                                  validation: controller
                                          .mailingAddCityList.isEmpty
                                      ? (value) => null
                                      : (String? value) {
                                          if (value?.trim().isEmpty ?? true) {
                                            return LocaleKeys
                                                .validation_city_is_empty.tr;
                                          }
                                          return null;
                                        },
                                ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(
                () => controller.isCheck() &&
                        controller.mailingAddressExpansionTile()
                    ? 100.verticalSpace
                    : controller.mailingAddressExpansionTile()
                        ? 100.verticalSpace
                        : 0.verticalSpace,
              ),
            ],
          ),
        ),
      );

  /// Physical Address Expansion Tile
  Widget _buildPhysicalAddressExpansionTile(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: GetBuilder<NewCompanyRegistrationController>(
          builder: (_) => ExpansionTile(
            title: Text(
              LocaleKeys.new_company_registration_physical_address.tr,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
                letterSpacing: 5.sp,
              ),
            ),
            maintainState: true,
            onExpansionChanged: (bool value) {
              controller.physicalAddressExpansionToggle(value);
            },
            initiallyExpanded: controller.physicalAddressExpansionTile(),
            dense: true,
            tilePadding: EdgeInsets.zero,
            trailing: Container(
              height: 100.h,
              width: min(100.w, 50),
              decoration: const BoxDecoration(
                color: AppColors.kEAEFF4,
                shape: BoxShape.circle,
              ),
              child: Obx(
                () => Icon(
                  controller.physicalAddressExpansionTile.value
                      ? Icons.expand_less_sharp
                      : Icons.expand_more_sharp,
                  color: AppColors.k101C28,
                ),
              ),
            ),
            children: <Widget>[
              60.verticalSpace,
              _buildTextFormField(
                initalValue: controller.isEdit() ||
                        controller.isFromBuyRequest()
                    ? AuthProvider.userEntity().billingAddress?.fullName ?? ''
                    : '',
                name: 'Physical address full name',
                keyboardType: TextInputType.name,
                autofillHints: <String>[CustomAutofillHints.fullName],
                isRequired: true,
                labelText: LocaleKeys.new_company_registration_full_name.tr,
                maxLines: 3,
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if (trimmedValue?.isEmpty ?? true) {
                    return LocaleKeys.validation_please_enter_your_name.tr;
                  }
                  return null;
                },
              ),
              60.verticalSpace,
              _buildTextFormField(
                initalValue:
                    controller.isEdit() || controller.isFromBuyRequest()
                        ? AuthProvider.userEntity().billingAddress?.street ?? ''
                        : '',
                name: RegistrationFormFields.physical_adress_street,
                keyboardType: TextInputType.streetAddress,
                autofillHints: <String>[AutofillHints.fullStreetAddress],
                isRequired: true,
                onChange: (String? value) => controller.debouncer.run(() =>
                    controller.getAddressDetails(
                        address: value ?? '', isPhysicalAddress: true)),
                labelText: LocaleKeys.new_company_registration_address_line.tr,
                maxLines: 3,
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if (trimmedValue?.isEmpty ?? true) {
                    return LocaleKeys.validation_address1_is_empty.tr;
                  }
                  return null;
                },
              ),
              60.verticalSpace,
              _buildTextFormField(
                initalValue:
                    controller.isEdit() || controller.isFromBuyRequest()
                        ? AuthProvider.userEntity()
                                .billingAddress
                                ?.addressLineTwo ??
                            ''
                        : '',
                name: RegistrationFormFields.physical_adress_line_two,
                onChange: (String? value) => controller.debouncer.run(() =>
                    controller.getAddressDetails(
                        address: value ?? '', isPhysicalAddress: true)),
                keyboardType: TextInputType.streetAddress,
                autofillHints: <String>[CustomAutofillHints.addressLineTwo],
                labelText:
                    LocaleKeys.new_company_registration_address_line_two.tr,
                maxLines: 3,
              ),
              60.verticalSpace,
              _buildTextFormField(
                onTap: () {
                  commonShowCountryPicker(context, onSelect: (Country country) {
                    clearPhysicalAddField();
                    controller
                      ..selectedCountryData =
                          controller.filterCoutryFromJson(country) ?? {}
                      ..selectedCountryForPhysicalAddress(country)
                      ..isForChangePhysical(true)
                      ..isForChangePhysical.refresh()
                      ..update()
                      ..setPhysicalStateList();
                    logE(controller.isForChangePhysical());

                    controller.fbKey.currentState
                        ?.fields[RegistrationFormFields.physical_adress_country]
                        ?.didChange(country.name);
                    controller.update();
                  });
                },
                readOnly: true,
                initalValue: controller.isEdit() ||
                        controller.isFromBuyRequest()
                    ? AuthProvider.userEntity().billingAddress?.country ?? ''
                    : controller.selectCountryForPhysicalAddress(),
                name: RegistrationFormFields.physical_adress_country,
                labelText: LocaleKeys.new_company_registration_country.tr,
                keyboardType: TextInputType.text,
                autofillHints: const <String>[AutofillHints.countryName],
                isRequired: true,
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if (trimmedValue?.isEmpty ?? true) {
                    return LocaleKeys.validation_country_is_empty.tr;
                  }
                  return null;
                },
              ),
              76.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: _buildTextFormField(
                      initalValue: controller.isEdit() ||
                              controller.isFromBuyRequest()
                          ? AuthProvider.userEntity().billingAddress?.zipCode ??
                              ''
                          : '',
                      onChange: (zip) {
                        if ((zip?.isNotEmpty ?? false) &&
                            controller.physicalCityList.isEmpty) {
                          controller.physicalCityList(
                              controller.getCityListFromState(
                                      zip,
                                      RegistrationFormFields
                                          .physical_adress_state) ??
                                  []);
                          controller.city(
                              controller.physicalCityList.firstOrNull ?? '');
                          controller.physicalCityList.refresh();
                          controller.update();
                        }
                      },
                      name: RegistrationFormFields.physical_adress_zip_code,
                      keyboardType: TextInputType.number,
                      autofillHints: const <String>[AutofillHints.postalCode],
                      labelText: LocaleKeys.new_company_registration_zip.tr,
                      isRequired: true,
                      validation: (String? value) {
                        final String? trimmedValue = value?.trim();
                        if (trimmedValue?.isEmpty ?? true) {
                          return LocaleKeys.validation_zip_is_empty.tr;
                        } else if (trimmedValue!.length > 10) {
                          return LocaleKeys
                              .validation_zip_code_should_be_less_than_10_digits
                              .tr;
                        }
                        return null;
                      },
                      onSubmit: (String? zip) {
                        controller.physicalCityList(
                            controller.getCityListFromState(
                                    zip,
                                    RegistrationFormFields
                                        .physical_adress_state) ??
                                []);
                        controller.city(
                            controller.physicalCityList.firstOrNull ?? '');
                        controller.physicalCityList.refresh();
                        controller.update();
                      },
                    ),
                  ),
                  40.horizontalSpace,
                  Obx(
                    () => Expanded(
                      child: controller.isPhysicalUs ||
                              controller.stateList.isEmpty ||
                              controller.selectedState().isEmpty
                          ? _buildTextFormField(
                              onTap: () {},
                              initalValue: controller.selectedState(),
                              name:
                                  RegistrationFormFields.physical_adress_state,
                              labelText:
                                  LocaleKeys.new_company_registration_state.tr,
                              keyboardType: TextInputType.text,
                              autofillHints: const <String>[
                                AutofillHints.addressState
                              ],
                              isRequired: true,
                              readOnly: controller.isPhysicalUs ||
                                  controller.stateList.isEmpty,
                              validation: controller.isPhysicalUs ||
                                      controller.stateList.isEmpty
                                  ? (value) => null
                                  : (String? value) {
                                      final String? trimmedValue =
                                          value?.trim();
                                      if (trimmedValue?.isEmpty ?? true) {
                                        return LocaleKeys
                                            .validation_state_is_empty.tr;
                                      }
                                      return null;
                                    },
                            )
                          : controller.stateList.isEmpty &&
                                  controller.selectedState().isEmpty
                              ? const SizedBox.shrink()
                              : DropDownFormField(
                                  isSearchable: true,
                                  hintText:
                                      '${LocaleKeys.new_company_registration_search_state.tr}...',
                                  onChanged: (value) {
                                    controller.selectedState(value);
                                    controller.physicalCityList(
                                        controller.getCityListFromState('', '',
                                                selectedState: value) ??
                                            []);
                                    controller.city(controller
                                            .physicalCityList.firstOrNull ??
                                        '');
                                    controller.physicalCityList.refresh();
                                  },
                                  style: AppFontStyles.skolaSans(
                                    color: AppColors.k70777E,
                                    fontSize: 45.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  items: controller.stateList(),
                                  selectedValue:
                                      controller.selectedState.isEmpty
                                          ? null
                                          : controller.selectedState(),
                                  menuItemStyle: AppFontStyles.skolaSans(
                                    color: AppColors.k101C28,
                                    fontSize: 45.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  decoration: InputDecoration(
                                    contentPadding: REdgeInsets.only(
                                      left: 48,
                                      bottom: 30,
                                      right: 48,
                                    ),
                                    constraints: BoxConstraints(
                                      minHeight: 150.h,
                                    ),
                                    label: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: <Widget>[
                                        Text(
                                          LocaleKeys
                                              .new_company_registration_state
                                              .tr,
                                        ),
                                        Text(
                                          ' *',
                                          style: AppFontStyles.skolaSans(
                                            fontSize: 35.sp,
                                            fontWeight: FontWeight.w900,
                                            color: Colors.red,
                                          ),
                                        ),
                                      ],
                                    ),
                                    labelStyle: AppFontStyles.skolaSans(
                                      color: AppColors.k70777E,
                                      fontSize: 45.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                    errorStyle:
                                        const TextStyle(color: Colors.red),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(24.r),
                                      borderSide: BorderSide(
                                        width: 1.w,
                                        color: AppColors.k70777E,
                                      ),
                                    ),
                                  ),
                                ),
                    ),
                  ),
                ],
              ),
              controller.physicalCityList().isNotEmpty
                  ? 76.verticalSpace
                  : 100.verticalSpace,
              Column(
                children: [
                  Obx(
                    () => controller.physicalCityList().isNotEmpty ||
                            controller.city().isNotEmpty
                        ? DropDownFormField(
                            isSearchable: true,
                            hintText:
                                '${LocaleKeys.new_company_registration_search_city.tr}...',
                            onChanged: (value) {
                              controller.city(value);
                              controller.city.refresh();
                            },
                            selectedValue: controller.city(),
                            items: controller.physicalCityList(),
                            menuItemStyle: AppFontStyles.skolaSans(
                              fontSize: 45.sp,
                              color: AppColors.k101C28,
                              fontWeight: FontWeight.w400,
                            ),
                            style: AppFontStyles.skolaSans(
                              fontSize: 45.sp,
                              color: AppColors.k101C28,
                              fontWeight: FontWeight.w400,
                            ),
                            decoration: InputDecoration(
                              contentPadding: REdgeInsets.only(
                                left: 48,
                                bottom: 30,
                                right: 48,
                              ),
                              constraints: BoxConstraints(
                                minHeight: 150.h,
                              ),
                              label: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Text(
                                    LocaleKeys.new_company_registration_city.tr,
                                  ),
                                  Text(
                                    ' *',
                                    style: AppFontStyles.skolaSans(
                                      fontSize: 35.sp,
                                      fontWeight: FontWeight.w900,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                              labelStyle: AppFontStyles.skolaSans(
                                color: AppColors.k70777E,
                                fontSize: 45.sp,
                                fontWeight: FontWeight.w400,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(24.r),
                                borderSide: BorderSide(
                                  width: 1.w,
                                  color: AppColors.k70777E,
                                ),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(24.r),
                                borderSide: BorderSide(
                                  width: 1.w,
                                  color: AppColors.k70777E,
                                ),
                              ),
                              errorStyle: const TextStyle(color: Colors.red),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(24.r),
                                borderSide: BorderSide(
                                  width: 1.w,
                                  color: AppColors.k70777E,
                                ),
                              ),
                            ),
                          )
                        : _buildTextFormField(
                            initalValue: controller.city(),
                            name: RegistrationFormFields.physical_adress_city,
                            keyboardType: TextInputType.text,
                            readOnly: controller.physicalCityList.isEmpty,
                            autofillHints: const <String>[
                              AutofillHints.addressCity
                            ],
                            labelText:
                                LocaleKeys.new_company_registration_city.tr,
                            isRequired: true,
                            validation: controller.physicalCityList.isEmpty
                                ? (value) {
                                    return null;
                                  }
                                : (String? value) {
                                    if (value?.trim().isEmpty ?? true) {
                                      return LocaleKeys
                                          .validation_city_is_empty.tr;
                                    }
                                    return null;
                                  },
                          ),
                  ),
                  Obx(
                    () => controller.physicalAddressExpansionTile()
                        ? 100.verticalSpace
                        : 0.verticalSpace,
                  ),
                ],
              ),
              // Obx(
              //   () => controller.physicalAddressExpansionTile()
              //       ? 100.verticalSpace
              //       : 0.verticalSpace,
              // ),
            ],
          ),
        ),
      );

  void clearPhysicalAddField() {
    controller.fbKey.currentState
        ?.fields[RegistrationFormFields.physical_adress_zip_code]
        ?.didChange('');
    controller.selectedState('');
    controller.stateList.clear();
    controller.fbKey.currentState
        ?.fields[RegistrationFormFields.physical_adress_state]
        ?.didChange('');
    controller.city('');
    controller.physicalCityList([]);
    controller
        .fbKey.currentState?.fields[RegistrationFormFields.physical_adress_city]
        ?.didChange('');
  }

  void clearMailingAddField() {
    controller.fbKey.currentState
        ?.fields[RegistrationFormFields.mailing_adress_zip_code]
        ?.didChange('');
    controller.selectedStateForMailing('');
    controller.mailingAddressStateList = [];
    controller
        .fbKey.currentState?.fields[RegistrationFormFields.mailing_adress_state]
        ?.didChange('');
    controller.mailingCity('');
    controller.mailingAddCityList([]);
    controller
        .fbKey.currentState?.fields[RegistrationFormFields.mailing_adress_city]
        ?.didChange('');
  }

  /// Company Expansion Tile
  Widget _buildCompanyExpansionTile(BuildContext context) => Obx(
        () => Theme(
          data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
          child: ExpansionTile(
            title: Text(
              LocaleKeys.new_company_registration_company.tr,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
                letterSpacing: 5.sp,
              ),
            ),
            onExpansionChanged: (bool value) {
              controller.companyExpansionToggle(value);
            },
            dense: true,
            maintainState: true,
            tilePadding: EdgeInsets.zero,
            initiallyExpanded: controller.companyExpansionTile(),
            trailing: Container(
              height: 100.h,
              width: min(100.w, 50),
              decoration: const BoxDecoration(
                color: AppColors.kEAEFF4,
                shape: BoxShape.circle,
              ),
              child: Icon(
                controller.companyExpansionTile.value
                    ? Icons.expand_less_sharp
                    : Icons.expand_more_sharp,
                color: AppColors.k101C28,
              ),
            ),
            children: <Widget>[
              60.verticalSpace,
              _buildTextFormField(
                initalValue: controller.isEdit()
                    ? AuthProvider.userEntity().legalRegisteredName ?? ''
                    : '',
                name: RegistrationFormFields.companyLegalName,
                keyboardType: TextInputType.text,
                autofillHints: <String>[CustomAutofillHints.companyLegalName],
                labelText: LocaleKeys
                    .new_company_registration_registered_legal_company_name.tr,
                isRequired: true,
                validation: (String? value) {
                  if (value?.trim().isEmpty ?? true) {
                    return LocaleKeys
                        .validation_registered_legal_company_name.tr;
                  }
                  return null;
                },
              ),
              76.verticalSpace,
              _buildTextFormField(
                initalValue: controller.isEdit()
                    ? AuthProvider.userEntity().companyOperatingName ?? ''
                    : '',
                name: RegistrationFormFields.tradeStyle,
                keyboardType: TextInputType.text,
                autofillHints: <String>[CustomAutofillHints.dbaTradeStyle],
                labelText:
                    LocaleKeys.new_company_registration_dba_trade_style.tr,
                validation: (String? value) {
                  if (value?.trim().isNotEmpty ?? false) {
                    return null;
                  }
                  return null;
                },
              ),
              76.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: _buildTextFormField(
                      initalValue: controller.isEdit()
                          ? AuthProvider.userEntity().fedTaxId ?? ''
                          : '',
                      name: RegistrationFormFields.fedTaxId,
                      labelText:
                          LocaleKeys.new_company_registration_fed_tax_id.tr,
                      isRequired: true,
                      keyboardType: TextInputType.number,
                      autofillHints: <String>[CustomAutofillHints.fedTaxId],
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.digitsOnly,
                        FedTaxIdInputFormatter(),
                      ],
                      validation: (String? value) {
                        final String? trimmedValue = value?.trim();
                        if (trimmedValue?.isEmpty ?? true) {
                          return LocaleKeys.validation_fed_tax_id_is_empty.tr;
                        } else if (trimmedValue!.length != 10) {
                          return LocaleKeys.validation_fed_tax_id_length.tr;
                        }
                        return null;
                      },
                    ),
                  ),
                  40.horizontalSpace,
                  Expanded(
                    child: _buildTextFormField(
                      initalValue: controller.isEdit()
                          ? AuthProvider.userEntity().resaleTax ?? ''
                          : '',
                      name: RegistrationFormFields.resaleTax,
                      labelText:
                          LocaleKeys.new_company_registration_resale_tax.tr,
                      keyboardType: TextInputType.url,
                      autofillHints: <String>[CustomAutofillHints.resaleTax],
                      validation: (String? value) {
                        if (value?.trim().isNotEmpty ?? false) {
                          return null;
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              76.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: _buildTextFormField(
                      initalValue: controller.isEdit()
                          ? AuthProvider.userEntity().jbtId ?? ''
                          : '',
                      name: RegistrationFormFields.jbtId,
                      labelText: LocaleKeys.new_company_registration_jbt_id.tr,
                      keyboardType: TextInputType.number,
                      autofillHints: <String>[CustomAutofillHints.jbtId],
                      validation: (String? value) {
                        final String? trimmedValue = value?.trim();
                        if (trimmedValue?.isNotEmpty ?? false) {
                          if (trimmedValue!.length > 10) {
                            return LocaleKeys
                                .validation_jbt_id_number_should_be_at_least_10_digits
                                .tr;
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  40.horizontalSpace,
                  Expanded(
                    child: _buildTextFormField(
                      onTap: () {
                        controller.showYearPicker(context);

                        /// Default
                        // controller.selectYear(context);
                      },
                      initalValue: controller.isEdit()
                          ? AuthProvider.userEntity().businessStartDate?.yyyy ??
                              ''
                          : controller.selectedYear.toString(),

                      /// Default
                      // : controller.selectedDate.yyyy,
                      autofillHints: const <String>[AutofillHints.birthdayYear],
                      name: RegistrationFormFields.businessStartDate,
                      labelText: LocaleKeys
                          .new_company_registration_year_business_started.tr,
                      isRequired: true,
                      readOnly: true,
                      validation: (String? value) {
                        if (value?.trim().isEmpty ?? true) {
                          return LocaleKeys
                              .validation_year_business_started_is_empty.tr;
                        }
                        return null;
                      },
                      suffix: IconButton(
                        onPressed: () {
                          controller.showYearPicker(context);
                          // controller.selectYear(context);
                        },
                        padding: EdgeInsets.zero,
                        icon: SizedBox(
                          height: 64.h,
                          width: 64.w,
                          child: AppImages.calenderLogo,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              60.verticalSpace,
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  LocaleKeys
                      .new_company_registration_anti_money_laundering_program
                      .tr,
                  style: AppFontStyles.skolaSans(
                    color: AppColors.k101C28,
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              40.verticalSpace,
              Column(
                children: controller.antiMoneyLaundering
                    .asMap()
                    .entries
                    .map(
                      (MapEntry<int, String> e) => ListTile(
                        onTap: () => controller.selectProgramIndex(e.key),
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        dense: true,
                        horizontalTitleGap: min(20.w, 8),
                        visualDensity: VisualDensity.compact,
                        leading: _buildAnimatedContainerAntiMoneyLaundering(
                          e: e,
                          value: [
                            controller.selectProgramIndex.value.toString()
                          ],
                        ),
                        title: Text(
                          e.value,
                          style: AppFontStyles.skolaSans(
                            color: AppColors.k101C28,
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
              60.verticalSpace,
              Align(
                alignment: Alignment.centerLeft,
                child: Row(
                  children: <Widget>[
                    Text(
                      LocaleKeys.new_company_registration_type_of_business.tr,
                      style: AppFontStyles.skolaSans(
                        color: AppColors.k101C28,
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    10.horizontalSpace,
                    Text(
                      '*',
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w900,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
              40.verticalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: controller.businessTypeList
                    .asMap()
                    .entries
                    .map(
                      (MapEntry<int, String> e) => ListTile(
                        onTap: () {
                          if (controller.selectBusinessTypeList
                              .contains(e.value)) {
                            controller.selectBusinessTypeList.remove(e.value);
                          } else {
                            controller.selectBusinessTypeList.add(e.value);
                          }

                          controller.selectBusinessTypeList.refresh();
                        },
                        contentPadding: EdgeInsets.zero,
                        minVerticalPadding: 0,
                        dense: true,
                        horizontalTitleGap: min(20.w, 8),
                        visualDensity: VisualDensity.compact,
                        leading: _buildAnimatedContainer(
                          e: e,
                          value: controller.selectBusinessTypeList(),
                        ),
                        title: Text(
                          _buildBusinessTypeTitle(e.value),
                          style: AppFontStyles.skolaSans(
                            color: AppColors.k101C28,
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
              AnimatedSize(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                child: Visibility(
                  visible: controller.errorMessage.value.isNotEmpty,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: REdgeInsets.only(left: 44, top: 28),
                      child: Text(
                        controller.errorMessage.value,
                        style: AppFontStyles.skolaSans(
                          color: Colors.red,
                          fontSize: 38.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              60.verticalSpace,
              _buildTextFormField(
                initalValue: controller.isEdit()
                    ? AuthProvider.userEntity().yearsAtPresentLocation ?? ''
                    : controller.yearPresentLocation,
                name: RegistrationFormFields.yearsAtPresentLocation,
                labelText: LocaleKeys
                    .new_company_registration_year_at_present_location.tr,
                isRequired: true,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                autofillHints: <String>[
                  CustomAutofillHints.yearsAtPresentLocation
                ],
                keyboardType: TextInputType.number,
                // suffix: Padding(
                //   padding: REdgeInsets.only(right: 48),
                //   child: SizedBox(
                //     height: 64.h,
                //     width: 64.w,
                //     child: AppImages.calenderLogo,
                //   ),
                // ),
                validation: (String? value) {
                  if (value?.trim().isEmpty ?? true) {
                    return LocaleKeys
                        .validation_year_present_location_is_empty.tr;
                  }
                  return null;
                },
              ),
              controller.companyExpansionTile()
                  ? 100.verticalSpace
                  : 0.verticalSpace,
            ],
          ),
        ),
      );

  String _buildBusinessTypeTitle(String value) {
    if (value == 'Retailer') {
      return '${LocaleKeys.new_company_registration_retailer.tr}';
    } else if (value == 'Wholesaler') {
      return '${LocaleKeys.new_company_registration_wholesaler.tr}';
    } else if (value == 'Etailer') {
      return '${LocaleKeys.new_company_registration_etailer.tr}';
    }
    return '';
  }

  /// Animated Container
  Widget _buildAnimatedContainer(
          {required MapEntry<int, String> e, required List<String> value}) =>
      AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: 56.h,
        width: 56.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          border: Border.all(
            width: value.contains(e.value) ? min(19.w, 6) : min(3.w, 2),
          ),
        ),
      );

  /// Animated Container
  Widget _buildAnimatedContainerAntiMoneyLaundering(
          {required MapEntry<int, String> e, required List<String> value}) =>
      AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: 56.h,
        width: 56.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.kffffff,
          border: Border.all(
            width:
                value.contains(e.key.toString()) ? min(19.w, 6) : min(3.w, 2),
            color: AppColors.k101C28,
          ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (!controller.isLoading()) {
            if (kIsWeb) {
              DesignConfig.popItem();
            } else {
              Get.back();
            }
          }
        },
        title: Text(
          controller.isEdit()
              ? LocaleKeys.legal_status_organization_edit_profile.tr
              : controller.isFromBuyRequest()
                  ? LocaleKeys.edit_address_edit_address.tr
                  : LocaleKeys
                      .new_company_registration_new_company_registration.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          (controller.isEdit() || controller.isFromBuyRequest())
              ? const SizedBox.shrink()
              : FittedBox(
                  child: StatusChip(
                    text: LocaleKeys.new_company_registration_one_five.tr,
                    textStyle: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 35.sp,
                      letterSpacing: 10.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    color: AppColors.kEAEFF4,
                  ),
                ),
          //30.horizontalSpace,
          SizedBox(width: min(30.w, 15)),
        ],
      );

  /// Common TextFormField
  AppTextFormField<Widget> _buildTextFormField({
    required String name,
    String? Function(String?)? validation,
    TextEditingController? controller,
    void Function()? onTap,
    String? labelText,
    String? hintText,
    String? initalValue,
    bool isRequired = false,
    bool readOnly = false,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Widget? suffix,
    Widget? prefix,
    int? maxLines,
    Iterable<String>? autofillHints,
    List<TextInputFormatter>? inputFormatters,
    dynamic Function(String?)? onSubmit,
    dynamic Function(String?)? onChange,
  }) =>
      AppTextFormField(
        onTap: onTap ?? () {},
        onChange: onChange,
        controller: controller,
        inputFormatters: inputFormatters,
        name: name,
        onSubmit: onSubmit,
        keyboardType: keyboardType,
        autofillHints: autofillHints,
        validator: validation,
        labelText: labelText ?? '',
        hintText: hintText ?? '',
        initialValue: initalValue ?? '',
        isRequired: isRequired,
        readOnly: readOnly,
        prefixIcon: prefix,
        textInputAction: textInputAction ?? TextInputAction.next,
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        maxLines: maxLines ?? 1,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        hintTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        suffixIcon: suffix,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48, bottom: 30),
      );

  /// Text Description
  Widget _buildDescriptionText() => Center(
        child: controller.isEdit() || controller.isFromBuyRequest()
            ? const SizedBox.shrink()
            : Column(
                children: [
                  Text(
                    LocaleKeys
                        .new_company_registration_to_comply_with_the_usa_patriot_act
                        .tr,
                    style: AppFontStyles.skolaSans(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.k101C28.withOpacity(0.6),
                    ),
                  ),
                  60.verticalSpace,
                ],
              ),
      );
}
