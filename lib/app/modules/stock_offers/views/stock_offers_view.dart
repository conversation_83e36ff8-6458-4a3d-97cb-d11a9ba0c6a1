import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/empty_screens.dart';
import 'package:diamond_company_app/app/ui/components/offer_filter_dialog.dart';
import 'package:diamond_company_app/app/ui/components/offer_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../controllers/stock_offers_controller.dart';

class StockOffersView extends GetView<StockOffersController> {
  const StockOffersView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : controller.onGoingOffers.isEmpty &&
                    controller.competedOffers.isEmpty
                ? EmptyScreen(
                    icon: AppImages.emptyOffer,
                    title: 'No Offers Yet',
                    subtitle:
                        "You haven't made any offers on diamonds. Start exploring and make your first offer!",
                  )
                : RefreshIndicator(
                    color: AppColors.k101C28,
                    onRefresh: () => controller.getOnGoingOffersList(),
                    child: ListView(
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      controller: controller.scrollController,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            20.verticalSpace,
                            if (controller.onGoingOffers.isNotEmpty)
                              Text(
                                'Ongoing Offers - ${controller.onGoingOffers.length.toString().padLeft(2, '0')}'
                                    .toUpperCase(),
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w900,
                                  fontSize: 40.sp,
                                  color: AppColors.k101C28,
                                ),
                              ).paddingSymmetric(horizontal: 60.w),
                            if (controller.onGoingOffers.isNotEmpty)
                              _buildOnGoingOffersList(),
                            50.verticalSpace,
                            if (controller.competedOffers.isNotEmpty)
                              Text(
                                'Past Offers - ${controller.competedOffers.length.toString().padLeft(2, '0')}'
                                    .toUpperCase(),
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w900,
                                  fontSize: 40.sp,
                                  color: AppColors.k101C28,
                                ),
                              ).paddingSymmetric(horizontal: 60.w),
                            if (controller.competedOffers.isNotEmpty)
                              _buildCompletedOfferList(),
                          ],
                        ),
                      ],
                    ),
                  ),
      );

  ListView _buildCompletedOfferList() => ListView.separated(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.only(
          left: 60.w,
          right: 60.w,
          top: 60.h,
          bottom: 60.h + MediaQuery.of(Get.context!).padding.bottom,
        ),
        clipBehavior: Clip.none,
        itemBuilder: (BuildContext context, int index) {
          if (index == controller.competedOffers.length) {
            // controller.loadMoreData();
            return Obx(
              () => controller.isLoadMore()
                  ? AppLoader()
                  : const SizedBox.shrink(),
            );
          } else {
            return OfferListTile(
              stockOffer: controller.competedOffers[index],
              onTap: () => controller
                  .navigateToOfferDetails(controller.competedOffers[index]),
            );
          }
        },
        separatorBuilder: (BuildContext context, int index) => 40.verticalSpace,
        itemCount: controller.competedOffers.length + 1,
      );

  ListView _buildOnGoingOffersList() => ListView.separated(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        padding: REdgeInsets.all(60),
        clipBehavior: Clip.none,
        itemBuilder: (BuildContext context, int index) => OfferListTile(
          stockOffer: controller.onGoingOffers[index],
          onTap: () => controller
              .navigateToOfferDetails(controller.onGoingOffers[index]),
        ),
        separatorBuilder: (BuildContext context, int index) => 40.verticalSpace,
        itemCount: controller.onGoingOffers.length,
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: Text(
          'My Offers',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton.outlined(
            constraints: BoxConstraints(
              maxHeight: 120.h,
              maxWidth: 120.w,
            ),
            style: OutlinedButton.styleFrom(
              backgroundColor: AppColors.kBEFFFC,
              side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
              shape: const CircleBorder(),
              padding: EdgeInsets.zero,
              fixedSize: Size(120.w, 120.h),
            ),
            onPressed: () => showFilterOfferDialog(),
            icon: SizedBox(
              height: 48.h,
              width: 48.w,
              child: AppImages.filterIcon,
            ),
          ),
        ],
        onBackPressed: () => Get.back(),
      );
}
