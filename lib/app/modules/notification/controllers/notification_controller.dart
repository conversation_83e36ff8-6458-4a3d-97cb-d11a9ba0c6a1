import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/notification_model/notification_model.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/providers/notification_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/download_invoice_dialog.dart';
import 'package:diamond_company_app/app/ui/components/kyc_resubmission.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// NotificationController
class NotificationController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_scrollListener);
    fetchNotificationList();
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      if (!isLoading() && isLoadMore()) {
        fetchNotificationList(skip: skip(), limit: limit());
      }
    }
  }

  /// isLoading
  RxBool isLoading = false.obs;

  /// isLoadMore
  RxBool isLoadMore = true.obs;

  /// limit
  RxInt limit = 10.obs;

  /// limit
  RxInt skip = 0.obs;

  /// scroll controller
  final ScrollController scrollController = ScrollController();

  /// is read all visible
  RxBool get isMarkReadAsAllVisible => notificationList
      .any((NotificationModel element) => element.isRead ?? false)
      .obs;

  /// notification list
  RxList<NotificationModel> notificationList = <NotificationModel>[].obs;

  /// fetch notification list
  Future<void> fetchNotificationList({int skip = 0, int limit = 10}) async {
    try {
      if (skip == 0) {
        this.skip(0);
        isLoading(true);
        isLoadMore(true);
        notificationList().clear();
      }

      logI('api call $skip');

      final List<NotificationModel> notificationData =
          await NotificationProvider.notificationList(
        skip: skip,
        limit: limit,
      );

      if (notificationData.isEmpty || notificationData.length < this.limit()) {
        isLoadMore(false);
      } else {
        this.skip(this.skip() + this.limit());
      }
      notificationList
        ..addAll(notificationData)
        ..refresh();

      isLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logI(e);
    } finally {
      isLoading(false);
    }
  }

  /// read notification
  Future<void> readNotification({String? notificationId, bool? readAll}) async {
    try {
      await NotificationProvider.readNotification(
        notificationId: notificationId,
        readAll: readAll,
      );
      if (Get.isRegistered<DashboardController>()) {
        final DashboardController controller = Get.find<DashboardController>();

        final int updatedCount = max(0,
            (controller.dashboardMetrics().unreadNotificationCount ?? 0) - 1);

        controller.dashboardMetrics(
          controller.dashboardMetrics().copyWith(
              unreadNotificationCount: (readAll ?? false) ? 0 : updatedCount),
        );

        if (readAll ?? false) {
          notificationList(notificationList
              .map(
                  (NotificationModel element) => element.copyWith(isRead: true))
              .toList());
          notificationList.refresh();
        }
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logI(e);
    }
  }

  /// navigate from notification
  Future<void> navigateFromNotification(NotificationModel notification) async {
    if (!(notification.isRead ?? true)) {
      final int index = notificationList.indexWhere(
          (NotificationModel element) => element.id == notification.id);
      if (index != -1) {
        notificationList[index] =
            notificationList[index].copyWith(isRead: true);
        notificationList.refresh();
      }
      unawaited(readNotification(notificationId: notification.id ?? ''));
    }
    final Map<String, dynamic> payload =
        jsonDecode(notification.payload.toString());

    if ([
      NotificationType.BUY_REQUEST_CREATED,
      NotificationType.BUY_REQUEST_REJECTED,
      NotificationType.BUY_REQUEST_UPDATED
    ].contains(notification.notificationType)) {
      if (payload['buy_request_id'] != null) {
        await Get.toNamed(
          Routes.BUY_REQUEST_DETAILS,
          arguments: BuyRequest(id: payload['buy_request_id']),
        );
      }
    } else if ([
      NotificationType.ORDER_CANCELED,
      NotificationType.ORDER_CREATED,
      NotificationType.ORDER_DELIVERED,
      NotificationType.ORDER_SHIPPED,
    ].contains(notification.notificationType)) {
      if (payload['order_id'] != null) {
        await Get.toNamed(
          Routes.MY_ORDER_DETAIL,
          arguments: UserOrder(id: payload['order_id']),
        );
      }
    } else if ([
      NotificationType.RETURN_ORDER_ACCEPTED,
      NotificationType.RETURN_ORDER_REJECTED,
    ].contains(notification.notificationType)) {
      if (payload['order_id'] != null) {
        await Get.toNamed(
          Routes.RETURN_ORDER,
          arguments: UserOrder(id: payload['order_id']),
        );
      }
    } else if ([
      NotificationType.STOCK_UPDATED,
      NotificationType.STOCK_AVAILABLE,
      NotificationType.STOCK_HOLD,
      NotificationType.STOCK_SOLD,
      NotificationType.STOCK_PRICE_DECREASED,
      NotificationType.STOCK_PRICE_INCREASED
    ].contains(notification.notificationType)) {
      if (payload['stock_id'] != null) {
        logI('stock_id');
        logI(payload['stock_id']);
        await Get.toNamed(
          Routes.DIAMOND_DETAILS,
          arguments: DiamondArgs(
            id: payload['id'],
            diamond: DiamondEntity(
              stockId: payload['stock_id'],
              adminId: payload['admin_id'],
              vendorId: payload['vendor_id'],
            ),
          ),
        );
      }
    } else if (NotificationType.INVOICE_AVAILABLE ==
        notification.notificationType) {
      if (payload['invoice_url'] != null) {
        // await Get.toNamed<void>(
        //   Routes.MY_ORDER_DETAIL,
        //   arguments: UserOrder(id: payload['order_id']),
        // );
        showDownloadInvoiceDialog(
          showInvoiceOnly: true,
          invoiceUrl: payload['invoice_url'],
        );
      }
    } else if ([
      NotificationType.OFFER_ACCEPTED,
      NotificationType.OFFER_REJECTED,
      NotificationType.OFFER_REVISED,
      NotificationType.OFFER_CREATED,
    ].contains(notification.notificationType)) {
      if (payload['offer_id'] != null) {
        await Get.toNamed(Routes.OFFER_DETAILS,
            arguments: StockOffer(id: payload['offer_id']));
      }
    } else if ([NotificationType.KYC_ACCEPTED, NotificationType.KYC_REJECTED]
        .contains(notification.notificationType)) {
      await previewKYCDocument();
    }
  }

  @override
  void onClose() {
    super.onClose();
    scrollController
      ..removeListener(_scrollListener)
      ..dispose();
  }
}
