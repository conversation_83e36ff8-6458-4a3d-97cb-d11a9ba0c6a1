import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/modules/inquiry_messages/controllers/inquiry_messages_controller.dart';
import 'package:diamond_company_app/app/modules/inquiry_messages/views/message_media_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ChatMediaWidget {
  static final InquiryMessagesController controller =
      Get.find<InquiryMessagesController>();

  /// Audio Message widget
  ///
  static Widget buildAudioMessage(
    int index, {
    required Map<String, dynamic> media,
    required BuildContext context,
    // required PlayerController playerController,
  }) =>
      FutureBuilder<PlayerController?>(
        future: controller.toggleAudioMessage(
          media['file_url'] ?? controller.localMediaFiles.first['file'],
          index,
        ),
        builder: (BuildContext context, snapshot) {
          logE(snapshot.connectionState);
          if (snapshot.connectionState != ConnectionState.waiting) {
            return const Text('Loading audio...').paddingAll(8);
          }
          if (snapshot.hasError) {
            return const Text('Error loading audio').paddingAll(8);
          }
          final PlayerController playerController =
              snapshot.data ?? PlayerController();

          controller.audioPlayers[index] = playerController;
          playerController.updateFrequency = UpdateFrequency.high;

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
            decoration: BoxDecoration(
              color: AppColors.kffffff,
              border: Border.all(
                color: AppColors.kffffff,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                IconButton(
                  onPressed: () {
                    if (playerController.playerState.isPlaying) {
                      playerController.pausePlayer();
                      logE('PAUSE');
                      controller.audioPlayingStatus()[index] = false;
                    } else if (playerController.playerState.isStopped) {
                      logE('STOPPER');
                      controller.restartAudioPlayer(media['file_url'], index);
                    } else {
                      logE('PLAY');
                      playerController.startPlayer();
                      controller.audioPlayingStatus()[index] = true;
                    }
                    controller.audioPlayingStatus.refresh();
                  },
                  icon: Icon(
                    controller.audioPlayingStatus[index] ?? false
                        ? Icons.pause
                        : Icons.play_arrow,
                  ),
                ),
                AudioFileWaveforms(
                  size: Size(context.width * 0.5, 50),
                  playerController: playerController,
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  playerWaveStyle: PlayerWaveStyle(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    liveWaveColor: Theme.of(context).colorScheme.primary,
                    fixedWaveColor: AppColors.k1A47E8.withOpacity(0.5),
                    seekLineColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        },
      );
  // static Widget buildAudioMessage(
  //   int index, {
  //   required Map<String, dynamic> media,
  //   required BuildContext context,
  //   required PlayerController playerControllerh,
  // }) =>
  //     FutureBuilder<dynamic>(
  //       future: controller.toggleAudioMessage(
  //         media['file_url'],
  //         index,
  //       ),
  //       builder: (BuildContext context, snapshot) {
  //         PlayerController playerController = PlayerController();
  //         if (snapshot.data != null) {
  //           final PlayerController playerController =
  //               snapshot.data as PlayerController;
  //           playerController.updateFrequency = UpdateFrequency.high;
  //         }
  //
  //         return Container(
  //           padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
  //           decoration: BoxDecoration(
  //             color: AppColors.kffffff,
  //             border: Border.all(
  //               color: AppColors.kffffff,
  //             ),
  //             // borderRadius: Ro,
  //           ),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             crossAxisAlignment: CrossAxisAlignment.end,
  //             children: <Widget>[
  //               Row(
  //                 mainAxisSize: MainAxisSize.min,
  //                 children: <Widget>[
  //                   if (snapshot.data != null) ...<Widget>[
  //                     IconButton(
  //                       onPressed: () {
  //                         if (playerController.playerState.isPlaying) {
  //                           playerController.pausePlayer();
  //
  //                           controller
  //                               .audioPlayingStatus()
  //                               .update(index, (bool value) => value = false);
  //                         } else if (playerController.playerState.isStopped) {
  //                           controller.restartAudioPlayer(
  //                               media['file_url'], index);
  //                         } else {
  //                           playerController.startPlayer();
  //
  //                           controller
  //                               .audioPlayingStatus()
  //                               .update(index, (bool value) => value = true);
  //                         }
  //                         controller.audioPlayingStatus.refresh();
  //                       },
  //                       icon: Icon(
  //                         controller.audioPlayingStatus[index] ?? false
  //                             ? Icons.pause
  //                             : Icons.play_arrow,
  //                       ),
  //                     ),
  //                     AudioFileWaveforms(
  //                       size: Size(context.width * 0.5, 50),
  //                       playerController: playerController,
  //                       backgroundColor: Theme.of(context).colorScheme.surface,
  //                       playerWaveStyle: PlayerWaveStyle(
  //                         backgroundColor:
  //                             Theme.of(context).colorScheme.surface,
  //                         liveWaveColor: Theme.of(context).colorScheme.primary,
  //                         fixedWaveColor: AppColors.k1A47E8.withOpacity(0.5),
  //                         seekLineColor: Theme.of(context).colorScheme.primary,
  //                       ),
  //                     )
  //                   ] else
  //                     const Text('Loading audio...').paddingAll(8),
  //                 ],
  //               )
  //             ],
  //           ),
  //         );
  //         // }
  //         // return const Text('Loading audio...').paddingAll(8);
  //       },
  //     );

  /// Recording view widget
  static Widget recordingView(
    BuildContext context,
  ) =>
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2),
        child: Column(
          children: <Widget>[
            GestureDetector(
              onTap: () {
                controller.handleRecordingState();
              },
              child: Container(
                height: 300.h,
                margin: REdgeInsets.only(
                  top: 10,
                  bottom: 5,
                ),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.background,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Align(
                      alignment: Alignment.topCenter,
                      child: Text(
                        '${controller.timerValue}',
                        style: TextStyle(
                          fontSize: 55.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (controller.isRecording())
                      IgnorePointer(
                        child: AudioWaveforms(
                          size: Size(
                            double.infinity,
                            100.h,
                          ),
                          recorderController: controller.recorderController,
                          waveStyle: WaveStyle(
                            middleLineColor:
                                Theme.of(context).colorScheme.primary,
                          ),
                          enableGesture: true,
                        ),
                      ),
                    if (!controller.isRecording())
                      const Text(
                        'Tap to record your voice',
                      ),
                  ],
                ),
              ),
            ),
            20.verticalSpace,
            Row(
              children: <Widget>[
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      controller.stopRecording(
                        saveRecord: false,
                      );
                      logWTF(controller.isRecordingView.value);
                      controller.refreshTimer();
                    },
                    child: const CircleAvatar(
                      child: Icon(
                        Icons.close,
                        size: 20,
                      ),
                    ),
                  ),
                ),
                10.horizontalSpace,
                Expanded(
                  child: IconButton(
                    icon: Icon(
                      controller.isRecording()
                          ? (controller.isPaused.value ? Icons.mic : Icons.stop)
                          : Icons.mic,
                    ),
                    color: controller.isRecording() ? Colors.red : Colors.black,
                    onPressed: () {
                      controller.handleRecordingState();
                    },
                  ),
                ),
                10.horizontalSpace,
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      controller.stopRecording(saveRecord: true);
                    },
                    child: const CircleAvatar(
                      child: Icon(
                        Icons.check,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      );

  /// Video message widget
  static Widget videoMessage(
    Map<String, dynamic> message,
  ) {
    final bool isFromAssets =
        message['from'] != null && message['from'] == 'assets';
    return GestureDetector(
      onTap: () {
        logW(isFromAssets);
        if (isFromAssets) {
          Get.to(
            MessageMediaPreview(
              mediaType: message['type'],
              mediaUrl: message['file'],
              fromAssets: isFromAssets,
            ),
          );
        } else {
          Get.to(
            MessageMediaPreview(
              mediaType: message['type'],
              mediaUrl: message['file_url'],
            ),
          );
        }
      },
      child: Stack(
        children: <Widget>[
          Container(
            width: 200,
            height: 280,
            decoration: BoxDecoration(
              image: DecorationImage(
                fit: BoxFit.cover,
                image: (isFromAssets
                    ? MemoryImage(
                        File('${message['thumbnail'] ?? ''}').readAsBytesSync())
                    : NetworkImage(
                        '${message['thumbnail_url'] ?? ''}',
                      )) as ImageProvider<Object>,
              ),
            ),
          ),
          const Positioned(
            top: 120,
            right: 80,
            child: Icon(
              Icons.play_circle,
              size: 40,
              color: AppColors.kffffff,
            ),
          ),
        ],
      ),
    );
  }
}
