import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/inquiry_messages/views/media_widget.dart';
import 'package:diamond_company_app/app/modules/inquiry_messages/views/message_media_preview.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/utils/chat_message_ext.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:linkfy_text/linkfy_text.dart';
import 'package:linkfy_text/src/model/link.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../data/config/design_config.dart';
import '../../../data/models/chat-messages/chat_message_model.dart';
import '../../../ui/components/app_loader.dart';
import '../controllers/inquiry_messages_controller.dart';

/// InquiryMessagesView
class InquiryMessagesView extends GetView<InquiryMessagesController> {
  /// InquiryMessagesView
  const InquiryMessagesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
        bottomSheet: _buildSendMessage(),
      );

  Widget _buildSendMessage() => LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final bool isKeyboardOpen =
              MediaQuery.of(Get.context!).viewInsets.bottom > 0;

          return Obx(
            () => Container(
              margin: EdgeInsets.only(
                  bottom: isKeyboardOpen
                      ? 0
                      : MediaQuery.of(Get.context!).padding.bottom),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Visibility(
                    visible: controller.listofMediaWithoutAudio.isNotEmpty,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      height: 220.h,
                      color: AppColors.kF6F6F6,
                      child: Center(
                        child: ListView.separated(
                          padding: REdgeInsets.symmetric(
                              horizontal: 40, vertical: 20),
                          physics: const BouncingScrollPhysics(),
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (BuildContext context, int index) {
                            final Map<String, dynamic> media =
                                controller.listofMediaWithoutAudio[index];
                            return Stack(
                              alignment: Alignment.topRight,
                              clipBehavior: Clip.none,
                              children: <Widget>[
                                Container(
                                  height: 175.r,
                                  width: 175.r,
                                  padding: REdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(24.r)),
                                  ),
                                  child: kIsWeb
                                      ? Image.memory(
                                          media['bytes'],
                                          fit: BoxFit.cover,
                                        )
                                      : Image.file(
                                          File(media['type'] == 'image'
                                              ? media['file']
                                              : media['thumbnail'] ?? ''),
                                          fit: BoxFit.fill,
                                        ),
                                ),
                                Positioned(
                                  top: -5,
                                  right: -5,
                                  child: CircleAvatar(
                                    radius: 30.r,
                                    backgroundColor: AppColors.kffffff,
                                    child: IconButton(
                                      onPressed: () {
                                        controller.listOfMedia.removeWhere(
                                            (Map<String, dynamic> element) =>
                                                element['file'] ==
                                                media['file']);

                                        controller.localMediaFiles.removeWhere(
                                            (Map<String, dynamic> element) =>
                                                element['file'] ==
                                                media['file']);

                                        controller.listOfMedia.refresh();

                                        logWTF(controller
                                            .listofMediaWithoutAudio.length);
                                      },
                                      style: IconButton.styleFrom(
                                        tapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                        padding: EdgeInsets.zero,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                      constraints: BoxConstraints(),
                                      visualDensity: VisualDensity.compact,
                                      padding: EdgeInsets.zero,
                                      icon: Icon(
                                        size: 50.sp,
                                        Icons.close,
                                        color: AppColors.k000000,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                          itemCount: controller.listofMediaWithoutAudio.length,
                          separatorBuilder: (BuildContext context, int index) =>
                              40.horizontalSpace,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: Get.width,
                    padding: REdgeInsets.symmetric(vertical: 65),
                    decoration: BoxDecoration(
                      color: AppColors.kffffff,
                      borderRadius: BorderRadius.all(Radius.circular(24.r)),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: AppColors.k000000.withOpacity(0.1),
                          offset: Offset(0, 10.h),
                          blurRadius: 104.r,
                        ),
                      ],
                    ),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: AppTextFormField<dynamic>(
                            name: 'name',
                            controller: controller.messageController,
                            hintText: LocaleKeys.inquiries_write_message.tr,
                            labelText: LocaleKeys.inquiries_write_message.tr,
                            textInputAction: TextInputAction.newline,
                            keyboardType: TextInputType.multiline,
                            maxLines: null,
                            style: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.k101C28,
                            ),
                            onChange: (String? p0) {
                              controller.messageText(p0);
                              controller.messageText.refresh();
                            },
                            hintTextStyle: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.k70777E,
                            ),
                            floatingLabelBehavior: FloatingLabelBehavior.never,
                            labelTextStyle: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.k70777E,
                            ),
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            IconButton.outlined(
                              color: AppColors.k101C28,
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(
                                  color: AppColors.k101C28,
                                  width: 1.w,
                                ),
                                shape: const CircleBorder(),
                              ),
                              onPressed: () {
                                Get.bottomSheet(attachmentWidget());
                              },
                              icon: SizedBox(
                                height: 64.h,
                                width: min(64.w, 50),
                                child: AppImages.attach,
                              ),
                            ),
                            Obx(
                              () => AnimatedOpacity(
                                opacity: (controller.messageText().isNotEmpty ||
                                        controller
                                            .listofMediaWithoutAudio.isNotEmpty)
                                    ? 1
                                    : 0.5,
                                duration: const Duration(milliseconds: 300),
                                child: IconButton.filled(
                                  color: AppColors.kBEFFFC,
                                  style: OutlinedButton.styleFrom(
                                    backgroundColor: AppColors.kBEFFFC,
                                    shape: const CircleBorder(),
                                  ),
                                  onPressed: () {
                                    if (controller.messageText().isNotEmpty ||
                                        controller.listofMediaWithoutAudio
                                            .isNotEmpty) {
                                      controller.sendMessage();
                                    }
                                  },
                                  icon: SizedBox(
                                    height: 64.h,
                                    width: min(64.w, 50),
                                    child: AppImages.send,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        //40.horizontalSpace,
                        SizedBox(width: min(40.w, 20))
                      ],
                    ),
                  ),
                  // Obx(
                  //   () => controller.isRecordingView()
                  //       ? AnimatedContainer(
                  //           height: controller.isRecordingView() ? 600.h : 0,
                  //           duration: const Duration(milliseconds: 300),
                  //           child: ChatMediaWidget.recordingView(Get.context!),
                  //         )
                  //       : const SizedBox.shrink(),
                  // ),
                ],
              ),
            ),
          );
        },
      );

  Widget attachmentWidget() => LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final bool isKeyboardOpen =
              MediaQuery.of(Get.context!).viewInsets.bottom > 0;
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                width: Get.width,
                height: 350.h,
                padding: REdgeInsets.symmetric(vertical: 30, horizontal: 40),
                margin: EdgeInsets.only(
                    bottom: isKeyboardOpen
                        ? 0
                        : MediaQuery.of(Get.context!).padding.bottom),
                decoration: BoxDecoration(
                  color: AppColors.k101C28,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24.r),
                    topRight: Radius.circular(24.r),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: <Widget>[
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Flexible(
                          child: IconButton.filled(
                            color: AppColors.kffffff,
                            style: OutlinedButton.styleFrom(
                              backgroundColor: AppColors.kffffff,
                              shape: const CircleBorder(),
                              fixedSize: Size(120.w, 120.h),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            onPressed: () {
                              Get.back();
                              controller.pickedFile(fileType: FileType.image);
                            },
                            icon: SizedBox(
                              height: 64.h,
                              width: 64.w,
                              child: AppImages.image,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        Text(
                          LocaleKeys.inquiries_image.tr,
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.kffffff,
                          ),
                        )
                      ],
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Flexible(
                          child: IconButton.filled(
                            color: AppColors.kffffff,
                            style: OutlinedButton.styleFrom(
                              backgroundColor: AppColors.kffffff,
                              shape: const CircleBorder(),
                              fixedSize: Size(120.w, 120.h),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            onPressed: () {
                              Get.back();
                              controller.pickedFile(fileType: FileType.video);
                            },
                            icon: SizedBox(
                              height: 64.h,
                              width: 64.w,
                              child: AppImages.video,
                            ),
                          ),
                        ),
                        15.verticalSpace,
                        Text(
                          LocaleKeys.inquiries_video.tr,
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.kffffff,
                          ),
                        )
                      ],
                    ),
                    // Column(
                    //   mainAxisAlignment: MainAxisAlignment.center,
                    //   children: <Widget>[
                    //     Flexible(
                    //       child: IconButton.filled(
                    //         color: AppColors.kffffff,
                    //         style: OutlinedButton.styleFrom(
                    //           backgroundColor: AppColors.kffffff,
                    //           shape: const CircleBorder(),
                    //           fixedSize: Size(120.w, 120.h),
                    //           tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    //         ),
                    //         onPressed: () {
                    //           Get.close(0);
                    //           controller.isRecordingView.toggle();
                    //         },
                    //         icon: SizedBox(
                    //           height: 64.h,
                    //           width: 64.w,
                    //           child: AppImages.audio,
                    //         ),
                    //       ),
                    //     ),
                    //     15.verticalSpace,
                    //     Text(
                    //       LocaleKeys.inquiries_audio.tr,
                    //       style: AppFontStyles.skolaSans(
                    //         fontWeight: FontWeight.w400,
                    //         fontSize: 40.sp,
                    //         color: AppColors.kffffff,
                    //       ),
                    //     ),
                    //   ],
                    // ),
                  ],
                ),
              ),
            ],
          );
        },
      );

  Widget _buildBody() => Obx(
        () => ListView.separated(
          controller: controller.messageScrollController,
          padding: EdgeInsets.only(
            top: 60.h,
            bottom: 300.h + MediaQuery.of(Get.context!).padding.bottom,
          ),
          itemBuilder: (BuildContext context, int index) {
            if (index < controller.chatMessage().length) {
              final ChatMessage messageList = controller.chatMessage()[index];

              return _buildMessageContent(index, messageList);
            } else {
              return AppLoader();
            }
          },
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          reverse: true,
          itemCount: controller.chatMessage().length +
              (controller.isPagination() ? 1 : 0),
          separatorBuilder: (BuildContext context, int index) =>
              30.verticalSpace,
        ),
      );

  Widget _buildMessageContent(int index, ChatMessage messageList) {
    final int nextIndex = index + 1;
    bool showDate = true;
    String? currentCreatedAt =
        controller.chatMessage[index].createdAt?.toLocal().ddMMyyyy;
    if (nextIndex < controller.chatMessage.length) {
      final String? nextCreatedAt =
          controller.chatMessage[nextIndex].createdAt?.toLocal().ddMMyyyy;
      showDate = currentCreatedAt != nextCreatedAt;
    }
    if (currentCreatedAt == DateTime.now().ddMMyyyy) {
      currentCreatedAt = 'Today';
    }

    if (controller.chatMessage[index].type.toString().toLowerCase() ==
        'checkout') {
      // final List<String> checkoutMessages = controller.chatMessage
      //     .where((ChatMessage element) =>
      //         element.type.toString().toLowerCase() == 'checkout')
      //     .toList()
      //     .map((ChatMessage e) => e.id ?? '')
      //     .toList();
      return _buildAddToCart(controller.chatMessage[index]);
    }

    return Column(
      children: <Widget>[
        showDate
            ? _buildTimeStamp(currentCreatedAt ?? '')
            : const SizedBox.shrink(),
        30.verticalSpace,
        Align(
          alignment: (messageList.fromUser ?? false)
              ? Alignment.centerRight
              : Alignment.centerLeft,
          child: _senderTile(messageList, index),
        ).paddingOnly(bottom: 30.h),
      ],
    );
  }

  Widget _buildAddToCart(ChatMessage chatMessage) => Container(
        padding: REdgeInsets.all(64),
        margin: REdgeInsets.all(60),
        decoration: BoxDecoration(
          color: AppColors.kBEFFFC,
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
        ),
        child: Column(
          children: [
            Text(
              LocaleKeys.inquiries_kindly_add_to_cart_and_place_the_order.tr,
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 40.sp,
                color: AppColors.k101C28,
              ),
              textAlign: TextAlign.center,
            ),
            48.verticalSpace,
            Text(
              chatMessage.variantPrice.toString().toPrice(),
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w900,
                fontSize: 72.sp,
                color: AppColors.k128807,
              ),
            ),
            48.verticalSpace,
            if (chatMessage.showCart ?? false)
              Obx(
                () => Center(
                  child: AppButton.custom(
                    onPressed: () => controller.addToCart(chatMessage),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Obx(
                          () => Text(
                            controller.isAddedTocart()
                                ? '${LocaleKeys.diamond_details_view_added_to_cart.tr}'
                                    .toUpperCase()
                                : '${LocaleKeys.diamond_details_view_add_to_cart.tr}'
                                    .toUpperCase(),
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color: controller.isAddedTocart()
                                  ? AppColors.kffffff
                                  : AppColors.k101C28,
                            ),
                          ),
                        ),
                      ],
                    ),
                    loaderHeight: min(50.h, 15),
                    loaderWidth: min(50.w, 15),
                    strokeWidth: 2,
                    isLoading: controller.isLoading(),
                    buttonSize: Size(522.w, 120.h),
                    buttonTextStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 35.sp,
                      color: controller.isAddedTocart()
                          ? AppColors.kffffff
                          : AppColors.k101C28,
                    ),
                    borderRadius: 500.r,
                    backgroundColor: controller.isAddedTocart()
                        ? AppColors.k128807
                        : AppColors.kffffff,
                    borderColor: controller.isAddedTocart()
                        ? AppColors.k128807
                        : AppColors.k101C28,
                  ),
                ),
              ),
          ],
        ),
      );

  Widget _buildTimeStamp(String date) => Center(
        child: Container(
          padding: REdgeInsets.symmetric(horizontal: 40, vertical: 20),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.all(Radius.circular(500.r)),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k70777E,
                offset: Offset(0, 5.h),
              )
            ],
            border: Border.all(
              color: AppColors.k70777E,
              width: 2.w,
            ),
          ),
          child: Text(
            '$date',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 30.sp,
              color: AppColors.k70777E,
            ),
          ),
        ),
      );

  Widget _senderTile(ChatMessage message, int i) => Container(
        margin: REdgeInsets.symmetric(horizontal: 60),
        padding: REdgeInsets.symmetric(vertical: 30, horizontal: 34),
        decoration: BoxDecoration(
          color: AppColors.kEAEFF4,
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
        ),
        child: Column(
          crossAxisAlignment: message.fromUser ?? false
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
          children: <Widget>[
            if (message.media?.isNotEmpty ?? false)
              Wrap(
                spacing: 10,
                runSpacing: 20,
                children: List<Widget>.generate(
                  message.media?.length ?? 0,
                  (int index) {
                    final bool isFromAssets =
                        message.media?[index]['from'] != null &&
                            message.media?[index]['from'] == 'assets';
                    return message.media?[index]['type'] == 'image'
                        ? GestureDetector(
                            onTap: () {
                              if (!isFromAssets) {
                                Get.to(MessageMediaPreview(
                                  mediaUrl: message.media?[index]['file_url'],
                                  mediaType: 'image',
                                  fromAssets: isFromAssets,
                                ));
                              } else {
                                Get.to(MessageMediaPreview(
                                  mediaUrl: message.media?[index]['file'],
                                  mediaType: 'image',
                                  fromAssets: true,
                                ));
                              }
                            },
                            child: Container(
                              height: 250.r,
                              width: 250.r,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              child: isFromAssets
                                  ? kIsWeb
                                      ? Image.memory(
                                          message.media?[index]['bytes'] ?? '',
                                          fit: BoxFit.cover,
                                        )
                                      : Image.file(
                                          File(message.media?[index]['file'] ??
                                              ''),
                                          fit: BoxFit.cover,
                                        )
                                  : Image.network(
                                      message.media?[index]['file_url'] ?? '',
                                      fit: BoxFit.cover,
                                    ),
                            ),
                          )
                        : message.media?[index]['type'] == 'video'
                            ? ChatMediaWidget.videoMessage(
                                message.media?[index] ?? {})
                            : message.media?[index]['type'] == 'audio'
                                ? ChatMediaWidget.buildAudioMessage(i,
                                    media: message.media?[index] ??
                                        <String, dynamic>{},
                                    context: Get.context!)
                                : const SizedBox.shrink();
                  },
                ),
              ),
            if (message.message?.isNotEmpty ?? false) _buildLinkyText(message),
            24.verticalSpace,
            Text(
              '${message.createdAt?.mmhh}',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w400,
                fontSize: 30.sp,
                color: AppColors.k101C28,
              ),
            ),
          ],
        ),
      );

  /// linkify text
  LinkifyText _buildLinkyText(ChatMessage message) => LinkifyText(
        message.message ?? '',
        onTap: (Link link) async {
          if (link.value?.isEmpty ?? true) {
            logE('Link is empty');
            return;
          }
          if (link.value?.contains(ApiConstants.shareBaseUrl) ?? false) {
            if (Get.isRegistered<DashboardController>()) {
              unawaited(Get.find<DashboardController>()
                  .handleIncomingLink(Uri.parse(link.value ?? '')));
            }
          } else if (!await launchUrl(Uri.parse(link.value ?? ''))) {
            logE('Could not launch ${link.value}');
          }
        },
        textStyle: AppFontStyles.skolaSans(
          fontWeight: FontWeight.w400,
          fontSize: 40.sp,
          color: AppColors.k101C28,
        ),
        linkStyle: AppFontStyles.skolaSans(
          fontWeight: FontWeight.w400,
          fontSize: 40.sp,
          color: AppColors.k1A47E8,
          decoration: TextDecoration.none,
        ),
      );

  Container _buildLinkCard() => Container(
        decoration: BoxDecoration(
          color: AppColors.kBEFFFC,
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
        ),
        width: 825.w,
        margin: REdgeInsets.only(bottom: 24),
        padding: REdgeInsets.symmetric(
          vertical: 40,
          horizontal: 30,
        ),
        child: Text(
          'https://www.google.com/search?q=link+in+whatsapp&sca_esv=469084b56',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w700,
            fontSize: 35.sp,
            color: AppColors.k1A47E8,
          ),
        ),
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: GestureDetector(
          onTap: () => controller.navigateToDetailsScreen(),
          child: Column(
            children: <Widget>[
              Text(
                '${controller.inquiryData.inquiry?.title ?? controller.inquiryObject['title'] ?? ''}'
                    .toUpperCase(),
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  fontSize: 45.sp,
                  color: AppColors.k101C28,
                ),
              ),
              Text(
                '${controller.inquiryData.inquiry?.subTitle ?? controller.inquiryObject['sub_title'] ?? ''}'
                    .toUpperCase(),
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 40.sp,
                  color: AppColors.k70777E,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.k101C28,
                  decorationThickness: 1,
                ),
              ),
            ],
          ),
        ),
        centerTitle: true,
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
      );
}
