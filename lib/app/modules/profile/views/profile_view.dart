import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/static_pages/static_pages.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_bottom_sheets.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/delete_alert_dialog.dart';
import 'package:diamond_company_app/app/ui/components/kyc_resubmission.dart';
import 'package:diamond_company_app/app/ui/components/language_selection_bottom_sheet.dart';
import 'package:diamond_company_app/app/ui/components/logout_alert_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/design_config.dart';
import '../../../routes/app_pages.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/profile_controller.dart';

/// ProfileView
class ProfileView extends GetView<ProfileController> {
  /// ProfileView
  const ProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildCommonAppbar(),
        body: _buildBody(),
      );

  /// Build Body
  Widget _buildBody() => SingleChildScrollView(
        clipBehavior: Clip.none,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              62.verticalSpace,
              _buildProfile(),
              100.verticalSpace,
              if (!(AuthProvider.userEntity().isVerified ?? true))
                const KycResubmission()
                    .paddingSymmetric(horizontal: min(60.w, 15)),
              if (!(AuthProvider.userEntity().isVerified ?? true))
                40.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_my_order.tr,
                svgImage: AppImages.myOrderLogo,
                onPressed: () => Get.toNamed(Routes.MY_ORDER,
                    id: DesignConfig.listingSideMenuId),
                onTap: () => Get.toNamed(Routes.MY_ORDER,
                    id: DesignConfig.listingSideMenuId),
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_inquires.tr,
                svgImage: AppImages.inquiresLogo,
                onPressed: () => Get.toNamed(Routes.INQUIRIES,
                    id: DesignConfig.listingSideMenuId),
                onTap: () => Get.toNamed(Routes.INQUIRIES,
                    id: DesignConfig.listingSideMenuId),
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_buy_requests.tr,
                svgImage: AppImages.buyRequestsLogo,
                onPressed: () => Get.toNamed(Routes.BUY_REQUEST,
                    id: DesignConfig.listingSideMenuId),
                onTap: () => Get.toNamed(Routes.BUY_REQUEST,
                    id: DesignConfig.listingSideMenuId),
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_my_address.tr,
                svgImage: AppImages.myAddressLogo,
                onPressed: () => Get.toNamed(Routes.MY_ADDRESS,
                    id: DesignConfig.listingSideMenuId),
                onTap: () => Get.toNamed(Routes.MY_ADDRESS,
                    id: DesignConfig.listingSideMenuId),
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_change_password.tr,
                svgImage: AppImages.changePasswordLogo,
                onPressed: () => Get.toNamed(Routes.CHANGE_PASSWORD,
                    id: DesignConfig.listingSideMenuId),
                onTap: () => Get.toNamed(Routes.CHANGE_PASSWORD,
                    id: DesignConfig.listingSideMenuId),
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_language.tr,
                svgImage: AppImages.languageLogo,
                onPressed: () {},
                onTap: () {
                  showLanguageSelection(Get.context!);
                },
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_shipping_policy.tr,
                svgImage: AppImages.shippingPolicyLogo,
                onPressed: () {},
                onTap: () {
                  Get.routing.args = StaticPagesType.shipping_policy;
                  Get.toNamed(Routes.PRIVACY_POLICY,
                      arguments: StaticPagesType.shipping_policy,
                      id: DesignConfig.listingSideMenuId);
                },
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_return_policy.tr,
                svgImage: AppImages.returnPolicyLogo,
                onPressed: () {},
                onTap: () {
                  Get.routing.args = StaticPagesType.return_policy;
                  Get.toNamed(Routes.PRIVACY_POLICY,
                      arguments: StaticPagesType.return_policy,
                      id: DesignConfig.listingSideMenuId);
                },
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_legal_policy.tr,
                svgImage: AppImages.legalPolicyLogo,
                onPressed: () {},
                onTap: () {
                  Get.routing.args = StaticPagesType.legal_policy;
                  Get.toNamed(Routes.PRIVACY_POLICY,
                      arguments: StaticPagesType.legal_policy,
                      id: DesignConfig.listingSideMenuId);
                },
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_help_support.tr,
                svgImage: AppImages.helpSupportLogo,
                onPressed: () {},
                onTap: () {
                  // Get.toNamed(
                  //   Routes.PRIVACY_POLICY,
                  //   arguments: StaticPagesType.help_and_support,
                  // );
                  showCustomerRelationBottomSheet();
                },
              ),
              10.verticalSpace,
              _buildDetails(
                text: LocaleKeys.profile_delete_account.tr,
                svgImage: AppImages.deleteAccountLogo,
                onPressed: () => showDeleteAlertDialog(),
                onTap: () => showDeleteAlertDialog(),
              ),
              50.verticalSpace,
            ],
          ),
        ),
      ).paddingOnly(
        bottom: MediaQuery.of(Get.context!).padding.bottom,
      );

  /// Build Profile
  Widget _buildProfile() => Container(
        margin: REdgeInsets.symmetric(horizontal: 60),
        width: Get.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: Padding(
          padding: REdgeInsets.symmetric(horizontal: 112.5, vertical: 60),
          child: Column(
            children: <Widget>[
              Text(
                '${UserProvider.currentUser?.firstName ?? ''} ${UserProvider.currentUser?.lastName ?? ''}',
                softWrap: true,
                style: AppFontStyles.skolaSans(
                  fontSize: 56.sp,
                  fontWeight: FontWeight.w900,
                  color: AppColors.k101C28,
                  letterSpacing: 5.w,
                ),
              ),
              40.verticalSpace,
              Text(
                '${UserProvider.currentUser?.email ?? ''}',
                style: AppFontStyles.skolaSans(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k70777E,
                ),
              ),
              40.verticalSpace,
              AppButton.text(
                onPressed: () => Get.toNamed(Routes.MY_ACCOUNT,
                    id: DesignConfig.listingSideMenuId),
                backgroundColor: AppColors.kffffff,
                padding: EdgeInsets.zero,
                buttonSize: Size(min(353.w, 150), 100.h),
                buttonText: LocaleKeys.side_menu_my_account.tr,
                buttonTextStyle: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k101C28,
                ),
                borderRadius: 500.r,
                borderWidth: min(4.w, 2),
                borderColor: AppColors.k101C28,
              ),
            ],
          ),
        ),
      );

  /// Build Details
  Widget _buildDetails({
    required Widget svgImage,
    required String text,
    required void Function() onPressed,
    required void Function() onTap,
  }) =>
      ListTile(
        onTap: onTap,
        dense: true,
        contentPadding: kIsWeb ? EdgeInsets.zero : null,
        // visualDensity: const VisualDensity(
        //   horizontal: VisualDensity.minimumDensity,
        //   vertical: VisualDensity.minimumDensity,
        // ),
        splashColor: Colors.grey.withOpacity(0.6),
        // minVerticalPadding: 0,
        // contentPadding: REdgeInsets.symmetric(horizontal: 60),
        leading: SizedBox(
          height: 64.h,
          width: 64.w,
          child: svgImage,
        ),
        title: Text(
          text,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k101C28,
          ),
        ),
        trailing: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(100.r),
          child: Container(
            height: 100.h,
            width: 100.w,
            decoration: const BoxDecoration(
              color: AppColors.kEAEFF4,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.keyboard_arrow_right_sharp,
              color: AppColors.k101C28,
            ),
          ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () => Get.back(),
        clipBehavior: Clip.none,
        title: Text(
          LocaleKeys.profile_profile.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          IconButton.filled(
            onPressed: () => showLogoutAlertDialog(),
            icon: SizedBox(
              height: 64.h,
              width: 64.w,
              child: AppImages.logoutIcon,
            ),
            style: IconButton.styleFrom(
              backgroundColor: AppColors.kBEFFFC,
              shape: const CircleBorder(),
              fixedSize: Size(min(120.w, 50), 120.h),
              padding: EdgeInsets.zero,
            ),
            constraints: BoxConstraints(
              minHeight: 120.h,
              minWidth: min(120.w, 50),
            ),
          ),
        ],
      );
}
