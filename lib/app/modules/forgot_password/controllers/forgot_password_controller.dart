import 'package:diamond_company_app/app/modules/forgot_password/models/otp1_args.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_login/controllers/vendor_login_controller.dart';
import 'package:diamond_company_app/app/providers/vendor_provider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import '../../../data/config/logger.dart';
import '../../../providers/auth_provider.dart';
import '../../../routes/app_pages.dart';
import '../../../ui/components/app_snackbar.dart';

/// ForgotPasswordController
class ForgotPasswordController extends GetxController {
  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// IsLoading
  RxBool isLoading = false.obs;

  /// is vendor
  RxBool isVendor = false.obs;

  @override
  void onReady() {
    super.onReady();

    if (Get.isRegistered<VendorLoginController>()) {
      isVendor(true);
    }

    if (Get.arguments != null) {
      fbKey.currentState?.fields['emailAddress']?.didChange(Get.arguments);
    }
  }

  /// forgot password
  Future<void> forgotPassword() async {
    try {
      if (fbKey.currentState?.saveAndValidate() ?? false) {
        isLoading(true);

        if (isVendor()) {
          await VendorProvider.sendVerificationOtpVendor(
            email: fbKey.currentState?.fields['emailAddress']?.value
                .toString()
                .toLowerCase()
                .trim(),
          );
        } else {
          await AuthProvider.sendVerificationOtp(
            email: fbKey.currentState?.fields['emailAddress']?.value
                .toString()
                .toLowerCase()
                .trim(),
          );
        }

        appSnackbar(
            message: 'Verification OTP sent successfully',
            snackBarState: SnackBarState.SUCCESS);

        await Get.toNamed(
          Routes.VERIFICATION_PHONE,
          arguments: OtpArgs(
            email:
                '${fbKey.currentState?.fields['emailAddress']?.value.toString().toLowerCase().trim()}',
          ),
        );
      }
    } on DioException catch (e) {
      isLoading(false);
      logE(e);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
    } finally {
      isLoading(false);
    }
  }
}
