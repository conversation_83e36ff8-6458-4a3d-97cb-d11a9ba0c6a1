import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/ui/components/custom_back_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../controllers/forgot_password_controller.dart';

/// ForgotPasswordView
class ForgotPasswordView extends GetView<ForgotPasswordController> {
  /// ForgotPasswordView
  const ForgotPasswordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: false,
        body: _buildBody(),
      );

  /// Build Body
  Widget _buildBody() => SafeArea(
        child: Padding(
          padding: REdgeInsets.symmetric(horizontal: 60),
          child: FormBuilder(
            key: controller.fbKey,
            child: Column(
              children: <Widget>[
                5.verticalSpace,
                _buildHeaderPart(),
                100.verticalSpace,
                _buildText(),
                50.verticalSpace,
                _buildDescriptionText(),
                100.verticalSpace,
                _buildEmailTextFormField(),
                76.verticalSpace,
                Obx(() => _buildCommonAppButton()),
              ],
            ),
          ),
        ),
      );

  /// Build Description
  Widget _buildDescriptionText() => Padding(
        padding: REdgeInsets.symmetric(horizontal: 85),
        child: Text(
          LocaleKeys.forgot_password_enter_email_password.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 40.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k70777E.withOpacity(0.6),
          ),
        ),
      );

  /// Build Text
  Widget _buildText() => Text(
        LocaleKeys.forgot_password_forgot_password.tr,
        style: AppFontStyles.skolaSans(
          fontSize: 70.sp,
          fontWeight: FontWeight.w500,
          color: AppColors.k101C28,
        ),
      );

  /// Build Common App Button
  AppButton _buildCommonAppButton() => AppButton.text(
        isLoading: controller.isLoading(),
        buttonText: LocaleKeys.forgot_password_sent_reset_code.tr,
        onPressed: () => controller.forgotPassword(),
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: AppColors.k101C28,
        borderRadius: 24.r,
        borderColor: AppColors.k101C28,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: AppColors.kBEFFFC,
        ),
      );

  /// Build Email Text Form Field
  AppTextFormField _buildEmailTextFormField() => AppTextFormField(
        name: 'emailAddress',
        keyboardType: TextInputType.emailAddress,
        autofillHints: const <String>[AutofillHints.email],
        validator: (String? value) {
          final String? trimmedValue = value?.trim();
          if (trimmedValue?.isEmpty ?? true) {
            return LocaleKeys.validation_email_is_empty.tr;
          } else if (!trimmedValue!.isEmail) {
            return LocaleKeys.validation_email_is_invalid.tr;
          }
          return null;
        },
        labelText: 'Email Address',
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48),
      );

  /// Build Header Part
  Widget _buildHeaderPart() => SizedBox(
        height: 200.h,
        width: Get.width,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: REdgeInsets.only(top: 45),
              child: CustomBackButton(onPressed: () {
                Get.back();
              }),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: SizedBox(
                height: 200.h,
                width: 200.w,
                child: AppImages.appLogo,
              ),
            ),
          ],
        ),
      );
}
