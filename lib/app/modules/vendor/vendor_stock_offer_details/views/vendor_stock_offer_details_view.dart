import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/dialog_manger.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/ui/components/stock_item.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/offer_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:diamond_company_app/app/utils/int_ext.dart';
import '../controllers/vendor_stock_offer_details_controller.dart';

/// Vendor stock details view
class VendorStockOfferDetailsView
    extends GetView<VendorStockOfferDetailsController> {
  /// Vendor stock details view
  const VendorStockOfferDetailsView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: CommonAppbar(
          centerTitle: true,
          leadingWidth: 220.w,
          onBackPressed: () {
            Get.back();
          },
          title: Text(
            'Offer Details',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w500,
              fontSize: 45.sp,
              color: AppColors.k101C28,
            ),
          ),
        ),
        body: RefreshIndicator(
          color: AppColors.k101C28,
          onRefresh: () async => controller.getOfferTrails(
            offerId: controller.stockOffer().id ?? '',
          ),
          child: ListView(
            shrinkWrap: true,
            clipBehavior: Clip.none,
            physics: const AlwaysScrollableScrollPhysics(),
            children: <Widget>[
              _diamondListItem(
                diamond: controller.stockOffer().stock ?? DiamondEntity(),
              ),
              40.verticalSpace,
              Obx(
                () => controller.isLoading()
                    ? SizedBox(
                        height: Get.height * 0.3,
                        child: AppLoader(),
                      )
                    : ListView.separated(
                        shrinkWrap: true,
                        //reverse: true,
                        clipBehavior: Clip.none,
                        physics: const ClampingScrollPhysics(),
                        itemBuilder: (BuildContext context, int index) =>
                            _buildOfferItem(
                                controller.offerTrails()[index], index),
                        separatorBuilder: (BuildContext context, int index) =>
                            40.verticalSpace,
                        itemCount: controller.offerTrails().length,
                      ),
              ),
            ],
          ),
        ),
      );

  /// Diamond item list
  Widget _diamondListItem({
    required DiamondEntity diamond,
  }) =>
      InkWell(
        onTap: () => Get.toNamed(
          Routes.VENDOR_DIAMOND_DETAILS,
          arguments: DiamondArgs(
            id: diamond.id ?? '',
            diamond: diamond,
          ),
        ),
        child: Container(
          padding: REdgeInsets.only(
            left: 60,
            right: 60,
            bottom: 30,
          ),
          color: AppColors.kFFF9EF,
          child: StockItem(
            width: Get.width,
            isAddedToCart: false.obs,
            isFromVendor: true,
            margin: REdgeInsets.symmetric(vertical: 40),
            onTap: () => Get.toNamed(
              Routes.VENDOR_DIAMOND_DETAILS,
              arguments: DiamondArgs(
                id: diamond.id ?? '',
                diamond: diamond,
              ),
            ),
            diamond: DiamondEntity.fromJson(
              <String, dynamic>{
                ...diamond.toJson(),
                'is_counter_offer': true,
              },
            ),
          ),
        ),
      );

  /// Build offer item
  Widget _buildOfferItem(StockOffer stockOffer, int index) {
    if (stockOffer.status == OfferStatus.ACCEPTED) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(64),
            margin: REdgeInsets.all(60),
            decoration: BoxDecoration(
              color: AppColors.kBEFFFC,
              borderRadius: BorderRadius.all(Radius.circular(24.r)),
            ),
            child: Column(
              children: <Widget>[
                Text(
                  'Congratulations!',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w900,
                    fontSize: 48.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                32.verticalSpace,
                Text(
                  '${stockOffer.lastActionId == UserProvider.adminUser?.id ? 'You have accepted offer.' : 'Buyer has accepted your offer,'}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                  textAlign: TextAlign.center,
                ),
                48.verticalSpace,
                Text(
                  '${stockOffer.updatedOfferPrice.toString().toPrice()}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w900,
                    fontSize: 72.sp,
                    color: AppColors.k128807,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    } else if (stockOffer.status == OfferStatus.REJECTED) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: REdgeInsets.all(64),
            margin: REdgeInsets.all(60),
            decoration: BoxDecoration(
              color: AppColors.kFFE4E4,
              borderRadius: BorderRadius.all(Radius.circular(24.r)),
            ),
            child: Column(
              children: <Widget>[
                Text(
                  'Offer Declined!',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w900,
                    fontSize: 48.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                32.verticalSpace,
                Text(
                  '${stockOffer.lastActionId == UserProvider.adminUser?.id ? 'You have declined offer' : 'The buyer has declined your offer'}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                  textAlign: TextAlign.center,
                ),
                48.verticalSpace,
                Text(
                  '${stockOffer.updatedOfferPrice.toString().toPrice()}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w900,
                    fontSize: 72.sp,
                    color: AppColors.k101C28,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }
    return Row(
      mainAxisAlignment: stockOffer.lastOfferId == UserProvider.adminUser?.id
          ? MainAxisAlignment.end
          : MainAxisAlignment.start,
      children: <Widget>[
        Stack(
          children: <Widget>[
            Container(
              margin: REdgeInsets.only(
                  left: 60,
                  right: 60,
                  top: (stockOffer.lastOfferId != UserProvider.adminUser?.id)
                      ? 43
                      : 0),
              padding: REdgeInsets.all(48),
              decoration: BoxDecoration(
                color: AppColors.kffffff,
                borderRadius: BorderRadius.circular(24.r),
                border: (stockOffer.isResponseRequired ?? false)
                    ? Border.all(
                        width: 5.w,
                        color: AppColors.k1A47E8,
                      )
                    : null,
                boxShadow: (stockOffer.isResponseRequired ?? false)
                    ? <BoxShadow>[
                        BoxShadow(
                          color: AppColors.k1A47E8.withOpacity(0.16),
                          offset: Offset(20.w, 20.h),
                          blurRadius: 40.r,
                        ),
                      ]
                    : <BoxShadow>[
                        BoxShadow(
                          color: AppColors.k000000.withOpacity(0.1),
                          offset: Offset(0, 10.h),
                          blurRadius: 104.r,
                        ),
                      ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    '${stockOffer.updatedOfferPrice.toString().toPrice()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight:
                          stockOffer.lastOfferId == UserProvider.adminUser?.id
                              ? FontWeight.w500
                              : FontWeight.w900,
                      fontSize: 56.sp,
                      color: (stockOffer.isResponseRequired ?? false)
                          ? AppColors.k1A47E8
                          : AppColors.k101C28,
                    ),
                  ),
                  Text(
                    '${stockOffer.vendorSidePricePerPrice(controller.stockOffer().stock ?? DiamondEntity()).toString().toPrice()} /ct'
                        .toUpperCase(),
                    style: AppFontStyles.skolaSans(
                      fontSize: 35.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.k70777E,
                    ),
                  ),
                  Center(
                    child: SizedBox(
                      width: 354.w,
                      child: Divider(
                        thickness: 0.5.w,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ),
                  Text(
                    'Offer submitted on ${stockOffer.createdAt?.toYYYYMMDD()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  32.verticalSpace,
                  if (stockOffer.lastOfferId != UserProvider.adminUser?.id &&
                      index == 0) ...<Widget>[
                    Obx(
                      () => AppButton.custom(
                        onPressed: () {
                          if (stockOffer.status != OfferStatus.ACCEPTED) {
                            DialogManager.showAcceptOfferDialog(
                              onAccept: () {
                                controller.onOfferAction(
                                  offerId: stockOffer.offerId ?? '',
                                  status: 'ACCEPTED',
                                );
                              },
                            );
                          }
                        },
                        borderColor: AppColors.k128807,
                        borderRadius: 500.r,
                        buttonSize: Size(354.w, 100.h),
                        loaderHeight: 50.h,
                        loaderWidth: 50.w,
                        strokeWidth: 8.w,
                        backgroundColor: AppColors.k128807,
                        isLoading: controller.isOfferAccepting(),
                        loaderColor: AppColors.kffffff,
                        buttonText:
                            LocaleKeys.my_order_detail_accept.tr.toUpperCase(),
                        buttonTextStyle: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 35.sp,
                          color: AppColors.kffffff,
                        ),
                      ),
                    ),
                    20.verticalSpace,
                    Obx(
                      () => AppButton.text(
                        onPressed: () {
                          // final StockOffer offer = stockOffer.copyWith(
                          //   stock: controller.stockOffer().stock,
                          // );
                          //
                          // controller.showCounterOffer(
                          //   offer: offer,
                          // );
                          controller.stockOffer().stock
                            ?..isCounterOffer = true
                            ..calculate.call();
                        },
                        backgroundColor: AppColors.k101C28,
                        padding: EdgeInsets.zero,
                        borderWidth: 0,
                        buttonSize: Size(354.w, 100.h),
                        buttonText: 'COUNTER',
                        borderRadius: 500.r,
                        loaderHeight: 50.h,
                        loaderWidth: 50.w,
                        strokeWidth: 8.w,
                        isLoading: controller.isOfferUpdating(),
                        buttonTextStyle: AppFontStyles.skolaSans(
                          fontSize: 35.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.kBEFFFC,
                        ),
                        borderColor: AppColors.k101C28,
                        loaderColor: AppColors.kffffff,
                      ),
                    ),
                    20.verticalSpace,
                    Obx(
                      () => AppButton.text(
                        buttonText: 'DECLINE',
                        onPressed: () {
                          if (stockOffer.status != OfferStatus.REJECTED) {
                            DialogManager.showDeclineOfferDialog(
                              onDecline: () {
                                controller.onOfferAction(
                                  offerId: stockOffer.offerId ?? '',
                                  status: 'REJECTED',
                                );
                              },
                            );
                          }
                        },
                        borderColor: AppColors.k101C28,
                        loaderColor: AppColors.k101C28,
                        borderRadius: 500.r,
                        buttonSize: Size(354.w, 100.h),
                        backgroundColor: AppColors.kffffff,
                        loaderHeight: 50.h,
                        loaderWidth: 50.w,
                        strokeWidth: 8.w,
                        isLoading: controller.isOfferRejecting(),
                        buttonTextStyle: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 35.sp,
                          color: stockOffer.stock?.isDeclined ?? false
                              ? AppColors.kFF9800
                              : AppColors.k101C28,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (stockOffer.lastOfferId != UserProvider.adminUser?.id)
              Positioned(
                top: 0,
                left: 100.w,
                child: StatusChip(
                  text: 'Buyer',
                  padding: REdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  border: Border.all(
                    width: 1.w,
                  ),
                  color: AppColors.kBEFFFC,
                  textStyle: TextStyle(
                    color: AppColors.k101C28,
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
          ],
        ),
      ],
    );
  }
}
