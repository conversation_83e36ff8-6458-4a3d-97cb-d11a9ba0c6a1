import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/vendor/create_vendor/widget/vendor_kyc_submission.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/common_country_picker.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/utils/create_vendor_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/create_vendor_controller.dart';

/// Create vendor view
class CreateVendorView extends GetView<CreateVendorController> {
  /// Create vendor view
  const CreateVendorView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: CommonAppbar(
          centerTitle: true,
          leadingWidth: 220.w,
          onBackPressed: () {
            Get.back();
          },
          title: Obx(
            () => Text(
              controller.isEdit() ? 'Update Profile' : 'Vendor Registration',
              style: AppFontStyles.skolaSans(
                fontSize: 20,
              ),
            ),
          ),
        ),
        bottomNavigationBar: Padding(
          padding: REdgeInsets.only(
            left: 60,
            right: 60,
            bottom: MediaQuery.of(context).viewInsets.bottom +
                MediaQuery.of(context).padding.bottom +
                120.h,
          ),
          child: Obx(
            () => AppButton.text(
              buttonText: controller.isEdit() ? 'Save' : 'Create',
              isLoading: controller.isLoading(),
              onPressed: () {
                if (controller.isEdit()) {
                  controller.updateVendor();
                  return;
                }
                controller.createVendor();
              },
              buttonSize: Size(Get.width, 150.h),
              backgroundColor: AppColors.k101C28,
              borderRadius: 24.r,
              borderColor: AppColors.k101C28,
              // borderColor: AppColors.k101C28,
              buttonTextStyle: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.kBEFFFC,
              ),
            ),
          ),
        ),
        body: FormBuilder(
          key: controller.fbKey,
          child: Obx(
            () => controller.isUserDataLoading()
                ? Center(
                    child: AppLoader(),
                  )
                : ListView(
                    controller: controller.scrollController,
                    physics: const ClampingScrollPhysics(),
                    padding: REdgeInsets.symmetric(horizontal: 60),
                    children: <Widget>[
                      60.verticalSpace,
                      Obx(
                        () => _buildTextFormField(
                          readOnly: controller.isLoading(),
                          name: CreateVendorUtils.companyName,
                          hintText: 'Enter company name',
                          labelText: 'Company Name',
                          isRequired: true,
                          validation: CreateVendorUtils.validateCompanyName,
                        ),
                      ),
                      60.verticalSpace,
                      Obx(
                        () => _buildTextFormField(
                          readOnly: controller.isLoading(),
                          name: CreateVendorUtils.firstName,
                          hintText: 'Enter first name',
                          labelText: 'First Name',
                          isRequired: true,
                          validation: CreateVendorUtils.validateFirstName,
                        ),
                      ),
                      60.verticalSpace,
                      Obx(
                        () => _buildTextFormField(
                          readOnly: controller.isLoading(),
                          name: CreateVendorUtils.lastName,
                          hintText: 'Enter last name',
                          labelText: 'Last Name',
                          isRequired: true,
                          validation: CreateVendorUtils.validateLastName,
                        ),
                      ),
                      60.verticalSpace,
                      Obx(
                        () => _buildTextFormField(
                          readOnly: controller.isLoading(),
                          name: CreateVendorUtils.phone,
                          initialValue: controller.phoneInitialValue,
                          prefix: GestureDetector(
                            onTap: () {
                              commonShowCountryPicker(
                                context,
                                onSelect: (Country country) {
                                  controller.countryCode(country.phoneCode);
                                  controller.countryCode.refresh();
                                },
                              );
                            },
                            child: Padding(
                              padding: REdgeInsets.only(
                                left: 20,
                                right: 20,
                              ),
                              child: Text(
                                '+ ${controller.countryCode()}',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 45.sp,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.k101C28,
                                ),
                              ),
                            ),
                          ),
                          hintText: 'Enter Phone Number',
                          labelText: 'Phone Number',
                          isRequired: true,
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          validation: CreateVendorUtils.validatePhoneNumber,
                        ),
                      ),
                      60.verticalSpace,
                      Obx(
                        () => _buildTextFormField(
                          readOnly: controller.isLoading(),
                          name: CreateVendorUtils.email,
                          hintText: 'Enter email',
                          labelText: 'Email',
                          isRequired: true,
                          validation: CreateVendorUtils.validateEmail,
                        ),
                      ),
                      Obx(
                        () => Visibility(
                          visible: !controller.isEdit(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              60.verticalSpace,
                              Obx(
                                () => _buildTextFormField(
                                  readOnly: controller.isLoading(),
                                  name: CreateVendorUtils.password,
                                  hintText: 'Enter password',
                                  labelText: 'Password',
                                  isRequired: true,
                                  validation:
                                      CreateVendorUtils.validatePassword,
                                  keyboardType: TextInputType.visiblePassword,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      60.verticalSpace,
                      Obx(
                        () => DropDownFormField(
                          items: controller.stockUploadType,
                          selectedValue: controller.selectedStockUploadType(),
                          menuItemStyle: AppFontStyles.skolaSans(
                            fontSize: 45.sp,
                            color: AppColors.k101C28,
                            fontWeight: FontWeight.w400,
                          ),
                          suffixPadding: REdgeInsets.only(right: 50),
                          decoration: buildInputDropDownDecoration(),
                          validate: CreateVendorUtils.validateStockUploadType,
                          onChanged: (dynamic value) {
                            controller.selectedStockUploadType(value);
                            controller.selectedStockUploadType.refresh();
                          },
                          style: AppFontStyles.skolaSans(
                            fontSize: 45.sp,
                            color: AppColors.k101C28,
                            fontWeight: FontWeight.w400,
                          ),
                          hintText: 'Select Stock Upload type',
                        ),
                      ),

                      60.verticalSpace,
                      VendorKycSubmission(
                        key: controller.kycDetailsKey,
                      ),

                      Obx(
                        () => Visibility(
                          key: controller.fillUpDetailsKey,
                          visible:
                              controller.isEdit() && !controller.scrollToKyc(),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              60.verticalSpace,
                              Text(
                                'Fill Up Your Details',
                                style: AppFontStyles.skolaSans(
                                  color: AppColors.k101C28,
                                  fontSize: 45.sp,
                                  fontWeight: FontWeight.w500,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                              60.verticalSpace,
                              Obx(
                                () => _buildTextFormField(
                                  readOnly: controller.isLoading(),
                                  name: CreateVendorUtils.bankName,
                                  hintText: 'Enter Bank name',
                                  labelText: 'Bank Name',
                                  isRequired: true,
                                  validation:
                                      CreateVendorUtils.validateBankName,
                                ),
                              ),
                              60.verticalSpace,
                              Obx(
                                () => _buildTextFormField(
                                  readOnly: controller.isLoading(),
                                  name: CreateVendorUtils.accountHolder,
                                  hintText: 'Enter Account Holder Name',
                                  labelText: 'Account Holder Name',
                                  isRequired: true,
                                  validation:
                                      CreateVendorUtils.validateAccountHolder,
                                ),
                              ),
                              60.verticalSpace,
                              Obx(
                                () => _buildTextFormField(
                                  readOnly: controller.isLoading(),
                                  name: CreateVendorUtils.accountNumber,
                                  hintText: 'Enter Account Number',
                                  labelText: 'Account Number',
                                  isRequired: true,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                                  validation:
                                      CreateVendorUtils.validateAccountNumber,
                                ),
                              ),
                              60.verticalSpace,
                              Obx(
                                () => _buildTextFormField(
                                  readOnly: controller.isLoading(),
                                  name: CreateVendorUtils.ifscCode,
                                  hintText: 'Enter IFSC Code',
                                  labelText: 'IFSC Code',
                                  isRequired: true,
                                  validation:
                                      CreateVendorUtils.validateIfscCode,
                                ),
                              ),
                              60.verticalSpace,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Expanded(
                                    child: Obx(
                                      () => _buildTextFormField(
                                        readOnly: controller.isLoading(),
                                        name: CreateVendorUtils.street,
                                        hintText: 'Enter Street',
                                        labelText: 'Street',
                                      ),
                                    ),
                                  ),
                                  20.horizontalSpace,
                                  Expanded(
                                    child: Obx(
                                      () => _buildTextFormField(
                                        readOnly: controller.isLoading(),
                                        name: CreateVendorUtils.city,
                                        hintText: 'Enter City',
                                        labelText: 'City',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              20.verticalSpace,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Expanded(
                                    child: Obx(
                                      () => _buildTextFormField(
                                        readOnly: controller.isLoading(),
                                        name: CreateVendorUtils.state,
                                        hintText: 'Enter State',
                                        labelText: 'State',
                                      ),
                                    ),
                                  ),
                                  20.horizontalSpace,
                                  Expanded(
                                    child: Obx(
                                      () => _buildTextFormField(
                                        readOnly: controller.isLoading(),
                                        name: CreateVendorUtils.country,
                                        hintText: 'Enter Country',
                                        labelText: 'Country',
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              20.verticalSpace,
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Expanded(
                                    child: Obx(
                                      () => _buildTextFormField(
                                        readOnly: controller.isLoading(),
                                        name: CreateVendorUtils.zipCode,
                                        hintText: 'Enter Zip Code',
                                        labelText: 'Zip Code',
                                      ),
                                    ),
                                  ),
                                  20.horizontalSpace,
                                  Expanded(
                                    child: Container(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),

                      // 90.verticalSpace,
                      // _buildTextFormField(
                      //   name: CreateVendorUtils.marginPercentage,
                      //   hintText: 'Enter Margin Percentage',
                      //   labelText: 'Margin (%)',
                      //   isRequired: true,
                      //   controller: controller.marginController,
                      //   inputFormatters: <TextInputFormatter>[
                      //     RemoveOnlyInputFormatter()
                      //   ],
                      //   keyboardType: TextInputType.number,
                      //   onTap: () {
                      //     controller.marginController.selection = TextSelection(
                      //       baseOffset: 0,
                      //       extentOffset: controller.marginController.text.length,
                      //     );
                      //   },
                      //   suffix: Padding(
                      //     padding: REdgeInsets.only(right: 30),
                      //     child: Column(
                      //       mainAxisAlignment: MainAxisAlignment.center,
                      //       mainAxisSize: MainAxisSize.min,
                      //       children: <Widget>[
                      //         GestureDetector(
                      //           onTap: () {
                      //             controller.incrementMargin();
                      //           },
                      //           child: Image(
                      //             height: 35.h,
                      //             width: 35.w,
                      //             color: AppColors.k333333.withOpacity(0.8),
                      //             image: const AssetImage(
                      //               AppImages.arrow,
                      //             ),
                      //           ),
                      //         ),
                      //         5.verticalSpace,
                      //         GestureDetector(
                      //           onTap: () {
                      //             controller.decrementMargin();
                      //           },
                      //           child: RotatedBox(
                      //             quarterTurns: 2,
                      //             child: Image(
                      //               height: 35.h,
                      //               width: 35.w,
                      //               color: AppColors.k333333.withOpacity(0.8),
                      //               image: const AssetImage(
                      //                 AppImages.arrow,
                      //               ),
                      //             ),
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      //   validation: CreateVendorUtils.validateMarginPercentage,
                      // ),
                      100.verticalSpace,
                    ],
                  ),
          ),
        ),
      );

  /// Build input decoration
  InputDecoration buildInputDropDownDecoration() => InputDecoration(
        hintText: 'Select Stock Upload type',
        label: RichText(
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          text: TextSpan(
            text: 'Stock Upload type',
            style: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              color: AppColors.k70777E,
              fontWeight: FontWeight.w400,
            ),
            children: <InlineSpan>[
              TextSpan(
                text: ' *',
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w900,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ),
        hintStyle: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k70777E,
          fontWeight: FontWeight.w400,
        ),
        errorStyle: AppFontStyles.skolaSans(
          color: Colors.red,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48, bottom: 30),
      );

  /// Build text form field
  AppTextFormField<Widget> _buildTextFormField({
    required String name,
    String? Function(String?)? validation,
    TextEditingController? controller,
    void Function()? onTap,
    String? labelText,
    String? hintText,
    String? initialValue,
    bool isRequired = false,
    bool readOnly = false,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Widget? suffix,
    Widget? prefix,
    int? maxLines,
    Iterable<String>? autofillHints,
    List<TextInputFormatter>? inputFormatters,
    dynamic Function(String?)? onSubmit,
    dynamic Function(String?)? onChange,
  }) =>
      AppTextFormField<Widget>(
        onTap: onTap ?? () {},
        onChange: onChange,
        controller: controller,
        inputFormatters: inputFormatters,
        name: name,
        onSubmit: onSubmit,
        keyboardType: keyboardType,
        autofillHints: autofillHints,
        validator: validation,
        labelText: labelText ?? '',
        hintText: hintText ?? '',
        initialValue: initialValue,
        isRequired: isRequired,
        readOnly: readOnly,
        prefixIcon: prefix,
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        textInputAction: textInputAction ?? TextInputAction.next,
        maxLines: maxLines ?? 1,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        hintTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        suffixIcon: suffix,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48),
      );
}
