import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../controllers/add_vendor_inventory_controller.dart';

class AddVendorInventoryView extends GetView<AddVendorInventoryController> {
  const AddVendorInventoryView({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
      );

  Widget _buildBody() => ListView(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              60.verticalSpace,
              _buildDirectUpload(),
              60.verticalSpace,
              _buildFTP(),
              60.verticalSpace,
              _buildAPI(),
              60.verticalSpace,
              RichText(
                text: TextSpan(
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 35.sp,
                    color: AppColors.k70777E,
                  ),
                  text:
                      'For any questions,please reach out to the Supplier Relations Team at ',
                  children: <TextSpan>[
                    TextSpan(
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          launchUrl(
                            Uri(
                              scheme: 'mailto',
                              path:
                                  '<EMAIL>', // Recipient's email address
                            ),
                          );
                        },
                      text: '<EMAIL>',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 40.sp,
                        color: AppColors.k101C28,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ).paddingSymmetric(horizontal: 80.w),
              160.verticalSpace,
            ],
          ),
        ],
      );

  Container _buildDirectUpload() => Container(
        width: Get.width,
        margin: REdgeInsets.symmetric(horizontal: 60),
        padding: REdgeInsets.symmetric(horizontal: 60),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k000000.withOpacity(0.2),
            width: 0.5.r,
          ),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 30.h),
              blurRadius: 100.r,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            60.verticalSpace,
            Text(
              'Direct Upload',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w600,
                fontSize: 45.sp,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            Text(
              'Click here for detailed instructions and acceptable values',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w400,
                fontSize: 35.sp,
                color: AppColors.k70777E,
              ),
            ),
            30.verticalSpace,
            AppButton.text(
              buttonText: 'Download Template',
              onPressed: () {
                launchUrl(
                  Uri.parse(
                      'https://diamond-company.api.dharmatech.in/excel/Template.xlsx'),
                ); // Add the URL here
              },
              buttonSize: Size(550.w, 100.h),
              backgroundColor: AppColors.kffffff,
              borderColor: AppColors.k70777E,
              borderRadius: 24.r,
              buttonTextStyle: TextStyle(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
            60.verticalSpace,
            Text(
              'Step 1 : Add your File',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w600,
                fontSize: 45.sp,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            ListTile(
              onTap: () => controller.pickFile(),
              title: AppButton.text(
                buttonText: 'Choose Stock File',
                onPressed: () => controller.pickFile(),
                buttonSize: Size(550.w, 100.h),
                backgroundColor: AppColors.k101C28,
                borderColor: AppColors.k101C28,
                borderRadius: 24.r,
                buttonTextStyle: TextStyle(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.kffffff,
                ),
              ),
              subtitle: Obx(
                () {
                  if ((controller.filePickerResult().files.firstOrNull?.name ??
                          '')
                      .isNotEmpty) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          controller
                                  .filePickerResult()
                                  .files
                                  .firstOrNull
                                  ?.name ??
                              '',
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 38.sp,
                            color: AppColors.k1A47E8,
                            height: 2,
                          ),
                        ),
                        IconButton(
                          onPressed: () => controller.removeFile(),
                          icon: Icon(
                            Icons.delete_forever_rounded,
                            color: AppColors.k1A47E8,
                            size: 45.sp,
                          ),
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
            60.verticalSpace,
            Text(
              'Step 2 : Offset',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w600,
                fontSize: 45.sp,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            AppTextFormField(
              name: 'Offset',
              labelText: 'Offset',
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter
                    .digitsOnly, // Restrict input to digits only
              ],
              validator: (String? value) {
                if (value?.trim().isNotEmpty ?? false) {
                  return null;
                }
                return null;
              },
              constraints: BoxConstraints(
                minHeight: 150.h,
              ),
              labelTextStyle: AppFontStyles.skolaSans(
                color: AppColors.k70777E,
                fontSize: 35.sp,
                fontWeight: FontWeight.w400,
              ),
              style: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                color: AppColors.k101C28,
                fontWeight: FontWeight.w400,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              showBorder: true,
              fillColor: AppColors.k70777E,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              border: const OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColors.k70777E,
                ),
              ),
              contentPadding: REdgeInsets.only(
                left: 48,
                bottom: 30,
              ),
            ),
            60.verticalSpace,
            Row(
              children: [
                Text(
                  'Replace ALL',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w600,
                    fontSize: 45.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                40.horizontalSpace,
                Obx(
                  () => Transform.scale(
                    scaleY: 0.7,
                    scaleX: 0.8,
                    child: CupertinoSwitch(
                      value: controller.replaceAll(),
                      onChanged: (bool value) => controller.replaceAll(value),
                    ),
                  ),
                ),
              ],
            ),
            60.verticalSpace,
            Obx(
              () => AppButton.text(
                buttonText: 'UPLOAD FILE',
                onPressed: () => controller.uploadStockFile(),
                buttonSize: Size(Get.width, 120.h),
                backgroundColor: AppColors.k1A47E8,
                borderColor: AppColors.k70777E,
                loaderHeight: 80.h,
                loaderWidth: 80.w,
                isLoading: controller.isLoading(),
                borderRadius: 24.r,
                buttonTextStyle: TextStyle(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.kffffff,
                ),
              ),
            ),
            60.verticalSpace,
          ],
        ),
      );

  Container _buildFTP() => Container(
        width: Get.width,
        margin: REdgeInsets.symmetric(horizontal: 60),
        padding: REdgeInsets.symmetric(horizontal: 60),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k000000.withOpacity(0.2),
            width: 0.5.r,
          ),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 30.h),
              blurRadius: 100.r,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            60.verticalSpace,
            Text(
              'FTP',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w600,
                fontSize: 45.sp,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            Text(
              'You can easily set up an automated upload to our FTP server by requesting your own secure login details. Need assistance? A Diamond Company rep would be more than happy to help.',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w400,
                fontSize: 35.sp,
                color: AppColors.k70777E,
              ),
            ),
            60.verticalSpace,
            AppButton.icon(
              buttonText: 'Book a meeting',
              onPressed: () {
                launchUrl(Uri.parse(
                    'https://calendar.app.google/qzcRELkPiNLwZE9FA')); // Add the URL here
              },
              prefixIcon: Icon(
                Icons.calendar_today,
                color: AppColors.k101C28,
                size: 45.sp,
              ),
              prefixWidth: 80.w,
              buttonSize: Size(550.w, 100.h),
              backgroundColor: AppColors.kffffff,
              borderColor: AppColors.k70777E,
              borderRadius: 24.r,
              buttonTextStyle: TextStyle(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
            60.verticalSpace,
          ],
        ),
      );

  Container _buildAPI() => Container(
        width: Get.width,
        margin: REdgeInsets.symmetric(horizontal: 60),
        padding: REdgeInsets.symmetric(horizontal: 60),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k000000.withOpacity(0.2),
            width: 0.5.r,
          ),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 30.h),
              blurRadius: 100.r,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            60.verticalSpace,
            Text(
              'API',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w600,
                fontSize: 45.sp,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            RichText(
              text: TextSpan(
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 35.sp,
                  color: AppColors.k70777E,
                ),
                text:
                    'If you have an API link available, this is the best way for you to upload your stock to Diamond Company. Please send your link and instructions to ',
                children: <TextSpan>[
                  TextSpan(
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        launchUrl(
                          Uri(
                            scheme: 'mailto',
                            path:
                                '<EMAIL>', // Recipient's email address
                          ),
                        );
                      },
                    text: '<EMAIL>',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            60.verticalSpace,
          ],
        ),
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: Text(
          'Import Inventory',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        onBackPressed: () => Get.back(),
      );
}
