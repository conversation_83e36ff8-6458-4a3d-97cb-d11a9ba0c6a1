import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/calculator/views/diamond_calculator_view.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/offer_utils.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Counter offer widget
class CounterOfferWidget extends StatelessWidget {
  /// Counter offer widget constructor
  const CounterOfferWidget({
    required this.offer,
    this.onUpdate,
    this.controller,
    super.key,
  });

  /// Stock offer
  final StockOffer offer;

  /// On counter offer
  final void Function(StockOffer? offer)? onUpdate;

  /// Get offer status widget
  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) => Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(50),
            topRight: Radius.circular(50),
          ).r,
        ),
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: Wrap(
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          runAlignment: WrapAlignment.center,
          children: <Widget>[
            Center(child: 40.verticalSpace),
            Center(
              child: Text(
                'Counter Offer',
                style: AppFontStyles.skolaSans(
                  color: Colors.black,
                  fontSize: 50.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Center(child: 50.verticalSpace),
            Center(
              child: Align(
                alignment: Alignment.centerLeft,
                child: RichText(
                  text: TextSpan(
                    text: 'SKU ',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 35.sp,
                      color: AppColors.k70777E,
                    ),
                    children: <InlineSpan>[
                      TextSpan(
                        text:
                            '${offer.stock?.skuPrefix}${offer.stock?.skuNumber}',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 35.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Text(
                    _buildListToString().toUpperCase(),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  FittedBox(
                    child: getOfferStatusWidget(offer.status),
                  ),
                ],
              ),
            ),
            Center(
              child: Divider(
                color: AppColors.k101C28,
                thickness: 1.w,
                height: 64.h,
              ),
            ),
            Center(
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'Buyer Offer',
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 30.sp,
                            color: AppColors.k70777E,
                          ),
                        ),
                        15.verticalSpace,
                        RichText(
                          text: TextSpan(
                            text:
                                '${offer.lastOfferPrice.toString().toPrice()}  ',
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color: AppColors.k101C28,
                            ),
                            children: <InlineSpan>[
                              TextSpan(
                                text:
                                    '${((offer.offerPrice ?? 0) / (offer.stock?.weight ?? 0)).toStringAsFixed(2).toPrice()} /CT',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 30.sp,
                                  color: AppColors.k101C28,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: <Widget>[
                        Text(
                          'Diamond Price',
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 30.sp,
                            color: AppColors.k70777E,
                          ),
                        ),
                        15.verticalSpace,
                        RichText(
                          text: TextSpan(
                            text: '${offer.basePrice.toString().toPrice()}  ',
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color: AppColors.k101C28,
                            ),
                            children: <InlineSpan>[
                              TextSpan(
                                text:
                                    '${((offer.basePrice ?? 0) / (offer.stock?.weight ?? 0)).toStringAsFixed(2).toPrice()} /CT',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 30.sp,
                                  color: AppColors.k101C28,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Center(child: 80.verticalSpace),
            Center(
              child: Container(
                margin: REdgeInsets.only(right: 250),
                child: Row(
                  children: <Widget>[
                    Text(
                      'Counter Offer Price',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 30.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                    40.horizontalSpace,
                    Expanded(
                      child: CalcTextField(
                        controller: controller,
                        labelText: 'Enter Price',
                        suffixText: 'ct',
                        autofocus: true,
                        onChanged: (String text) {},
                        prefixText: '\$',
                        focusNode: FocusNode(),
                        textStyle: AppFontStyles.skolaSans(
                          color: AppColors.k101C28,
                          fontSize: 45.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
            Center(child: 70.verticalSpace),
            Center(
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: AppButton.text(
                      buttonText: 'Cancel'.toUpperCase(),
                      onPressed: () {
                        Get.back();
                      },
                      borderColor: AppColors.k101C28,
                      loaderColor: AppColors.k101C28,
                      borderRadius: 50.r,
                      buttonSize: Size(354.w, 100.h),
                      backgroundColor: AppColors.kffffff,
                      loaderHeight: min(50.h, 15),
                      loaderWidth: min(50.w, 15),
                      strokeWidth: 2,
                      buttonTextStyle: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 35.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ),
                  20.horizontalSpace,
                  Expanded(
                    child: AppButton.custom(
                      onPressed: () {
                        onUpdate?.call(offer);
                      },
                      borderColor: AppColors.k128807,
                      borderRadius: 50.r,
                      buttonSize: Size(354.w, 100.h),
                      loaderHeight: min(50.h, 15),
                      loaderWidth: min(50.w, 15),
                      strokeWidth: 2,
                      backgroundColor: AppColors.k128807,
                      loaderColor: AppColors.kffffff,
                      buttonText: 'Update'.toUpperCase(),
                      buttonTextStyle: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 35.sp,
                        color: AppColors.kffffff,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Center(child: 70.verticalSpace),
          ],
        ),
      );

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          offer.stock?.displayShape.toString().capitalizeFirst,
          '${offer.stock?.weight}Ct',
          ((offer.stock?.color?.isNotEmpty ?? false) &&
                  !(offer.stock?.isFancyColor ?? false))
              ? '${offer.stock?.color?.wordCapital}'
              : ((offer.stock?.isFancyColor ?? false) &&
                      (offer.stock?.fancyColorMainBody?.isNotEmpty ?? false))
                  ? ' ${offer.stock?.fancyColorMainBody?.wordCapital}'
                  : '',
          '${offer.stock?.displayClarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}, ${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(offer.stock?.displayCut),
          DiamondUtils.getFinishAbbreviation(offer.stock?.displayPolish),
          DiamondUtils.getFinishAbbreviation(offer.stock?.displaySymmetry),
          DiamondUtils.getFluoAbbreviation(offer.stock?.displayIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';
}
