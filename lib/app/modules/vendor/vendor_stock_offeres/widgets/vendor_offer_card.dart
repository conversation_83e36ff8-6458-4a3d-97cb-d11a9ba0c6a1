import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/int_ext.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/offer_ext.dart';
import 'package:diamond_company_app/app/utils/offer_utils.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Vendor offer card
class VendorOfferCard extends StatelessWidget {
  /// Vendor offer card
  const VendorOfferCard({
    super.key,
    this.stockOffer,
    this.onTap,
    this.onAccept,
    this.onDecline,
    this.onCounter,
    this.iaAccepting = false,
    this.isUpdating = false,
    this.isDeclining = false,
  });

  /// stock offer
  final StockOffer? stockOffer;

  /// On tap
  final Function()? onTap;

  /// On accept
  final Function(StockOffer? offer)? onAccept;

  /// On decline
  final Function(StockOffer? offer)? onDecline;

  /// On counter
  final Function(StockOffer? offer)? onCounter;

  /// Is loading on offer
  final bool iaAccepting;

  /// Is loading on offer
  final bool isUpdating;

  /// Is loading on offer
  final bool isDeclining;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: onTap,
        child: Stack(
          children: <Widget>[
            Container(
              padding: REdgeInsets.all(10),
              child: Container(
                padding: REdgeInsets.all(40),
                margin: (stockOffer?.status == OfferStatus.PENDING) ||
                        (stockOffer?.offerId != UserProvider.adminUser?.id &&
                            [OfferStatus.PENDING, OfferStatus.REVISED]
                                .contains(stockOffer?.status))
                    ? REdgeInsets.only(top: 30)
                    : null,
                decoration: BoxDecoration(
                  color: AppColors.kffffff,
                  borderRadius: BorderRadius.circular(24.r),
                  gradient: LinearGradient(
                    colors: <Color>[
                      AppColors.kffffff,
                      AppColors.kffffff,
                      AppColors.kffffff.withOpacity(0.91),
                      getOfferStatusColor(stockOffer?.status).withOpacity(0.5),
                    ],
                    stops: const <double>[0, 0.65, 0.75, 1],
                    end: Alignment.bottomCenter,
                    begin: Alignment.topCenter,
                  ),
                  border: Border.all(
                    width: 10.w,
                    color: AppColors.kffffff,
                  ),
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: AppColors.k000000.withOpacity(0.16),
                      offset: Offset(0, 16.h),
                      blurRadius: 80.r,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    if (stockOffer?.status == OfferStatus.PENDING)
                      RichText(
                        text: TextSpan(
                          text: 'SKU ',
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 35.sp,
                            color: AppColors.k70777E,
                          ),
                          children: <InlineSpan>[
                            TextSpan(
                              text:
                                  '${stockOffer?.stock?.skuPrefix}${stockOffer?.stock?.skuNumber}',
                              style: AppFontStyles.skolaSans(
                                fontWeight: FontWeight.w700,
                                fontSize: 35.sp,
                                color: AppColors.k101C28,
                              ),
                            ),
                          ],
                        ),
                      ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        Text(
                          _buildListToString().toUpperCase(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.k101C28,
                          ),
                        ),
                        if (stockOffer?.orderId == null &&
                            stockOffer?.status != OfferStatus.REJECTED)
                          FittedBox(
                              child: getOfferStatusWidget(stockOffer?.status)),
                      ],
                    ),
                    if (!(stockOffer?.status == OfferStatus.REVISED ||
                        stockOffer?.status == OfferStatus.ACCEPTED &&
                            stockOffer?.orderId == null))
                      Divider(
                        color: AppColors.k101C28,
                        thickness: 1.w,
                        height: 64.h,
                      ),
                    if (stockOffer?.status == OfferStatus.REVISED ||
                        stockOffer?.status == OfferStatus.ACCEPTED &&
                            stockOffer?.orderId == null)
                      32.verticalSpace,
                    if (stockOffer?.status == OfferStatus.REVISED ||
                        stockOffer?.status == OfferStatus.ACCEPTED &&
                            stockOffer?.orderId == null)
                      Container(
                        width: Get.width,
                        padding: REdgeInsets.symmetric(vertical: 40),
                        decoration: BoxDecoration(
                          color: AppColors.kEAEFF4,
                          borderRadius: BorderRadius.all(Radius.circular(24.r)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            if (stockOffer?.status == OfferStatus.ACCEPTED)
                              SizedBox(
                                height: 56.h,
                                width: 56.w,
                                child: AppImages.checkCircle,
                              ),
                            if (stockOffer?.status == OfferStatus.ACCEPTED)
                              16.horizontalSpace,
                            Text(
                              '${stockOffer?.updatedOfferPrice.toString().toPrice()}',
                              style: AppFontStyles.skolaSans(
                                fontWeight: FontWeight.w700,
                                fontSize: 56.sp,
                                color:
                                    (stockOffer?.status == OfferStatus.ACCEPTED)
                                        ? AppColors.k128807
                                        : (stockOffer?.status ==
                                                OfferStatus.REVISED)
                                            ? AppColors.k1A47E8
                                            : AppColors.k101C28,
                              ),
                            ),
                            32.horizontalSpace,
                            Text(
                              (stockOffer?.status == OfferStatus.ACCEPTED)
                                  ? 'Final price'
                                  : stockOffer?.lastOfferId ==
                                          UserProvider.adminUser?.id
                                      ? 'My Revised offer'
                                      : 'Buyer Revised offer',
                              style: AppFontStyles.skolaSans(
                                fontWeight: FontWeight.w700,
                                fontSize: 35.sp,
                                color: AppColors.k101C28,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (stockOffer?.status == OfferStatus.REVISED ||
                        stockOffer?.status == OfferStatus.ACCEPTED &&
                            stockOffer?.orderId == null)
                      32.verticalSpace,
                    if (<OfferStatus>[OfferStatus.PENDING, OfferStatus.REVISED]
                        .contains(stockOffer?.status))
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  stockOffer?.lastOfferId !=
                                          UserProvider.adminUser?.id
                                      ? 'My offer'
                                      : 'Buyer offer',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 30.sp,
                                    color: AppColors.k70777E,
                                  ),
                                ),
                                15.verticalSpace,
                                RichText(
                                  text: TextSpan(
                                    text:
                                        '${stockOffer?.updatedLastOfferPrice.toString().toPrice()}  ',
                                    style: AppFontStyles.skolaSans(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 35.sp,
                                      color: AppColors.k101C28,
                                    ),
                                    children: <InlineSpan>[
                                      TextSpan(
                                        text:
                                            '${stockOffer?.vendorSideLastOfferPricePerPrice(stockOffer?.stock ?? DiamondEntity()).toStringAsFixed(2).toPrice()} /CT',
                                        style: AppFontStyles.skolaSans(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 30.sp,
                                          color: AppColors.k101C28,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  'Diamond Price',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 30.sp,
                                    color: AppColors.k70777E,
                                  ),
                                ),
                                15.verticalSpace,
                                RichText(
                                  text: TextSpan(
                                    text:
                                        '${stockOffer?.basePriceVendor.toString().toPrice()}  ',
                                    style: AppFontStyles.skolaSans(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 35.sp,
                                      color: AppColors.k101C28,
                                    ),
                                    children: <InlineSpan>[
                                      TextSpan(
                                        text:
                                            '${(stockOffer?.stock?.price?.pricePerCaratOri ?? 0).toStringAsFixed(2).toPrice()} /CT',
                                        style: AppFontStyles.skolaSans(
                                          fontWeight: FontWeight.w400,
                                          fontSize: 30.sp,
                                          color: AppColors.k101C28,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    if ((stockOffer?.status == OfferStatus.PENDING) &&
                        stockOffer?.lastActionId != UserProvider.adminUser?.id)
                      40.verticalSpace,
                    // if ((stockOffer?.status == OfferStatus.PENDING) &&
                    //     stockOffer?.lastActionId !=
                    //         UserProvider.adminUser?.id) ...<Widget>[
                    //   10.verticalSpace,
                    //   Row(
                    //     children: <Widget>[
                    //       Expanded(
                    //         child: AppButton.custom(
                    //           onPressed: () {
                    //             onAccept?.call(stockOffer);
                    //           },
                    //           borderColor: AppColors.k128807,
                    //           borderRadius: 500.r,
                    //           buttonSize: Size(354.w, 100.h),
                    //           loaderHeight: min(50.h, 15),
                    //           loaderWidth: min(50.w, 15),
                    //           strokeWidth: 2,
                    //           isLoading: iaAccepting,
                    //           backgroundColor: AppColors.k128807,
                    //           loaderColor: AppColors.kffffff,
                    //           buttonText: 'Accept'.toUpperCase(),
                    //           buttonTextStyle: AppFontStyles.skolaSans(
                    //             fontWeight: FontWeight.w700,
                    //             fontSize: 35.sp,
                    //             color: AppColors.kffffff,
                    //           ),
                    //         ),
                    //       ),
                    //       20.horizontalSpace,
                    //       Expanded(
                    //         child: AppButton.text(
                    //           onPressed: () {
                    //             onCounter?.call(stockOffer);
                    //           },
                    //           backgroundColor: AppColors.k101C28,
                    //           padding: EdgeInsets.zero,
                    //           borderWidth: 0,
                    //           buttonSize: Size(354.w, 100.h),
                    //           buttonText: 'COUNTER',
                    //           borderRadius: 500.r,
                    //           loaderHeight: min(50.h, 15),
                    //           loaderWidth: min(50.w, 15),
                    //           strokeWidth: 2,
                    //           isLoading: isUpdating,
                    //           buttonTextStyle: AppFontStyles.skolaSans(
                    //             fontSize: 35.sp,
                    //             fontWeight: FontWeight.w700,
                    //             color: AppColors.kBEFFFC,
                    //           ),
                    //           borderColor: AppColors.k101C28,
                    //           loaderColor: AppColors.kffffff,
                    //         ),
                    //       ),
                    //       20.horizontalSpace,
                    //       Expanded(
                    //         child: AppButton.text(
                    //           buttonText: 'DECLINE',
                    //           onPressed: () {
                    //             onDecline?.call(stockOffer);
                    //           },
                    //           borderColor: AppColors.k101C28,
                    //           loaderColor: AppColors.k101C28,
                    //           borderRadius: 500.r,
                    //           buttonSize: Size(354.w, 100.h),
                    //           backgroundColor: AppColors.kffffff,
                    //           loaderHeight: min(50.h, 15),
                    //           loaderWidth: min(50.w, 15),
                    //           strokeWidth: 2,
                    //           isLoading: isDeclining,
                    //           buttonTextStyle: AppFontStyles.skolaSans(
                    //             fontWeight: FontWeight.w700,
                    //             fontSize: 35.sp,
                    //             color: AppColors.k101C28,
                    //           ),
                    //         ),
                    //       )
                    //     ],
                    //   ),
                    // ],
                    if ((stockOffer?.status == OfferStatus.ACCEPTED &&
                            stockOffer?.orderId != null) ||
                        stockOffer?.status == OfferStatus.REJECTED)
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  '${stockOffer?.updatedOfferPrice.toString().toPrice()}',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 35.sp,
                                    color: AppColors.k101C28,
                                  ),
                                ),
                                10.verticalSpace,
                                Text(
                                  'Final offer on ${stockOffer?.createdAt?.todMYY()}',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 35.sp,
                                    color: AppColors.k101C28,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          FittedBox(
                            child: getOfferStatusWidget(stockOffer?.status),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
            if (stockOffer?.status == OfferStatus.PENDING ||
                (stockOffer?.offerId != UserProvider.adminUser?.id &&
                    [OfferStatus.PENDING, OfferStatus.REVISED]
                        .contains(stockOffer?.status)))
              Positioned(
                top: 0,
                left: 40.w,
                child: StatusChip(
                  text: 'Response Required',
                  padding: REdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  border: Border.all(
                    width: 1.w,
                  ),
                  color: AppColors.kBEFFFC,
                  textStyle: TextStyle(
                    color: AppColors.k101C28,
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )
          ],
        ),
      );

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          stockOffer?.stock?.displayShape.toString().capitalizeFirst,
          '${stockOffer?.stock?.weight}Ct',
          ((stockOffer?.stock?.color?.isNotEmpty ?? false) &&
                  !(stockOffer?.stock?.isFancyColor ?? false))
              ? '${stockOffer?.stock?.color?.wordCapital}'
              : ((stockOffer?.stock?.isFancyColor ?? false) &&
                      (stockOffer?.stock?.fancyColorMainBody?.isNotEmpty ??
                          false))
                  ? ' ${stockOffer?.stock?.fancyColorMainBody?.wordCapital}'
                  : '',
          '${stockOffer?.stock?.displayClarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}, ${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(stockOffer?.stock?.displayCut),
          DiamondUtils.getFinishAbbreviation(stockOffer?.stock?.displayPolish),
          DiamondUtils.getFinishAbbreviation(
              stockOffer?.stock?.displaySymmetry),
          DiamondUtils.getFluoAbbreviation(stockOffer?.stock?.displayIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';
}
