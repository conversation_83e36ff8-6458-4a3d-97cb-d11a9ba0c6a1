import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/my_order/models/filter_order.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_stock_offeres/widgets/vendor_offer_card.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/dialog_manger.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/vendor_stock_offeres_controller.dart';

/// Vendor stock offers view
class VendorStockOffersView extends GetView<VendorStockOffersController> {
  /// Vendor stock offers view constructor
  const VendorStockOffersView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: AppColors.kffffff,
        appBar: CommonAppbar(
          centerTitle: true,
          leadingWidth: 220.w,
          onBackPressed: () {
            Get.back();
          },
          title: RichText(
            text: TextSpan(
              text: 'Stock Offers',
              style: AppFontStyles.skolaSans(
                fontSize: 20,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ),
        body: Column(
          children: <Widget>[
            50.verticalSpace,
            Obx(
              () => Container(
                height: 90.h,
                child: TabBar(
                  splashBorderRadius: BorderRadius.circular(20).r,
                  controller: controller.tabController,
                  tabAlignment: TabAlignment.start,
                  indicatorColor: Colors.transparent,
                  dividerColor: Colors.transparent,
                  padding: REdgeInsets.only(left: 60, right: 60),
                  isScrollable: true,
                  labelPadding: EdgeInsets.zero,
                  onTap: (int index) {
                    if (controller.isLoading() || controller.isLoadingMore()) {
                      return;
                    }
                    controller.selectedFilter(
                      controller.filterList[index],
                    );
                    controller.selectedFilter.refresh();
                    controller.getStockOffers();
                  },
                  tabs: <Widget>[
                    for (final FilterModel filter in controller.filterList)
                      Tab(
                        child: Container(
                          padding: REdgeInsets.symmetric(horizontal: 40),
                          decoration: BoxDecoration(
                            color: controller.selectedFilter().title ==
                                    filter.title
                                ? AppColors.k6CECE6
                                : AppColors.kffffff,
                            borderRadius: BorderRadius.circular(20).r,
                          ),
                          alignment: Alignment.center,
                          child: Center(
                            child: Text(
                              '${filter.title}',
                              style: AppFontStyles.skolaSans(
                                fontSize: 15,
                                color: AppColors.k101C28,
                                fontWeight: controller.selectedFilter().title ==
                                        filter.title
                                    ? FontWeight.w700
                                    : FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            20.verticalSpace,
            Obx(
              () => controller.isLoading()
                  ? Center(
                      child: Padding(
                        padding: REdgeInsets.only(top: 100),
                        child: AppLoader(),
                      ),
                    )
                  : controller.stockOffers().isNotEmpty
                      ? Expanded(
                          child: RefreshIndicator(
                            color: AppColors.k101C28,
                            onRefresh: () async => controller.getStockOffers(),
                            child: ListView.builder(
                              controller: controller.scrollController,
                              itemCount: controller.stockOffers().length,
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: REdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 40,
                              ),
                              itemBuilder: (BuildContext context, int index) =>
                                  Padding(
                                padding: REdgeInsets.only(bottom: 25),
                                child: diamondDataCard(index),
                              ),
                            ),
                          ),
                        )
                      : Center(
                          child: Padding(
                            padding: REdgeInsets.only(top: 100),
                            child: RichText(
                              text: TextSpan(
                                text: 'No stock offers found!',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 15,
                                  color: AppColors.k101C28,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
            ),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              alignment: Alignment.bottomCenter,
              reverseDuration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Obx(
                () => Visibility(
                  visible: controller.isLoadingMore(),
                  replacement: SizedBox(
                    width: context.width,
                  ),
                  child: Padding(
                    padding: REdgeInsets.only(bottom: 70, top: 50),
                    child: AppLoader(
                      strokeWidth: 2,
                      color: AppColors.k7CB9E8,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  /// Diamond data card
  Widget diamondDataCard(int index) => Obx(
        () => VendorOfferCard(
          onTap: () {
            Get.toNamed<dynamic>(
              Routes.VENDOR_STOCK_OFFER_DETAILS,
              arguments: <String, StockOffer>{
                'stock_offer': controller.stockOffers()[index],
              },
            );
          },
          stockOffer: controller.stockOffers()[index],
          iaAccepting: controller.isOfferAccepting(),
          isDeclining: controller.isOfferRejecting(),
          isUpdating: controller.isOfferUpdating(),
          onAccept: (StockOffer? offer) {
            DialogManager.showAcceptOfferDialog(
              onAccept: () {
                controller.onOfferAction(
                  offerId: offer?.id ?? '',
                  status: 'ACCEPTED',
                );
              },
            );
          },
          onDecline: (StockOffer? offer) {
            DialogManager.showDeclineOfferDialog(
              onDecline: () {
                controller.onOfferAction(
                  offerId: offer?.id ?? '',
                  status: 'REJECTED',
                );
              },
            );
          },
          onCounter: (StockOffer? offer) {
            controller.showCounterOffer(
              offer: offer ?? const StockOffer(),
            );
          },
        ),
      );

  /// Can show actions
  bool canShowActions(int index) =>
      controller.stockOffers()[index].status == OfferStatus.PENDING;

  /// Button card
  Widget buttonCard({
    required String name,
    required Color cardColor,
    required Color textColor,
    required void Function() onTap,
    bool showBorder = false,
    bool isLoading = false,
  }) =>
      GestureDetector(
        onTap: () {
          onTap.call();
        },
        child: Container(
          height: 90.h,
          margin: REdgeInsets.only(right: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(500).r,
            color: showBorder ? Colors.white : cardColor,
            border: showBorder
                ? Border.all(
                    color: cardColor,
                    width: 1.w,
                  )
                : null,
          ),
          child: Center(
            child: isLoading
                ? SizedBox(
                    height: 35.h,
                    width: 35.w,
                    child: AppLoader(
                      color: textColor,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    name,
                    style: AppFontStyles.skolaSans(
                      fontSize: 35.sp,
                      color: textColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
          ),
        ),
      );
}
