import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/my_order/models/filter_order.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_stock_offeres/widgets/counter_offer_widget.dart';
import 'package:diamond_company_app/app/providers/vendor_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Vendor stock offers controller
class VendorStockOffersController extends GetxController
    with GetSingleTickerProviderStateMixin {
  /// On init
  @override
  void onInit() {
    tabController = TabController(
      length: filterList.length,
      vsync: this,
    );

    selectedFilter(filterList.first);
    tabController.addListener(
      () {
        selectedFilter(filterList[tabController.index]);
        selectedFilter.refresh();
      },
    );
    getStockOffers();
    scrollController.addListener(_scrollListener);
    super.onInit();
  }

  /// On ready
  @override
  void onReady() {
    super.onReady();
  }

  /// On close
  @override
  void onClose() {
    scrollController.removeListener(_scrollListener);
    super.onClose();
  }

  /// Scroll listener
  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      loadMoreData();
    }
  }

  /// Filter list
  RxList<FilterModel> filterList = <FilterModel>[
    FilterModel(
      title: 'All',
      value: 'ALL',
    ),
    FilterModel(
      title: 'Pending',
      value: 'PENDING',
    ),
    FilterModel(
      title: 'In Progress',
      value: 'REVISED',
      isResponseRequired: true,
    ),
    FilterModel(
      title: 'Accepted',
      value: 'ACCEPTED',
    ),
    FilterModel(
      title: 'Rejected',
      value: 'REJECTED',
    ),
  ].obs;

  /// Is loading
  final RxBool isLoading = false.obs;

  /// limit
  RxInt limit = 10.obs;

  /// skip
  RxInt skip = 0.obs;

  /// hasMoreData
  RxBool hasMoreData = true.obs;

  /// isLoadingMore
  RxBool isLoadingMore = false.obs;

  /// Selected filter
  Rx<FilterModel> selectedFilter = FilterModel().obs;

  /// Scroll controller
  ScrollController scrollController = ScrollController();

  /// Stock offers
  RxList<StockOffer> stockOffers = <StockOffer>[].obs;

  /// Tab controller
  late TabController tabController;

  /// Is offer accepting
  RxBool isOfferAccepting = false.obs;

  /// Is offer updating
  RxBool isOfferUpdating = false.obs;

  /// Is offer rejecting
  RxBool isOfferRejecting = false.obs;

  /// Controller
  TextEditingController controller = TextEditingController();

  /// Vendor stock offers controller
  Future<void> getStockOffers({
    int skip = 0,
    int limit = 10,
  }) async {
    try {
      if (skip == 0) {
        this.skip(0);
        hasMoreData(true);
        isLoading(true);
        isLoadingMore(false);
        stockOffers().clear();
      } else {
        isLoadingMore(true);
      }

      final List<StockOffer>? offers = await VendorProvider.getStockOffers(
        status:
            selectedFilter().value != 'ALL' ? selectedFilter().value ?? '' : '',
        skipLimit: skip,
        page: limit,
        isResponseRequired: selectedFilter().isResponseRequired,
      );

      if ((offers?.isEmpty ?? true) || (offers?.length ?? 0) < this.limit()) {
        hasMoreData(false);
      } else {
        this.skip(
          this.skip() + this.limit(),
        );
      }
      stockOffers
        ..addAll(offers ?? <StockOffer>[])
        ..refresh();

      isLoading(false);
      isLoadingMore(false);
    } on DioException catch (e, t) {
      isLoading(false);
      isLoadingMore(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(
        'Error in get stock offers: $e here ====> $t',
      );
    } finally {
      isLoading(false);
      isLoadingMore(false);
    }
  }

  /// Load more data
  void loadMoreData() {
    if (hasMoreData()) {
      if (!isLoadingMore()) {
        getStockOffers(
          skip: skip(),
          limit: limit(),
        );
      }
    }
  }

  /// Accept offer
  Future<void> onOfferAction({
    required String offerId,
    required String status,
    double? offerPrice,
  }) async {
    try {
      if (isOfferUpdating() || isOfferAccepting() || isOfferRejecting()) {
        appSnackbar(
          message: 'Please wait while we are processing your request',
          snackBarState: SnackBarState.INFO,
        );
        return;
      }

      switch (status) {
        case 'ACCEPTED':
          isOfferAccepting(true);
          break;
        case 'REJECTED':
          isOfferRejecting(true);
          break;
        case 'PENDING':
          isOfferUpdating(true);
          break;
      }

      final Map<String, dynamic> payload = <String, dynamic>{
        'offer_id': offerId,
        'status': status,
      };

      if (status == 'PENDING') {
        payload['offer_price'] = offerPrice;
      }

      final StockOffer? updatedOffer = await VendorProvider.updateOffer(
        payload,
      );

      if (updatedOffer != null) {
        switch (status) {
          case 'ACCEPTED':
            final int index = stockOffers.indexWhere(
              (StockOffer element) => element.id == offerId,
            );

            if (index != -1) {
              final StockOffer updatedOffer = stockOffers[index].copyWith(
                status: OfferStatus.ACCEPTED,
              );
              if (selectedFilter().value == 'PENDING') {
                stockOffers
                  ..removeAt(index)
                  ..refresh();
                return;
              }
              stockOffers[index] = updatedOffer;
              stockOffers.refresh();
            }
            break;
          case 'REJECTED':
            final int index = stockOffers.indexWhere(
              (StockOffer element) => element.id == offerId,
            );

            if (index != -1) {
              final StockOffer updatedOffer = stockOffers[index].copyWith(
                status: OfferStatus.REJECTED,
              );

              if (selectedFilter().value == 'PENDING') {
                stockOffers
                  ..removeAt(index)
                  ..refresh();
                return;
              }

              stockOffers[index] = updatedOffer;
              stockOffers.refresh();
            }
            break;
          case 'PENDING':
            final int index = stockOffers.indexWhere(
              (StockOffer element) => element.id == offerId,
            );

            if (index != -1) {
              final StockOffer updatedOffer = stockOffers[index].copyWith(
                status: OfferStatus.REVISED,
                offerPrice: offerPrice,
              );
              stockOffers[index] = updatedOffer;
              stockOffers.refresh();
            }
            break;
        }
      }

      switch (status) {
        case 'ACCEPTED':
          appSnackbar(
            message: 'Offer accepted successfully!!!',
            snackBarState: SnackBarState.SUCCESS,
          );
          break;
        case 'REJECTED':
          appSnackbar(
            message: 'Offer rejected successfully!!!',
            snackBarState: SnackBarState.SUCCESS,
          );
          break;
        case 'PENDING':
          appSnackbar(
            message: 'Offer updated successfully!!!',
            snackBarState: SnackBarState.SUCCESS,
          );
          break;
      }

      isOfferAccepting(false);
      isOfferRejecting(false);
      isOfferUpdating(false);
    } on DioException catch (e) {
      isOfferAccepting(false);
      isOfferRejecting(false);
      isOfferUpdating(false);
      appSnackbar(
        message: e.response?.data?['message'] ?? 'Something went wrong!!!',
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// Show counter offer
  void showCounterOffer({
    required StockOffer offer,
  }) {
    controller.clear();
    Get.bottomSheet(
      CounterOfferWidget(
        offer: offer,
        controller: controller,
        onUpdate: (StockOffer? offer) {
          if (controller.text.isEmpty) {
            appSnackbar(
              message: 'Please enter offer price',
              snackBarState: SnackBarState.DANGER,
            );
            return;
          }
          Get.back();
          onOfferAction(
            offerId: offer?.id ?? '',
            status: 'PENDING',
            offerPrice: double.tryParse(controller.text.trim()) ?? 0.0,
          );
        },
      ),
    );
  }
}
