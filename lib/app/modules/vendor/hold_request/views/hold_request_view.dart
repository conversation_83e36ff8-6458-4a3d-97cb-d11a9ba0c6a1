import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/ui/components/accept_decline_cofirmation_dialog.dart';
import 'package:diamond_company_app/app/ui/components/accept_decline_hold_request_confirmation.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/diamond_list_tile.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/hold_request_controller.dart';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/my_order/models/filter_order.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_stock_offeres/widgets/vendor_offer_card.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/dialog_manger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Hold request
class HoldRequestView extends GetView<HoldRequestController> {
  /// Hold request
  const HoldRequestView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: AppColors.kffffff,
        appBar: CommonAppbar(
          centerTitle: true,
          leadingWidth: 220.w,
          onBackPressed: () {
            Get.back();
          },
          title: RichText(
            text: TextSpan(
              text: 'Hold Request',
              style: AppFontStyles.skolaSans(
                fontSize: 20,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ),
        body: Column(
          children: <Widget>[
            50.verticalSpace,
            Obx(
              () => Container(
                height: 90.h,
                child: TabBar(
                  splashBorderRadius: BorderRadius.circular(20).r,
                  controller: controller.tabController,
                  tabAlignment: TabAlignment.start,
                  indicatorColor: Colors.transparent,
                  dividerColor: Colors.transparent,
                  padding: REdgeInsets.only(left: 60, right: 60),
                  isScrollable: true,
                  labelPadding: EdgeInsets.zero,
                  onTap: (int index) {
                    if (controller.isLoading() || controller.isLoadingMore()) {
                      return;
                    }
                    controller.selectedFilter(
                      controller.filterList[index],
                    );
                    controller.selectedFilter.refresh();
                    controller.getHoldRequests();
                  },
                  tabs: <Widget>[
                    for (final FilterModel filter in controller.filterList)
                      Tab(
                        child: Container(
                          padding: REdgeInsets.symmetric(horizontal: 40),
                          decoration: BoxDecoration(
                            color: controller.selectedFilter().title ==
                                    filter.title
                                ? AppColors.k6CECE6
                                : AppColors.kffffff,
                            borderRadius: BorderRadius.circular(20).r,
                          ),
                          alignment: Alignment.center,
                          child: Center(
                            child: Text(
                              '${filter.title}',
                              style: AppFontStyles.skolaSans(
                                fontSize: 15,
                                color: AppColors.k101C28,
                                fontWeight: controller.selectedFilter().title ==
                                        filter.title
                                    ? FontWeight.w700
                                    : FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            20.verticalSpace,
            Obx(
              () => controller.isLoading()
                  ? Center(
                      child: Padding(
                        padding: REdgeInsets.only(top: 100),
                        child: AppLoader(),
                      ),
                    )
                  : controller.holdRequests().isNotEmpty
                      ? Expanded(
                          child: RefreshIndicator(
                            color: AppColors.k101C28,
                            onRefresh: () async => controller.getHoldRequests(),
                            child: ListView.builder(
                              controller: controller.scrollController,
                              itemCount: controller.holdRequests().length,
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: REdgeInsets.symmetric(
                                horizontal: 60,
                                vertical: 40,
                              ),
                              itemBuilder: (BuildContext context, int index) =>
                                  _buildListItem(index),
                            ),
                          ),
                        )
                      : Center(
                          child: Padding(
                            padding: REdgeInsets.only(top: 100),
                            child: RichText(
                              text: TextSpan(
                                text: 'No stock offers found!',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 15,
                                  color: AppColors.k101C28,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ),
                        ),
            ),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              alignment: Alignment.bottomCenter,
              reverseDuration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Obx(
                () => Visibility(
                  visible: controller.isLoadingMore(),
                  replacement: SizedBox(
                    width: context.width,
                  ),
                  child: Padding(
                    padding: REdgeInsets.only(bottom: 70, top: 50),
                    child: AppLoader(
                      strokeWidth: 2,
                      color: AppColors.k7CB9E8,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  GetBuilder<HoldRequestController> _buildListItem(int index) =>
      GetBuilder<HoldRequestController>(
        builder: (_) => Padding(
          padding: REdgeInsets.only(bottom: 25),
          child: Container(
            padding: REdgeInsets.all(50),
            decoration: BoxDecoration(
              color: AppColors.kffffff,
              borderRadius: BorderRadius.circular(24.r),
              boxShadow: <BoxShadow>[
                BoxShadow(
                  color: AppColors.k000000.withOpacity(0.1),
                  offset: Offset(0, 10.h),
                  blurRadius: 104.r,
                ),
              ],
            ),
            child: Column(
              children: [
                _buildStockTile(index),
                if (controller.isOngoing()) _buildActionButtons(index),
                if (controller.isRejectedTab()) _buildRejectReason(index),
              ],
            ),
          ),
        ),
      );

  InkWell _buildStockTile(int index) => InkWell(
        onTap: () => Get.toNamed(
          Routes.VENDOR_DIAMOND_DETAILS,
          arguments: DiamondArgs(
            id: controller.holdRequests[index].stock?.id ?? '',
            diamond: controller.holdRequests[index].stock,
          ),
        ),
        child: AbsorbPointer(
          child: DiamondListTile(
            isFromVendor: true,
            isActionTaken: controller.isOngoing()
                ? controller.holdRequests[index].isActionTaken
                : true,
            isAvailable: controller.holdRequests[index].isAvailable,
            diamond: controller.holdRequests[index].stock,
          ),
        ),
      );

  Container _buildRejectReason(int index) => Container(
        width: Get.width,
        padding: REdgeInsets.all(40),
        margin: REdgeInsets.only(top: 30),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          boxShadow: [
            BoxShadow(
              color: AppColors.k101C28.withOpacity(0.1),
              offset: Offset(0, 4.h),
              blurRadius: 4.r,
            )
          ],
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Text(
          controller.holdRequests[index].rejectReason ?? '',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w400,
            fontSize: 35.sp,
            color: AppColors.k101C28,
          ),
        ),
      );

  Widget _buildActionButtons(int index) =>
      (controller.holdRequests[index].isActionTaken ?? false)
          ? const SizedBox.shrink()
          : Row(
              children: <Widget>[
                Expanded(
                  child: Obx(
                    () => AppButton.text(
                      buttonText: 'ACCEPT',
                      onPressed: () {
                        showAcceptDeclineHoldRequestAlertDialog(
                          index: index,
                          dialogType: DialogType.ACCEPT,
                        );
                        //controller.onAccept(index);
                      },
                      loaderHeight: 50.h,
                      loaderWidth: 50.w,
                      strokeWidth: 8.w,
                      isLoading:
                          controller.holdRequests[index].isAccepting ?? false,
                      buttonSize: Size(Get.width.w, 100.h),
                      buttonTextStyle: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 35.sp,
                        color: AppColors.kffffff,
                      ),
                      borderRadius: 500.r,
                      backgroundColor: AppColors.k128807,
                      borderColor: AppColors.k128807,
                    ),
                  ),
                ),
                24.horizontalSpace,
                Expanded(
                  child: Obx(
                    () => AppButton.text(
                      buttonText: 'DECLINE',
                      onPressed: () {
                        showAcceptDeclineHoldRequestAlertDialog(
                          index: index,
                          dialogType: DialogType.DECLINE,
                        );
                      },
                      loaderHeight: 50.h,
                      loaderWidth: 50.w,
                      strokeWidth: 8.w,
                      isLoading:
                          controller.holdRequests[index].isDeclining ?? false,
                      buttonSize: Size(Get.width.w, 100.h),
                      buttonTextStyle: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w700,
                        fontSize: 35.sp,
                        color: AppColors.kffffff,
                      ),
                      borderRadius: 500.r,
                      backgroundColor: AppColors.k70777E,
                      borderColor: AppColors.k70777E,
                    ),
                  ),
                ),
              ],
            ).paddingOnly(top: 30.h);

  /// Diamond data card
  Widget diamondDataCard(int index) => Obx(
        () => VendorOfferCard(
          onTap: () {
            // Get.toNamed<dynamic>(
            //   Routes.VENDOR_STOCK_OFFER_DETAILS,
            //   arguments: <String, StockOffer>{
            //     'stock_offer': controller.holdRequests()[index],
            //   },
            // );
          },
          //stockOffer: controller.stockOffers()[index],
          iaAccepting: controller.isOfferAccepting(),
          isDeclining: controller.isOfferRejecting(),
          isUpdating: controller.isOfferUpdating(),
          onAccept: (StockOffer? offer) {
            DialogManager.showAcceptOfferDialog(
              onAccept: () {
                controller.onOfferAction(
                  offerId: offer?.id ?? '',
                  status: 'ACCEPTED',
                );
              },
            );
          },
          onDecline: (StockOffer? offer) {
            DialogManager.showDeclineOfferDialog(
              onDecline: () {
                controller.onOfferAction(
                  offerId: offer?.id ?? '',
                  status: 'REJECTED',
                );
              },
            );
          },
          onCounter: (StockOffer? offer) {
            controller.showCounterOffer(
              offer: offer ?? const StockOffer(),
            );
          },
        ),
      );

  /// Can show actions
  bool canShowActions(int index) =>
      controller.holdRequests()[index].vendorId == OfferStatus.PENDING;

  /// Button card
  Widget buttonCard({
    required String name,
    required Color cardColor,
    required Color textColor,
    required void Function() onTap,
    bool showBorder = false,
    bool isLoading = false,
  }) =>
      GestureDetector(
        onTap: () {
          onTap.call();
        },
        child: Container(
          height: 90.h,
          margin: REdgeInsets.only(right: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(500).r,
            color: showBorder ? Colors.white : cardColor,
            border: showBorder
                ? Border.all(
                    color: cardColor,
                    width: 1.w,
                  )
                : null,
          ),
          child: Center(
            child: isLoading
                ? SizedBox(
                    height: 35.h,
                    width: 35.w,
                    child: AppLoader(
                      color: textColor,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    name,
                    style: AppFontStyles.skolaSans(
                      fontSize: 35.sp,
                      color: textColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
          ),
        ),
      );
}
