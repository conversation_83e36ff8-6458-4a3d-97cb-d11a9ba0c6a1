import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_account/widgets/vendor_kyc_resubmission.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/logout_alert_dialog.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../controllers/vendor_account_controller.dart';

/// Vendor account view
class VendorAccountView extends GetView<VendorAccountController> {
  /// Vendor account view constructor
  const VendorAccountView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: CommonAppbar(
          onBackPressed: () => Get.back(),
          leadingWidth: 200.w,
          clipBehavior: Clip.none,
          title: Text(
            LocaleKeys.my_account_my_account.tr,
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
          centerTitle: true,
          actions: <Widget>[
            IconButton.filled(
              onPressed: () => showLogoutAlertDialog(),
              icon: SizedBox(
                height: 64.h,
                width: 64.w,
                child: AppImages.logoutIcon,
              ),
              style: IconButton.styleFrom(
                backgroundColor: AppColors.kBEFFFC,
                shape: const CircleBorder(),
                fixedSize: Size(120.w, 120.h),
                padding: EdgeInsets.zero,
              ),
              constraints: BoxConstraints(
                minHeight: 120.h,
                minWidth: 120.w,
              ),
            ),
            30.horizontalSpace,
          ],
        ),
        body: Obx(
          () => controller.isDataLoading()
              ? Center(
                  child: AppLoader(),
                )
              : RefreshIndicator(
                  color: AppColors.k101C28,
                  onRefresh: () async {
                    await controller.getUserData();
                  },
                  child: ListView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: REdgeInsets.symmetric(horizontal: 60),
                    children: <Widget>[
                      60.verticalSpace,
                      _buildProfile(),
                      if (UserProvider.adminUser?.canShowKyc ??
                          false) ...<Widget>[
                        80.verticalSpace,
                        const VendorKycResubmission(),
                      ],
                      80.verticalSpace,
                      _buildAddressDetails(),
                      60.verticalSpace,
                      _buildBillingAddressDetails(),
                      60.verticalSpace,
                      FutureBuilder<PackageInfo>(
                        future: PackageInfo.fromPlatform(),
                        builder: (BuildContext context,
                            AsyncSnapshot<PackageInfo> snapshot) {
                          switch (snapshot.connectionState) {
                            case ConnectionState.done:
                              return Align(
                                alignment: Alignment.bottomCenter,
                                child: Text(
                                  'V : ${snapshot.data?.version ?? ''}',
                                  style: AppFontStyles.skolaSans(
                                    fontSize: 40.sp,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.grey,
                                  ),
                                ),
                              );
                            default:
                              return const SizedBox.shrink();
                          }
                        },
                      ),
                      100.verticalSpace,
                    ],
                  ),
                ),
        ),
      );

  /// Profile view
  Widget _buildProfile() => Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: Padding(
          padding: REdgeInsets.symmetric(horizontal: 30, vertical: 60),
          child: Column(
            children: <Widget>[
              Text(
                '${UserProvider.adminUser?.firstName ?? ''} ${UserProvider.adminUser?.lastName ?? ''}',
                style: AppFontStyles.skolaSans(
                  fontSize: 56.sp,
                  fontWeight: FontWeight.w900,
                  color: AppColors.k101C28,
                  letterSpacing: 5.w,
                ),
              ),
              40.verticalSpace,
              Text(
                '${UserProvider.adminUser?.email}',
                style: AppFontStyles.skolaSans(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k70777E,
                ),
              ),
              40.verticalSpace,
              Text(
                '${UserProvider.adminUser?.phone}',
                style: AppFontStyles.skolaSans(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k70777E,
                ),
              ),
            ],
          ),
        ),
      );

  /// Address
  Widget _buildAddressDetails() => Container(
        width: Get.width,
        padding: REdgeInsets.only(
          left: 60,
          right: 30,
          top: 20,
          bottom: UserProvider.adminUser?.address != null ? 40 : 20,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Text(
                  'Address',
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
                const Spacer(),
                if (UserProvider.adminUser?.address != null) ...<Widget>[
                  IconButton(
                    onPressed: () {
                      Get.toNamed(
                        Routes.CREATE_VENDOR,
                        arguments: <String, bool>{
                          'is_edit': true,
                        },
                      );
                    },
                    icon: SizedBox(
                      height: 64.h,
                      width: 64.w,
                      child: AppImages.editLogo,
                    ),
                  )
                ] else ...<Widget>[
                  AppButton.text(
                    buttonText: 'Add',
                    onPressed: () {
                      Get.toNamed(
                        Routes.CREATE_VENDOR,
                        arguments: <String, bool>{
                          'is_edit': true,
                          'can_scroll': true,
                        },
                      );
                    },
                    buttonSize: Size(170.w, 80.h),
                    backgroundColor: AppColors.k101C28,
                    borderRadius: 24.r,
                    borderColor: AppColors.k101C28,
                    buttonTextStyle: TextStyle(
                      fontSize: 40.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.kBEFFFC,
                    ),
                  ),
                ],
              ],
            ),
            if (UserProvider.adminUser?.address != null) ...<Widget>[
              Text(
                '${UserProvider.adminUser?.address?.street ?? ''}\n'
                '${UserProvider.adminUser?.address?.city ?? ''}\n'
                '${UserProvider.adminUser?.address?.zipCode ?? ''} ${UserProvider.adminUser?.address?.state ?? ''}\n'
                '${UserProvider.adminUser?.address?.country ?? ''}',
                style: AppFontStyles.skolaSans(
                  color: AppColors.k70777E,
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ],
        ),
      );

  /// Billing address
  Widget _buildBillingAddressDetails() => Container(
        width: Get.width,
        padding: REdgeInsets.only(
          left: 60,
          right: 30,
          top: 20,
          bottom: UserProvider.adminUser?.billingDetails != null ? 40 : 20,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Text(
                  'Billing Details',
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
                const Spacer(),
                if (UserProvider.adminUser?.billingDetails != null) ...<Widget>[
                  IconButton(
                    onPressed: () {
                      Get.toNamed(
                        Routes.CREATE_VENDOR,
                        arguments: <String, bool>{
                          'is_edit': true,
                          'can_scroll': true,
                        },
                      );                    },
                    icon: SizedBox(
                      height: 64.h,
                      width: 64.w,
                      child: AppImages.editLogo,
                    ),
                  )
                ] else ...<Widget>[
                  AppButton.text(
                    buttonText: 'Add',
                    onPressed: () {
                      Get.toNamed(
                        Routes.CREATE_VENDOR,
                        arguments: <String, bool>{
                          'is_edit': true,
                          'can_scroll': true,
                        },
                      );
                    },
                    buttonSize: Size(170.w, 80.h),
                    backgroundColor: AppColors.k101C28,
                    borderRadius: 24.r,
                    borderColor: AppColors.k101C28,
                    buttonTextStyle: TextStyle(
                      fontSize: 40.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.kBEFFFC,
                    ),
                  ),
                ]
              ],
            ),
            if (UserProvider.adminUser?.billingDetails != null) ...<Widget>[
              Text(
                '${UserProvider.adminUser?.billingDetails?.accountHolderName ?? ''}\n'
                '${UserProvider.adminUser?.billingDetails?.bankName ?? ''}\n'
                '${UserProvider.adminUser?.billingDetails?.accountNumber ?? ''}\n'
                '${UserProvider.adminUser?.billingDetails?.ifsc ?? ''}',
                style: AppFontStyles.skolaSans(
                  color: AppColors.k70777E,
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ],
        ),
      );
}
