import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_dashboard/controllers/vendor_dashboard_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// Vendor kyc re-submission
class VendorKycResubmission extends StatelessWidget {
  /// Vendor kyc re-submission
  const VendorKycResubmission({
    super.key,
  });

  @override
  Widget build(BuildContext context) => Container(
        width: Get.width,
        padding: REdgeInsets.all(60),
        decoration: BoxDecoration(
          color: (UserProvider.adminUser?.documentName ?? '').isNotEmpty
              ? AppColors.kBEFFFC
              : AppColors.kFFE4E4,
          borderRadius: BorderRadius.all(Radius.circular(50.r)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              (UserProvider.adminUser?.isKycUploaded ?? false)
                  ? 'KYC DOCUMENT (${(UserProvider.adminUser?.documentName ?? '').isNotEmpty ? 'KYC process under progress' : 'KYC Rejected'})'
                  : 'UPLOAD KYC',
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
                letterSpacing: 5.sp,
              ),
            ),
            if ((UserProvider.adminUser?.documentName ?? '').isEmpty ||
                (UserProvider.adminUser?.isKycUploaded ?? false))
              60.verticalSpace,
            if ((UserProvider.adminUser?.documentName ?? '').isEmpty ||
                (UserProvider.adminUser?.isKycUploaded ?? false))
              Text(
                UserProvider.adminUser?.kycRejectReason ?? '',
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.k101C28,
                ),
              ),
            if ((UserProvider.adminUser?.kycRejectReason ?? '').isNotEmpty)
              60.verticalSpace,
            (UserProvider.adminUser?.isKycUploaded ?? false)
                ? Row(
                    children: <Widget>[
                      Expanded(
                        child: AppButton.text(
                          buttonText: 'RESUBMIT',
                          onPressed: () async {
                            Get.back();
                            await Get.toNamed(
                              Routes.CREATE_VENDOR,
                              arguments: <String, dynamic>{
                                'is_edit': true,
                                'is_kyc': true,
                              },
                            );
                            await Get.find<VendorDashboardController>()
                                .callAPI();
                          },
                          borderColor: AppColors.k101C28,
                          borderWidth: 4.w,
                          borderRadius: 500.r,
                          padding: EdgeInsets.zero,
                          loaderColor: AppColors.k101C28,
                          buttonSize: Size(Get.width, 120.h),
                          backgroundColor: AppColors.kffffff,
                          buttonTextStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 32.sp,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ),
                      30.horizontalSpace,
                      Expanded(
                        child: AppButton.text(
                          buttonText: 'PREVIEW',
                          onPressed: () {
                            if (UserProvider
                                    .adminUser?.documentUrl?.isNotEmpty ??
                                false) {
                              previewDocument();
                            } else {
                              appSnackbar(
                                message: 'Something went wrong!!',
                                snackBarState: SnackBarState.DANGER,
                              );
                            }
                          },
                          borderColor: AppColors.k101C28,
                          borderWidth: 4.w,
                          borderRadius: 500.r,
                          padding: EdgeInsets.zero,
                          loaderColor: AppColors.k101C28,
                          buttonSize: Size(Get.width, 120.h),
                          backgroundColor: AppColors.k101C28,
                          buttonTextStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 32.sp,
                            color: AppColors.kBEFFFC,
                          ),
                        ),
                      ),
                    ],
                  )
                : Row(
                    children: [
                      Expanded(
                        child: AppButton.text(
                          buttonText: 'SUBMIT',
                          onPressed: () async {
                            Get.back();
                            await Get.toNamed(
                              Routes.CREATE_VENDOR,
                              arguments: <String, dynamic>{
                                'is_edit': true,
                                'is_kyc': true,
                              },
                            );
                            await Get.find<VendorDashboardController>()
                                .callAPI();
                          },
                          borderColor: AppColors.k101C28,
                          borderWidth: 4.w,
                          borderRadius: 500.r,
                          padding: EdgeInsets.zero,
                          loaderColor: AppColors.k101C28,
                          buttonSize: Size(Get.width, 120.h),
                          backgroundColor: AppColors.kffffff,
                          buttonTextStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 32.sp,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      );
}

/// preview KYC
Future<void> previewDocument() async {
  final WebViewController webController = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {
          // Update loading bar.
        },
        onPageStarted: (String url) {},
        onPageFinished: (String url) {},
        onHttpError: (HttpResponseError error) {},
        onWebResourceError: (WebResourceError error) {},
        onNavigationRequest: (NavigationRequest request) {
          if (request.url.startsWith('https://www.youtube.com/')) {
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate;
        },
      ),
    )
    ..loadRequest(Uri.parse(UserProvider.adminUser?.documentUrl ?? ''));

  await Get.dialog(
    SimpleDialog(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24.r))),
      insetPadding: REdgeInsets.all(60),
      titlePadding: EdgeInsets.zero,
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      contentPadding: REdgeInsets.all(60),
      title: ListTile(
        contentPadding: REdgeInsets.only(left: 60),
        visualDensity: VisualDensity.compact,
        dense: true,
        title: Text(
          UserProvider.adminUser?.documentName ?? '',
          style: AppFontStyles.skolaSans(
            color: AppColors.k101C28,
            fontSize: 40.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
      ),
      children: <Widget>[
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.6,
            minWidth: Get.width,
          ),
          child: WebViewWidget(
            controller: webController,
          ),
        ),
      ],
    ),
  );
}
