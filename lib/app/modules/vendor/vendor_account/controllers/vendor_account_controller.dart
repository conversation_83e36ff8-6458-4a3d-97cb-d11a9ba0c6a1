import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/user/admin_user_entity.dart';
import 'package:diamond_company_app/app/providers/vendor_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

/// Vendor account controller
class VendorAccountController extends GetxController {
  /// On init
  @override
  void onInit() {
    getUserData();
    super.onInit();
  }

  /// On ready
  @override
  void onReady() {
    super.onReady();
  }

  /// On close
  @override
  void onClose() {
    super.onClose();
  }

  /// Is data loading
  RxBool isDataLoading = false.obs;

  /// Open Mail
  Future<void> openMail() async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: UserProvider.adminUser?.email ?? '',
      query: 'subject=demo Email&body=Hello, this is a demo email.',
    );
    if (await canLaunchUrl(emailLaunchUri)) {
      await launchUrl(emailLaunchUri);
    } else {
      logE('Could not launch $emailLaunchUri');
    }
  }

  /// Open Instagram
  Future<void> openInstagram({
    String? instagramId,
  }) async {
    final Uri instagramUri =
        Uri.parse('${instagramId ?? 'https://www.instagram.com/'}');
    if (await canLaunchUrl(instagramUri)) {
      await launchUrl(instagramUri);
    } else {
      logE('Could not launch $instagramUri');
    }
  }

  /// Open Facebook
  Future<void> openFacebook({
    String? faceBookId,
  }) async {
    final Uri facebookUri =
        Uri.parse('${faceBookId ?? 'https://www.facebook.com/'}');
    if (await canLaunchUrl(facebookUri)) {
      await launchUrl(facebookUri);
    } else {
      logE('Could not launch $facebookUri');
    }
  }

  /// Get user data
  Future<void> getUserData() async {
    isDataLoading(true);
    try {
      final AdminUserEntity? user = await VendorProvider.getUserData();

      if (user != null) {
        final String? authToken = UserProvider.authToken;

        await UserProvider.onLogin(
          userAuthToken: authToken ?? '',
          adminUser: user,
        );
      }

      isDataLoading(false);
    } on DioException catch (e, t) {
      isDataLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE('Error in get user data: $e here ====> $t');
    } finally {
      isDataLoading(false);
    }
  }
}
