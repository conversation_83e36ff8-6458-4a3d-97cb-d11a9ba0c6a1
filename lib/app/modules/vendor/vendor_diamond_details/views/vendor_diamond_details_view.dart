import 'package:diamond_company_app/app/modules/vendor/vendor_diamond_details/controllers/vendor_diamond_details_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/modules/diamond_details/views/video_view.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/share_utils.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// DiamondDetailsView
class VendorDiamondDetailsView extends GetView<VendorDiamondDetailsController> {
  /// DiamondDetailsView
  const VendorDiamondDetailsView({
    required this.viewTag,
    super.key,
  });

  final String viewTag;

  @override
  String get tag => viewTag;

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  Stack _buildBody() => Stack(
        children: <Widget>[
          Obx(
            () => controller.isLoading()
                ? const LinearProgressIndicator(
                    color: AppColors.k1A47E8,
                  )
                : RefreshIndicator(
                    onRefresh: () => controller.fetchDiamondDetails(),
                    color: AppColors.k101C28,
                    child: CustomScrollView(
                      clipBehavior: Clip.none,
                      controller: controller.scrollController,
                      keyboardDismissBehavior:
                          ScrollViewKeyboardDismissBehavior.onDrag,
                      slivers: <Widget>[
                        // _buildHeader(controller.diamond()),
                        _buildDetails(controller.diamond()),
                      ],
                    ),
                  ),
          ),
        ],
      );

  SliverToBoxAdapter _buildDetails(DiamondEntity diamond) => SliverToBoxAdapter(
        child: Column(
          children: <Widget>[
            50.verticalSpace,
            _buildSlider(),
            30.verticalSpace,
            Padding(
              padding: REdgeInsets.symmetric(horizontal: 60),
              child: Row(
                children: <Widget>[
                  StatusChip(
                    text: diamond.type.toString() ?? '',
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kEAEFF4,
                  ),
                  16.horizontalSpace,
                  // if (diamond.availability?.isNotEmpty ??
                  //     false || diamond.availability != null)
                  //   StatusChip(
                  //     text:
                  //         diamond.availability.toString().capitalizeFirst ?? '',
                  //     textStyle: AppFontStyles.skolaSans(
                  //       fontWeight: FontWeight.w500,
                  //       fontSize: 40.sp,
                  //       color: AppColors.kffffff,
                  //     ),
                  //     color: diamond.statusColor,
                  //   ),
                  // 16.horizontalSpace,
                  (diamond.country?.isEmpty ?? false || diamond.country == null)
                      ? const SizedBox.shrink()
                      : StatusChip(
                          text: diamond.country == null
                              ? ''
                              : diamond.country.toString().toUpperCase() ?? '',
                          color: Colors.white,
                          textStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.k101C28,
                          ),
                          border: Border.all(
                            color: AppColors.k000000.withOpacity(0.6),
                            width: 2.w,
                          ),
                        ),
                  const Spacer(),
                  IconButton.outlined(
                    constraints: BoxConstraints(
                      maxHeight: 120.h,
                      maxWidth: 120.w,
                    ),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: AppColors.kEAEFF4,
                      side: BorderSide(color: AppColors.kEAEFF4, width: 1.w),
                      shape: const CircleBorder(),
                      padding: EdgeInsets.zero,
                      fixedSize: Size(120.w, 120.h),
                    ),
                    onPressed: () {
                      share(text: controller.diamond().toShareText());
                    },
                    icon: const Icon(Icons.share, color: AppColors.k101C28),
                  ),
                ],
              ),
            ),
            Padding(
              padding: REdgeInsets.symmetric(horizontal: 60),
              child: ListTile(
                contentPadding: REdgeInsets.all(0),
                title: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Flexible(
                      child: Text(
                        '${diamond.shape?.toUpperCase() ?? '-'}, '
                        '${diamond.weight ?? '-'} ct',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w400,
                          fontSize: 64.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ),
                    30.horizontalSpace,
                    Text(
                      '${diamond.color?.toUpperCase() ?? '-'},',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                    30.horizontalSpace,
                    Text(
                      '${diamond.clarity?.toUpperCase() ?? '-'}',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ],
                ),
                subtitle: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      'Stock ID: ${diamond.skuPrefix?.toUpperCase() ?? ''}${diamond.skuNumber == null ? '' : diamond.skuNumber}',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ],
                ),
                // trailing: SizedBox(
                //   height: 60.h,
                //   width: 182.w,
                //   child: AppImages.gia,
                // ),
              ),
            ),
            Padding(
              padding: REdgeInsets.symmetric(horizontal: 60),
              child: Row(
                children: <Widget>[
                  Text(
                    '${diamond.price?.finalPriceOri.toString().toPrice()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 50.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Text(
                    ' \$/CT: ${diamond.price?.pricePerCaratOri.toString().toPrice()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 50.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Text(
                    ' ${diamond.price?.discountOri?.toStringAsFixed(2)}%',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 50.sp,
                      color: AppColors.k1A47E8,
                    ),
                  )
                ],
              ),
            ),
            60.verticalSpace,
            _buildBasicDetails(diamond),
            350.verticalSpace,
          ],
        ),
      );

  Widget _buildBasicDetails(DiamondEntity diamond) {
    String fluoColor = diamond.fluorescenceColor?.toUpperCase() ?? '';

    if (fluoColor.isEmpty) {
      fluoColor = '-';
    }

    DiamondUtils.getFinishAbbreviation(diamond.cut);

    return Column(
      children: <Widget>[
        Container(
          margin: REdgeInsets.symmetric(horizontal: 60),
          padding: REdgeInsets.all(60),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.r),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 10.h),
                blurRadius: 104.r,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildAboutText(
                text: 'Certificate Number',
                about: diamond.certificateNumber == ''
                    ? '-'
                    : diamond.certificateNumber ?? '-',
              ),
              _buildAboutText(
                text: 'Lab',
                about:
                    diamond.lab == '' ? '-' : diamond.lab?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: 'Cut',
                about:
                    diamond.cut == '' ? '-' : diamond.cut?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: 'Polish',
                about: diamond.polish == ''
                    ? '-'
                    : diamond.polish?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: 'Symmetry',
                about: diamond.symmetry == ''
                    ? '-'
                    : diamond.symmetry?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: 'Fluo.',
                about: diamond.fluorescenceIntensity == ''
                    ? '-'
                    : diamond.fluorescenceIntensity?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: 'Fluo. Color.',
                about: diamond.fluorescenceColor == ''
                    ? '-'
                    : diamond.fluorescenceColor?.toUpperCase() ?? '-',
              ),
              if (controller.diamond().isFancyColor)
                Column(
                  children: [
                    _buildAboutText(
                      text: 'F. Intensity',
                      about: diamond.fancyColorIntensity == ''
                          ? '-'
                          : diamond.fancyColorIntensity?.toUpperCase() ?? '-',
                    ),
                    _buildAboutText(
                      text: 'F. Overtone',
                      about: diamond.fancyColorOvertone == ''
                          ? '-'
                          : diamond.fancyColorOvertone?.toUpperCase() ?? '-',
                    ),
                  ],
                ),
              _buildAboutText(
                text: 'Measurements',
                about: diamond.measurements == ''
                    ? '-'
                    : diamond.measurements ?? '-',
              ),
              _buildAboutText(
                text: 'Ratio',
                about: diamond.lwRatio == '' ? '-' : diamond.lwRatio ?? '-',
              ),
              _buildAboutText(
                text: 'Depth (%)',
                about: '${diamond.depth ?? '-'}',
              ),
              _buildAboutText(
                text: 'Table (%)',
                about: '${diamond.table ?? '-'}',
              ),
              _buildAboutText(
                text: 'Culet',
                about: diamond.culetSize == '' || diamond.culetSize == null
                    ? '-'
                    : diamond.culetSize.toString().firstCharCapital ?? '-',
              ),
              _buildAboutText(
                text: 'Culet Cond.',
                about: diamond.culetCondition == '' || diamond.culetSize == null
                    ? '-'
                    : diamond.culetCondition.toString().firstCharCapital ?? '-',
              ),
              _buildAboutText(
                text: 'Crown Angle',
                about: '${diamond.crownAngle ?? '-'}',
              ),
              _buildAboutText(
                text: 'Crown Height',
                about: '${diamond.crownHeight ?? '-'}',
              ),
              _buildAboutText(
                text: 'Pav Angle',
                about: '${diamond.pavilionAngle ?? '-'}',
              ),
              _buildAboutText(
                text: 'Pav Height',
                about: '${diamond.pavilionDepth ?? '-'}',
              ),
              _buildAboutText(
                text: 'Girdle',
                about: diamond.girdle == '' ? '-' : diamond.girdle ?? '-',
              ),
            ],
          ),
        ),
        60.verticalSpace,
        controller.diamond().memberComment == null ||
                controller.diamond().memberComment == ''
            ? const SizedBox.shrink()
            : Column(
                children: [
                  Container(
                    padding: REdgeInsets.all(60),
                    margin: REdgeInsets.symmetric(horizontal: 60),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24.r),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: AppColors.k000000.withOpacity(0.1),
                          offset: Offset(0, 10.h),
                          blurRadius: 104.r,
                        ),
                      ],
                    ),
                    child: Column(
                      children: <Widget>[
                        Row(
                          children: [
                            StatusChip(
                              text: 'Comment',
                              textStyle: AppFontStyles.skolaSans(
                                fontWeight: FontWeight.w500,
                                fontSize: 40.sp,
                                color: AppColors.k101C28,
                              ),
                              color: AppColors.kEAEFF4,
                            ),
                          ],
                        ),
                        40.verticalSpace,
                        Text(
                          controller.diamond().memberComment ?? '',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                  ),
                  24.verticalSpace,
                ],
              ),
        controller.diamond().certificateComment == null ||
                controller.diamond().certificateComment == ''
            ? const SizedBox.shrink()
            : Column(
                children: [
                  Container(
                    width: Get.width,
                    padding: REdgeInsets.all(60),
                    margin: REdgeInsets.symmetric(horizontal: 60),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24.r),
                      border: Border.all(color: AppColors.k1A47E8, width: 2.w),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        RichText(
                          text: TextSpan(
                            text: 'LAB NOTE',
                            style: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w900,
                              color: AppColors.k101C28,
                              letterSpacing: 5.sp,
                            ),
                            children: <TextSpan>[
                              TextSpan(
                                text: ' 🔬',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 48.sp,
                                  fontWeight: FontWeight.w900,
                                  color: AppColors.k101C28,
                                  letterSpacing: 5.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                        40.verticalSpace,
                        Text(
                          controller.diamond().certificateComment ?? '',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                  ),
                  60.verticalSpace,
                ],
              ),
        // Padding(
        //   padding: REdgeInsets.symmetric(horizontal: 60),
        //   child: Theme(
        //     data: Theme.of(Get.context!)
        //         .copyWith(dividerColor: Colors.transparent),
        //     child: ExpansionTile(
        //       title: Row(
        //         children: <Widget>[
        //           Text(
        //             LocaleKeys.diamond_details_view_certificate.tr,
        //             style: AppFontStyles.skolaSans(
        //               fontSize: 40.sp,
        //               fontWeight: FontWeight.w900,
        //               color: AppColors.k101C28,
        //               letterSpacing: 5.sp,
        //             ),
        //           ),
        //           const Spacer(),
        //           AppButton.text(
        //             buttonText: LocaleKeys.diamond_details_view_download.tr,
        //             onPressed: () {
        //               if (controller.diamond().haveCertificate) {
        //                 if (controller.diamond().haveCertificateImage) {
        //                   bool isPdf = controller.diamond().isCertificatePdf;
        //                   if (isPdf) {
        //                     controller.getCertificateUrl(
        //                       controller.diamond().certificateImage,
        //                     );
        //                   }
        //                   Get.to(
        //                     () => CertificateView(
        //                       viewTag: viewTag,
        //                       isWebView: !isPdf,
        //                     ),
        //                   );
        //                 } else {
        //                   controller.diamond().openNativeCertificate();
        //                 }
        //               }
        //             },
        //             borderColor: AppColors.k101C28,
        //             borderWidth: 4.w,
        //             padding: EdgeInsets.zero,
        //             buttonSize: Size(373.w, 80.h),
        //             backgroundColor: AppColors.kffffff,
        //             borderRadius: 500.r,
        //             buttonTextStyle: AppFontStyles.skolaSans(
        //               fontWeight: FontWeight.w700,
        //               fontSize: 35.sp,
        //               color: AppColors.k101C28,
        //             ),
        //           ),
        //         ],
        //       ),
        //       onExpansionChanged: (bool value) =>
        //           controller.certificateToggle(value),
        //       dense: true,
        //       visualDensity: const VisualDensity(
        //         horizontal: VisualDensity.minimumDensity,
        //         vertical: VisualDensity.minimumDensity,
        //       ),
        //       tilePadding: EdgeInsets.zero,
        //       trailing: Container(
        //         height: 100.h,
        //         width: 100.w,
        //         decoration: const BoxDecoration(
        //           color: AppColors.kEAEFF4,
        //           shape: BoxShape.circle,
        //         ),
        //         child: Obx(
        //           () => Icon(
        //             controller.certificateExpansionTile.value
        //                 ? Icons.expand_less_sharp
        //                 : Icons.expand_more_sharp,
        //             color: AppColors.k101C28,
        //           ),
        //         ),
        //       ),
        //       children: <Widget>[
        //         40.verticalSpace,
        //         Container(
        //           height: 735.h,
        //           width: Get.width,
        //           margin: REdgeInsets.only(right: 14, bottom: 14),
        //           decoration: BoxDecoration(
        //             color: Colors.white,
        //             borderRadius: BorderRadius.circular(24.r),
        //             boxShadow: <BoxShadow>[
        //               BoxShadow(
        //                 color: AppColors.k000000.withOpacity(0.25),
        //                 offset: Offset(5.w, 5.h),
        //                 blurRadius: 10.r,
        //               ),
        //             ],
        //           ),
        //           child: AppCachedImage(
        //             imageUrl:
        //                 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSot4yUKTPcV1QMYSizy25G-k7wMJAEIomb0g&s',
        //             // imageUrl: diamond.certificateImage.toString(),
        //             width: Get.width,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),
        // 60.verticalSpace,
        // CommonAskQueries(
        //   textTitle: LocaleKeys.diamond_details_view_chat_title.tr,
        //   onPressed: () => Get.toNamed(Routes.INQUIRY_MESSAGES, arguments: {
        //     'type': ChatType.DIAMOND_DETAILS,
        //     'diamond': diamond,
        //   }),
        // ),
        80.verticalSpace,
      ],
    );
  }

  /// Build About Text
  Widget _buildAboutText({required String text, String? about}) => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SizedBox(
            width: Get.width * 0.3,
            child: Text(
              text,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28.withOpacity(0.6),
              ),
            ),
          ),
          20.verticalSpace,
          Text(
            about ?? '-',
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      ).paddingOnly(bottom: 10.h);

  /// APP BAR
  CommonAppbar _buildAppBar() => CommonAppbar(
        titleSpacing: 0.w,
        title: Obx(
          () => controller.isLoading()
              ? const SizedBox.shrink()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${controller.diamond().shape?.toUpperCase() ?? ''}, ${controller.diamond().weight ?? 'NONE'} ct, ${controller.diamond().color?.toUpperCase() ?? ''}, ${controller.diamond().clarity?.toUpperCase() ?? ''}',
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.k101C28,
                      ),
                    ),
                    8.verticalSpace,
                    Text(
                      '${controller.diamond().cut?.toUpperCase() ?? '-'} ${controller.diamond().polish?.toUpperCase() ?? ''} ${controller.diamond().symmetry?.toUpperCase() ?? ''} ${controller.diamond().fluorescenceIntensity?.toUpperCase() ?? 'NONE'}'
                          .trim(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ],
                ),
        ),
        onBackPressed: () => Get.back(),
      );

  /// Build Slider
  Widget _buildSlider() => Obx(
        () => Column(
          children: <Widget>[
            _buildSliderImages(diamond: controller.diamond()),
            30.verticalSpace,
            _buildSliderIndicator(diamond: controller.diamond()),
          ],
        ),
      );

  /// Build Slider Indicator
  Widget _buildSliderIndicator({required DiamondEntity diamond}) => Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List<Widget>.generate(
          diamond.images.length + 1,
          (int index) => GestureDetector(
            onTap: () {
              controller.carouselController.animateToPage(index);
              controller.current(index);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 20.w,
              height: 20.h,
              margin: REdgeInsets.only(right: 20),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: controller.current() == index
                    ? AppColors.k101C28
                    : AppColors.kffffff,
                border: Border.all(
                  width: 2.w,
                  color: controller.current() == index
                      ? AppColors.k101C28
                      : AppColors.k9FA4A9,
                ),
              ),
            ),
          ),
        ),
      );

  /// Build Slider Images
  Widget _buildSliderImages({required DiamondEntity diamond}) {
    final List<String> sliderUrls = <String>[...diamond.media];
    if (sliderUrls.isEmpty) {
      sliderUrls.add('');
    }
    return sliderUrls.isEmpty
        ? const SizedBox.shrink()
        : Container(
            height: 1005.h,
            width: Get.width,
            child: CarouselSlider(
              items: sliderUrls
                  .map((String url) => _buildSliderItem(url))
                  .toList(),
              carouselController: controller.carouselController,
              options: CarouselOptions(
                  enableInfiniteScroll: diamond.images.length > 1,
                  autoPlay: diamond.images.length > 1,
                  enlargeCenterPage: true,
                  aspectRatio: 1,
                  viewportFraction: 1,
                  onPageChanged:
                      (int index, CarouselPageChangedReason reason) =>
                          controller.current(index)),
            ),
          );
  }

  Widget _buildSliderItem(String url) {
    if (url.isEmpty) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: SizedBox(
          height: 420.h,
          width: Get.width,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24.r),
            child: Image.asset(
              AppImages.diamondPlaceholderPngPath,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    } else if (url.isImageUrl()) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24.r),
          child: SizedBox(
            height: 420.h,
            width: Get.width,
            child: PhotoView(
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              imageProvider: CachedNetworkImageProvider(url),
            ),
          ),
        ),
      );
    } else if (!url.isImageUrl()) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: SizedBox(
          height: 420.h,
          width: Get.width,
          child: Stack(
            children: [
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24.r),
                  child: WebViewWidget(
                    controller: WebViewController()
                      ..setJavaScriptMode(JavaScriptMode.unrestricted)
                      ..setBackgroundColor(Get.theme.scaffoldBackgroundColor)
                      ..loadRequest(Uri.parse(url)),
                  ),
                ),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: IconButton(
                  onPressed: () => Get.to(
                    VideoView(
                      viewTag: viewTag,
                      url: url,
                    ),
                  ),
                  icon: const Icon(Icons.open_in_new_outlined),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
