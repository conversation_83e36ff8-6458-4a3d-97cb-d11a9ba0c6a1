import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_dashboard/views/vendor_side_menu.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_dashboard/widgets/info_card.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../controllers/vendor_dashboard_controller.dart';

/// Vendor dash board view
class VendorDashboardView extends GetView<VendorDashboardController> {
  /// Vendor dash board view constructor
  const VendorDashboardView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        key: controller.scaffoldKey,
        appBar: _buildAppBar(context),
        drawer: const VendorSideMenu(),
        body: RefreshIndicator(
          color: AppColors.k101C28,
          onRefresh: () => controller.callAPI(),
          child: ListView(
            padding: REdgeInsets.symmetric(horizontal: 60),
            controller: controller.scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            children: <Widget>[
              100.verticalSpace,
              vendorStockCard(),
              100.verticalSpace,
              Obx(
                () => InfoCard(
                  isDataLoading: controller.isLoading(),
                  buyRequest: controller.kpiData().stockOffers,
                  cardColor: AppColors.kE9F4F2,
                  title: 'STOCK OFFERS',
                  onTap: () {
                    Get.toNamed(Routes.VENDOR_STOCK_OFFERES);
                  },
                  onShowAllData: () {
                    controller.canShowStockOffers.toggle();
                  },
                  showAllData: controller.canShowStockOffers(),
                ),
              ),
              60.verticalSpace,
              Obx(
                () => InfoCard(
                  isDataLoading: controller.isLoading(),
                  buyRequest: controller.kpiData().buyRequest,
                  title: 'HOLD REQUESTS',
                  onTap: () => Get.toNamed(Routes.HOLD_REQUEST),
                  onShowAllData: () {
                    controller.canShowHoldRequests.toggle();
                  },
                  showAllData: controller.canShowHoldRequests(),
                ),
              ),
              60.verticalSpace,
              Obx(
                () => InfoCard(
                  isDataLoading: controller.isLoading(),
                  buyRequest: controller.kpiData().returnOrder,
                  cardColor: AppColors.kF4F0FB,
                  title: 'RETURN ORDERS',
                  onTap: () {
                    Get.toNamed(Routes.VENDOR_RETURN_ORDER);
                  },
                  onShowAllData: () {
                    controller.canShowReturnOrders.toggle();
                  },
                  showAllData: controller.canShowReturnOrders(),
                ),
              ),
              60.verticalSpace,
              Obx(
                () => InfoCard(
                  isDataLoading: controller.isLoading(),
                  cardColor: AppColors.kFCF6E5,
                  buyRequest: controller.kpiData().vendorOrders,
                  title: 'VENDOR ORDERS',
                  onTap: () => Get.toNamed(Routes.VENDOR_ORDER),
                  onShowAllData: () {
                    controller.canShowVendorOrders.toggle();
                  },
                  showAllData: controller.canShowVendorOrders(),
                ),
              ),
              180.verticalSpace,
            ],
          ),
        ),
      );

  /// App bar
  AppBar _buildAppBar(BuildContext context) => AppBar(
        surfaceTintColor: AppColors.kF1F1F1,
        clipBehavior: Clip.none,
        leading: _buildAppbarButton(
          onPressed: () {
            controller.scaffoldKey.currentState?.openDrawer();
          },
          icon: AppImages.drawer,
        ),
        title: SizedBox(
          height: 100.h,
          width: 308.w,
          child: AppImages.diamondCompany,
        ),
        titleSpacing: 10,
        backgroundColor: AppColors.kF1F1F1,
        actions: <Widget>[
          Obx(
            () => Badge(
              backgroundColor: AppColors.k1A47E8,
              alignment: Alignment.topRight,
              isLabelVisible: (controller.unreadNotificationCount()) > 0,
              label: Center(
                child: Text(
                  '${controller.unreadNotificationCount()}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    color: AppColors.kffffff,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              offset: const Offset(-5, 3),
              child: _buildAppbarButton(
                onPressed: () => Get.toNamed(Routes.VENDOR_NOTIFICATION),
                icon: AppImages.bell,
              ),
            ),
          ),
          Badge(
            backgroundColor: AppColors.k1A47E8,
            alignment: Alignment.topRight,
            offset: const Offset(-5, 3),
            child: _buildAppbarButton(
              onPressed: () {
                Get.toNamed(
                  Routes.VENDOR_ACCOUNT,
                );
              },
              icon: SvgPicture.asset(
                AppImages.personIconPath,
              ),
            ),
          ),
        ],
      );

  /// App bar button
  IconButton _buildAppbarButton({
    required void Function()? onPressed,
    required Widget icon,
  }) =>
      IconButton(
        onPressed: onPressed,
        icon: Container(
          height: 120.h,
          width: 120.w,
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            shape: BoxShape.circle,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.kCECECE,
                offset: Offset(0, 15.h),
                blurRadius: 40.r,
              ),
            ],
          ),
          child: CircleAvatar(
            backgroundColor: AppColors.kffffff,
            child: SizedBox(
              width: 60.w,
              height: 42.h,
              child: icon,
            ),
          ),
        ),
      );

  /// Vendor stock card
  Widget vendorStockCard() => InkWell(
        onTap: () => Get.toNamed(Routes.VENDOR_INVENTORY),
        child: Container(
          width: Get.width,
          padding: REdgeInsets.symmetric(horizontal: 30, vertical: 50),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30).r,
            color: AppColors.kffffff,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.kCECECE,
                offset: Offset(0, 15.h),
                blurRadius: 40.r,
              ),
            ],
          ),
          child: Row(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Image(
                    width: 110.w,
                    image: const AssetImage(
                      AppImages.vendorStock,
                    ),
                  ),
                  20.horizontalSpace,
                  Text(
                    'Vendor Stocks',
                    style: AppFontStyles.skolaSans(
                      fontSize: 50.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.k000000,
                    ),
                    textAlign: TextAlign.center,
                  )
                ],
              ),
              const Spacer(),
              Obx(
                () => controller.isLoading()
                    ? SizedBox(
                        height: 55.h,
                        width: 55.h,
                        child: AppLoader(
                          strokeWidth: 2,
                          color: AppColors.k7CB9E8,
                        ),
                      )
                    : Text(
                        '${controller.kpiData().vendorsStockCount ?? 0}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 70.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.k7CB9E8,
                        ),
                        textAlign: TextAlign.center,
                      ),
              ),
              30.horizontalSpace,
            ],
          ),
        ),
      );
}
