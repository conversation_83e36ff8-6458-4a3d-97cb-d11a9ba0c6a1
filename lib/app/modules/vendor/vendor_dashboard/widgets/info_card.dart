import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/vendor_dashboard/vendor_kpidata_model.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Info card widget
class InfoCard extends StatelessWidget {
  /// Info card constructor
  const InfoCard({
    required this.title,
    super.key,
    this.cardColor,
    this.showAllData,
    this.onShowAllData,
    this.onTap,
    this.buyRequest,
    this.isDataLoading,
  });

  /// Card color
  final Color? cardColor;

  /// Show all data
  final bool? showAllData;

  /// On show all data
  final void Function()? onShowAllData;

  /// On tap
  final void Function()? onTap;

  /// Title
  final String title;

  /// Buy request
  final List<BuyRequest>? buyRequest;

  /// Is data loading
  final bool? isDataLoading;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          onTap?.call();
        },
        child: Container(
          decoration: BoxDecoration(
            color: cardColor ?? AppColors.kE8F2FA,
            borderRadius: BorderRadius.circular(20).r,
            boxShadow: const <BoxShadow>[
              BoxShadow(
                color: AppColors.kCECECE,
                offset: Offset(0, 1),
                blurRadius: 2,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Padding(
                padding: REdgeInsets.symmetric(horizontal: 40, vertical: 30),
                child: Row(
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          title,
                          style: TextStyle(
                            color: AppColors.k000000,
                            fontSize: 40.sp,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        10.verticalSpace,
                        GestureDetector(
                          onTap: () {
                            if (isDataLoading ?? false) {
                              return;
                            }
                            onShowAllData?.call();
                          },
                          child: Row(
                            children: <Widget>[
                              Text(
                                'View all ${title.toLowerCase()}',
                                style: TextStyle(
                                  color: AppColors.k000000,
                                  fontSize: 30.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              10.horizontalSpace,
                              (isDataLoading ?? false)
                                  ? SizedBox(
                                      height: 20.h,
                                      width: 20.w,
                                      child: AppLoader(
                                        strokeWidth: 1,
                                        color: Colors.black,
                                      ),
                                    )
                                  : Icon(
                                      showAllData ?? false
                                          ? Icons.expand_less
                                          : Icons.expand_more,
                                      color: AppColors.k000000,
                                      size: 40.sp,
                                    ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: AppColors.k000000,
                      size: 40.sp,
                    ),
                  ],
                ),
              ),
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                alignment: Alignment.topCenter,
                curve: Curves.easeInOut,
                child: Visibility(
                  visible: showAllData ?? false,
                  replacement: SizedBox(
                    width: context.width,
                  ),
                  child: (buyRequest?.isNotEmpty ?? false)
                      ? Container(
                          margin: REdgeInsets.only(
                            bottom: 40,
                            left: 40,
                            right: 40,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.kffffff,
                            borderRadius: BorderRadius.circular(10).r,
                          ),
                          padding: REdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 20,
                          ),
                          child: Column(
                            children: List<Widget>.generate(
                              buyRequest?.length ?? 0,
                              (int index) {
                                final bool isNotLast =
                                    index != (buyRequest?.length ?? 0) - 1;

                                if (buyRequest?[index]
                                        .status
                                        ?.toLowerCase()
                                        .contains('auto-canceled') ??
                                    false) {
                                  return const SizedBox.shrink();
                                }
                                return Padding(
                                  padding: REdgeInsets.only(
                                      bottom: isNotLast ? 10 : 0),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Expanded(
                                        child: Text(
                                          buyRequest?[index].status ?? '',
                                          style: TextStyle(
                                            color: buyRequest?[index].color ??
                                                AppColors.k000000,
                                            fontSize: 40.sp,
                                            fontWeight: FontWeight.w400,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      20.horizontalSpace,
                                      Text(
                                        '${buyRequest?[index].count ?? 0}',
                                        style: TextStyle(
                                          color: buyRequest?[index].color ??
                                              AppColors.k7CB9E8,
                                          fontSize: 40.sp,
                                          fontWeight: FontWeight.w700,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        )
                      : Center(
                          child: Padding(
                            padding: REdgeInsets.only(bottom: 30),
                            child: Text(
                              'No data available',
                              style: TextStyle(
                                color: AppColors.k000000,
                                fontSize: 35.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      );
}
