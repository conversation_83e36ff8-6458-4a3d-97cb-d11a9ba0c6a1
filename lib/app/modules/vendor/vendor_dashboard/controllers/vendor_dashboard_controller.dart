import 'dart:async';

import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/local_store.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/rap_price/rap_price.dart';
import 'package:diamond_company_app/app/data/models/rap_price/rap_price_helper.dart';
import 'package:diamond_company_app/app/data/models/static_pages/static_pages.dart';
import 'package:diamond_company_app/app/data/models/user/admin_user_entity.dart';
import 'package:diamond_company_app/app/data/models/vendor_dashboard/vendor_kpidata_model.dart';
import 'package:diamond_company_app/app/data/remote/services/rapnet.dart';
import 'package:diamond_company_app/app/modules/dashboard/views/update_rap_view.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_account/widgets/vendor_kyc_resubmission.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/providers/static_pages_provider.dart';
import 'package:diamond_company_app/app/providers/vendor_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/dialog_manger.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Vendor dashboard controller
class VendorDashboardController extends GetxController {
  /// static page
  Rx<StaticPages> staticPage = StaticPages().obs;

  /// stepper
  final stepper = 0.obs;

  /// On init
  @override
  void onInit() {
    callAPI();

    super.onInit();
  }

  /// On ready
  @override
  void onReady() {
    super.onReady();
  }

  /// On close
  @override
  void onClose() {
    super.onClose();
  }

  /// Scroll controller
  final ScrollController scrollController = ScrollController();

  /// scaffold key
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  /// Can show hold requests
  final RxBool canShowHoldRequests = true.obs;

  /// Can show vendor order
  final RxBool canShowVendorOrders = true.obs;

  /// Can show return order
  final RxBool canShowReturnOrders = true.obs;

  /// Can show stock offers
  final RxBool canShowStockOffers = true.obs;

  /// Vendor stock card
  final RxBool isLoading = false.obs;

  /// Kpi data
  Rx<KpiData> kpiData = KpiData().obs;

  /// Unread notification count
  RxInt unreadNotificationCount = 0.obs;

  /// Kpi data
  Future<void> getKpiData() async {
    canShowManger();
    try {
      isLoading(true);

      final KpiData? kpiData = await VendorProvider.getKpiData();

      if (kpiData != null) {
        this.kpiData(kpiData);
        this.kpiData.refresh();
        isLoading(false);
      }

      boolManger();
    } on DioException catch (e, t) {
      boolManger();
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE('Error in get kpi data: $e here ====> $t');
    } finally {
      boolManger();
      isLoading(false);
    }
  }

  /// Can show manager
  void canShowManger() {
    canShowHoldRequests(false);
    canShowReturnOrders(false);
    canShowReturnOrders(false);
    canShowStockOffers(false);
  }

  /// Bool manager
  void boolManger() {
    canShowHoldRequests(true);
    canShowReturnOrders(true);
    canShowReturnOrders(true);
    canShowStockOffers(true);
  }

  /// Get user data
  Future<void> getUserData() async {
    try {
      final AdminUserEntity? user = await VendorProvider.getUserData();

      if (user != null) {
        final String? authToken = UserProvider.authToken;

        await UserProvider.onLogin(
          userAuthToken: authToken ?? '',
          adminUser: user,
        );
        AuthProvider.adminUserEntity(user);
        AuthProvider.adminUserEntity.refresh();
      }
    } on DioException catch (e, t) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE('Error in get user data: $e here ====> $t');
    }
  }

  /// Get notification count
  Future<void> getNotificationCount() async {
    try {
      final int? count = await VendorProvider.notificationUnReadCount();

      if (count != null) {
        unreadNotificationCount(count);
        unreadNotificationCount.refresh();
      }
    } on DioException catch (e, t) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE('Error in get user data: $e here ====> $t');
    }
  }

  /// show kyc popup
  Future<void> showKYCPOPUPUntilAccepted() async {
    if (UserProvider.adminUser?.canShowKyc ?? false) {
      await Get.dialog(
        const PopScope(
          canPop: false,
          child: SimpleDialog(
            contentPadding: EdgeInsets.zero,
            children: [
              VendorKycResubmission(),
            ],
          ),
        ),
        barrierDismissible: false,
      );
    } else if (!(UserProvider.adminUser?.isTermsAccepted ?? false)) {
      await getStaticPage();
      DialogManager.showTermsDialog(terms: staticPage().content ?? '');
    }
  }

  /// get static page
  Future<void> getStaticPage() async {
    try {
      staticPage(await StaticPagesProvider.getStaticPage(
          {'slug': StaticPagesType.vendor_terms_and_conditions.name}));
      staticPage.refresh();
    } on DioException catch (e) {
      logE(e);
      appSnackbar(
        message: e.response?.data?['message'] ?? 'Something went wrong!!!',
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  Future<void> callAPI() async {
    await getUserData();
    unawaited(showKYCPOPUPUntilAccepted());
    await getKpiData();
    await getNotificationCount();
  }

  Future<void> updatePriceList() async {
    unawaited(_getData());
    stepper(0);
    await Get.bottomSheet<bool?>(
      const UpdateRapView(),
      elevation: 0,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(4),
          topLeft: Radius.circular(4),
        ),
      ),
    ).then((bool? response) {
      if (response != null) {
        if (response) {
          appSnackbar(
            message: 'Price list updated',
            snackBarState: SnackBarState.SUCCESS,
          );
        } else {
          appSnackbar(
            message: 'Error updating price list, please try again.',
            snackBarState: SnackBarState.DANGER,
          );
        }
      }
    });
  }

  Future<void> _getData() async {
    try {
      await _fetchAndSaveRapPrice();
    } on Exception catch (e, s) {
      logW(e);
      logW(s);
    }
  }

  Future<void> _fetchAndSaveRapPrice() async {
    stepper(1);
    final List<RapPrice> priceList = await RapnetService.getPriceList();
    if (priceList.isNotEmpty) {
      vibrate();
      stepper(2);
      RapPriceHelper.addRapPrices(priceList);

      await Future<void>.delayed(
        const Duration(milliseconds: 400),
        () {
          LocalStore.priceUpdatedAt(DateTime.now().toLocal().mmmDDyyyyHmmA);
          Get.back(result: true);
        },
      );
    } else {
      Get.back(result: false);
    }
  }
}
