import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/empty_screens.dart';
import 'package:diamond_company_app/app/ui/components/notification_tile.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../controllers/vendor_notification_controller.dart';

/// Vendor notification view
class VendorNotificationView extends GetView<VendorNotificationController> {
  /// Vendor Notification view controller
  const VendorNotificationView({super.key});

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: CommonAppbar(
          titleSpacing: 0,
          leadingWidth: 200.w,
          centerTitle: true,
          title: Text(
            LocaleKeys.notifications_title.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
          actions: [
            _buildPopupMenu(),
            30.horizontalSpace,
          ],
          onBackPressed: () => Get.back(),
        ),
        body: Obx(
          () => controller.isLoading()
              ? AppLoader()
              : controller.notificationList.isEmpty
                  ? EmptyScreen(
                      icon: AppImages.emptyNotificationSvg,
                      title: 'No Notification Yet',
                      subtitle:
                          "You're all caught up! We'll notify you here about important updates and offers.",
                    )
                  : RefreshIndicator(
                      color: AppColors.k101C28,
                      onRefresh: () => controller.fetchNotifications(),
                      child: Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(Get.context!).padding.bottom),
                        child: ListView.separated(
                          controller: controller.scrollController,
                          padding: REdgeInsets.all(60),
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemBuilder: (BuildContext context, int index) {
                            if (index == controller.notificationList.length) {
                              return controller.isLoadMore()
                                  ? AppLoader()
                                  : const SizedBox.shrink();
                            } else {
                              return NotificationTile(
                                notificationModel:
                                    controller.notificationList[index],
                                onTap: () {
                                  controller.navigateFromNotification(
                                      controller.notificationList[index]);
                                },
                              );
                            }
                          },
                          separatorBuilder: (BuildContext context, int index) =>
                              30.verticalSpace,
                          itemCount: controller.notificationList.length + 1,
                        ),
                      ),
                    ),
        ),
      );

  PopupMenuButton<String> _buildPopupMenu() => PopupMenuButton<String>(
        color: AppColors.kffffff,
        onSelected: (String value) {
          controller.readNotification(readAll: true);
        },
        itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
          PopupMenuItem<String>(
            value: '0',
            child: Text(
              'Mark all as read',
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ],
        child: const Icon(
            Icons.more_vert), // Optional custom widget to trigger menu
      );
}
