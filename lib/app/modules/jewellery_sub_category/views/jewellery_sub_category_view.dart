import 'package:cached_network_image/cached_network_image.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/jewellery_sub_category/controllers/jewellery_sub_category_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:get/get.dart';

/// This is the view for the JewelleryCategoryView.
class JewellerySubCategoryView extends GetView<JewellerySubCategoryController> {
  /// This is the constructor for the JewelleryCategoryView.
  const JewellerySubCategoryView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  Widget _buildBody() => GridView.builder(
        clipBehavior: Clip.none,
        physics: const AlwaysScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          // childAspectRatio: Get.width / Get.height,
          crossAxisSpacing: 60.w,
          mainAxisSpacing: 60.h,
          mainAxisExtent: 470.h,
        ),
        padding: EdgeInsets.only(
            left: 60.w,
            right: 60.w,
            top: 60.h,
            bottom: 60.h + MediaQuery.of(Get.context!).padding.bottom),
        itemCount: 10,
        itemBuilder: (BuildContext context, int index) =>
            _buildSubCategoryItem(),
      );

  Widget _buildSubCategoryItem() => InkWell(
        onTap: () => Get.toNamed(Routes.JEWELLERIES),
        child: SizedBox(
          height: 470.h,
          width: 480.w,
          child: Stack(
            children: [
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 340.h,
                  width: 480.w,
                  decoration: BoxDecoration(
                    color: AppColors.kffffff,
                    borderRadius: BorderRadius.circular(24.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.k000000.withOpacity(0.1),
                        blurRadius: 100.r,
                        offset: Offset(0, 30.w),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      130.verticalSpace,
                      Expanded(
                        child: Center(
                          child: Text(
                            'Bracelets & Bangles',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppFontStyles.skolaSans(
                              fontSize: 48.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.k101C28,
                            ),
                            textAlign: TextAlign.center,
                          ).paddingSymmetric(
                            horizontal: 24.w,
                            vertical: 40.h,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 0,
                left: 20.w,
                right: 20.w,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24.r),
                  child: CachedNetworkImage(
                    imageUrl:
                        'https://kinclimg9.bluestone.com/f_jpg,c_scale,w_828,q_80,b_rgb:f0f0f0/giproduct/BIAR0097R13_YAA18DIG6XXXXXXXX_ABCD00-PICS-00001-1024-64527.png',
                    fit: BoxFit.cover,
                    height: 260.h,
                    width: 440.w,
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  CommonAppbar _buildAppBar() => CommonAppbar(
        title: Text(
          'Categories',
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w700,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: false,
        titleSpacing: 40.w,
        shadowColor: AppColors.k000000.withOpacity(0.8),
        backgroundColor: AppColors.kF1F1F1,
        leadingBorderColor: AppColors.kffffff,
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppColors.k000000.withOpacity(0.08),
            offset: Offset(0, 15.h),
            blurRadius: 40.r,
          ),
        ],
      );
}
