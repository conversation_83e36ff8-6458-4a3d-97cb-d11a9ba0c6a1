import 'dart:async';
import 'dart:developer';

import 'package:carousel_slider/carousel_controller.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/db/app_isar.dart';
import 'package:diamond_company_app/app/data/local/local_store.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/crm_model/crm_model.dart';
import 'package:diamond_company_app/app/data/models/dashboard_metrics/dashboard_metrics.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/filter_entity.dart';
import 'package:diamond_company_app/app/data/models/inquiry/inquiry.dart';
import 'package:diamond_company_app/app/data/models/inventory_response.dart';
import 'package:diamond_company_app/app/data/models/rap_price/rap_price.dart';
import 'package:diamond_company_app/app/data/models/rap_price/rap_price_helper.dart';
import 'package:diamond_company_app/app/data/models/slider_images/slider_images.dart';
import 'package:diamond_company_app/app/data/models/sort_repo.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/data/remote/services/marketplace_service.dart';
import 'package:diamond_company_app/app/data/remote/services/rapnet.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/modules/dashboard/views/update_rap_view.dart';
import 'package:diamond_company_app/app/modules/get_filter/controllers/get_filter_args.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/providers/crm_provider.dart';
import 'package:diamond_company_app/app/providers/inquiry_provider.dart';
import 'package:diamond_company_app/app/providers/dashboard_provider.dart';
import 'package:diamond_company_app/app/providers/stocks_provider.dart';
import 'package:diamond_company_app/app/providers/user_order_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/unavailable_diamond_dialog.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:diamond_company_app/app/utils/dashnoard_metrics_ext.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart' hide CarouselController;
import 'package:get/get.dart';

/// DashboardController
class DashboardController extends GetxController {
  /// current
  final current = 0.obs;

  /// stepper
  final stepper = 0.obs;

  /// carousel controller
  final CarouselController carouselController = CarouselController();

  /// scaffold key
  final GlobalKey<ScaffoldState> scaffoldKey = new GlobalKey<ScaffoldState>();

  /// diamonds
  RxList<DiamondEntity> diamonds = <DiamondEntity>[].obs;

  /// new arrival
  RxList<DiamondEntity> newArrival = <DiamondEntity>[].obs;

  /// featured
  RxList<DiamondEntity> featured = <DiamondEntity>[].obs;

  /// banner images
  RxList<SliderImages> bannerImages = <SliderImages>[].obs;

  /// CRM details
  Rx<CRMModel> crmDetails = const CRMModel().obs;

  /// isLoading
  RxBool isLoading = false.obs;

  /// isFeatured
  RxBool isFeaturedLoading = false.obs;

  /// newArrival
  RxBool newArrivalLoading = false.obs;

  /// isLoading orders
  RxBool isLoadingOrders = false.obs;

  /// isLoading inquiry
  RxBool isLoadingInquiry = false.obs;

  /// dashboard metrics
  Rx<DashboardMetrics> dashboardMetrics = const DashboardMetrics().obs;

  /// orders
  RxList<UserOrder> userOrder = <UserOrder>[].obs;

  RxList<InquiryData> inquiryData = <InquiryData>[].obs;

  final Rx<FilterEntity> appliedFilter = FilterEntity.empty.copyWith().obs;

  final TextEditingController stockIdController = TextEditingController();

  final FocusNode stockIdFocusNode = FocusNode();

  RxBool isLoadingDashboard = false.obs;

  /// scroll controller
  ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();

    checkConnectivity();
    loadDashBoard();

    log(' User Provider Data ================ ${UserProvider.currentUser}');
  }

  Future<void> onFilter() async {
    appliedFilter().status.clear();
    appliedFilter().status.add('AVAILABLE');
    appliedFilter().diamondType.clear(); //lab_grown
    //appliedFilter().diamondType.add('cvd');
    //if (appliedFilter().diamondType.isNotEmpty) {

    var updatedArgs = GetFilterArgs(
      filter: appliedFilter(),
      sizeFrom: '',
      sizeTo: '',
      sort: SortOption(
        sortBy: SortBy.discounts,
        sortOrder: SortOrder.asc,
      ),
    );
    await Get.toNamed(
      Routes.LISTING,
      arguments: updatedArgs,
    );
    // } else {
    //   appSnackbar(
    //     message: 'Please select the type of diamond to search',
    //     snackBarState: SnackBarState.INFO,
    //   );
    //   await scrollController.animateTo(
    //     0,
    //     duration: const Duration(milliseconds: 300),
    //     curve: Curves.easeInOut,
    //   );
    // }
  }

  /// get user inquiry
  Future<void> getUserInquiry() async {
    try {
      isLoadingInquiry(true);
      final List<InquiryData>? response =
          await InquiryProvider.getInquiryList(params: {
        'skip': 0,
        'limit': 3,
      });
      inquiryData(response);
      inquiryData.refresh();
      if (response != null) {
        log('Inquiry fetched successfully');
      }
      isLoadingInquiry(false);
    } on Exception catch (e) {
      logE(e);
      isLoadingInquiry(false);
      appSnackbar(
          message: 'Something went wrong!!!',
          snackBarState: SnackBarState.DANGER);
    }
  }

  /// get stock status
  Future<void> getStocksWithAvailability() async {
    try {
      isLoading(true);
      final List<DiamondEntity> diamonds = Get.find<CartItemService>().diamonds;
      if (diamonds.isNotEmpty) {
        final List<DiamondEntity> diamondListStatus =
            await StockProvider.getStockStatus(
                {'ids': diamonds.map((e) => e.id ?? '').toList()});
        if (diamondListStatus.isNotEmpty) {
          final List<DiamondEntity> updatedList = diamonds.map(
            (DiamondEntity e) {
              final String? status = diamondListStatus
                  .firstWhere((DiamondEntity element) => element.id == e.id)
                  .availability;
              e
                ..availability = status
                ..isAvailable = status == 'AVAILABLE';
              return e;

              // return CartDiamond(
              //   diamondType: e.diamondType,
              //   addedOn: e.addedOn,
              //   available: status == 'AVAILABLE',
              //   id: e.id,
              //   diamond: e.diamond,
              // );
            },
          ).toList();
          final List<DiamondEntity> notAvailableDiamonds = updatedList
              .where((DiamondEntity element) => !(element.isAvailable ?? true))
              .toList();
          if (notAvailableDiamonds.isNotEmpty) {
            await showUnAvailableDiamondDialog(diamonds: notAvailableDiamonds);
          }
        }
      }
      isLoading(false);
    } on Exception catch (e) {
      logE(e);
      isLoading(false);
      appSnackbar(
          message: 'Something went wrong!!!',
          snackBarState: SnackBarState.DANGER);
    }
  }

  /// check connectivity
  void checkConnectivity() {
    Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      if (result.first == ConnectivityResult.none) {
        Get.toNamed(Routes.CONNECTIVITY_SERVICE);
      }
    });
  }

  /// fetch banner images
  // Future<void> fetchBannerImages() async {
  //   try {
  //     final List<SliderImages>? images = await DashBoardProvider.sliderImages();
  //     if (images != null) {
  //       bannerImages(images);
  //       bannerImages.refresh();
  //     }
  //   } on DioException catch (e) {
  //     appSnackbar(
  //       message: e.response?.data['message'] ?? 'Oops! Something went wrong',
  //       snackBarState: SnackBarState.DANGER,
  //     );
  //     logE(e);
  //   }
  // }

  /// get crm details
  Future<void> getCRMDetails() async {
    try {
      final CRMModel? crm = await CrmProvider.getCRMDetails();
      if (crm != null) {
        crmDetails(crm);
        crmDetails.refresh();
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    }
  }

  /// fetch diamonds
  Future<void> fetchDiamonds() async {
    try {
      isLoading(true);
      final InventoryResponse inventory =
          await MarketplaceService.searchDiamonds(
        skip: 0,
        limit: 10,
        filterObject: {
          'filterObject': {
            'status': ['AVAILABLE'],
          }
        },
      );

      diamonds(inventory.diamonds ?? []);
      diamonds.refresh();
      isLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    }
  }

  /// featured diamonds
  Future<void> fetchFeaturedDiamonds() async {
    try {
      isFeaturedLoading(true);
      final InventoryResponse inventory =
          await MarketplaceService.searchDiamonds(
        skip: 0,
        limit: 10,
        isFeatured: true,
        filterObject: {
          'filterObject': {
            'status': ['AVAILABLE'],
          }
        },
      );
      featured(inventory.diamonds ?? <DiamondEntity>[]);
      featured.refresh();
      isFeaturedLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    }
  }

  /// new arrival diamonds
  Future<void> fetchNewArrivalDiamonds() async {
    try {
      newArrivalLoading(true);
      final InventoryResponse inventory =
          await MarketplaceService.searchDiamonds(
        skip: 0,
        limit: 10,
        isNewArrival: true,
        filterObject: {
          'filterObject': {
            'status': ['AVAILABLE'],
          }
        },
      );
      newArrival(inventory.diamonds ?? <DiamondEntity>[]);
      newArrival.refresh();
      newArrivalLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    }
  }

  /// buy request
  Future<void> fetchUserOrder({
    int skip = 0,
    int limit = 3,
  }) async {
    try {
      isLoadingOrders(true);
      final List<UserOrder> orders = await UserOrderProvider.userOrder(
          skip: skip, limit: limit, orderStatus: 'PENDING');
      userOrder(orders);
      // userOrder
      //   ..removeWhere((UserOrder order) =>
      //       (order.orderStatus == OrdersStatus.DELIVERED) ||
      //       (order.orderStatus == OrdersStatus.CANCELED))
      //   ..refresh();
      isLoadingOrders(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
      isLoadingOrders(false);
    }
  }

  /// remove unavailable diamond from cart
  void removeAllFromCart(List<DiamondEntity> diamonds) {
    for (final DiamondEntity diamond in diamonds) {
      CartItemHelper.removeDiamond(diamond);
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// get unread counts
  Future<void> getUnreadCounts() async {
    try {
      final DashboardMetrics? data = await DashBoardProvider.unreadCounts();
      if (data != null) {
        dashboardMetrics(data);
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data?['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logI(e);
    }
  }

  Future<void> _checkAndUpdatePrices() async {
    if (AppIsar.instance.store.rapPrices.count() < 3608) {
      await Future<void>.delayed(
        const Duration(milliseconds: 500),
        () {
          updatePriceList();
        },
      );
    }
  }

  Future<void> _getData() async {
    try {
      await _fetchAndSaveRapPrice();
    } on Exception catch (e, s) {
      logW(e);
      logW(s);
    }
  }

  Future<void> _fetchAndSaveRapPrice() async {
    stepper(1);
    final List<RapPrice> priceList = await RapnetService.getPriceList();
    if (priceList.isNotEmpty) {
      vibrate();
      stepper(2);
      RapPriceHelper.addRapPrices(priceList);

      await Future<void>.delayed(
        const Duration(milliseconds: 400),
        () {
          LocalStore.priceUpdatedAt(DateTime.now().toLocal().mmmDDyyyyHmmA);
          Get.back(result: true);
        },
      );
    } else {
      Get.back(result: false);
    }
  }

  Future<void> updatePriceList() async {
    unawaited(_getData());
    stepper(0);
    await Get.bottomSheet<bool?>(
      const UpdateRapView(),
      elevation: 0,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(4),
          topLeft: Radius.circular(4),
        ),
      ),
    ).then((bool? response) {
      if (response != null) {
        if (response) {
          appSnackbar(
            message: 'Price list updated',
            snackBarState: SnackBarState.SUCCESS,
          );
        } else {
          appSnackbar(
            message: 'Error updating price list, please try again.',
            snackBarState: SnackBarState.DANGER,
          );
        }
      }
    });
  }

  /// refresh user data
  Future<void> loadDashBoard() async {
    try {
      isLoadingDashboard(true);
      final UserEntity? user = await AuthProvider.getUserDetails();
      if (user != null) {
        await UserProvider.onLogin(
            user: user, userAuthToken: UserProvider.authToken ?? '');
      }
      userOrder.clear();
      diamonds.clear();
      featured.clear();
      newArrival.clear();
      inquiryData.clear();
      // bannerImages.clear();
      await getUnreadCounts();
      // await fetchBannerImages();
      await fetchUserOrder();
      await getCRMDetails();
      // await fetchDiamonds();
      // await fetchFeaturedDiamonds();
      await getUserInquiry();
      unawaited(getStocksWithAvailability());
      // await fetchNewArrivalDiamonds();
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoadingDashboard(false);
    }
  }

  /// gold price indicator
  bool isGoldPriceGoingDown() =>
      (dashboardMetrics().calculatedChangeAmount ?? 0).toString().contains('-');

  /// gold price with prefix
  String getGoldPriceWithPrefix() {
    if (isGoldPriceGoingDown()) {
      return '\$${dashboardMetrics().calculatedChangeAmount ?? 0} (${dashboardMetrics().goldPrices?.calculateXauChangePercent}%)';
    }
    return '+ \$${dashboardMetrics().calculatedChangeAmount ?? 0} (${dashboardMetrics().goldPrices?.calculateXauChangePercent}%)';
  }

  /// get total amount
  double getTotalItemsAmount() {
    final double diamondTotal =
        Get.find<CartItemService>().diamondSummary().totalFinalPrice;
    final double jewelleryTotal =
        Get.find<CartItemService>().jewellerySummary().totalFinalPrice;
    final double meleeTotal =
        Get.find<CartItemService>().meleeSummary().totalFinalPrice;
    return diamondTotal + jewelleryTotal + meleeTotal;
  }

  /// get item count
  int getTotalItemsCounts() {
    final int diamondTotal =
        Get.find<CartItemService>().diamondSummary().totalPcs;
    final int jewelleryTotal =
        Get.find<CartItemService>().jewellerySummary().totalPcs;
    final int meleeTotal = Get.find<CartItemService>().meleeSummary().totalPcs;
    return diamondTotal + jewelleryTotal + meleeTotal;
  }

//// Add your code here
}
