import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// side menu
class SideMenu extends StatelessWidget {
  /// side menu
  const SideMenu({super.key});

  @override
  Widget build(BuildContext context) => Drawer(
        backgroundColor: AppColors.kEAEFF4,
        surfaceTintColor: AppColors.kEAEFF4,
        shape: const RoundedRectangleBorder(),
        elevation: 0,
        width: 900.w,
        child: ListView(
          padding: EdgeInsets.zero,
          children: <Widget>[
            240.verticalSpace,
            _buildHeader(),
            80.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                60.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.side_menu_marketplace.tr,
                  icon: AppImages.marketPlace,
                  onTap: () => Get.toNamed(Routes.GET_FILTER_UPDATED),
                ),
                30.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.side_menu_my_cart.tr,
                  icon: AppImages.cartIcon,
                  onTap: () => Get.toNamed(Routes.CART_UPDATED),
                ),
                60.horizontalSpace,
              ],
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                60.horizontalSpace,
                _buildDrawerItem(
                  title: 'My Offers',
                  icon: AppImages.myOffer,
                  onTap: () => Get.toNamed(Routes.STOCK_OFFERS),
                ),
                30.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.side_menu_inquiries.tr,
                  icon: AppImages.inquiries,
                  onTap: () {
                    Get.toNamed(Routes.INQUIRIES);
                  },
                ),
                60.horizontalSpace,
              ],
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                60.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.side_menu_my_orders.tr,
                  icon: AppImages.order,
                  onTap: () => Get.toNamed(Routes.MY_ORDER),
                ),
                30.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.side_menu_buy_requests.tr,
                  icon: AppImages.buyRequest,
                  onTap: () => Get.toNamed(Routes.BUY_REQUEST),
                ),
                60.horizontalSpace,
              ],
            ),
            30.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                60.horizontalSpace,
                _buildDrawerItem(
                  title: LocaleKeys.whishlist_title.tr,
                  icon: AppImages.favourite,
                  onTap: () => Get.toNamed(Routes.WHISHLIST),
                ),
                30.horizontalSpace,
                _buildDrawerItem(
                  title: 'Returned Diamonds',
                  icon: AppImages.diamondBlack,
                  onTap: () => Get.toNamed(Routes.RETURNED_DIAMONDS),
                ),
                60.horizontalSpace,
              ],
            ),
            80.verticalSpace,
            FutureBuilder<PackageInfo>(
              future: PackageInfo.fromPlatform(),
              builder:
                  (BuildContext context, AsyncSnapshot<PackageInfo> snapshot) {
                switch (snapshot.connectionState) {
                  case ConnectionState.done:
                    return Align(
                      alignment: Alignment.bottomCenter,
                      child: Text(
                        'V : ${snapshot.data?.version ?? ''}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 40.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),
          ],
        ),
      );

  Column _buildHeader() => Column(
        children: <Widget>[
          Text(
            '${UserProvider.currentUser?.firstName ?? ''} '
            '${UserProvider.currentUser?.lastName ?? ''}',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w900,
              fontSize: 56.sp,
              color: AppColors.k101C28,
            ),
          ),
          40.verticalSpace,
          Text(
            '${UserProvider.currentUser?.email ?? ''}',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w500,
              fontSize: 40.sp,
              color: AppColors.k70777E,
            ),
          ),
          40.verticalSpace,
          AppButton.text(
            onPressed: () => Get
              ..back()
              ..toNamed(Routes.PROFILE),
            backgroundColor: AppColors.kffffff,
            padding: EdgeInsets.zero,
            buttonSize: Size(353.w, 100.h),
            buttonText: LocaleKeys.side_menu_my_account.tr,
            buttonTextStyle: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
            borderRadius: 500.r,
            borderWidth: 4.w,
            borderColor: AppColors.k101C28,
          ),
        ],
      );

  Expanded _buildDrawerItem({
    required String title,
    required Widget icon,
    required Function()? onTap,
  }) =>
      Expanded(
        child: GestureDetector(
          onTap: () {
            Get.back();
            onTap?.call();
          },
          child: Container(
            // width: 375.w,
            height: 350.h,
            decoration: BoxDecoration(
              color: AppColors.kffffff,
              borderRadius: BorderRadius.circular(40.r),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  height: 120.h,
                  width: 120.w,
                  padding: EdgeInsets.all(35.r),
                  decoration: const BoxDecoration(
                    color: AppColors.kBEFFFC,
                    shape: BoxShape.circle,
                  ),
                  child: icon,
                ),
                40.verticalSpace,
                Text(
                  title,
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.k101C28,
                  ),
                  textAlign: TextAlign.center,
                ).paddingSymmetric(horizontal: 10.w),
              ],
            ),
          ),
        ),
      );
}
