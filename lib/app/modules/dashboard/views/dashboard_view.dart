import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/inquiry/inquiry.dart';
import 'package:diamond_company_app/app/data/models/slider_images/slider_images.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/calculator/views/diamond_calculator_view.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/dashboard/views/side_menu.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/crm_widgets.dart';
import 'package:diamond_company_app/app/ui/components/horizontal_shimmer_list.dart';
import 'package:diamond_company_app/app/ui/components/launch_url.dart';
import 'package:diamond_company_app/app/ui/components/order_item.dart';
import 'package:diamond_company_app/app/ui/components/shadow_button.dart';
import 'package:diamond_company_app/app/ui/components/stock_item.dart';
import 'package:diamond_company_app/app/ui/components/stock_item_tile.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

///dashboard
class DashboardView extends GetView<DashboardController> {
  ///dashboard
  const DashboardView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Obx(
        () => Scaffold(
          key: controller.scaffoldKey,
          drawer: const SideMenu(),
          appBar: _buildAppBar(context),
          body: _buildBody(),
          // floatingActionButton: AnimatedSize(
          //   duration: const Duration(milliseconds: 300),
          //   child: controller.isLoadingDashboard()
          //       ? const SizedBox.shrink()
          //       : _buildFloatingActionButton(),
          // ),
        ),
      );

  Widget _buildFloatingActionButton() => StatefulBuilder(
      builder: (BuildContext context, void Function(void Function()) state) =>
          Visibility(
            visible: GetStorage().read('isFabVisible') ?? true,
            child: FloatingActionButton(
              onPressed: () {
                //showCustomerRelationBottomSheet();
                controller.scrollController
                    .animateTo(
                  controller.scrollController.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                )
                    .then((_) {
                  GetStorage().write('isFabVisible', false);
                  state(() {});
                });
              },
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(50.r),
                  topRight: Radius.circular(50.r),
                  bottomLeft: Radius.circular(50.r),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(50.r),
                  topRight: Radius.circular(50.r),
                  bottomLeft: Radius.circular(50.r),
                ),
                child: Container(
                  padding: REdgeInsets.all(7),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: <Color>[
                        AppColors.k7899B8,
                        AppColors.kE3E9F3,
                        AppColors.k8BA1B2,
                        AppColors.kE3E9F3,
                        AppColors.k7899B8,
                      ],
                      begin: Alignment.topRight,
                      end: Alignment.topLeft,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(42.r),
                      topRight: Radius.circular(42.r),
                      bottomLeft: Radius.circular(42.r),
                    ),
                    child: Container(
                      padding: REdgeInsets.all(36),
                      color: AppColors.kF1F1F1,
                      child: Image.asset(
                        AppImages.supportIconPath,
                        color: AppColors.k333333,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ));

  Widget _buildBody() => Obx(
        () => Stack(
          children: [
            RefreshIndicator(
              color: AppColors.k101C28,
              onRefresh: () => controller.loadDashBoard(),
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                controller: controller.scrollController,
                children: <Widget>[
                  controller.isLoadingDashboard()
                      ? const DashboardShimmer()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            _buildHeader(),
                            60.verticalSpace,
                            _buildGoldPriceTile(),
                            60.verticalSpace,
                            _buildDiamondCount(),
                            if (!(AuthProvider.userEntity().isVerified ??
                                true)) ...<Widget>[
                              60.verticalSpace,
                              _buildKYC(),
                            ],
                            _buildButtons(),
                            //if (controller.bannerImages.isNotEmpty) _buildSlider(),
                            30.verticalSpace,
                            if (controller.userOrder.isNotEmpty)
                              _buildHeaderTitle(
                                title: LocaleKeys.dashboard_on_going_orders.tr,
                                onTap: () => Get.toNamed(Routes.MY_ORDER),
                              ),
                            if (controller.userOrder.isNotEmpty)
                              _buildOnGoingOrderList(),
                            if (controller.userOrder.isNotEmpty)
                              30.verticalSpace,
                            // if (controller.featured.isNotEmpty)
                            //   _buildHeaderTitle(
                            //     title: 'FEATURED',
                            //     onTap: () => Get.toNamed(
                            //       Routes.VIEW_ALL,
                            //       arguments: {'is_featured': true},
                            //     ),
                            //   ),
                            // if (controller.featured.isNotEmpty)
                            //   _buildFeaturedItemList(),
                            // if (controller.featured.isNotEmpty) 30.verticalSpace,
                            if (controller.inquiryData.isNotEmpty)
                              _buildHeaderTitle(
                                title: LocaleKeys.dashboard_inquiries.tr,
                                onTap: () => Get.toNamed(Routes.INQUIRIES),
                              ),
                            if (controller.inquiryData.isNotEmpty)
                              _buildInquiriesItemList(),
                            // if (controller.newArrival.isNotEmpty) 30.verticalSpace,
                            // if (controller.newArrival.isNotEmpty)
                            //   _buildHeaderTitle(
                            //     title: 'NEW ARRIVAL',
                            //     onTap: () => Get.toNamed(
                            //       Routes.VIEW_ALL,
                            //       arguments: <String, bool>{
                            //         'new_arrival': true,
                            //       },
                            //     ),
                            //   ),
                            // if (controller.newArrival.isNotEmpty)
                            //   _buildNewArrivalItemList(),
                            70.verticalSpace,
                            CRMCard(crmModel: controller.crmDetails()),
                            270.verticalSpace,
                          ],
                        ),
                ],
              ),
            ),
            if (Get.find<CartItemService>().cartItems.isNotEmpty) _buildCart(),
          ],
        ),
      );

  Widget _buildCart() => Positioned(
        bottom: MediaQuery.of(Get.context!).padding.bottom + 60.h,
        left: 0,
        right: 0,
        child: ShadowButton(
          onTap: () => Get.toNamed(Routes.CART_UPDATED),
          height: 150.h,
          width: Get.width,
          buttonColor: AppColors.k101C28,
          shadowColor: AppColors.kBEFFFC,
          margin: EdgeInsets.only(
            left: 60.w,
            right: 60.w,
          ),
          padding: REdgeInsets.symmetric(horizontal: 40),
          buttonName: 'cart',
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              24.horizontalSpace,
              Text(
                'Cart',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w700,
                  fontSize: 45.sp,
                  color: AppColors.kffffff,
                ),
              ),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    'Items',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 30.sp,
                      color: AppColors.k9FA4A9,
                    ),
                  ),
                  20.horizontalSpace,
                  Text(
                    '${controller.getTotalItemsCounts()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.kffffff,
                    ),
                  ),
                ],
              ),
              32.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    'Total',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 30.sp,
                      color: AppColors.k9FA4A9,
                    ),
                  ),
                  20.horizontalSpace,
                  Text(
                    '${controller.getTotalItemsAmount()}'.toPrice(),
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w900,
                      fontSize: 35.sp,
                      color: AppColors.kffffff,
                    ),
                  ),
                ],
              ),
              48.horizontalSpace,
              SizedBox(
                height: 70.h,
                width: 70.w,
                child: IconButton(
                  onPressed: () => Get.toNamed(Routes.CART_UPDATED),
                  style: IconButton.styleFrom(
                    fixedSize: Size(70.w, 70.h),
                    padding: EdgeInsets.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  icon: SizedBox(
                    height: 70.h,
                    width: 70.w,
                    child: AppImages.cart,
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  Container _buildGoldPriceTile() => Container(
        // width: Get.width,
        height: 154.h,
        // padding: REdgeInsets.symmetric(horizontal: 60),
        margin: REdgeInsets.symmetric(horizontal: 60),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.all(
            Radius.circular(24.r),
          ),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.10),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
          image: DecorationImage(
            image: AppImages.gradientTileBg,
            fit: BoxFit.fill,
          ),
        ),
        child: Row(
          children: [
            10.horizontalSpace,
            Center(
              child: Container(
                width: 200.w,
                height: 200.h,
                padding: REdgeInsets.only(top: 25),
                child: FittedBox(
                  child: AppImages.gold,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            12.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Gold  (24k)',
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.kFCDB67,
                  ),
                ),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: (controller.dashboardMetrics().currentGoldRate ??
                                0.001)
                            .toString()
                            .toPrice(),
                        style: AppFontStyles.skolaSans(
                          fontSize: 48.sp,
                          fontWeight: FontWeight.w900,
                          color: AppColors.k101C28,
                        ),
                      ),
                      TextSpan(
                        text: ' /oz',
                        style: AppFontStyles.skolaSans(
                          fontSize: 42.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Spacer(),
            32.horizontalSpace,
            Row(
              children: [
                Text(
                  controller.getGoldPriceWithPrefix(),
                  style: AppFontStyles.skolaSans(
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w700,
                    color:
                        (controller.dashboardMetrics().calculatedChangeAmount ==
                                0)
                            ? AppColors.kffffff
                            : controller.isGoldPriceGoingDown()
                                ? AppColors.kff0000
                                : AppColors.k19DC98,
                  ),
                ),
                if (controller.dashboardMetrics().calculatedChangeAmount != 0)
                  Icon(
                    controller.isGoldPriceGoingDown()
                        ? Icons.arrow_drop_down_outlined
                        : Icons.arrow_drop_up_outlined,
                    color: controller.isGoldPriceGoingDown()
                        ? AppColors.kff0000
                        : AppColors.k19DC98,
                    size: 50.sp,
                  ),
              ],
            ),
            20.horizontalSpace,
          ],
        ),
      );

  Container _buildDiamondCount() => Container(
        width: Get.width,
        margin: REdgeInsets.symmetric(horizontal: 60),
        padding: REdgeInsets.all(38),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.10),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildCountIndicator(
                title: 'Diamonds Listed',
                count: controller.dashboardMetrics().diamondsCount ?? 0,
                onTap: () => Get.toNamed(Routes.GET_FILTER_UPDATED),
              ),
            ),
            Expanded(
              child: _buildCountIndicator(
                title: 'Ongoing offers',
                count: controller.dashboardMetrics().pendingOffersCount ?? 0,
                onTap: () {},
              ),
            ),
          ],
        ),
      );

  Widget _buildCountIndicator({
    required int count,
    required String title,
    Function()? onTap,
  }) =>
      InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: REdgeInsets.symmetric(
                horizontal: 32,
                vertical: 15,
              ),
              decoration: BoxDecoration(
                color: AppColors.kE6F3FF,
                borderRadius: BorderRadius.all(
                  Radius.circular(500.r),
                ),
              ),
              child: Text(
                '$count',
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k1A47E8,
                ),
              ),
            ),
            24.verticalSpace,
            Text(
              title,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );

  Widget _buildButtons() => Column(
        children: [
          60.verticalSpace,
          Row(
            children: <Widget>[
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(
                    Routes.MELEE,
                    arguments: {'is_round_melle': true},
                  ),
                  title: 'Round Melee',
                  icon: AppImages.melee,
                  count: 0,
                  color: AppColors.kFCF6E5,
                ),
              ),
              32.horizontalSpace,
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(Routes.MELEE),
                  title: 'Fancy Melee Layouts',
                  icon: AppImages.fancyMelee,
                  count: 0,
                  color: AppColors.kF4F0FB,
                ),
              ),
            ],
          ),
          32.verticalSpace,
          Row(
            children: <Widget>[
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(Routes.JEWELLERY_CATEGORY),
                  title: 'Diamond Jewelry',
                  icon: AppImages.diamondJewellery,
                  count: 0,
                  color: AppColors.kFBEEF5,
                ),
              ),
              32.horizontalSpace,
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(Routes.GET_FILTER_UPDATED),
                  title: 'Lab Grown Diamonds',
                  icon: AppImages.labGrown,
                  count: 0,
                  color: AppColors.kE9F4F2,
                ),
              ),
            ],
          ),
          32.verticalSpace,
          Row(
            children: <Widget>[
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(Routes.STOCK_OFFERS),
                  title: 'My Offers',
                  icon: AppImages.myOffers,
                  color: AppColors.kE8F2FA,
                  count:
                      controller.dashboardMetrics().userPendingOffersCount ?? 0,
                ),
              ),
              32.horizontalSpace,
              Expanded(
                child: _buildNewButton(
                  onTap: () => Get.toNamed(Routes.CART_UPDATED),
                  title: LocaleKeys.dashboard_my_cart.tr,
                  icon: AppImages.cartOrange,
                  count: Get.find<CartItemService>().cartItems.length,
                  color: AppColors.kFDF2EA,
                ),
              ),
            ],
          ),
          60.verticalSpace,
        ],
      ).paddingSymmetric(horizontal: 60.w);

  Container _buildKYC() => Container(
        width: Get.width,
        padding: REdgeInsets.all(60),
        margin: REdgeInsets.symmetric(horizontal: 60),
        decoration: BoxDecoration(
          color: (UserProvider.currentUser?.documentName ?? '').isNotEmpty
              ? AppColors.kBEFFFC
              : AppColors.kFFE4E4,
          borderRadius: BorderRadius.all(Radius.circular(50.r)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              (UserProvider.currentUser?.documentName ?? '').isNotEmpty
                  ? 'KYC process under progress'
                  : 'KYC Rejected',
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
            30.verticalSpace,
            Text(
              (UserProvider.currentUser?.documentName ?? '').isNotEmpty
                  ? 'Access to Buy Requests, Orders, Favorites, and Cart restricted. Confirmation needed within 24 hours. Thank you.'
                  : UserProvider.currentUser?.kycRejectReason ??
                      'Access to Buy Requests, Orders, Favorites, and Cart restricted. Confirmation needed within 24 hours. Thank you.',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
            if (!(UserProvider.currentUser?.isVerified ?? false) &&
                (UserProvider.currentUser?.documentName ?? '').isEmpty)
              40.verticalSpace,
            if (!(UserProvider.currentUser?.isVerified ?? false) &&
                (UserProvider.currentUser?.documentName ?? '').isEmpty)
              InkWell(
                onTap: () =>
                    Get.toNamed(Routes.USA_PATRIOT_ACT, arguments: true),
                child: Text(
                  'RESUBMIT KYC'.toUpperCase(),
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 35.sp,
                    color: AppColors.kff0000,
                  ),
                ),
              ),
          ],
        ),
      );

  // Container _buildInquiriesItemList() => Container(
  //       width: Get.width,
  //       height: 300.h,
  //       child: ListView.separated(
  //         clipBehavior: Clip.none,
  //         scrollDirection: Axis.horizontal,
  //         physics: const BouncingScrollPhysics(),
  //         padding: REdgeInsets.symmetric(horizontal: 60),
  //         itemBuilder: (BuildContext context, int index) => StockItemTile(
  //           inquiryData: controller.inquiryData[index],
  //         ),
  //         separatorBuilder: (BuildContext context, int index) =>
  //             SizedBox(width: 30.w),
  //         itemCount: controller.inquiryData.length,
  //       ),
  //     );

  Widget _buildInquiriesItemList() => SingleChildScrollView(
        clipBehavior: Clip.none,
        physics: const BouncingScrollPhysics(),
        padding: REdgeInsets.symmetric(vertical: 60),
        child: Column(
          children: controller.inquiryData
              .asMap()
              .entries
              .map(
                (MapEntry<int, InquiryData> map) => StockItemTile(
                  inquiryData: map.value,
                  width: Get.width,
                  fromInquiries: true,
                ).paddingOnly(
                  left: 60.w,
                  right: 60.w,
                  bottom: 30.h,
                ),
              )
              .toList(),
        ),
      );

  Widget _buildFeaturedItemList() => SingleChildScrollView(
        clipBehavior: Clip.none,
        scrollDirection: Axis.horizontal,
        padding: REdgeInsets.symmetric(vertical: 40),
        physics: const BouncingScrollPhysics(),
        child: Row(
          children: controller.featured
              .asMap()
              .entries
              .map((MapEntry<int, DiamondEntity> map) => StockItem(
                    width: 915.w,
                    onTap: () {
                      Get.toNamed(
                        Routes.DIAMOND_DETAILS,
                        arguments: DiamondArgs(
                          diamond: map.value,
                          id: map.value.id ?? '',
                        ),
                      );
                    },
                    diamond: map.value,
                    isAddedToCart: CartItemHelper.isDiamondAdded(map.value).obs,
                  ).paddingOnly(right: 30.w))
              .toList(),
        ).paddingOnly(left: 60.w, right: 30.w),
      );

  Widget _buildNewArrivalItemList() => SingleChildScrollView(
        clipBehavior: Clip.none,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        padding: REdgeInsets.symmetric(vertical: 40),
        child: Row(
          children: controller.newArrival
              .asMap()
              .entries
              .map((MapEntry<int, DiamondEntity> map) => StockItem(
                    width: 915.w,
                    onTap: () {
                      Get.toNamed(
                        Routes.DIAMOND_DETAILS,
                        arguments: DiamondArgs(
                          diamond: map.value,
                          id: map.value.id ?? '',
                        ),
                      );
                    },
                    diamond: map.value,
                    isAddedToCart: CartItemHelper.isDiamondAdded(map.value).obs,
                  ).paddingOnly(right: 30.w))
              .toList(),
        ).paddingOnly(left: 60.w, right: 30.w),
      );

  Widget _buildOnGoingOrderList() => SingleChildScrollView(
        clipBehavior: Clip.none,
        padding: REdgeInsets.symmetric(vertical: 60),
        physics: const BouncingScrollPhysics(),
        child: Column(
          children: controller.userOrder
              .map(
                (UserOrder userOrder) => OrderItem(
                  userOrder: userOrder,
                  width: Get.width,
                  onTap: () => Get.toNamed(
                    Routes.MY_ORDER_DETAIL,
                    arguments: userOrder,
                  ),
                ).paddingOnly(right: 60.w, left: 60.w, bottom: 30.h),
              )
              .toList(),
        ),
      );

  Widget _buildHeaderTitle({
    required String title,
    required Function() onTap,
  }) =>
      Container(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(
              title,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
              ),
            ),
            AppButton.text(
              onPressed: onTap,
              backgroundColor: AppColors.kffffff,
              padding: EdgeInsets.zero,
              buttonSize: Size(256.w, 80.h),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              buttonText: LocaleKeys.dashboard_view_all.tr,
              buttonTextStyle: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
              borderRadius: 500.r,
              borderWidth: 4.w,
              borderColor: AppColors.k101C28,
            ),
          ],
        ),
      );

  Widget _buildSlider() => Obx(
        () => Column(children: [
          SizedBox(
            height: 350.h,
            width: Get.width,
            child: CarouselSlider(
              items: controller.bannerImages
                  .map((SliderImages banner) => Padding(
                        padding: REdgeInsets.symmetric(horizontal: 60),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24.r),
                          child: GestureDetector(
                            onTap: () => openURL(url: banner.deepLink ?? ''),
                            child: CachedNetworkImage(
                              imageUrl: banner.bannerImageUrl.toString(),
                              width: Get.width,
                              height: 350.h,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ))
                  .toList(),
              carouselController: controller.carouselController,
              options: CarouselOptions(
                  autoPlay: true,
                  enlargeCenterPage: true,
                  aspectRatio: 2,
                  viewportFraction: 1,
                  onPageChanged:
                      (int index, CarouselPageChangedReason reason) =>
                          controller.current(index)),
            ),
          ),
          15.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List<Widget>.generate(
              3,
              (int index) => GestureDetector(
                onTap: () {
                  controller.carouselController.animateToPage(index);
                  controller.current(index);
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 12,
                  height: 12,
                  margin: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 4,
                  ),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.kEAEFF4,
                    border: Border.all(
                      width: 4.w,
                      color: controller.current() == index
                          ? AppColors.k1A47E8
                          : AppColors.kEAEFF4,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ]),
      );

  Container _buildHeader() => Container(
        color: AppColors.kF1F1F1,
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: Column(
          children: <Widget>[
            60.verticalSpace,
            _buildStockSearch(),
            60.verticalSpace,
            _buildBrowseMarketPlace(),
            // _buildBrowseMarketPlace(),
            60.verticalSpace,
          ],
        ),
      );

  Container _buildStockSearch() => Container(
        //height: 150.h,
        width: Get.width,
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(500.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.08),
              offset: Offset(0, 15.h),
              blurRadius: 40.r,
            ),
          ],
        ),
        child: AppTextFormField(
          name: 'q',
          textInputAction: TextInputAction.send,
          focusNode: controller.stockIdFocusNode,
          cursorColor: AppColors.k333333,
          fillColor: AppColors.kffffff,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(500.r),
            borderSide: BorderSide(
              width: 1.w,
              color: AppColors.kB6C3D0.withOpacity(0.3),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(500.r),
            borderSide: BorderSide(
              width: 1.w,
              color: AppColors.kB6C3D0.withOpacity(0.3),
            ),
          ),
          autofillHints: <String>[CustomAutofillHints.search],
          onChange: (String? value) {
            controller.appliedFilter().certificationId = value?.trim() ?? '';
            controller.appliedFilter.refresh();
          },
          onSubmit: (String? value) {
            if (value?.isNotEmpty ?? false) {
              controller.appliedFilter().certificationId = value?.trim() ?? '';
              controller.onFilter();
            }
          },
          prefixIconConstraints: BoxConstraints(minWidth: 140.w),
          prefixIcon: const CircleAvatar(
            backgroundColor: AppColors.kffffff,
            child: Icon(
              Icons.manage_search_outlined,
              color: AppColors.k101C28,
            ),
          ),
          hintText: 'Certificate# / SKU',
          labelText: 'Certificate# / SKU',
          floatingLabelBehavior: FloatingLabelBehavior.never,
          labelTextStyle: AppFontStyles.skolaSans(
            fontSize: 48.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
          hintTextStyle: AppFontStyles.skolaSans(
            fontSize: 48.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
          style: AppFontStyles.skolaSans(
            fontSize: 48.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
          textCapitalization: TextCapitalization.characters,
          controller: controller.stockIdController,
          suffixIcon: IconButton(
            onPressed: () {
              // controller.stockIdController.clear();
              // controller.appliedFilter().certificationId = '';
              // controller.appliedFilter.refresh();
              if (controller.stockIdController.text.isNotEmpty) {
                Get.focusScope?.unfocus();
                controller.appliedFilter().certificationId =
                    controller.stockIdController.text.trim();
                controller.onFilter();
              } else {
                controller.stockIdFocusNode.requestFocus();
              }
            },
            icon: const Icon(
              Icons.send,
              color: AppColors.k101C28,
            ),
          ),
        ),
      );

  Widget _buildBrowseMarketPlace() => Row(
        children: [
          Expanded(
            child: Container(
              //height: 150.h,
              width: Get.width,
              decoration: BoxDecoration(
                color: AppColors.kffffff,
                borderRadius: BorderRadius.circular(500.r),
                boxShadow: <BoxShadow>[
                  BoxShadow(
                    color: AppColors.k000000.withOpacity(0.08),
                    offset: Offset(0, 15.h),
                    blurRadius: 40.r,
                  ),
                ],
              ),
              child: AppTextFormField(
                name: 'browse',
                onTap: () => Get.toNamed(Routes.GET_FILTER_UPDATED),
                textInputAction: TextInputAction.send,
                cursorColor: AppColors.k333333,
                fillColor: AppColors.kffffff,
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(500.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.kB6C3D0.withOpacity(0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(500.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.kB6C3D0.withOpacity(0.3),
                  ),
                ),
                autofillHints: <String>[CustomAutofillHints.search],
                onChange: (String? value) {
                  controller.appliedFilter().certificationId =
                      value?.trim() ?? '';
                  controller.appliedFilter.refresh();
                },
                onSubmit: (String? value) {
                  if (value?.isNotEmpty ?? false) {
                    controller.appliedFilter().certificationId =
                        value?.trim() ?? '';
                    controller.onFilter();
                  }
                },
                prefixIconConstraints: BoxConstraints(minWidth: 140.w),
                hintText: '${LocaleKeys.dashboard_browse_market_place.tr}',
                hintTextStyle: AppFontStyles.skolaSans(
                  fontSize: 48.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k101C28,
                ),
                labelText: '${LocaleKeys.dashboard_browse_market_place.tr}',
                labelTextStyle: AppFontStyles.skolaSans(
                  fontSize: 48.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k101C28,
                ),
                floatingLabelBehavior: FloatingLabelBehavior.never,
                prefixIcon: CircleAvatar(
                  backgroundColor: AppColors.kffffff,
                  child: SizedBox(
                    width: 55.w,
                    height: 70.h,
                    child: AppImages.diamondIcon,
                  ),
                ),
                readOnly: true,
              ),
            ),
          ),
          60.horizontalSpace,
          IconButton(
            onPressed: () {
              /// If we want to open with dialog
              // Get.dialog(
              //   barrierColor: Colors.black.withOpacity(0.7),
              //   Container(
              //     margin: EdgeInsets.symmetric(
              //       horizontal: 40.w,
              //     ),
              //     child: Dialog(
              //       surfaceTintColor: Colors.white,
              //       shape: RoundedRectangleBorder(
              //         borderRadius: BorderRadius.circular(24.r),
              //       ),
              //       insetPadding: REdgeInsets.only(
              //         left: 60.w,
              //         right: 60.w,
              //       ),
              //       backgroundColor: Colors.white,
              //       child: DiamondCalculator(
              //         viewTag:
              //             'dashboard_${DateTime.now().millisecondsSinceEpoch}',
              //       ),
              //     ),
              //   ),
              // );

              Get.bottomSheet<void>(
                SingleChildScrollView(
                  child: DiamondCalculator(
                    viewTag:
                        'dashboard_${DateTime.now().millisecondsSinceEpoch}',
                  ),
                ),
                backgroundColor: AppColors.kffffff,
                isScrollControlled: true,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24.r),
                ),
              );
            },
            padding: EdgeInsets.zero,
            style: const ButtonStyle(
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            icon: SizedBox(
              height: 80.h,
              width: 80.w,
              child: AppImages.calcIcon,
            ),
          ),
        ],
      );

  // Widget _buildBrowseMarketPlace() => Row(
  //       children: <Widget>[
  //         Expanded(
  //           child: Container(
  //             height: 150.h,
  //             decoration: BoxDecoration(
  //               color: AppColors.k1E2E39,
  //               borderRadius: BorderRadius.circular(500.r),
  //             ),
  //             child: AppButton.custom(
  //               onPressed: () {
  //                 //Get.toNamed(Routes.GET_FILTER);
  //                 Get.toNamed(Routes.GET_FILTER_UPDATED);
  //               },
  //               padding: REdgeInsets.symmetric(horizontal: 70),
  //               backgroundColor: AppColors.k1E2E39,
  //               borderRadius: 500.r,
  //               borderWidth: 0.w,
  //               borderColor: AppColors.k1E2E39,
  //               buttonSize: Size(Get.width, 150.h),
  //               child: Row(
  //                 children: <Widget>[
  //                   SizedBox(
  //                     width: 63.w,
  //                     height: 50.h,
  //                     child: AppImages.diamondIcon,
  //                   ),
  //                   30.horizontalSpace,
  //                   Text(
  //                     '${LocaleKeys.dashboard_browse_market_place.tr}',
  //                     style: AppFontStyles.skolaSans(
  //                       fontSize: 48.sp,
  //                       fontWeight: FontWeight.w500,
  //                       color: AppColors.kffffff,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //         60.horizontalSpace,
  //         IconButton(
  //           onPressed: () {
  //             /// If we want to open with dialog
  //             // Get.dialog(
  //             //   barrierColor: Colors.black.withOpacity(0.7),
  //             //   Container(
  //             //     margin: EdgeInsets.symmetric(
  //             //       horizontal: 40.w,
  //             //     ),
  //             //     child: Dialog(
  //             //       surfaceTintColor: Colors.white,
  //             //       shape: RoundedRectangleBorder(
  //             //         borderRadius: BorderRadius.circular(24.r),
  //             //       ),
  //             //       insetPadding: REdgeInsets.only(
  //             //         left: 60.w,
  //             //         right: 60.w,
  //             //       ),
  //             //       backgroundColor: Colors.white,
  //             //       child: DiamondCalculator(
  //             //         viewTag:
  //             //             'dashboard_${DateTime.now().millisecondsSinceEpoch}',
  //             //       ),
  //             //     ),
  //             //   ),
  //             // );
  //
  //             Get.bottomSheet<void>(
  //               SingleChildScrollView(
  //                 child: DiamondCalculator(
  //                   viewTag:
  //                       'dashboard_${DateTime.now().millisecondsSinceEpoch}',
  //                 ),
  //               ),
  //               backgroundColor: AppColors.kffffff,
  //               isScrollControlled: true,
  //               shape: RoundedRectangleBorder(
  //                 borderRadius: BorderRadius.circular(24.r),
  //               ),
  //             );
  //           },
  //           padding: EdgeInsets.zero,
  //           style: const ButtonStyle(
  //             tapTargetSize: MaterialTapTargetSize.shrinkWrap,
  //           ),
  //           icon: SizedBox(
  //             height: 80.h,
  //             width: 80.w,
  //             child: AppImages.calcIcon,
  //           ),
  //         )
  //       ],
  //     );

  Widget _buildNewButton({
    required Function()? onTap,
    required String title,
    required Widget icon,
    required Color? color,
    int? count,
  }) =>
      GestureDetector(
        onTap: onTap,
        child: Container(
          width: 487.w,
          height: 176.h,
          decoration: BoxDecoration(
            color: color ?? AppColors.kEFE7FF,
            borderRadius: BorderRadius.all(
              Radius.circular(24.r),
            ),
          ),
          child: Row(
            children: [
              40.horizontalSpace,
              SizedBox(
                height: 125.h,
                width: 115.w,
                child: Stack(
                  children: [
                    Positioned(
                      bottom: 0,
                      top: 0,
                      child: SizedBox(
                        child: icon,
                        height: 80.h,
                        width: 80.w,
                      ),
                    ),
                    if ((count ?? 0) > 0)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          height: 60.h,
                          width: 60.w,
                          decoration: const BoxDecoration(
                            color: AppColors.k1A47E8,
                            shape: BoxShape.circle,
                          ),
                          //padding: REdgeInsets.all(8),
                          constraints: BoxConstraints(
                            minWidth: 60.w,
                            minHeight: 60.h,
                          ),
                          child: Center(
                            child: Text(
                              (count ?? 0) > 99 ? '9+' : '${count ?? 0}',
                              style: AppFontStyles.skolaSans(
                                fontSize: 35.sp,
                                fontWeight: FontWeight.w500,
                                color: AppColors.kffffff,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              8.horizontalSpace,
              Expanded(
                child: Text(
                  title,
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.k101C28,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
              SizedBox(
                height: 35.h,
                width: 25.w,
                child: AppImages.arrowForward,
              ),
              40.horizontalSpace,
            ],
          ),
        ),
      );

  ShadowButton _buildShadowedButton({
    required Function()? onTap,
    required String title,
    required Widget icon,
    int? count,
  }) =>
      ShadowButton(
        onTap: onTap,
        height: 150.h,
        width: 482.w,
        buttonColor: AppColors.kffffff,
        buttonName: title,
        borderSide: BorderSide(
          width: 2.w,
          color: AppColors.kCFCFCF,
        ),
        borderRadius: BorderRadius.all(Radius.circular(24.r)),
        child: Row(
          children: <Widget>[
            24.horizontalSpace, // 40 default
            SizedBox(
              height: 80.h,
              width: 80.w,
              child: icon,
            ),
            10.horizontalSpace, // 24 default
            Text(
              title,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
            const Spacer(),
            if ((count ?? 0) > 0)
              Container(
                height: 60.h,
                width: 60.w,
                decoration: const BoxDecoration(
                  color: AppColors.k1A47E8,
                  shape: BoxShape.circle,
                ),
                //padding: REdgeInsets.all(8),
                constraints: BoxConstraints(
                  minWidth: 60.w,
                  minHeight: 60.h,
                ),
                child: Center(
                  child: Text(
                    (count ?? 0) > 99 ? '9+' : '${count ?? 0}',
                    style: AppFontStyles.skolaSans(
                      fontSize: 35.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.kffffff,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            if ((count ?? 0) > 0) 24.horizontalSpace,
          ],
        ),
      );

  AppBar _buildAppBar(BuildContext context) => AppBar(
        surfaceTintColor: AppColors.kF1F1F1,
        clipBehavior: Clip.none,
        leading: _buildAppbarButton(
          onPressed: () => controller.scaffoldKey.currentState?.openDrawer(),
          icon: AppImages.drawer,
        ),
        title: SizedBox(
          height: 100.h,
          width: 308.w,
          child: AppImages.diamondCompany,
        ),
        titleSpacing: 10,
        backgroundColor: AppColors.kF1F1F1,
        actions: <Widget>[
          Badge(
            backgroundColor: AppColors.k1A47E8,
            alignment: Alignment.topRight,
            isLabelVisible:
                (controller.dashboardMetrics().unreadNotificationCount ?? 0) >
                    0,
            label: Center(
              child: Text(
                '${controller.dashboardMetrics().unreadNotificationCount ?? 0}',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  color: AppColors.kffffff,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            offset: const Offset(-5, 3),
            child: _buildAppbarButton(
              onPressed: () => Get.toNamed(Routes.NOTIFICATION),
              icon: AppImages.bell,
            ),
          ),
          Badge(
            backgroundColor: AppColors.k1A47E8,
            alignment: Alignment.topRight,
            label: Text(
              Get.find<CartItemService>().cartItems.length.toString(),
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                color: AppColors.kffffff,
              ),
              textAlign: TextAlign.center,
            ),
            isLabelVisible: Get.find<CartItemService>().cartItems.isNotEmpty,
            offset: const Offset(-5, 3),
            child: _buildAppbarButton(
              onPressed: () => Get.toNamed(Routes.CART_UPDATED),
              icon: AppImages.myCartOutline,
            ),
          ),
          // _buildAppbarButton(
          //   onPressed: () => Get.toNamed(Routes.MY_ACCOUNT),
          //   icon: AppImages.person,
          // ),
        ],
      );

  IconButton _buildAppbarButton({
    required void Function()? onPressed,
    required Widget icon,
  }) =>
      IconButton(
        onPressed: onPressed,
        icon: Container(
          height: 120.h,
          width: 120.w,
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            shape: BoxShape.circle,
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.08),
                offset: Offset(0, 15.h),
                blurRadius: 40.r,
              ),
            ],
          ),
          child: CircleAvatar(
            backgroundColor: AppColors.kffffff,
            child: SizedBox(
              width: 60.w,
              height: 42.h,
              child: icon,
            ),
          ),
        ),
      );
}
