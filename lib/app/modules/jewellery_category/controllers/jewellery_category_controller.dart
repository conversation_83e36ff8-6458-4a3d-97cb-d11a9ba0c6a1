import 'dart:async';

import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/jewellery_category/jewellery_category.dart';
import 'package:diamond_company_app/app/providers/jewellery_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// JewelleryCategoryController
class JewelleryCategoryController extends GetxController {
  /// is loading
  RxBool isLoading = false.obs;

  /// jewellery categories
  RxList<JewelleryCategory> jewelleryCategories = <JewelleryCategory>[].obs;

  /// isLoadMore
  RxBool isLoadMore = false.obs;

  /// limit
  int limit = 10;

  /// limit
  int skip = 0;

  /// scroll controller
  final ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();

    scrollController.addListener(_scrollListener);

    fetchJewelleryCategories();
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      loadMoreData();
    }
  }

  /// load more
  Widget loadMoreData() {
    if (isLoadMore()) {
      if (!isLoading()) {
        fetchJewelleryCategories(skip: skip, limit: limit);
      }
    }
    return const SizedBox.shrink();
  }

  /// jewellery categories
  Future<void> fetchJewelleryCategories({int? skip, int? limit}) async {
    try {
      if ((skip ?? 0) == 0) {
        this.skip = 0;
        isLoadMore(true);
        isLoading(true);
        jewelleryCategories.clear();
      }

      final List<JewelleryCategory> categories =
          await JewelleryProvider.getJewelleryCategories(
        limit: limit ?? this.limit,
        skip: skip ?? 0,
      );

      if (categories.isEmpty || categories.length < this.limit) {
        isLoadMore(false);
      } else {
        this.skip = this.skip + this.limit;
      }
      jewelleryCategories
        ..addAll(categories)
        ..refresh();
      isLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
