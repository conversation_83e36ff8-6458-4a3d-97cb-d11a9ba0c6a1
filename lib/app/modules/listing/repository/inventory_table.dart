import 'package:diamond_company_app/app/data/config/app_colors.dart';
//import 'package:diamond_company_app/app/modules/core/extensions/num_ext.dart';
//import 'package:diamond_company_app/app/modules/core/models/value_item.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

import '../../../ui/components/app_text_style.dart';

class InventoryTable {
  static List<GridColumn> gridColumns = [
    GridColumn(
      width: 146,
      columnName: 'Media',
      label: _buildLabel(
        'Media',
        alignment: Alignment.center,
      ),
    ),
    GridColumn(
      maximumWidth: 140,
      minimumWidth: 100,
      columnName: 'Stock #',
      label: _buildLabel(
        'Stock #',
        // tooltip: Column(
        //   mainAxisSize: MainAxisSize.min,
        //   mainAxisAlignment: MainAxisAlignment.start,
        //   crossAxisAlignment: CrossAxisAlignment.start,
        //   children: [
        //     4.verticalSpace,
        //     _buildStatusRow(
        //       color: AppColors.kDEE5EC,
        //       label: 'Lab Grown',
        //     ),
        //     4.verticalSpace,
        //     _buildStatusRow(
        //       color: AppColors.k296198,
        //       label: 'Natural',
        //     ),
        //     4.verticalSpace,
        //   ],
        // ),
      ),
      allowEditing: false,
    ),
    GridColumn(
      width: 64,
      columnName: 'Status',
      label: _buildLabel(
        'Status',
        alignment: Alignment.center,
        /*tooltip: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            4.verticalSpace,
            _buildStatusRow(
              color: AppColors.success,
              label: 'Available',
            ),
            4.verticalSpace,
            _buildStatusRow(
              color: AppColors.warning,
              label: 'Hold',
            ),
            4.verticalSpace,
            _buildStatusRow(
              color: AppColors.error,
              label: 'Memo',
            ),
            4.verticalSpace,
          ],
        ),*/
      ),
      allowEditing: false,
    ),
    GridColumn(
      width: 100,
      columnName: 'Shape',
      label: _buildLabel('Shape'),
      allowEditing: false,
    ),
    GridColumn(
      width: 70,
      columnName: 'Weight',
      label: _buildLabel('Weight'),
    ),
    GridColumn(
      width: 70,
      columnName: 'Color',
      label: _buildLabel('Color'),
      allowEditing: false,
    ),
    GridColumn(
      width: 80,
      columnName: 'Clarity',
      label: _buildLabel('Clarity'),
      allowEditing: false,
    ),
    // GridColumn(
    //   width: 90,
    //   columnName: 'Discount',
    //   label: _buildLabel('Disc.'),
    //   allowEditing: false,
    // ),
    GridColumn(
      maximumWidth: 140,
      minimumWidth: 80,
      columnName: 'Price Per Carat',
      label: _buildLabel('\$/Ct'),
      allowEditing: false,
    ),
    GridColumn(
      maximumWidth: 140,
      minimumWidth: 100,
      columnName: 'Final Price',
      label: _buildLabel('Price'),
      allowEditing: false,
    ),
    GridColumn(
      width: 60,
      columnName: 'Cut',
      label: _buildLabel('Cut'),
      allowEditing: false,
    ),
    GridColumn(
      width: 60,
      columnName: 'Pol',
      label: _buildLabel('Pol'),
      allowEditing: false,
    ),
    GridColumn(
      width: 60,
      columnName: 'Symm',
      label: _buildLabel('Sym'),
      allowEditing: false,
    ),
    GridColumn(
      width: 70,
      columnName: 'Fluo',
      label: _buildLabel('Fluo.'),
      allowEditing: false,
    ),
    GridColumn(
      width: 70,
      columnName: 'Lab',
      label: _buildLabel('Lab'),
    ),
    GridColumn(
      width: 120,
      columnName: 'Certificate ID',
      label: _buildLabel('Certificate #'),
    ),
    GridColumn(
      columnName: 'Measurements Length',
      label: _buildLabel('Measurements'),
      width: 150,
    ),
    // GridColumn(
    //   columnName: 'Measurements Width',
    //   label: _buildLabel('Width'),
    //   width: 70,
    // ),
    // GridColumn(
    //   columnName: 'Measurements Depth',
    //   label: _buildLabel('Height'),
    //   width: 70,
    // ),
    GridColumn(
      columnName: 'Ratio',
      label: _buildLabel('Ratio'),
      width: 70,
    ),
    GridColumn(
      columnName: 'Table %',
      label: _buildLabel('Table %'),
    ),
    GridColumn(
      columnName: 'Depth %',
      label: _buildLabel('Depth %'),
    ),
    GridColumn(
      columnName: 'fancy color main body',
      label: _buildLabel('F. Color'),
    ),
    GridColumn(
      columnName: 'Fancy Color Intensity',
      label: _buildLabel('F. Intensity'),
    ),
    GridColumn(
      columnName: 'Fancy Color Overtone',
      label: _buildLabel('F. Overtone'),
    ),
    GridColumn(
      columnName: 'stone location country',
      label: _buildLabel('Location'),
      allowEditing: false,
    ),
    GridColumn(
      columnName: 'Growth Type',
      label: _buildLabel('Type'),
      width: 110,
    ),
  ];

  static Row _buildStatusRow({
    required Color color,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        8.horizontalSpace,
        Text(
          label,
          style: AppFontStyles.skolaSans(
            color: AppColors.kFAFAFA,
          ),
        ),
      ],
    );
  }

  static Container _buildLabel(
    String label, {
    Alignment alignment = Alignment.centerLeft,
    Widget? tooltip,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      alignment: alignment,
      child: tooltip != null
          ? Tooltip(
              preferBelow: true,
              decoration: const BoxDecoration(
                color: AppColors.k141414,
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              richMessage: TextSpan(
                children: [
                  WidgetSpan(
                    child: tooltip,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.bold,
                      color: AppColors.k141414,
                    ),
                  ),
                  8.horizontalSpace,
                  const Icon(
                    FluentIcons.info_48_regular,
                    size: 16,
                    color: Colors.black54,
                  ),
                ],
              ),
            )
          : Text(
              label,
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.bold,
                color: AppColors.k141414,
              ),
            ),
    );
  }

  static List<String> whiteColors = const [
    '-',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z'
  ];

  static List<String> fluoColors = const [
    '-',
    'none',
    'blue',
    'yellow',
    'white',
    'green',
    'red',
    'orange',
  ];

  // static List<ValueItem<String>> enhancementOptions = [
  //   const ValueItem<String>(
  //     label: 'HPHT Processed',
  //     value: 'hpht',
  //   ),
  //   const ValueItem<String>(
  //     label: 'Clarity Enhanced or Fracture Filled',
  //     value: 'ce',
  //   ),
  //   const ValueItem<String>(
  //     label: 'Laser Drilled',
  //     value: 'ld',
  //   ),
  //   const ValueItem<String>(
  //     label: 'Irradiated',
  //     value: 'irr',
  //   ),
  // ];
}
