import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/listing/controllers/marketplace_controller.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../ui/components/app_text_style.dart';
import '../../../../utils/app_utils.dart';

class LinkCell extends StatelessWidget {
  const LinkCell({
    super.key,
    required this.rowColumnIndex,
    required this.value,
    this.isFirst = false,
    this.isLast = false,
    this.isValueEmpty = false,
    required this.marketplaceController,
  });

  final MarketplaceController marketplaceController;
  final bool isFirst;
  final bool isLast;
  final bool isValueEmpty;
  final String value;
  final RowColumnIndex rowColumnIndex;

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
        decoration: BoxDecoration(
          color: isFirst ? AppColors.kD6FFE9 : AppColors.kDEE5EC,
          border: Border(
            right: const BorderSide(
              color: Colors.black12,
              width: 1,
            ),
            left: BorderSide(
              color: isFirst ? Colors.black12 : Colors.transparent,
              width: 1,
            ),
          ),
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: isValueEmpty
              ? [
                  TextButton.icon(
                    style: TextButton.styleFrom(
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                    ),
                    onPressed: () {
                      marketplaceController.tableController.beginEdit(
                        rowColumnIndex,
                      );
                    },
                    label: Text(
                      'Add Link',
                      style: AppFontStyles.skolaSans(
                        fontSize: 14,
                        color: AppColors.k141414,
                      ),
                    ),
                    icon: const Icon(
                      FluentIcons.link_add_24_regular,
                      size: 18,
                    ),
                  ),
                ]
              : [
                  IconButton(
                    style: IconButton.styleFrom(
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                      padding: const EdgeInsets.all(0),
                    ),
                    onPressed: () {
                      launchUrl(Uri.parse(value));
                    },
                    iconSize: 18,
                    icon: const Icon(
                      FluentIcons.eye_48_regular,
                    ),
                  ),
                  4.horizontalSpace,
                  IconButton(
                    style: IconButton.styleFrom(
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                      padding: const EdgeInsets.all(0),
                    ),
                    onPressed: () {
                      marketplaceController.tableController.beginEdit(
                        rowColumnIndex,
                      );
                    },
                    iconSize: 18,
                    icon: const Icon(
                      FluentIcons.edit_48_regular,
                    ),
                  ),
                  4.horizontalSpace,
                  IconButton(
                    style: IconButton.styleFrom(
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                      padding: const EdgeInsets.all(0),
                    ),
                    onPressed: () {
                      copyToClipboard(value);
                    },
                    iconSize: 18,
                    icon: const Icon(
                      FluentIcons.copy_32_regular,
                    ),
                  ),
                ],
        ),
      );
}
