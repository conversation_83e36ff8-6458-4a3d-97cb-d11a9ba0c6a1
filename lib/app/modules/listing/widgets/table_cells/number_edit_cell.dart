import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';

import '../../../../ui/components/app_text_style.dart';
import '../../../../utils/input_formatters.dart';
import '../../../../utils/validate_decimal.dart';

class NumberEditCell extends StatelessWidget {
  const NumberEditCell({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.value,
    this.onChange,
    this.onSubmit,
  });

  final TextEditingController controller;
  final FocusNode focusNode;

  final String value;

  final Function(String newValue)? onChange;
  final Function(String newValue)? onSubmit;

  @override
  Widget build(BuildContext context) => TextField(
        autofocus: true,
        autocorrect: false,
        style: AppFontStyles.skolaSans(
          color: AppColors.k141414,
          fontSize: 14,
        ),
        controller: controller..text = value,
        focusNode: focusNode,
        decoration: InputDecoration(
          contentPadding:
              const EdgeInsets.symmetric(vertical: 13, horizontal: 8),
          isDense: true,
          focusedBorder: _buildOutlineInputBorder(),
          border: _buildOutlineInputBorder(),
          enabledBorder: _buildOutlineInputBorder(),
          disabledBorder: _buildOutlineInputBorder(),
          errorBorder: _buildOutlineInputBorder(),
          focusedErrorBorder: _buildOutlineInputBorder(),
        ),
        //cursorColor: AppColors.primary,
        keyboardType: TextInputType.number,
        textInputAction: TextInputAction.next,
        inputFormatters: [
          twoDecimal,
          DecimalTextInputFormatter(decimalRange: 2),
        ],
        onChanged: (String newValue) {
          onChange?.call(newValue);
        },
        onSubmitted: (String newValue) {
          focusNode.unfocus();
          if (newValue.toLowerCase() == value.toLowerCase()) {
            return;
          }
          onSubmit?.call(newValue);
        },
      );

  OutlineInputBorder _buildOutlineInputBorder() => const OutlineInputBorder(
        borderSide: BorderSide(
          color: Colors.transparent,
        ),
      );
}
