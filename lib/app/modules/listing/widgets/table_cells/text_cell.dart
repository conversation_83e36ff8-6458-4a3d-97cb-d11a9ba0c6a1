import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';

import '../../../../ui/components/app_text_style.dart';

class TextCell extends StatelessWidget {
  const TextCell({
    super.key,
    this.prefix,
    this.subStyle,
    this.style,
    this.suffix,
    this.isValueEmpty = false,
    this.value,
  });

  final String? value;
  final String? prefix;
  final TextStyle? subStyle;
  final TextStyle? style;
  final String? suffix;
  final bool isValueEmpty;

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
        alignment: Alignment.centerLeft,
        child: Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: prefix,
                style: subStyle,
              ),
              TextSpan(
                text: value?.toString() ?? '-',
              ),
              TextSpan(
                text: suffix,
                style: subStyle,
              ),
            ],
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: style ??
              AppFontStyles.skolaSans(
                fontSize: 14,
                color: isValueEmpty ? Colors.black38 : AppColors.k141414,
              ),
        ),
      );
}
