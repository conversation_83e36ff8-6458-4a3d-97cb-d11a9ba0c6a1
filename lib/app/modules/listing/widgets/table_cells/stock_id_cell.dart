import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';

import '../../../../data/models/diamond/diamond_entity.dart';
import '../../controllers/marketplace_controller.dart';

class StockIDCell extends StatelessWidget {
  const StockIDCell({
    super.key,
    required this.diamond,
    required this.isValueEmpty,
    required this.controller,
  });

  final MarketplaceController controller;
  final DiamondEntity diamond;
  final bool isValueEmpty;

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 8),
        alignment: Alignment.centerLeft,
        color:
            diamond.isLabGrown == 'Y' ? AppColors.kDEE5EC : AppColors.k1A47E8,
        child: TextButton(
          onPressed: () {
            controller.navigateToDetailsFromList(diamond);
          },
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 4),
          ),
          child: Text(
            '${diamond.skuPrefix}${diamond.skuNumber}'.toUpperCase(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: isValueEmpty ? Colors.black26 : AppColors.k141414,
              decoration: TextDecoration.underline,
              decorationStyle: TextDecorationStyle.dotted,
              decorationColor: AppColors.k1A47E8,
            ),
          ),
        ),
      );
}
