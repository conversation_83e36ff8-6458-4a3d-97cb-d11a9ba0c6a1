import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../utils/diamond_utils.dart';

class DiamondStatusIndicator extends StatelessWidget {
  final String? status;
  final bool tooltip;
  final bool isLong;

  const DiamondStatusIndicator({
    super.key,
    required this.status,
    this.tooltip = true,
    this.isLong = false,
  });

  @override
  Widget build(BuildContext context) {
    if (!tooltip) {
      return _buildIndicator();
    }
    return Tooltip(
      message: _statusTooltip(),
      child: _buildIndicator(),
    );
  }

  AnimatedContainer _buildIndicator() => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: BoxDecoration(
          color: _statusColor.withOpacity(0.2),
          borderRadius: const BorderRadius.all(Radius.circular(6)),
          border: Border.all(
            color: _statusColor.withOpacity(0.5),
          ),
        ),
        margin: const EdgeInsets.symmetric(horizontal: 9, vertical: 8),
        constraints: BoxConstraints(
          minHeight: isLong ? 30 : 22,
          minWidth: 22,
          maxHeight: isLong ? 30 : 22,
          maxWidth: isLong ? 100 : 22,
        ),
        alignment: Alignment.center,
        child: Text(
          isLong ? _statusTooltip() : status ?? 'A',
          style: GoogleFonts.ubuntu(
            color: _statusColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      );

  String _statusTooltip() {
    switch (status) {
      case 'A':
        return 'Available';
      case 'H':
        return 'Hold';
      case 'M':
        return 'Memo';
      case 'S':
        return 'Sold';
      default:
        return 'Status of the diamond is unknown';
    }
  }

  Color get _statusColor => DiamondUtils.statusColor(status??'');
}
