import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/empty_screens.dart';
import 'package:diamond_company_app/app/ui/components/scroll_to_hide.dart';
import 'package:diamond_company_app/app/ui/components/shadow_button.dart';
import 'package:diamond_company_app/app/utils/filter_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../controllers/marketplace_controller.dart';

class MarketplaceView extends GetView<MarketplaceController> {
  const MarketplaceView({required this.viewTag, super.key});

  final String viewTag;

  @override
  String get tag => viewTag;

  @override
  MarketplaceController get controller =>
      Get.put(MarketplaceController(), tag: viewTag);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: _buildFloatingBtns(),
        bottomNavigationBar: _buildCart(),
      );

  Widget _buildCart() => Obx(
        () => Get.find<CartItemService>().diamonds().isEmpty ||
                controller.isLoading()
            ? const SizedBox.shrink()
            : ScrollToHide(
                scrollController: controller.scrollController,
                duration: const Duration(milliseconds: 100),
                child: ShadowButton(
                  onTap: () => controller.navigateToCart(),
                  height: 150.h,
                  width: Get.width.w,
                  buttonColor: AppColors.k101C28,
                  margin: EdgeInsets.only(
                    left: 60.w,
                    right: 60.w,
                    bottom: MediaQuery.of(Get.context!).padding.bottom + 60.w,
                  ),
                  padding: REdgeInsets.symmetric(horizontal: 40),
                  buttonName: 'cart',
                  borderRadius: BorderRadius.all(Radius.circular(24.r)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      SizedBox(
                        height: 80.h,
                        width: 80.w,
                        child: AppImages.cart,
                      ),
                      24.horizontalSpace,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Cart',
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 45.sp,
                              color: AppColors.kBEFFFC,
                            ),
                          ),
                          Text(
                            'Diamond',
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 25.sp,
                              color: AppColors.kffffff,
                            ),
                          ),
                        ],
                      ),
                      Spacer(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                'Pcs',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 30.sp,
                                  color: AppColors.k9FA4A9,
                                ),
                              ),
                              20.horizontalSpace,
                              Center(
                                child: Text(
                                  '${Get.find<CartItemService>().diamondSummary().totalPcs.toString().padLeft(2, '0')}',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 35.sp,
                                    color: AppColors.kffffff,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          48.horizontalSpace,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                'Cts',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 30.sp,
                                  color: AppColors.k9FA4A9,
                                ),
                              ),
                              20.horizontalSpace,
                              Text(
                                '${Get.find<CartItemService>().diamondSummary().totalCts.toStringAsFixed(2)}',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 35.sp,
                                  color: AppColors.kffffff,
                                ),
                              ),
                            ],
                          ),
                          48.horizontalSpace,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                'Price/ct',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 30.sp,
                                  color: AppColors.k9FA4A9,
                                ),
                              ),
                              20.horizontalSpace,
                              Text(
                                '\$${Get.find<CartItemService>().diamondSummary().avgPricePerCt.toStringAsFixed(2)}',
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 35.sp,
                                  color: AppColors.kffffff,
                                ),
                              ),
                            ],
                          ),
                          48.horizontalSpace,
                          SizedBox(
                            height: 70.h,
                            width: 70.w,
                            child: IconButton.filled(
                              onPressed: () {},
                              style: IconButton.styleFrom(
                                backgroundColor: AppColors.kBEFFFC,
                                shape: const CircleBorder(),
                                fixedSize: Size(70.w, 70.h),
                                padding: EdgeInsets.zero,
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              icon: SizedBox(
                                height: 38.h,
                                width: 38.w,
                                child: AppImages.arrowForward,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
      );

  Container _buildFilterOptions() => Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(10.0),
            topLeft: Radius.circular(10.0),
          ),
          color: Colors.black,
        ),
        padding: const EdgeInsets.all(10),
        child: Row(
          children: <Widget>[
            Expanded(
              child: AppButton.icon(
                onPressed: controller.selectSort,
                prefixIcon: Obx(() => Badge(
                      isLabelVisible: controller.sort().isNotDefault,
                      smallSize: 8,
                      largeSize: 8,
                      backgroundColor: AppColors.kFF9800,
                      child: const Icon(CupertinoIcons.sort_down),
                    )),
                buttonText: 'Sort',
              ),
            ),
            // Vertical Divider using Container
            Container(
              height: 30,
              width: 1,
              color: AppColors.kffffff.withOpacity(0.5),
              margin: const EdgeInsets.symmetric(horizontal: 8),
            ),
            Expanded(
              child: AppButton.icon(
                onPressed: controller.openFilter,
                prefixIcon: Obx(() => Badge(
                      isLabelVisible: controller.appliedFilter().isNotEmpty,
                      smallSize: 8,
                      largeSize: 8,
                      backgroundColor: AppColors.kFF9800,
                      child: const Icon(
                        Icons.tune_outlined,
                        color: AppColors.kffffff,
                        size: 20,
                      ),
                    )),
                buttonText: 'Filter',
              ),
            ),
          ],
        ),
      );

  CommonAppbar _buildAppBar() => CommonAppbar(
        title: Text(
          LocaleKeys.side_menu_marketplace.tr,
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        titleSpacing: 0.w,
        onBackPressed: () => Get.back(),
        //centerTitle: true,
        actions: <Widget>[
          Obx(
            () => controller.isLoading()
                ? const SizedBox.shrink()
                : Badge(
                    backgroundColor: AppColors.k1A47E8,
                    smallSize: 27.r,
                    isLabelVisible: controller.sort().isNotDefault,
                    child: IconButton.outlined(
                      constraints: BoxConstraints(
                        maxHeight: 120.h,
                        maxWidth: 120.w,
                      ),
                      style: OutlinedButton.styleFrom(
                        backgroundColor: AppColors.kBEFFFC,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
                        shape: const CircleBorder(),
                        padding: EdgeInsets.zero,
                        fixedSize: Size(120.w, 120.h),
                      ),
                      onPressed: controller.selectSort,
                      icon: SizedBox(
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: AppImages.sort,
                        ),
                        width: 80.w,
                        height: 64.h,
                      ),
                    ),
                  ),
          ),
          30.horizontalSpace,
          Obx(
            () => controller.isLoading()
                ? const SizedBox.shrink()
                : Badge(
                    backgroundColor: AppColors.k1A47E8,
                    smallSize: 27.r,
                    isLabelVisible: controller.appliedFilter().isNotEmpty,
                    child: IconButton.outlined(
                      constraints: BoxConstraints(
                        maxHeight: 120.h,
                        maxWidth: 120.w,
                      ),
                      style: OutlinedButton.styleFrom(
                        backgroundColor: AppColors.kBEFFFC,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
                        shape: const CircleBorder(),
                        padding: EdgeInsets.zero,
                        fixedSize: Size(120.w, 120.h),
                      ),
                      onPressed: controller.openFilter,
                      icon: const Icon(
                        Icons.filter_alt_outlined,
                        color: AppColors.k101C28,
                        // size: 48.sp,
                      ),
                      // icon: SizedBox(
                      //   child: FittedBox(
                      //     child: AppImages.filterIcon,
                      //     fit: BoxFit.scaleDown,
                      //   ),
                      //   width: 48.w,
                      //   height: 48.h,
                      // ),
                    ),
                  ),
          ),
          30.horizontalSpace,
          Obx(
            () => controller.isLoading()
                ? const SizedBox.shrink()
                : IconButton.outlined(
                    constraints: BoxConstraints(
                      maxHeight: 120.h,
                      maxWidth: 120.w,
                    ),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: AppColors.kBEFFFC,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
                      shape: const CircleBorder(),
                      padding: EdgeInsets.zero,
                      fixedSize: Size(120.w, 120.h),
                    ),
                    onPressed: () => controller.isGridList.toggle(),
                    icon: controller.isGridList()
                        ? const Icon(Icons.format_list_bulleted,
                            color: AppColors.k101C28)
                        : const Icon(Icons.grid_view_outlined,
                            color: AppColors.k101C28),
                  ),
          ),
          30.horizontalSpace,
        ],
      );

  Obx _buildBody() => Obx(
        () => SizedBox(
          height: Get.height,
          width: Get.width,
          child: controller.isLoading()
              ? AppLoader()
              : RefreshIndicator(
                  color: AppColors.k101C28,
                  onRefresh: controller.fetchInventory,
                  strokeWidth: 2,
                  child: controller.listings.isEmpty
                      ? _buildEmptyView()
                      : controller.isGridList()
                          ? _buildGrid()
                          : _buildList(),
                ),
        ),
      );

  Widget _buildGrid() => GetBuilder<MarketplaceController>(
        tag: tag,
        builder: (_) => GridView.builder(
          clipBehavior: Clip.none,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            //childAspectRatio: Get.width / Get.height,
            //childAspectRatio: 1,
            crossAxisSpacing: 45.w,
            mainAxisSpacing: 45.h,
            mainAxisExtent: (Get.width * Get.height) / (Get.width + Get.height),
          ),
          padding: EdgeInsets.only(
              left: 60.w,
              right: 60.w,
              top: 60.h,
              bottom: 60.h + MediaQuery.of(Get.context!).padding.bottom),
          controller: controller.scrollController,
          itemCount: controller.listings().length + 1,
          itemBuilder: (BuildContext context, int index) {
            //return const SizedBox.shrink();
            if (index < controller.listings().length) {
              return controller.listings[index].renderGrid(index);
              /*return DiamondTile(
                            diamond: controller.diamonds[index],
                            onTap: () {
                              if (!controller.diamonds[index].seen) {
                                controller.diamonds[index].seen = true;
                                controller.diamonds.refresh();
                              }
                            },
                            seen: controller.diamonds[index].seen,
                          );*/
            } else {
              if (controller.canPaginate() && controller.isPaginating.isFalse) {
                controller
                  ..skip += controller.limit
                  ..pagination();
              }

              return controller.isPaginating()
                  ? AppLoader()
                  : const SizedBox.shrink();
            }
          },
        ),
      );

  Widget _buildList() => GetBuilder<MarketplaceController>(
        tag: tag,
        builder: (_) => ListView.separated(
          clipBehavior: Clip.none,
          controller: controller.scrollController,
          padding: EdgeInsets.only(
              left: 60.w,
              right: 60.w,
              top: 60.h,
              bottom: 60.h + MediaQuery.of(Get.context!).padding.bottom),
          itemCount: controller.listings().length + 1,
          itemBuilder: (BuildContext context, int index) {
            if (index < controller.listings().length) {
              return controller.listings[index].render();
              /*return DiamondTile(
                                diamond: controller.diamonds[index],
                                onTap: () {
                                  if (!controller.diamonds[index].seen) {
                                    controller.diamonds[index].seen = true;
                                    controller.diamonds.refresh();
                                  }
                                },
                                seen: controller.diamonds[index].seen,
                              );*/
            } else {
              if (controller.canPaginate() && controller.isPaginating.isFalse) {
                controller
                  ..skip += controller.limit
                  ..pagination();
              }

              return controller.isPaginating()
                  ? AppLoader()
                  : const SizedBox.shrink();
            }
          },
          separatorBuilder: (BuildContext context, int index) =>
              20.verticalSpace,
        ),
      );

  Widget _buildFloatingBtns() => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Obx(
                () => AnimatedOpacity(
                  duration: const Duration(milliseconds: 300),
                  opacity: controller.hideScrollToTop() ? 0 : 1,
                  child: AnimatedCrossFade(
                    alignment: Alignment.center,
                    duration: const Duration(milliseconds: 300),
                    crossFadeState: controller.hideScrollToTop()
                        ? CrossFadeState.showFirst
                        : CrossFadeState.showSecond,
                    firstChild: const SizedBox.shrink(),
                    secondChild: IconButton.filled(
                      onPressed: () {
                        controller.scrollController.animateTo(
                          0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeIn,
                        );
                      },
                      style: IconButton.styleFrom(
                        backgroundColor: AppColors.k111111,
                      ),
                      icon: const Icon(
                        Icons.vertical_align_top_outlined,
                        color: AppColors.kffffff,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );

  Widget _buildEmptyView() {
    if (controller.appliedFilter().isEmpty && controller.isLoading()) {
      AppLoader();
    }
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          EmptyScreen(
            icon: AppImages.emptyDiamondSvg,
            title: 'No Diamonds Found Yet',
            subtitle:
                "Don't worry, though! Our sellers are constantly adding new diamonds to dazzle you. Keep checking back for the latest sparklers or browse through our other glittering offerings. Shine bright like a diamond!",
          ),
          50.verticalSpace,
          OutlinedButton.icon(
            onPressed: controller.openFilter,
            icon: const Icon(
              Icons.filter_list_outlined,
            ),
            label: Text(
              'Modify Filter',
              style: GoogleFonts.ubuntu(),
            ),
          ),
        ],
      ),
    );
  }
}
