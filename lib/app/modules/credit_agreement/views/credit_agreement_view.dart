import 'package:diamond_company_app/app/data/models/static_pages/static_pages.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/credit_agreement_controller.dart';

/// CreditAgreementView
class CreditAgreementView extends GetView<CreditAgreementController> {
  /// CreditAgreementView
  const CreditAgreementView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Obx(
        () => PopScope(
          canPop: !controller.isLoading(),
          child: Scaffold(
            appBar: _buildCommonAppbar(),
            bottomNavigationBar: _buildBottomButtons(context),
            body: IgnorePointer(
              ignoring: controller.isLoading(),
              child: _buildBody(context),
            ),
          ),
        ),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: REdgeInsets.only(
            left: 60,
            right: 60,
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: FormBuilder(
            key: controller.fbKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                87.verticalSpace,
                _buildReadMoreContainer(),
                60.verticalSpace,
                _buildBySigningContainer(),
                60.verticalSpace,

                /// Authorized Signature
                _buildAuthorizedSignature(),
                100.verticalSpace,
                _buildTextFormField(
                  initialValue: controller
                      .newCompanyRegistrationController
                      .fbKey
                      .currentState
                      ?.fields[RegistrationFormFields.companyLegalName]
                      ?.value,
                  readOnly: true,
                  name: RegistrationFormFields.businessEntityName,
                  labelText:
                      LocaleKeys.credit_agreement_business_entity_name.tr,
                  keyboardType: TextInputType.name,
                  autofillHints: <String>[AutofillHints.name],
                  isRequired: true,
                  validation: (String? value) {
                    final String? trimmedValue = value?.trim();
                    if (trimmedValue?.isEmpty ?? true) {
                      return LocaleKeys.validation_business_entity_is_empty.tr;
                    }
                    return null;
                  },
                ),
                76.verticalSpace,
                _buildTextFormField(
                  initialValue: controller.selectedDate.ddMMyyyy,
                  onTap: () {
                    controller.selectDate(context);
                  },
                  // controller: controller.dateController,
                  name: RegistrationFormFields.signDate,
                  labelText: LocaleKeys.credit_agreement_date.tr,
                  isRequired: true,
                  readOnly: true,
                  validation: (String? value) {
                    if (value?.trim().isEmpty ?? true) {
                      return LocaleKeys.validation_select_date_is_empty.tr;
                    }
                    return null;
                  },
                  suffix: IconButton(
                    onPressed: () {
                      controller.selectDate(context);
                    },
                    padding: EdgeInsets.zero,
                    icon: SizedBox(
                      height: 64.h,
                      width: 64.w,
                      child: AppImages.calenderLogo,
                    ),
                  ),
                ),
                76.verticalSpace,
                DropDownFormField(
                  onChanged: (dynamic value) => controller.printName(value),
                  items: controller.legalStatusOrganizationController
                      .orderAuthorizedpersonList
                      .map((OrderAuthorizedPerson element) =>
                          '${element.firstName} ${element.lastName}')
                      .toList(),
                  menuItemStyle: AppFontStyles.skolaSans(
                    color: AppColors.k70777E,
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  decoration: InputDecoration(
                    contentPadding: REdgeInsets.only(
                      left: 48,
                      bottom: 30,
                      right: 48,
                    ),
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                      minWidth: Get.width,
                    ),
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          LocaleKeys.credit_agreement_print_name.tr,
                        ),
                        Text(
                          ' *',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w900,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                    labelStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    errorStyle: const TextStyle(color: Colors.red),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ),
                ),
                // 76.verticalSpace,
                // _buildTextFormField(
                //   name: RegistrationFormFields.printName,
                //   labelText: LocaleKeys.credit_agreement_print_name.tr,
                //   keyboardType: TextInputType.name,
                //   autofillHints: <String>[AutofillHints.name],
                //   isRequired: true,
                //   validation: (String? value) {
                //     final String? trimmedValue = value?.trim();
                //     if (trimmedValue?.isEmpty ?? true) {
                //       return LocaleKeys.validation_print_name_is_empty.tr;
                //     }
                //     return null;
                //   },
                // ),
                76.verticalSpace,
                _buildFooterText(),
                48.verticalSpace,
              ],
            ),
          ),
        ),
      );

  /// Build Footer Text
  Widget _buildFooterText() => Center(
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            text: LocaleKeys
                .credit_agreement_by_clicking_register_you_agree_to_our_all.tr,
            style: AppFontStyles.skolaSans(
              color: AppColors.k101C28,
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
            ),
            children: <InlineSpan>[
              TextSpan(
                text: LocaleKeys.credit_agreement_terms_conditions.tr,
                recognizer: TapGestureRecognizer()
                  ..onTap = () => Get.toNamed(
                        Routes.PRIVACY_POLICY,
                        arguments: StaticPagesType.terms_and_conditions,
                      ),
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  decoration: TextDecoration.underline,
                ),
              ),
              TextSpan(
                text: LocaleKeys.credit_agreement_and_that_you_have_read_our.tr,
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.underline,
                ),
              ),
              TextSpan(
                text: LocaleKeys.credit_agreement_privacy_policy.tr,
                recognizer: TapGestureRecognizer()
                  ..onTap = () => Get.toNamed(
                        Routes.PRIVACY_POLICY,
                        arguments: StaticPagesType.privacy_policy,
                      ),
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  decoration: TextDecoration.underline,
                ),
              ),
            ],
          ),
        ),
      );

  /// Build BySigning Container
  Widget _buildBySigningContainer() => Container(
        height: 278.h,
        width: Get.width,
        decoration: BoxDecoration(
          color: AppColors.kEAEFF4,
          borderRadius: BorderRadius.circular(24.r),
        ),
        alignment: Alignment.center,
        child: Padding(
          padding: REdgeInsets.all(60),
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              text: LocaleKeys.credit_agreement_by_signing.tr,
              style: AppFontStyles.skolaSans(
                letterSpacing: 5.sp,
                color: AppColors.k101C28,
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
              ),
              children: <InlineSpan>[
                TextSpan(
                  text: LocaleKeys
                      .credit_agreement_i_confirm_the_above_details.tr,
                  style: AppFontStyles.skolaSans(
                    letterSpacing: 2.sp,
                    color: AppColors.k101C28,
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  /// Build Authorized Signature
  Widget _buildAuthorizedSignature() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Text(
                LocaleKeys.credit_agreement_sign_of_authorized_signature.tr,
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w900,
                ),
              ),
              Text(
                '*',
                style: AppFontStyles.skolaSans(
                  color: Colors.red,
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w900,
                ),
              ),
            ],
          ),
          40.verticalSpace,

          /// Signature Pad
          _buildSignaturePad(),
          20.verticalSpace,

          /// Custom Error Message
          _buildCustomErrorMessage(),
          20.verticalSpace,
          _buildClearSignatureButton(),
        ],
      );

  /// Build Clear Signature Button
  Widget _buildClearSignatureButton() => InkWell(
        onTap: () => controller.signaturePadKey.currentState?.clear(),
        borderRadius: BorderRadius.circular(500.r),
        child: Container(
          height: 100.h,
          width: 451.w,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.k101C28,
              width: 4.w,
            ),
            borderRadius: BorderRadius.circular(500.r),
          ),
          alignment: Alignment.center,
          child: Text(
            LocaleKeys.credit_agreement_clear_signature.tr,
            style: AppFontStyles.skolaSans(
              color: AppColors.k101C28,
              fontSize: 35.sp,
              letterSpacing: 3.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      );

  /// Build Custom Error Message
  Widget _buildCustomErrorMessage() => Obx(
        () => AnimatedSize(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          child: Visibility(
            visible: controller.errorMessage.value.isNotEmpty,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: REdgeInsets.only(left: 44),
                child: Text(
                  controller.errorMessage.value,
                  style: AppFontStyles.skolaSans(
                    color: Colors.red,
                    fontSize: 38.sp,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

  /// Build Signature Pad
  Widget _buildSignaturePad() => Container(
        height: 800.h,
        width: Get.width,
        padding: REdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            width: 1.w,
          ),
        ),
        child: Column(
          children: <Widget>[
            Expanded(
              child: SfSignaturePad(
                key: controller.signaturePadKey,
                minimumStrokeWidth: 1,
                maximumStrokeWidth: 3,
                strokeColor: Colors.blue,
                backgroundColor: Colors.white,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  LocaleKeys.credit_agreement_sign_here.tr,
                  style: AppFontStyles.skolaSans(
                    color: AppColors.k000000.withOpacity(0.2),
                    fontSize: 75.sp,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                30.horizontalSpace,
              ],
            ),
          ],
        ),
      );

  /// Read More Agreement Container
  Widget _buildReadMoreContainer() => Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: AppColors.kBEFFFC,
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Padding(
          padding: REdgeInsets.all(60),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                LocaleKeys.credit_agreement_description_text.tr,
                maxLines: 6,
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.k101C28,
                ),
              ),
              50.verticalSpace,
              AppButton.text(
                onPressed: () => Get.toNamed(
                  Routes.PRIVACY_POLICY,
                  arguments: StaticPagesType.agreement,
                ),
                backgroundColor: AppColors.kffffff,
                borderColor: AppColors.k101C28,
                borderRadius: 500.w,
                borderWidth: 4.w,
                buttonSize: Size(553.w, 80.h),
                buttonText: LocaleKeys.credit_agreement_read_more_agreement.tr,
                buttonTextStyle: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  letterSpacing: 3.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
      );

  /// Build Bottom Buttons
  Widget _buildBottomButtons(BuildContext context) => Padding(
        padding: EdgeInsets.only(
          left: 60.w,
          right: 60.w,
          bottom: MediaQuery.of(context).viewInsets.bottom +
              MediaQuery.of(context).padding.bottom +
              60.h,
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: _buildCommonAppButton(
                onPressed: () => Get.back(),
                buttonText: LocaleKeys.credit_agreement_previous.tr,
                backgroundColor: AppColors.kEAEFF4,
                borderColor: AppColors.kEAEFF4,
                buttonTextColor: AppColors.k101C28,
              ),
            ),
            30.horizontalSpace,
            Expanded(
              child: Obx(
                () => _buildCommonAppButton(
                  onPressed: () => controller.register(),
                  buttonText: LocaleKeys.credit_agreement_register.tr,
                  buttonTextColor: AppColors.kBEFFFC,
                  isLoading: controller.isLoading(),
                ),
              ),
            ),
          ],
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (!controller.isLoading()) {
            Get.back();
          }
        },
        title: Text(
          LocaleKeys.credit_agreement_credit_agreement.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          FittedBox(
            child: StatusChip(
              text: LocaleKeys.credit_agreement_five_five.tr,
              textStyle: AppFontStyles.skolaSans(
                color: AppColors.k101C28,
                fontSize: 35.sp,
                letterSpacing: 10.sp,
                fontWeight: FontWeight.w400,
              ),
              color: AppColors.kEAEFF4,
            ),
          ),
          30.horizontalSpace,
        ],
      );

  /// Common App Button
  Widget _buildCommonAppButton({
    required String buttonText,
    Function? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
    bool isLoading = false,
  }) =>
      AppButton.text(
        buttonText: buttonText,
        onPressed: () {
          onPressed!();
        },
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        isLoading: isLoading,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );

  /// Common TextFormField
  AppTextFormField _buildTextFormField({
    required String name,
    required String? Function(String?)? validation,
    // TextEditingController? controller,
    void Function()? onTap,
    String? labelText,
    String? initialValue,
    bool isRequired = false,
    bool enabled = true,
    bool readOnly = false,
    TextInputType? keyboardType,
    Widget? suffix,
    int? maxLines,
    Iterable<String>? autofillHints,
  }) =>
      AppTextFormField(
        onTap: onTap ?? () {},
        initialValue: initialValue ?? '',
        // controller: controller,
        name: name,
        keyboardType: keyboardType,
        autofillHints: autofillHints,
        validator: validation,
        labelText: labelText ?? '',
        isRequired: isRequired,
        enabled: enabled,
        readOnly: readOnly,
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        maxLines: maxLines ?? 1,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        suffixIcon: suffix,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48, bottom: 30),
      );
}
