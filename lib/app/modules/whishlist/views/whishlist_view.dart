import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/empty_screens.dart';
import 'package:diamond_company_app/app/ui/components/stock_item.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/whishlist_controller.dart';

/// Whish list View
class WhishlistView extends GetView<WhishlistController> {
  /// Constructor for Whish list View
  const WhishlistView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody()
            .paddingOnly(bottom: MediaQuery.of(Get.context!).padding.bottom),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : controller.diamond.isEmpty
                ? EmptyScreen(
                    icon: AppImages.emptyWishlistSvg,
                    title: 'Your Wishlist is Empty',
                    subtitle:
                        'Browse our collection and add your favorite diamonds to your wishlist for easy access later.',
                  )
                : RefreshIndicator(
                    onRefresh: () => controller.wishlistDiamonds(),
                    color: AppColors.k101C28,
                    child: ListView.separated(
                      clipBehavior: Clip.none,
                      padding: REdgeInsets.all(60),
                      physics: const AlwaysScrollableScrollPhysics(),
                      controller: controller.scrollController,
                      itemBuilder: (BuildContext context, int index) {
                        if (index == controller.diamond.length) {
                          return controller.isLoadMore()
                              ? AppLoader()
                              : const SizedBox.shrink();
                        } else {
                          return StockItem(
                            margin: EdgeInsets.zero,
                            //height: 439.h,
                            border: Border.all(
                              width: 0.5.w,
                              color: AppColors.k000000.withOpacity(0.2),
                            ),
                            onTap: () {
                              Get.toNamed(
                                Routes.DIAMOND_DETAILS,
                                arguments: DiamondArgs(
                                  diamond: controller.diamond[index],
                                  id: controller.diamond[index].id ?? '',
                                ),
                              );
                            },
                            diamond: controller.diamond[index],
                            // diamond: DiamondEntity(id: '1'),
                            isAddedToCart: false.obs,
                            onFavourite: (bool value) {
                              controller.wishlist(isFav: value, index: index);
                            },
                          );
                        }
                      },
                      separatorBuilder: (BuildContext context, int index) =>
                          20.verticalSpace,
                      itemCount: controller.diamond.length + 1,
                    ),
                  ),
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: Text(
          LocaleKeys.whishlist_title.tr,
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          // _buildActionIcon(),
          30.horizontalSpace,
        ],
        onBackPressed: () => Get.back(),
      );

  Badge _buildActionIcon() => Badge(
        backgroundColor: AppColors.k1A47E8,
        alignment: Alignment.topRight,
        label: Text(
          '8',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            color: AppColors.kffffff,
          ),
          textAlign: TextAlign.center,
        ),
        offset: const Offset(-1, -1),
        child: IconButton.outlined(
          style: OutlinedButton.styleFrom(
            backgroundColor: AppColors.kBEFFFC,
            side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
            shape: const CircleBorder(),
          ),
          onPressed: () {},
          icon: SizedBox(
            height: 64.h,
            width: 64.w,
            child: AppImages.whishlist,
          ),
        ),
      );
}
