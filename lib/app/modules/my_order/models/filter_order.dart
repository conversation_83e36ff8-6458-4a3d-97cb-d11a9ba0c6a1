import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';

/// FilterType: Enum class for filter type
enum FilterType {
  /// orderStatus: Order status filter
  orderStatus,

  /// paymentStatus: Payment status filter
  paymentStatus,
}

/// FilterModel: Model class for filter
class FilterModel {
  /// FilterModel: Constructor for FilterModel
  FilterModel({
    this.title,
    this.value,
    this.filterType,
    this.isResponseRequired = false,
  });

  /// title: Title of filter
  String? title;

  /// value: Value of filter
  String? value;

  /// filterType: Type of filter
  FilterType? filterType;

  /// Is response required
  bool isResponseRequired;

  /// Color
  Color get color {
    switch (value) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return AppColors.k19DC98;
      case 'REJECTED':
        return AppColors.kEF002F;
      default:
        return AppColors.k7CB9E8;
    }
  }
}
