import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_dialogs.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/order_item.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/my_order_controller.dart';

/// MyOrderView
class MyOrderView extends GetView<MyOrderController> {
  /// Constructor for MyOrderView
  const MyOrderView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Obx(
        () => Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      );

  Widget _buildBody() => controller.isLoading()
      ? AppLoader()
      : controller.userOrder.isEmpty
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                SizedBox(
                  height: 200.h,
                  width: 200.w,
                  child: AppImages.emptyOrder,
                ),
                100.verticalSpace,
                Text(
                  LocaleKeys.my_orders_no_orders_found.tr,
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 70.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                50.verticalSpace,
                Padding(
                  padding: REdgeInsets.symmetric(horizontal: 100),
                  child: Text(
                    LocaleKeys.my_orders_no_orders_desc.tr,
                    textAlign: TextAlign.center,
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 45.sp,
                      color: AppColors.k70777E,
                    ),
                  ),
                ),
              ],
            )
          : RefreshIndicator(
              color: AppColors.k101C28,
              onRefresh: () =>
                  controller.fetchUserOrder(skip: 0, limit: controller.limit()),
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                shrinkWrap: true,
                controller: controller.scrollController,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      30.verticalSpace,
                      Text(
                        '${LocaleKeys.my_orders_orders.tr} - ${controller.userOrder().length.toString().padLeft(2, '0')}'
                            .toUpperCase(),
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w900,
                          fontSize: 40.sp,
                          color: AppColors.k101C28,
                        ),
                      ).paddingSymmetric(horizontal: 60.w),
                      60.verticalSpace,
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const ClampingScrollPhysics(),
                        padding: REdgeInsets.symmetric(horizontal: 15),
                        itemBuilder: (BuildContext context, int index) {
                          if (controller.userOrder.length == index) {
                            // WidgetsBinding.instance
                            //     .addPostFrameCallback((Duration timeStamp) {
                            //   controller.loadMoreData();
                            // });
                            return Obx(
                              () => controller.isLoadingMore()
                                  ? AppLoader()
                                  : const SizedBox.shrink(),
                            );
                          } else {
                            return OrderItem(
                              userOrder: controller.userOrder[index],
                              toMyOrderScreen: true,
                              width: Get.width,
                              index: index,
                              onTap: () {
                                if (kIsWeb) {
                                  Get.routing.args =
                                      controller.userOrder[index];
                                  Get.toNamed(
                                    Routes.MY_ORDER_DETAIL,
                                    id: DesignConfig.listingSideMenuId,
                                  );
                                } else {
                                  Get.toNamed(
                                    Routes.MY_ORDER_DETAIL,
                                    arguments: controller.userOrder[index],
                                  );
                                }
                              },
                            );
                          }
                        },
                        separatorBuilder: (BuildContext context, int index) =>
                            40.verticalSpace,
                        itemCount: controller.userOrder.length + 1,
                      ),
                      60.verticalSpace,
                    ],
                  ),
                ],
              ),
            );

  CommonAppbar _buildAppBar() => CommonAppbar(
        title: Text(
          LocaleKeys.my_orders_title.tr,
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          IconButton.outlined(
            constraints: BoxConstraints(
              maxHeight: 120.h,
              maxWidth: min(120.w, 50),
            ),
            style: OutlinedButton.styleFrom(
              backgroundColor: AppColors.kBEFFFC,
              side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
              shape: const CircleBorder(),
              padding: EdgeInsets.zero,
              fixedSize: Size(min(120.w, 50), 120.h),
            ),
            onPressed: () => showFilterOrderDialog(),
            icon: SizedBox(
              height: 48.h,
              width: 48.w,
              child: AppImages.filterIcon,
            ),
          ),
        ],
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
      );
}
