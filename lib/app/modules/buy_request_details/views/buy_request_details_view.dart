import 'dart:math';

import 'package:diamond_company_app/app/chat_service/chat_enum.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/modules/buy_request_details/controllers/buy_request_details_controller.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/accept_decline_cofirmation_dialog.dart';
import 'package:diamond_company_app/app/ui/components/address_card.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_dialogs.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/bill_summary_widget.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/common_ask_queries.dart';
import 'package:diamond_company_app/app/ui/components/diamond_list_tile.dart';
import 'package:diamond_company_app/app/ui/components/payment_card.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/buy_request_utils.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../data/config/design_config.dart';

/// my order details view
class BuyRequestDetailsView extends GetView<BuyRequestDetailsController> {
  /// my order details view
  const BuyRequestDetailsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Obx(
        () => Scaffold(
          appBar: _buildAppbar(),
          body: _buildBody(),
        ),
      );

  Widget _buildBody() => SizedBox(
        height: Get.height,
        width: Get.width,
        child: controller.isLoading() ? AppLoader() : _buildList(),
      );

  Widget _buildList() => Container(
        width: Get.width,
        height: Get.height,
        child: Stack(
          children: [
            Positioned(
              bottom: MediaQuery.of(Get.context!).padding.bottom,
              left: 0,
              right: 0,
              top: 0,
              child: RefreshIndicator(
                color: AppColors.k101C28,
                onRefresh: () => controller.buyRequestDetails(),
                child: ListView(
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        30.verticalSpace,
                        _buildDiamonds(),
                        4.verticalSpace,
                        AddressCard(
                          title: LocaleKeys.address_billing_address.tr,
                          name:
                              '${controller.buyRequest().billingAddress?.fullName ?? ''}',
                          address: controller.getAddress(
                              controller.buyRequest().billingAddress),
                          phone:
                              '${controller.buyRequest().billingAddress?.phone ?? ''}',
                          email:
                              '${controller.buyRequest().billingAddress?.email ?? ''}',
                        ),
                        80.verticalSpace,
                        AddressCard(
                          title: LocaleKeys.address_shipping_address.tr,
                          name:
                              '${controller.buyRequest().shippingAddress?.fullName ?? ''}',
                          address: controller.getAddress(
                              controller.buyRequest().shippingAddress),
                          phone:
                              '${controller.buyRequest().shippingAddress?.phone ?? ''}',
                          email:
                              '${controller.buyRequest().shippingAddress?.email ?? ''}',
                        ),
                        80.verticalSpace,
                        PaymentCard(buyRequest: controller.buyRequest),
                        CommonAskQueries(
                          onPressed: () {
                            if (kIsWeb) {
                              Get.routing.args = <String, dynamic>{
                                'type': ChatType.BUY_REQUEST_ORDER,
                                'buyRequest': controller.buyRequest(),
                              };
                              Get.toNamed(Routes.INQUIRY_MESSAGES,
                                  id: DesignConfig.listingSideMenuId);
                            } else {
                              Get.toNamed(Routes.INQUIRY_MESSAGES,
                                  arguments: <String, dynamic>{
                                    'type': ChatType.BUY_REQUEST_ORDER,
                                    'buyRequest': controller.buyRequest(),
                                  });
                            }
                          },
                        ),
                        80.verticalSpace,
                        if (controller.buyRequest().status ==
                                BuyRequestStatus.PENDING &&
                            (controller.buyRequest().isCaptureFailed ?? false))
                          80.verticalSpace,
                        if (controller.buyRequest().status ==
                                BuyRequestStatus.PENDING &&
                            (controller.buyRequest().isCaptureFailed ?? false))
                          _buildPlaceOrder(),
                        60.verticalSpace,
                        if (controller.buyRequest().status ==
                            BuyRequestStatus.PENDING)
                          _buildCancelBuyRequest(),

                        //_buildInvoiceCard(),
                        780.verticalSpace,
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 0.h,
              left: 0.w,
              right: 0.w,
              child: Obx(
                () => BillSummaryWidget(
                  buyRequest: controller.buyRequest(),
                  onAccept:
                      (controller.isAccepting() || controller.isDeclining())
                          ? null
                          : () => showAcceptAlertDialog(),
                  onDecline:
                      (controller.isAccepting() || controller.isDeclining())
                          ? null
                          : () => showDeclineAlertDialog(),
                ),
              ),
            ),
          ],
        ),
      );

  Widget _buildCancelBuyRequest() => AppButton.text(
        buttonText:
            '${LocaleKeys.bill_summary_cancel_buy_request.tr}'.toUpperCase(),
        onPressed: () {
          showCancelBuyRequestDialog();
        },
        borderColor: AppColors.k101C28,
        borderWidth: min(4.w, 2),
        borderRadius: 500.r,
        isLoading: controller.isCancelling(),
        loaderColor: AppColors.k101C28,
        padding: EdgeInsets.zero,
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: AppColors.kffffff,
        buttonTextStyle: AppFontStyles.skolaSans(
          fontWeight: FontWeight.w700,
          fontSize: 35.sp,
          color: AppColors.k101C28,
        ),
      ).paddingSymmetric(horizontal: 60.w);

  /// Approve Buy request and place order
  Widget _buildPlaceOrder() => AppButton.text(
        buttonText: 'Place Order'.toUpperCase(),
        onPressed: () => controller.placeOrder(),
        borderColor: AppColors.k101C28,
        borderWidth: min(4.w, 1),
        borderRadius: 500.r,
        isLoading: controller.isPlacingOrder(),
        loaderColor: AppColors.kffffff,
        padding: EdgeInsets.zero,
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: AppColors.k101C28,
        buttonTextStyle: AppFontStyles.skolaSans(
          fontWeight: FontWeight.w700,
          fontSize: 35.sp,
          color: AppColors.kffffff,
        ),
      ).paddingSymmetric(horizontal: 60.w);

  Column _buildDiamonds() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildNotAvailable(),
          if (controller.availableDiamonds.isNotEmpty)
            Text(
              '${LocaleKeys.buy_requests_diamonds.tr} - ${controller.availableDiamonds.length.toString().padLeft(2, '0')}'
                  .toUpperCase(),
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
              ),
            ).paddingSymmetric(horizontal: min(60.w, 15)),
          if (controller.availableDiamonds.isNotEmpty) 60.verticalSpace,
          if (controller.availableDiamonds.isNotEmpty) _buildDiamondList(),
          if (controller.availableDiamonds.isNotEmpty) 80.verticalSpace,
        ],
      );

  Widget _buildAcceptBuyRequestButton() => Obx(
        () => AppButton.text(
          buttonText: 'Accept Buy Request'.toUpperCase(),
          //onPressed: () => controller.acceptBuyRequestUpdate(),
          borderColor: AppColors.k101C28,
          borderWidth: min(4.w, 1),
          borderRadius: 500.r,
          padding: EdgeInsets.zero,
          isLoading: controller.isUpdating(),
          loaderColor: AppColors.k101C28,
          buttonSize: Size(Get.width, 120.h),
          backgroundColor: AppColors.kffffff,
          buttonTextStyle: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w700,
            fontSize: 35.sp,
            color: AppColors.k101C28,
          ),
        ).paddingSymmetric(horizontal: 60.w),
      );

  Widget _buildNotAvailable() => controller.notAvailableDiamonds.isEmpty
      ? const SizedBox.shrink()
      : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            30.verticalSpace,
            Text(
              '${LocaleKeys.buy_requests_not_available.tr.toUpperCase()} - ${controller.notAvailableDiamonds.length.toString().padLeft(2, '0')}',
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
              ),
            ).paddingSymmetric(horizontal: min(60.w, 15)),
            60.verticalSpace,
            _buildNotAvailableList(),
            80.verticalSpace,
          ],
        );

  Widget _buildInvoiceCard() => Column(
        children: <Widget>[
          47.verticalSpace,
          Container(
            width: Get.width,
            height: 200.h,
            margin: REdgeInsets.symmetric(horizontal: 60),
            padding: REdgeInsets.symmetric(horizontal: 60),
            decoration: BoxDecoration(
              color: AppColors.kBEFFFC,
              borderRadius: BorderRadius.all(Radius.circular(24.r)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Get your invoice now!',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 45.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                AppButton.custom(
                  onPressed: () {},
                  borderRadius: 500.r,
                  backgroundColor: AppColors.kffffff,
                  borderWidth: min(4.w, 1),
                  borderColor: AppColors.k101C28,
                  padding: EdgeInsets.zero,
                  buttonSize: Size(328.w, 100.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        'INVOICE',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 35.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                      24.horizontalSpace,
                      SizedBox(
                        height: 48.h,
                        width: 48.w,
                        child: AppImages.downloadInvoice,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          40.verticalSpace,
        ],
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        titleSpacing: 0,
        title: _buildAppbarTitle(),
        actions: <Widget>[
          if (controller.buyRequest().status != null)
            FittedBox(
              child: StatusChip(
                text: BuyRequestUtils.getStatusString(
                            controller.buyRequest().status)
                        .capitalizeFirst ??
                    '',
                color: BuyRequestUtils.getStatusColor(
                    controller.buyRequest().status),
                textStyle: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w500,
                  color: BuyRequestUtils.getStatusTextColor(
                      controller.buyRequest().status),
                ),
              ),
            ),
          //30.horizontalSpace,
          SizedBox(width: min(30.w, 10)),
          // IconButton(
          //   onPressed: () => showCancelBuyRequestMenu(),
          //   icon: const Icon(
          //     Icons.more_vert,
          //     color: AppColors.k70777E,
          //   ),
          // ),
        ],
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
      );

  Widget _buildAppbarTitle() => Obx(
        () => controller.isLoading()
            ? const SizedBox.shrink()
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  RichText(
                    text: TextSpan(
                      text: LocaleKeys.my_order_detail_order.tr,
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.k70777E,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: controller.orderCode().toUpperCase(),
                          style: AppFontStyles.skolaSans(
                            fontSize: 45.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      );

  ListView _buildDiamondList() => ListView.separated(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        padding: REdgeInsets.symmetric(horizontal: 15),
        itemBuilder: (BuildContext context, int index) => _diamondListItem(
            diamond:
                controller.availableDiamonds[index].stock ?? DiamondEntity()),
        separatorBuilder: (BuildContext context, int index) => 20.verticalSpace,
        itemCount: controller.availableDiamonds.length,
      );

  Container _diamondListItem({required DiamondEntity diamond}) => Container(
        padding: REdgeInsets.all(40),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: Stack(
          children: [
            InkWell(
              onTap: () {
                if (kIsWeb) {
                  Get.routing.args = DiamondArgs(
                    id: diamond.id ?? '',
                    diamond: diamond,
                  );
                  Get.toNamed(Routes.DIAMOND_DETAILS,
                      id: DesignConfig.listingSideMenuId);
                } else {
                  Get.toNamed(
                    Routes.DIAMOND_DETAILS,
                    arguments: DiamondArgs(
                      id: diamond.id ?? '',
                      diamond: diamond,
                    ),
                  );
                }
              },
              child: DiamondListTile(
                diamond: diamond,
              ),
            ),
            controller.buyRequest().status == BuyRequestStatus.UPDATED
                ? Positioned(
                    top: 0,
                    right: 0,
                    child: GetBuilder<BuyRequestDetailsController>(
                      builder: (_) => IconButton.outlined(
                        onPressed: () {
                          controller.onClickDeclineAvailableStock(diamond);
                        },
                        padding: REdgeInsets.all(10),
                        constraints: const BoxConstraints(),
                        style: OutlinedButton.styleFrom(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          side: BorderSide(
                            color: diamond.isDeclined
                                ? AppColors.kFF9800
                                : AppColors.k101C28,
                          ),
                        ),
                        icon: Icon(
                          Icons.close_rounded,
                          size: 40.sp,
                          color: diamond.isDeclined
                              ? AppColors.kFF9800
                              : AppColors.k101C28,
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
      );

  ListView _buildNotAvailableList() => ListView.separated(
        shrinkWrap: true,
        padding: REdgeInsets.symmetric(horizontal: 15),
        physics: const NeverScrollableScrollPhysics(),
        clipBehavior: Clip.none,
        itemBuilder: (BuildContext context, int index) =>
            _buildNotAvailableItem(
          index: index,
          stockId: controller.notAvailableDiamonds[index],
        ),
        separatorBuilder: (BuildContext context, int index) => 20.verticalSpace,
        itemCount: controller.notAvailableDiamonds.length,
      );

  Container _buildNotAvailableItem({
    required int index,
    required StockId stockId,
  }) =>
      Container(
        width: Get.width,
        // height: 563.h,
        padding: REdgeInsets.all(40),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: Column(
          children: <Widget>[
            DiamondListTile(
              diamond: stockId.stock!,
            ),
            if (stockId.suggestedStock != null) 32.verticalSpace,
            if (stockId.suggestedStock != null)
              Obx(
                () => ClipRRect(
                  borderRadius: BorderRadius.circular(
                      controller.isTileExpanded() ? 24.r : 60.r),
                  child: Theme(
                    data: ThemeData(dividerColor: Colors.transparent),
                    child: ExpansionTile(
                      backgroundColor: AppColors.kEAEFF4,
                      initiallyExpanded: true,
                      dense: true,
                      expansionAnimationStyle: AnimationStyle(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeIn,
                      ),
                      tilePadding: REdgeInsets.symmetric(horizontal: 50),
                      visualDensity: VisualDensity.compact,
                      iconColor: AppColors.k101C28,
                      collapsedIconColor: AppColors.k101C28,
                      collapsedBackgroundColor: AppColors.kEAEFF4,
                      onExpansionChanged: (bool value) {
                        controller.isTileExpanded.value = value;
                      },
                      title: Text(
                        LocaleKeys.my_order_detail_suggested_alternatives.tr,
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 35.sp,
                          color: controller.isTileExpanded()
                              ? AppColors.k101C28
                              : AppColors.k1A47E8,
                        ),
                      ),
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (kIsWeb) {
                              Get.routing.args = DiamondArgs(
                                id: (stockId.suggestedStock?.stock
                                            as DiamondEntity)
                                        .id ??
                                    '',
                                diamond: stockId.suggestedStock?.stock
                                    as DiamondEntity,
                              );
                              Get.toNamed(Routes.DIAMOND_DETAILS,
                                  id: DesignConfig.listingSideMenuId);
                            } else {
                              Get.toNamed(
                                Routes.DIAMOND_DETAILS,
                                arguments: DiamondArgs(
                                  id: (stockId.suggestedStock?.stock
                                              as DiamondEntity)
                                          .id ??
                                      '',
                                  diamond: stockId.suggestedStock?.stock
                                      as DiamondEntity,
                                ),
                              );
                            }
                          },
                          child: Container(
                            width: Get.width,
                            margin: REdgeInsets.only(
                              left: 50,
                              right: 50,
                              bottom: 50,
                            ),
                            padding: REdgeInsets.symmetric(
                              horizontal: 40,
                              vertical: 30,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.kffffff,
                              borderRadius: BorderRadius.circular(24.r),
                              boxShadow: <BoxShadow>[
                                BoxShadow(
                                  color: AppColors.k000000.withOpacity(0.1),
                                  offset: Offset(0, 10.h),
                                  blurRadius: 104.r,
                                ),
                              ],
                            ),
                            child: DiamondListTile(
                              isAlternative: true,
                              diamond: stockId.suggestedStock?.stock ??
                                  DiamondEntity(),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            if (stockId.suggestedStock != null) 32.verticalSpace,
            if (stockId.suggestedStock != null)
              GetBuilder<BuyRequestDetailsController>(
                builder: (_) => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: AppButton.custom(
                            onPressed: () {
                              controller.onClickAcceptSuggestedStock(stockId);
                            },
                            borderColor:
                                stockId.suggestedStock?.stock?.isAccepted ??
                                        false
                                    ? AppColors.k128807
                                    : AppColors.k101C28,
                            borderWidth:
                                stockId.suggestedStock?.stock?.isAccepted ??
                                        false
                                    ? 1.w
                                    : min(4.w, 2),
                            borderRadius: 500.r,
                            backgroundColor:
                                stockId.suggestedStock?.stock?.isAccepted ??
                                        false
                                    ? AppColors.kE2F3EF
                                    : AppColors.kffffff,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  stockId.suggestedStock?.stock?.isAccepted ??
                                          false
                                      ? LocaleKeys.my_order_detail_accepted.tr
                                      : LocaleKeys.my_order_detail_accept.tr,
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 35.sp,
                                    color: AppColors.k101C28,
                                  ),
                                ),
                                stockId.suggestedStock?.stock?.isAccepted ??
                                        false
                                    ? 15.horizontalSpace
                                    : const SizedBox.shrink(),
                                stockId.suggestedStock?.stock?.isAccepted ??
                                        false
                                    ? Icon(
                                        Icons.check_circle,
                                        color: AppColors.k128807,
                                        size: 50.sp,
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            ),
                          ),
                        ),
                        24.horizontalSpace,
                        Expanded(
                          child: AppButton.text(
                            buttonText:
                                stockId.suggestedStock?.stock?.isDeclined ??
                                        false
                                    ? LocaleKeys.my_order_detail_declined.tr
                                    : LocaleKeys.my_order_detail_decline.tr,
                            //onPressed: () => showDeclineWithReasonDialog(),
                            onPressed: () {
                              controller.onClickDeclineSuggestedStock(stockId);
                            },
                            borderColor:
                                stockId.suggestedStock?.stock?.isDeclined ??
                                        false
                                    ? AppColors.kFF9800
                                    : AppColors.k101C28,
                            borderWidth:
                                stockId.suggestedStock?.stock?.isDeclined ??
                                        false
                                    ? 1.w
                                    : min(4.w, 2),
                            borderRadius: 500.r,
                            backgroundColor:
                                stockId.suggestedStock?.stock?.isDeclined ??
                                        false
                                    ? AppColors.kFFF6EA
                                    : AppColors.kffffff,
                            buttonTextStyle: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color:
                                  stockId.suggestedStock?.stock?.isDeclined ??
                                          false
                                      ? AppColors.kFF9800
                                      : AppColors.k101C28,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (stockId.suggestedStock?.stock?.showError ?? false)
                      40.verticalSpace,
                    if (stockId.suggestedStock?.stock?.showError ?? false)
                      Text(
                        LocaleKeys.buy_requests_accept_or_decline_stock.tr,
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 35.sp,
                          color: AppColors.kff0000,
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
      );
}
