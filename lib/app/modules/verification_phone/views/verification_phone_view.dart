import 'package:diamond_company_app/app/ui/components/custom_back_button.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_style.dart';
import '../controllers/verification_phone_controller.dart';

/// VerificationPhoneView
class VerificationPhoneView extends GetView<VerificationPhoneController> {
  /// VerificationPhoneView
  const VerificationPhoneView({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: false,
        body: _buildBody(context),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SafeArea(
        child: Padding(
          padding: REdgeInsets.symmetric(horizontal: 60),
          child: FormBuilder(
            key: controller.fbKey,
            child: Column(
              children: <Widget>[
                5.verticalSpace,
                _buildHeaderPart(),
                100.verticalSpace,
                _buildText(),
                100.verticalSpace,
                _buildPinPut(),
                _buildCustomErrorMessage(context),
                76.verticalSpace,
                Obx(() => _buildAppButton()),
                76.verticalSpace,
                _buildBottomText(),
              ],
            ),
          ),
        ),
      );

  /// Build Bottom Text
  Widget _buildBottomText() => Obx(
        () => controller.timerSeconds.value == 0
            ? _buildResendButton(
                buttonText: LocaleKeys.verification_email_resend.tr,
                onPressed: () {
                  controller.resendOTPFunction();
                },
                backgroundColor: AppColors.kEAEFF4,
                borderColor: AppColors.kEAEFF4,
                isLoading: controller.resendIsLoading(),
                buttonTextColor: AppColors.k101C28,
              )
            : Text(
                '${LocaleKeys.verification_phone_resend_code_in_20_secs.tr}'
                    .replaceAll('20', '${controller.timerSeconds}'),
                textAlign: TextAlign.center,
                style: AppFontStyles.skolaSans(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.k101C28,
                ),
              ),
      );

  /// Build App Button
  AppButton _buildAppButton() => AppButton.text(
        isLoading: controller.isVerifyingOTP(),
        buttonText: LocaleKeys.verification_phone_continue.tr,
        onPressed: () => controller.onContinueButton(),
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: AppColors.k101C28,
        borderRadius: 24.r,
        borderColor: AppColors.k101C28,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: AppColors.kBEFFFC,
        ),
      );

  /// Build Custom Error Message
  Widget _buildCustomErrorMessage(BuildContext context) => Obx(
        () => AnimatedSize(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          child: Visibility(
            visible: controller.errorMessage.value.isNotEmpty,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: REdgeInsets.only(left: 44, top: 28),
                child: Text(
                  controller.errorMessage.value,
                  style: AppFontStyles.skolaSans(
                    color: Colors.red,
                    fontSize: 38.sp,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

  /// Build Pin Put
  Widget _buildPinPut() => Container(
        height: 150.h,
        width: Get.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        child: Pinput(
          controller: controller.otpController,
          onSubmitted: (String value) => controller.onContinueButton(),
          onCompleted: (String value) => controller.onContinueButton(),
          length: 6,
          preFilledWidget: Text(
            '-',
            style: TextStyle(
              color: AppColors.k101C28,
              fontSize: 45.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          defaultPinTheme: PinTheme(
            width: 100.w,
            textStyle: TextStyle(
              fontSize: 56.sp,
              color: AppColors.k101C28,
            ),
          ),
        ),
      );

  /// Build Text
  Widget _buildText() => Column(
        children: <Widget>[
          Text(
            LocaleKeys.verification_phone_email_verification.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 70.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
            textAlign: TextAlign.center,
          ),
          50.verticalSpace,
          Text(
            LocaleKeys.verification_phone_desc.tr,
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k70777E.withOpacity(0.6),
            ),
          ),
          20.verticalSpace,
          Text(
            controller.otpValue().email.toString().toLowerCase().trim() ?? '',
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );

  /// Build Header Part
  Widget _buildHeaderPart() => SizedBox(
        height: 200.h,
        width: Get.width,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: REdgeInsets.only(top: 45),
              child: CustomBackButton(
                onPressed: () => Get.back(),
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: SizedBox(
                height: 200.h,
                width: 200.w,
                child: AppImages.appLogo,
              ),
            ),
          ],
        ),
      );

  /// Build CommonAppButton
  Widget _buildResendButton({
    required String buttonText,
    void Function()? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
    bool? isLoading,
  }) =>
      AppButton.text(
        isLoading: isLoading ?? false,
        loaderColor: AppColors.k101C28,
        buttonText: buttonText,
        onPressed: onPressed ?? () {},
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );
}
