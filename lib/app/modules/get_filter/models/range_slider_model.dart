import 'package:flutter/material.dart';

class RangeSliderModel {
  double _min;
  double _max;
  RangeValues _rangeValues;

  RangeSliderModel({
    required double min,
    required double max,
  })  : _min = min,
        _max = max,
        _rangeValues = RangeValues(min, max); // Default range values

  // Getter and Setter for min
  double get min => _min;
  set min(double value) {
    _min = value;
  }

  // Getter and Setter for max
  double get max => _max;
  set max(double value) {
    _max = value;
  }

  // Getter and Setter for rangeValues
  RangeValues get rangeValues => _rangeValues;

  set setRangeValues(RangeValues value) {
    _rangeValues = value;
  }

  void clearRange() => _rangeValues = RangeValues(_min, _max);
}
