import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/filter_entity.dart';
import 'package:diamond_company_app/app/data/models/filter_repository.dart';
import 'package:diamond_company_app/app/data/models/sort_repo.dart';
import 'package:diamond_company_app/app/data/models/supplier/supplier_entity.dart';
import 'package:diamond_company_app/app/data/remote/services/marketplace_service.dart';
import 'package:diamond_company_app/app/modules/get_filter/controllers/get_filter_args.dart';
import 'package:diamond_company_app/app/modules/get_filter/models/range_slider_model.dart';
import 'package:diamond_company_app/app/modules/get_filter/views/carat_range_selector.dart';
import 'package:diamond_company_app/app/modules/get_filter/views/sort_selector.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/filter_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class GetFilterController extends GetxController {
  /// find instances of GetFilterController
  static GetFilterController get find => Get.find<GetFilterController>();

  final TextEditingController sizeFrom = TextEditingController();
  final TextEditingController sizeTo = TextEditingController();
  final TextEditingController stockId = TextEditingController();

  //ratio
  final TextEditingController minRatio = TextEditingController();
  final TextEditingController maxRatio = TextEditingController();

  //ration range value
  Rx<RangeSliderModel> ratioRange = RangeSliderModel(min: 0, max: 10).obs;

  //length range value
  Rx<RangeSliderModel> lengthRange = RangeSliderModel(min: 0, max: 1320.18).obs;

  //width range value
  Rx<RangeSliderModel> widthRange = RangeSliderModel(min: 0, max: 865.12).obs;

  //measurement depth range value
  Rx<RangeSliderModel> measurementDepthRange =
      RangeSliderModel(min: 0, max: 524.49).obs;

  //depth range value
  Rx<RangeSliderModel> depthRange = RangeSliderModel(min: 0, max: 100).obs;

  //table range value
  Rx<RangeSliderModel> tableRange = RangeSliderModel(min: 0, max: 100).obs;

  //length
  final TextEditingController minLength = TextEditingController();
  final TextEditingController maxLength = TextEditingController();

  //width
  final TextEditingController minWidth = TextEditingController();
  final TextEditingController maxWidth = TextEditingController();

  //measurement depth
  final TextEditingController minMeasurmentDepth = TextEditingController();
  final TextEditingController maxMeasurmentDepth = TextEditingController();

  //depth
  final TextEditingController minDepth = TextEditingController();
  final TextEditingController maxDepth = TextEditingController();

  //table
  final TextEditingController minTable = TextEditingController();
  final TextEditingController maxTable = TextEditingController();

  final ScrollController scrollController = ScrollController();

  // Size filter categories
  List<String> sizeCategories = FilterRepo.sizeOptions.keys.toList();

  final Rx<DiamondColor> diamondColor = DiamondColor.white.obs;

  final Rx<FilterEntity> appliedFilter = FilterEntity.empty.copyWith().obs;

  final Rx<SortOption> sort = SortOption(
    sortBy: SortBy.discounts,
    sortOrder: SortOrder.asc,
  ).obs;

  final RxBool isLoadingLocations = true.obs;

  final RxInt selectedindex = 0.obs;

  final RxInt selectedFilterIndex = 0.obs;

  final RxList<String> locations = <String>[].obs;

  late GetFilterArgs args;

  PageController pageController = PageController();

  @override
  void onReady() {
    if (Get.arguments is GetFilterArgs) {
      args = Get.arguments as GetFilterArgs;
      appliedFilter(args.filter);
      sizeFrom.text = args.sizeFrom;
      sizeTo.text = args.sizeTo;
      stockId.text = args.filter.certificationId;
      diamondColor(args.filter.colorType == 'white'
          ? DiamondColor.white
          : DiamondColor.fancy);
      sort(args.sort);
      selectedindex(diamondColor() == DiamondColor.white ? 0 : 1);
    }
    super.onReady();
  }

  @override
  void onInit() {
    _fetchLocations();
    super.onInit();
  }

  void refreshFilter() {
    appliedFilter.refresh();
  }

  void selectSizeCategory(String category, {bool fullEffect = false}) {
    if (fullEffect) {
      if (isSizeCategorySelected(category) &&
          ((FilterRepo.sizeOptions[category]
                      ?.every((item) => appliedFilter().size.contains(item)) ??
                  false) ||
              fullEffect)) {
        appliedFilter().size.removeWhere((element) =>
            FilterRepo.sizeOptions[category]?.contains(element) ?? false);
      } else {
        appliedFilter().size.addAll(FilterRepo.sizeOptions[category] ?? []);
      }
    } else {
      if (appliedFilter().size.contains(category)) {
        appliedFilter().size.remove(category);
      } else {
        appliedFilter().size.add(category);
      }
    }
    refreshFilter();
  }

  void selectWhiteColor(String color) {
    if (appliedFilter().whiteColor.contains(color)) {
      appliedFilter().whiteColor.remove(color);
    } else {
      appliedFilter().whiteColor.add(color);
    }
    refreshFilter();
  }

  void selectFancyColor(String color) {
    if (appliedFilter().fancyColor.contains(color)) {
      appliedFilter().fancyColor.remove(color);
    } else {
      appliedFilter().fancyColor.add(color);
    }
    refreshFilter();
  }

  void selectFancyIntensity(String color) {
    if (appliedFilter().fancyIntensity.contains(color)) {
      appliedFilter().fancyIntensity.remove(color);
    } else {
      appliedFilter().fancyIntensity.add(color);
    }
    refreshFilter();
  }

  void selectFancyOvertone(String color) {
    if (appliedFilter().fancyOvertone.contains(color)) {
      appliedFilter().fancyOvertone.remove(color);
    } else {
      appliedFilter().fancyOvertone.add(color);
    }
    refreshFilter();
  }

  bool isSizeCategorySelected(String category) {
    return appliedFilter().size.any((element) =>
        FilterRepo.sizeOptions[category]?.contains(element) ?? false);
  }

  bool? sizeCategorySelectedLevel(String category) {
    var selectedSizes = appliedFilter().size.where((element) =>
        FilterRepo.sizeOptions[category]?.contains(element) ?? false);

    if (selectedSizes.isEmpty) {
      return false;
    }

    return selectedSizes.length != FilterRepo.sizeOptions[category]?.length
        ? null
        : true;
  }

  /// Select carat range
  void selectCaratRange() {
    vibrate();
    Get.bottomSheet(
      CaratRangeSelector(this),
      isScrollControlled: true,
      enableDrag: true,
      ignoreSafeArea: false,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(4),
        ),
      ),
    );
  }

  /// Select unselect a supplier
  void selectSupplier(List<Supplier> suppliers) {
    appliedFilter().suppliers = suppliers;
    refreshFilter();
  }

  void selectLocation(List<String> countries) {
    appliedFilter().locations = countries;
    refreshFilter();
  }

  Future<void> _fetchLocations() async {
    isLoadingLocations(true);
    locations(await MarketplaceService.fetchLocations());
    locations.refresh();
    isLoadingLocations(false);
  }

  void selectSort({
    Function(SortOption sort)? onSelection,
  }) {
    vibrate();
    Get.dialog(
      SimpleDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24.r),
        ),
        children: <Widget>[
          SortSelector(
            sort: sort(),
          ),
        ],
        insetPadding: REdgeInsets.symmetric(horizontal: 60),
        surfaceTintColor: AppColors.kffffff,
        backgroundColor: AppColors.kffffff,
      ),
    ).then((value) {
      if (value is SortOption) {
        sort(value);
        sort.refresh();
        onSelection?.call(value);
      }
    });
  }

  void toggleHPHT(bool selection) {
    if (selection) {
      appliedFilter().treatmentTypes.add('hpht');
    } else {
      appliedFilter().treatmentTypes.remove('hpht');
    }
    refreshFilter();
  }

  void tripleEx() {
    appliedFilter().cut = ['id', 'ex'];
    appliedFilter().polish = ['id', 'ex'];
    appliedFilter().symmetry = ['id', 'ex'];
    refreshFilter();
  }

  void exMinus() {
    appliedFilter().cut = ['ex'];
    appliedFilter().polish = ['ex', 'vg'];
    appliedFilter().symmetry = ['ex', 'vg'];
    refreshFilter();
  }

  void vgPlus() {
    appliedFilter().cut = ['id', 'ex', 'vg'];
    appliedFilter().polish = ['ex', 'vg'];
    appliedFilter().symmetry = ['ex', 'vg'];
    refreshFilter();
  }

  void vgMinus() {
    appliedFilter().cut = ['vg', 'g'];
    appliedFilter().polish = ['vg', 'g'];
    appliedFilter().symmetry = ['vg', 'g'];
    refreshFilter();
  }

  void resetFilter() {
    ratioRange().clearRange();
    lengthRange().clearRange();
    widthRange().clearRange();
    measurementDepthRange().clearRange();
    ratioRange.refresh();
    lengthRange.refresh();
    widthRange.refresh();
    measurementDepthRange.refresh();
    minRatio.clear();
    maxRatio.clear();
    minLength.clear();
    maxLength.clear();
    minWidth.clear();
    maxWidth.clear();
    minMeasurmentDepth.clear();
    maxMeasurmentDepth.clear();
    depthRange().clearRange();
    tableRange().clearRange();
    depthRange.refresh();
    tableRange.refresh();
    minDepth.clear();
    maxDepth.clear();
    minTable.clear();
    maxTable.clear();

    appliedFilter().clearAll();

    sizeFrom.text = '';
    sizeTo.text = '';
    sort().sortBy = SortBy.discounts;
    sort().sortOrder = SortOrder.asc;
    sort.refresh();
    locations.refresh();
    refreshFilter();
  }

  Future<void> onFilter() async {
    appliedFilter().status.clear();
    appliedFilter().status.add('AVAILABLE');
    appliedFilter().diamondType.clear(); //lab_grown
    //appliedFilter().diamondType.add('cvd');
    //if (appliedFilter().diamondType.isNotEmpty) {

    var updatedArgs = GetFilterArgs(
      filter: appliedFilter(),
      sizeFrom: sizeFrom.text,
      sizeTo: sizeTo.text,
      sort: sort(),
    );
    await Get.toNamed(
      Routes.LISTING,
      arguments: updatedArgs,
    );
    // } else {
    //   appSnackbar(
    //     message: 'Please select the type of diamond to search',
    //     snackBarState: SnackBarState.INFO,
    //   );
    //   await scrollController.animateTo(
    //     0,
    //     duration: const Duration(milliseconds: 300),
    //     curve: Curves.easeInOut,
    //   );
    // }
  }

  /// set fancy color
  void setFancyColor() {
    appliedFilter().colorType = 'fancy';
    diamondColor(DiamondColor.fancy);
    appliedFilter().whiteColor.clear();
    diamondColor.refresh();
    refreshFilter();
  }

  /// set white color
  void setWhiteColor() {
    selectedindex(0);
    diamondColor(DiamondColor.white);
    appliedFilter().colorType = 'white';
    appliedFilter().fancyColor.clear();
    appliedFilter().fancyColor.clear();
    appliedFilter().fancyIntensity.clear();
    appliedFilter().fancyOvertone.clear();
    refreshFilter();
  }
}
