import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/filter_repository.dart';
import 'package:diamond_company_app/app/modules/get_filter/controllers/get_filter_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class CaratRangeSelector extends GetView<GetFilterController> {
  GetFilterController controller;

  CaratRangeSelector(this.controller, {super.key});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          30.verticalSpace,
          Row(
            children: [
              const Icon(
                Icons.numbers_outlined,
                color: AppColors.k101C28,
              ),
              16.horizontalSpace,
              Text(
                'Carat Range',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w900,
                  fontSize: 40.sp,
                  color: AppColors.k101C28,
                ),
              ),
            ],
          ),
          // const Divider(),
          30.verticalSpace,
          ListView.separated(
            shrinkWrap: true,
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.all(0),
            itemCount: FilterRepo.sizeOptions.keys.length,
            itemBuilder: (BuildContext context, int index) =>
                _buildSizeOptions(FilterRepo.sizeOptions.keys.toList()[index]),
            separatorBuilder: (BuildContext context, int index) =>
                const Divider(
              color: AppColors.k70777E,
              thickness: 0.2,
            ),
          ),
        ],
      ).paddingAll(8);

  Obx _buildSizeOptions(String e) => Obx(() {
        List<String> sizeOptions = FilterRepo.sizeOptions[e] ?? <String>[];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Row(
              children: <Widget>[
                Checkbox(
                  activeColor: AppColors.k101C28,
                  value: controller.sizeCategorySelectedLevel(e),
                  onChanged: (bool? value) {
                    vibrate();
                    controller.selectSizeCategory(
                      e,
                      fullEffect: true,
                    );
                  },
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  tristate: true,
                ),
                Text(
                  e,
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            SizedBox(
              width: Get.width,
              height: 40,
              child: ListView.builder(
                padding: const EdgeInsets.only(
                  right: 8,
                ),
                scrollDirection: Axis.horizontal,
                itemCount: sizeOptions.length,
                itemBuilder: (BuildContext context, int index) {
                  RxBool isSelected = controller
                      .appliedFilter()
                      .size
                      .contains(sizeOptions[index])
                      .obs;
                  return FilterChip(
                    selectedColor: AppColors.kBEFFFC,
                    label: Text(
                      sizeOptions[index],
                    ),
                    side: BorderSide(
                      color: isSelected()
                          ? AppColors.k1A47E8
                          : AppColors.k70777E.withOpacity(0.5),
                      width: 0.5.w,
                    ),
                    selected: isSelected(),
                    //side: chipBorderSide(isSelected),
                    showCheckmark: false,
                    onSelected: (bool value) {
                      vibrate();
                      controller.selectSizeCategory(sizeOptions[index]);
                    },
                  ).paddingOnly(right: 5);
                },
              ),
            ),
          ],
        );
      });
}
