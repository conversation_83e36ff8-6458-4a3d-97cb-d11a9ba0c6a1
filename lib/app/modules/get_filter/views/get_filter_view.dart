import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/app_styles.dart';
import 'package:diamond_company_app/app/data/models/diamond_type.dart';
import 'package:diamond_company_app/app/data/models/filter_entity.dart';
import 'package:diamond_company_app/app/data/models/filter_repository.dart';
import 'package:diamond_company_app/app/data/models/supplier/supplier_entity.dart';
import 'package:diamond_company_app/app/modules/get_filter/controllers/get_filter_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/card_title.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/input_formatters.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:diamond_company_app/app/utils/validate_decimal.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

/// get filter view
class GetFilterView extends GetView<GetFilterController> {
  /// get filter view
  const GetFilterView({required this.viewTag, super.key});

  final String viewTag;

  @override
  String get tag => viewTag;

  @override
  GetFilterController get controller =>
      Get.put(GetFilterController(), tag: viewTag);

  @override
  Widget build(BuildContext context) {
    controller.diamondColor(
      controller.appliedFilter().colorType == 'white'
          ? DiamondColor.white
          : DiamondColor.fancy,
    );
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(context),
      bottomNavigationBar: _buildFilterActionsButtons(),
    );
  }

  CommonAppbar _buildAppBar() => CommonAppbar(
        onBackPressed: () => Get.back(),
        centerTitle: true,
        title: Text(
          'Filter',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        actions: <Widget>[
          IconButton.filled(
            constraints: BoxConstraints(
              maxHeight: 120.h,
              maxWidth: 120.w,
            ),
            style: OutlinedButton.styleFrom(
              backgroundColor: AppColors.kBEFFFC,
              shape: const CircleBorder(),
              padding: EdgeInsets.zero,
              fixedSize: Size(120.w, 120.h),
            ),
            onPressed: controller.selectSort,
            icon: SizedBox(
              width: 79.w,
              height: 64.h,
              child: AppImages.sort,
            ),
          ),
        ],
      );

  Obx _buildBody(BuildContext context) => Obx(
        () => ListView(
          controller: controller.scrollController,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          children: <Widget>[
            _buildBasicSpecs(context),

            10.verticalSpace,
            _buildDiamondPref(),
            // const SizedBox(
            //   height: 10,
            // ),
            40.verticalSpace,
            _buildAvailabilityPref(),
            const SizedBox(height: 8),
          ],
        ),
      );

  Widget _buildAvailabilityPref() => Column(
        children: <Widget>[
          _buildTitle(
            title: 'Availability Preference',
          ),
          40.verticalSpace,
          //const Divider(),
          const SizedBox(
            height: 6,
          ),
          // _buildSupplierSearch(),
          // const SizedBox(
          //   height: 12,
          // ),
          _buildLocationSelection(),
          const SizedBox(
            height: 10,
          ),
          AppTextFormField(
            name: 'q',
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(24.r),
              borderSide: BorderSide(
                width: 1.w,
                color: AppColors.k70777E,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(24.r),
              borderSide: BorderSide(
                width: 1.w,
                color: AppColors.k70777E,
              ),
            ),
            onChange: (String? value) {
              controller.appliedFilter().certificationId = value ?? '';
            },
            prefixIconConstraints: BoxConstraints(minWidth: 140.w),
            prefixIcon: const Icon(
              Icons.manage_search_outlined,
            ),
            hintText: 'Certificate# / Stock ID / SKU',
            labelText: 'Certificate# / Stock ID / SKU',
            floatingLabelBehavior: FloatingLabelBehavior.never,
            labelTextStyle: AppFontStyles.skolaSans(
              color: AppColors.k70777E,
              fontSize: 45.sp,
              fontWeight: FontWeight.w400,
            ),
            hintTextStyle: AppFontStyles.skolaSans(
              color: AppColors.k70777E,
              fontSize: 45.sp,
              fontWeight: FontWeight.w400,
            ),
            style: AppFontStyles.skolaSans(
              color: AppColors.k101C28,
              fontSize: 45.sp,
              fontWeight: FontWeight.w400,
            ),
            textCapitalization: TextCapitalization.characters,
            controller: controller.stockId,
            suffixIcon: IconButton(
              onPressed: () {
                controller.stockId.clear();
                controller.appliedFilter().certificationId = '';
                controller.refreshFilter();
              },
              icon: const Icon(
                Icons.clear,
              ),
            ).paddingAll(4),
          ).paddingSymmetric(
            horizontal: 12,
            vertical: 4,
          ),
          // _buildFilterChips(
          //   onTap: (value) {
          //     controller.appliedFilter().status.add(value);
          //     controller.refreshFilter();
          //   },
          //   title: 'Status',
          //   values: filterEntity.status,
          //   selectedList: controller.appliedFilter().status,
          //   onClearAll: () {
          //     controller.appliedFilter().status.clear();
          //     controller.refreshFilter();
          //   },
          //   onDelete: (value) {
          //     controller.appliedFilter().status.removeWhere(
          //           (e) => value == e,
          //         );
          //     controller.refreshFilter();
          //   },
          // ),
          const SizedBox(height: 12),
        ],
      );

  Column _buildDiamondPref() => Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildTitle(
            title: 'Pref.',
            icon: Icons.format_shapes_outlined,
            trailing: SizedBox(
              height: 100.h,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  ActionChip(
                    label: const Text(
                      '3EX',
                      textAlign: TextAlign.center,
                    ),
                    labelPadding: REdgeInsets.symmetric(horizontal: 18),
                    padding: EdgeInsets.zero,
                    onPressed: controller.tripleEx,
                  ),
                  18.horizontalSpace,
                  ActionChip(
                    label: const Text(
                      'EX-',
                    ),
                    labelPadding: REdgeInsets.symmetric(horizontal: 18),
                    padding: EdgeInsets.zero,
                    onPressed: controller.exMinus,
                  ),
                  18.horizontalSpace,
                  ActionChip(
                    label: const Text(
                      'VG+',
                    ),
                    labelPadding: REdgeInsets.symmetric(horizontal: 18),
                    padding: EdgeInsets.zero,
                    onPressed: controller.vgPlus,
                  ),
                  18.horizontalSpace,
                  ActionChip(
                    label: const Text(
                      'VG-',
                    ),
                    labelPadding: REdgeInsets.symmetric(horizontal: 18),
                    padding: EdgeInsets.zero,
                    onPressed: controller.vgMinus,
                  ),
                ],
              ),
            ),
          ),
          // const Divider(),
          20.verticalSpace,
          _buildFilterChips(
            onTap: (String value) {
              controller.appliedFilter().cut.add(value);
              controller.refreshFilter();
            },
            title: 'Cut',
            values: filterEntity.cut,
            selectedList: controller.appliedFilter().cut,
            onClearAll: () {
              controller.appliedFilter().cut.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().cut.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          _buildFilterChips(
            onTap: (value) {
              controller.appliedFilter().polish.add(value);
              controller.refreshFilter();
            },
            title: 'Polish',
            values: filterEntity.polish,
            selectedList: controller.appliedFilter().polish,
            onClearAll: () {
              controller.appliedFilter().polish.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().polish.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          _buildFilterChips(
            onTap: (value) {
              controller.appliedFilter().symmetry.add(value);
              controller.refreshFilter();
            },
            title: 'Symmetry',
            values: filterEntity.symmetry,
            selectedList: controller.appliedFilter().symmetry,
            onClearAll: () {
              controller.appliedFilter().symmetry.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().symmetry.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          _buildFilterChips(
            onTap: (value) {
              controller.appliedFilter().fluorescenceIntensity.add(value);
              controller.refreshFilter();
            },
            title: 'Fluorescence Intensity',
            values: filterEntity.fluorescenceIntensity,
            selectedList: controller.appliedFilter().fluorescenceIntensity,
            onClearAll: () {
              controller.appliedFilter().fluorescenceIntensity.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().fluorescenceIntensity.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          _buildFilterChips(
            onTap: (value) {
              controller.appliedFilter().lab.add(value);
              controller.refreshFilter();
            },
            title: 'Lab',
            values: filterEntity.lab,
            selectedList: controller.appliedFilter().lab,
            onClearAll: () {
              controller.appliedFilter().lab.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().lab.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          // if (controller
          //     .appliedFilter()
          //     .diamondType
          //     .contains(DiamondType.labGrown))
          _buildFilterChips(
            onTap: (value) {
              controller.appliedFilter().growthType.add(value);
              controller.refreshFilter();
            },
            title: 'Growth Type',
            values: filterEntity.growthType,
            selectedList: controller.appliedFilter().growthType,
            onClearAll: () {
              controller.appliedFilter().growthType.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().growthType.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
          if (controller
              .appliedFilter()
              .diamondType
              .contains(DiamondType.natural)) ...[
            40.verticalSpace,
            _buildTitle(
              title: 'Treatments',
            ),
            40.verticalSpace,
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.shade300,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  SwitchListTile(
                    value: controller.appliedFilter().isHPHT,
                    onChanged: controller.toggleHPHT,
                    dense: true,
                    activeColor: AppColors.kBEFFFC,
                    thumbColor: MaterialStateProperty.resolveWith(
                        (Set<MaterialState> states) => AppColors.k101C28),
                    contentPadding: const EdgeInsets.only(left: 12, right: 8),
                    title: Text(
                      'HPHT',
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          //const SizedBox(height: 12),
        ],
      );

  Widget _buildFilterActionsButtons() => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(Get.context!).padding.bottom),
        child: Row(
          children: <Widget>[
            60.horizontalSpace,
            Expanded(
              child: AppButton.text(
                buttonText: 'Reset',
                onPressed: controller.resetFilter,
                buttonSize: Size(Get.width / 2, 150.h),
                backgroundColor: AppColors.kEAEFF4,
                borderColor: AppColors.kEAEFF4,
                buttonTextStyle: TextStyle(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k101C28,
                ),
              ),
            ),
            30.horizontalSpace,
            Expanded(
              child: AppButton.text(
                buttonText: 'Apply',
                onPressed: controller.onFilter,
                buttonSize: Size(Get.width / 2, 150.h),
                backgroundColor: AppColors.k101C28,
                borderColor: AppColors.k101C28,
                borderRadius: 24.r,
                buttonTextStyle: TextStyle(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.kBEFFFC,
                ),
              ),
            ),
            60.horizontalSpace,
          ],
        ).paddingOnly(bottom: 60.h),
      );

  Widget _buildBasicSpecs(BuildContext context) => Obx(() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          // _buildTitle(
          //   icon: Icons.shape_line,
          //   title: 'Basic Specifications',
          //   /*trailing: GestureDetector(
          //       onTap: () {
          //         controller.appliedFilter().diamondType.clear();
          //         controller.refreshFilter();
          //       },
          //       child: const Icon(
          //         Icons.clear,
          //         color: AppColors.kFF9800,
          //       ),
          //     ),*/
          // ),
          // const Divider(),
          // SizedBox(
          //   width: context.width,
          //   child: SegmentedButton(
          //     segments: [
          //       ButtonSegment<String>(
          //         value: DiamondType.natural,
          //         label: Text(
          //           'Natural',
          //           style: GoogleFonts.monda(
          //             fontSize: 16,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //       ButtonSegment<String>(
          //         value: DiamondType.labGrown,
          //         label: Text(
          //           'Lab Grown',
          //           style: GoogleFonts.monda(
          //             fontSize: 16,
          //             fontWeight: FontWeight.w500,
          //           ),
          //         ),
          //       ),
          //     ],
          //     selected: controller.appliedFilter().diamondType.toSet(),
          //     emptySelectionAllowed: true,
          //     selectedIcon: const Icon(
          //       Icons.check_circle,
          //     ),
          //     onSelectionChanged: (value) {
          //       controller.appliedFilter().diamondType = value.toList();
          //       controller.refreshFilter();
          //     },
          //     style: ButtonStyle(
          //       shape: const MaterialStatePropertyAll(
          //         RoundedRectangleBorder(
          //           borderRadius: BorderRadius.all(
          //             Radius.circular(8),
          //           ),
          //         ),
          //       ),
          //       side: MaterialStateProperty.resolveWith((states) {
          //         if (states.contains(MaterialState.selected)) {
          //           return const BorderSide(
          //             color: AppColors.k1A47E8,
          //           );
          //         }
          //         return BorderSide(
          //           color: controller
          //                   .appliedFilter()
          //                   .diamondType
          //                   .contains(DiamondType.labGrown)
          //               ? AppColors.k128807
          //               : AppColors.k1A47E8,
          //         );
          //       }),
          //       backgroundColor: MaterialStateProperty.resolveWith((states) {
          //         if (states.contains(MaterialState.selected)) {
          //           return (controller
          //                       .appliedFilter()
          //                       .diamondType
          //                       .contains(DiamondType.labGrown)
          //                   ? AppColors.k128807
          //                   : AppColors.k1A47E8)
          //               .withOpacity(0.3);
          //         }
          //         return null;
          //       }),
          //     ),
          //   ),
          // ).paddingOnly(
          //   top: 12,
          //   right: 12,
          //   left: 12,
          // ),

          _buildTitle(
            title: 'Shape',
            onClearAll: () {
              controller.appliedFilter().shape.clear();
              controller.refreshFilter();
            },
          ),
          20.verticalSpace,
          shapeOptions(
            selected: controller.appliedFilter().shape,
            onChange: (List<String> shapes) {
              vibrate();
              controller.appliedFilter().shape = shapes;
              controller.refreshFilter();
            },
          ),
          ..._buildCaratOptions(),
          //const Divider(),
          _buildColorOptions(),
          //const Divider(),
          _buildFilterChips(
            onTap: (String value) {
              controller.appliedFilter().clarity.add(value);
              controller.refreshFilter();
            },
            title: 'Clarity',
            values: filterEntity.clarity,
            selectedList: controller.appliedFilter().clarity,
            onClearAll: () {
              controller.appliedFilter().clarity.clear();
              controller.refreshFilter();
            },
            onDelete: (value) {
              controller.appliedFilter().clarity.removeWhere(
                    (e) => value == e,
                  );
              controller.refreshFilter();
            },
          ),
        ],
      ).paddingOnly(
        bottom: 12,
      ));

  // Widget _buildSupplierSearch() => Obx(
  //       () => DropdownSearch<Supplier>.multiSelection(
  //         asyncItems: MarketplaceService.searchSuppliers,
  //         compareFn: (item1, item2) => item1.id == item2.id,
  //         itemAsString: (item) => item.name ?? item.id ?? '',
  //         onChanged: controller.selectSupplier,
  //         selectedItems: controller.appliedFilter().suppliers,
  //         dropdownBuilder: (context, selectedItems) => AnimatedSize(
  //           duration: const Duration(milliseconds: 300),
  //           child: AnimatedCrossFade(
  //             duration: const Duration(milliseconds: 300),
  //             crossFadeState: selectedItems.isNotEmpty
  //                 ? CrossFadeState.showSecond
  //                 : CrossFadeState.showFirst,
  //             firstChild: _buildTitle(
  //               title: 'Select Suppliers',
  //               padding: EdgeInsets.zero,
  //             ),
  //             secondChild: Wrap(
  //               spacing: 4,
  //               children: selectedItems
  //                   .map(
  //                     (item) => InputChip(
  //                       padding: const EdgeInsets.all(0),
  //                       labelPadding: const EdgeInsets.symmetric(horizontal: 8),
  //                       selected: true,
  //                       showCheckmark: false,
  //                       onDeleted: () {
  //                         selectedItems.removeWhere(
  //                           (e) => item.id == e.id,
  //                         );
  //                         controller.appliedFilter().suppliers = selectedItems;
  //                         controller.refreshFilter();
  //                       },
  //                       label: Text(
  //                         item.name ?? item.id ?? '',
  //                         style: GoogleFonts.montserrat(
  //                           fontSize: 12,
  //                           fontWeight: FontWeight.w500,
  //                         ),
  //                       ),
  //                     ),
  //                   )
  //                   .toList(),
  //             ),
  //           ),
  //         ),
  //         dropdownButtonProps: const DropdownButtonProps(
  //           icon: Icon(
  //             Icons.expand_circle_down,
  //           ),
  //           isVisible: false,
  //         ),
  //         dropdownDecoratorProps: DropDownDecoratorProps(
  //           dropdownSearchDecoration: _buildInputDecoration().copyWith(
  //             hintText: 'Select Suppliers',
  //             prefixIcon: const Icon(
  //               Icons.supervised_user_circle,
  //             ),
  //           ),
  //         ),
  //         popupProps: PopupPropsMultiSelection.dialog(
  //           searchFieldProps: TextFieldProps(
  //             decoration: _buildInputDecoration(),
  //             autofocus: true,
  //           ),
  //           isFilterOnline: true,
  //           showSearchBox: true,
  //           title: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               const SizedBox(height: 16),
  //               Row(
  //                 children: [
  //                   const SizedBox(width: 16),
  //                   const Icon(
  //                     Icons.supervised_user_circle,
  //                     color: AppColors.k1A47E8,
  //                   ),
  //                   const SizedBox(width: 16),
  //                   Text(
  //                     'Select Suppliers',
  //                     style: GoogleFonts.montserrat(
  //                       fontWeight: FontWeight.bold,
  //                       fontSize: 16,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               const SizedBox(height: 8),
  //               const Divider(),
  //             ],
  //           ),
  //           dialogProps: DialogProps(
  //             shape: RoundedRectangleBorder(
  //               borderRadius: BorderRadius.circular(8),
  //             ),
  //             backgroundColor: Get.theme.cardColor,
  //           ),
  //           loadingBuilder: (context, searchEntry) =>  AppLoader(),
  //           emptyBuilder: (context, searchEntry) => Center(
  //             child: Text(
  //               searchEntry.isNotEmpty
  //                   ? 'No Supplier Found for "$searchEntry"'
  //                   : 'Search for Suppliers by Name and get diamonds only from them.',
  //               textAlign: TextAlign.center,
  //             ).paddingAll(12),
  //           ),
  //           itemBuilder: (context, item, isSelected) =>
  //               _buildSupplierTile(item),
  //         ),
  //       ).paddingSymmetric(horizontal: 12),
  //     );

  Widget _buildLocationSelection() => Obx(
        () => DropdownSearch<String>.multiSelection(
          items: controller.locations(),
          onChanged: controller.selectLocation,
          selectedItems: controller.appliedFilter().locations,
          dropdownBuilder: (BuildContext context, List<String> selectedItems) =>
              AnimatedSize(
            duration: const Duration(milliseconds: 300),
            child: AnimatedCrossFade(
              duration: const Duration(milliseconds: 300),
              crossFadeState: selectedItems.isNotEmpty
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              firstChild: _buildTitle(
                title: 'Select Diamond Location',
                padding: EdgeInsets.zero,
              ),
              secondChild: Wrap(
                spacing: 4,
                children: selectedItems.map(
                  (String item) {
                    final Country? country =
                        CountryParser.tryParseCountryName(item);
                    return InputChip(
                      padding: const EdgeInsets.all(0),
                      labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                      selected: true,
                      backgroundColor: AppColors.kBEFFFC,
                      surfaceTintColor: AppColors.kBEFFFC,
                      color: const MaterialStatePropertyAll<Color>(
                          AppColors.kBEFFFC),
                      showCheckmark: false,
                      onDeleted: () {
                        selectedItems.removeWhere(
                          (e) => item == e,
                        );
                        controller.appliedFilter().locations = selectedItems;
                        controller.refreshFilter();
                      },
                      label: Text(
                        country?.countryCode ?? item.wordCapital,
                        style: GoogleFonts.montserrat(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  },
                ).toList(),
              ),
            ),
          ),
          dropdownButtonProps: const DropdownButtonProps(
            icon: Icon(Icons.expand_circle_down),
            isVisible: false,
          ),
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: _buildInputDecoration().copyWith(
              hintText: controller.isLoadingLocations()
                  ? 'Loading Locations'
                  : 'Select Location',
              prefixIcon: const Icon(
                Icons.location_on,
              ),
            ),
          ),
          popupProps: PopupPropsMultiSelection.dialog(
            searchFieldProps: TextFieldProps(
              decoration: _buildInputDecoration().copyWith(
                hintText: 'Search Location',
                fillColor: AppColors.kffffff,
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    color: AppColors.k101C28,
                    width: 1.w,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    color: AppColors.k101C28,
                    width: 1.w,
                  ),
                ),
              ),
              autofocus: true,
            ),
            showSearchBox: true,
            title: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const SizedBox(height: 16),
                Row(
                  children: [
                    const SizedBox(width: 16),
                    const Icon(
                      Icons.location_on,
                      color: AppColors.k101C28,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'Select Diamond Location',
                      style: GoogleFonts.montserrat(
                        fontWeight: FontWeight.w500,
                        fontSize: 40.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Divider(),
              ],
            ),
            dialogProps: DialogProps(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              backgroundColor: AppColors.kffffff,
            ),
            emptyBuilder: (BuildContext context, String searchEntry) => Center(
              child: Text(
                searchEntry.isNotEmpty
                    ? 'No Location Found for "$searchEntry"'
                    : 'Search for Location by country name and get diamonds available only in those locations.',
                textAlign: TextAlign.center,
              ).paddingAll(12),
            ),
            itemBuilder: (BuildContext context, String item, bool isSelected) =>
                _buildLocationTile(item),
          ),
        ).paddingSymmetric(horizontal: 12),
      );

  ListTile _buildSupplierTile(Supplier item) => ListTile(
        dense: true,
        title: Text(
          item.name ?? item.id ?? '',
          style: GoogleFonts.montserrat(
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: Container(
          height: 30,
          width: 30,
          decoration: BoxDecoration(
            color: item.getColor(),
            borderRadius: BorderRadius.circular(4),
          ),
          alignment: Alignment.center,
          child: Text(
            item.name?.substring(0, 1).toUpperCase() ?? '',
            style: GoogleFonts.montserrat(
              fontWeight: FontWeight.bold,
              color: AppColors.kffffff,
              fontSize: 14,
            ),
          ),
        ),
      );

  ListTile _buildLocationTile(String item) {
    final Country? country = CountryParser.tryParseCountryName(item);
    final String name = country?.name ?? item;
    return ListTile(
      dense: true,
      title: Text(
        name,
        style: GoogleFonts.montserrat(
          fontWeight: FontWeight.bold,
        ),
      ),
      leading: country?.flagEmoji != null
          ? Text(
              country?.flagEmoji ?? name.substring(0, 1).toUpperCase(),
              style: const TextStyle(
                fontSize: 24,
              ),
            )
          : CircleAvatar(
              maxRadius: 14,
              backgroundColor: AppColors.k128807,
              child: Text(
                name.substring(0, 1).toUpperCase(),
                style: GoogleFonts.montserrat(
                  fontWeight: FontWeight.bold,
                  color: AppColors.kffffff,
                ),
              ),
            ),
    );
  }

  Widget _buildBottomButtons() => Card(
        surfaceTintColor: AppColors.k000000,
        color: AppColors.k000000,
        elevation: 3,
        shape: const OutlineInputBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          borderSide: BorderSide.none,
        ),
        margin: EdgeInsets.zero,
        child: Row(
          children: [
            Expanded(
              child: AppButton.icon(
                onPressed: controller.resetFilter,
                prefixIcon: const Icon(CupertinoIcons.refresh_circled_solid),
                buttonText: 'Reset',
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: AppButton.icon(
                onPressed: controller.onFilter,
                prefixIcon: const Icon(Icons.search),
                buttonText: 'Search',
                backgroundColor: AppColors.k1A47E8,
              ),
            ),
          ],
        ).paddingAll(10),
      );

  Widget _buildFilterChips({
    required String title,
    required List<String> values,
    required List<String> selectedList,
    required Function(String value) onTap,
    required Function(String value) onDelete,
    required void Function() onClearAll,
  }) =>
      SizedBox(
        height: 285.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildTitle(
              title: title,
              onClearAll: onClearAll,
            ),
            40.verticalSpace,
            Expanded(
              child: ListView.separated(
                padding: REdgeInsets.symmetric(horizontal: 40),
                itemCount: values.length,
                scrollDirection: Axis.horizontal,
                separatorBuilder: (BuildContext context, int index) =>
                    30.horizontalSpace,
                itemBuilder: (BuildContext context, int index) {
                  var value = values[index];
                  return FilterChip(
                    selectedColor: AppColors.kBEFFFC,
                    shadowColor: AppColors.k000000.withOpacity(0.1),
                    elevation: 3,
                    surfaceTintColor: AppColors.kffffff,
                    showCheckmark: false,
                    onSelected: (bool selected) {
                      vibrate();
                      if (selected) {
                        onTap(value);
                      } else {
                        onDelete(value);
                      }
                    },
                    label: Text(
                      value.length == 1
                          ? ' ${value.toUpperCase()} '
                          : value.toUpperCase(),
                    ),
                    side: BorderSide(
                      color: selectedList.contains(value)
                          ? AppColors.k1A47E8
                          : AppColors.k70777E.withOpacity(0.5),
                      width: 0.5.w,
                    ),
                    selected: selectedList.contains(value),
                  );
                },
              ),
            ),
          ],
        ),
      ).paddingSymmetric(vertical: 25.h);

  List<Widget> _buildCaratOptions() => <Widget>[
        _buildTitle(
          title: 'Carat',
          onClearAll: () {
            controller.sizeFrom.clear();
            controller.sizeTo.clear();
            controller.appliedFilter().size.clear();
            controller.refreshFilter();
          },
        ),
        40.verticalSpace,
        Row(
          children: <Widget>[
            Expanded(
              child: AppTextFormField(
                name: 'size_from',
                controller: controller.sizeFrom,
                labelText: 'From',
                labelTextStyle: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 45.sp,
                  color: AppColors.k70777E,
                ),
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 45.sp,
                  color: AppColors.k101C28,
                ),
                suffixText: 'Ct.',
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  DecimalTextInputFormatter(decimalRange: 2),
                  twoDecimal,
                ],
              ),
            ),
            40.horizontalSpace,
            Expanded(
              child: AppTextFormField(
                name: 'size_to',
                controller: controller.sizeTo,
                hintText: 'To',
                suffixText: 'Ct.',
                labelText: 'To',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 45.sp,
                  color: AppColors.k101C28,
                ),
                labelTextStyle: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 45.sp,
                  color: AppColors.k70777E,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  DecimalTextInputFormatter(decimalRange: 2),
                  twoDecimal,
                ],
              ),
            ),
          ],
        ).paddingOnly(
          top: 4,
          right: 8,
          left: 8,
        ),
        40.verticalSpace,
        SizedBox(
          height: 150.h,
          child: Stack(
            alignment: Alignment.centerRight,
            children: <Widget>[
              ListView.separated(
                padding: REdgeInsets.only(
                  right: 154,
                  left: 28,
                ),
                scrollDirection: Axis.horizontal,
                itemBuilder: (BuildContext context, int index) {
                  var category = controller.sizeCategories[index];
                  RxBool isSelected =
                      controller.isSizeCategorySelected(category).obs;
                  return FilterChip(
                    /*selectedColor: ThemeProvider.isDarkModeOn
                      ? const Color(0xFF171f36)
                      : AppColors.k1A47E8.withOpacity(0.1),*/
                    shadowColor: AppColors.k000000.withOpacity(0.1),
                    elevation: 3,
                    color: MaterialStatePropertyAll<Color>(
                      isSelected() ? AppColors.kBEFFFC : AppColors.kffffff,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      side: BorderSide(
                        color: isSelected()
                            ? AppColors.k1A47E8
                            : AppColors.k70777E.withOpacity(0.5),
                        width: 0.5.w,
                      ),
                    ),
                    showCheckmark: false,
                    surfaceTintColor:
                        isSelected() ? AppColors.kBEFFFC : AppColors.kffffff,
                    backgroundColor:
                        isSelected() ? AppColors.kBEFFFC : AppColors.kffffff,
                    side: BorderSide(
                      color: isSelected()
                          ? AppColors.k1A47E8
                          : AppColors.k70777E.withOpacity(0.5),
                      width: 0.5.w,
                    ),
                    label: Text(
                      category,
                    ),
                    onSelected: (bool value) {
                      vibrate();
                      controller.selectSizeCategory(category, fullEffect: true);
                    },
                    selected: isSelected(),
                  );
                },
                separatorBuilder: (BuildContext context, int index) =>
                    30.horizontalSpace,
                itemCount: controller.sizeCategories.length,
              ),
              Container(
                decoration: BoxDecoration(
                  color: Get.theme.cardColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(100),
                    bottomLeft: Radius.circular(100),
                  ),
                ),
                child: IconButton.filledTonal(
                  style: IconButton.styleFrom(
                    backgroundColor: AppColors.k101C28,
                  ),
                  onPressed: controller.selectCaratRange,
                  icon: const Icon(
                    Icons.add,
                    color: AppColors.kffffff,
                  ),
                ),
              ),
            ],
          ),
        ),
      ];

  Widget _buildColorOptions() => AnimatedSize(
        duration: const Duration(milliseconds: 300),
        alignment: Alignment.topCenter,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitle(
              title: 'Color',
              onClearAll: () {
                if (controller.diamondColor() == DiamondColor.white) {
                  controller.appliedFilter().whiteColor.clear();
                } else {
                  controller.appliedFilter().fancyColor.clear();
                  controller.appliedFilter().fancyIntensity.clear();
                  controller.appliedFilter().fancyOvertone.clear();
                }
                controller.refreshFilter();
              },
            ),
            50.verticalSpace,
            Container(
              padding: REdgeInsets.all(10),
              margin: REdgeInsets.symmetric(horizontal: 30),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(500.r),
                border: Border.all(
                  color: AppColors.k101C28,
                  width: 4.w,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  _buildSegment('White', 0),
                  _buildSegment('Fancy', 1),
                ],
              ),
            ),
            40.verticalSpace,
            Visibility(
              visible: controller.diamondColor() == DiamondColor.white,
              child: SizedBox(
                height: 140.h,
                child: ListView.separated(
                  padding: REdgeInsets.symmetric(horizontal: 35),
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int index) {
                    var color = filterEntity.whiteColor[index];
                    var isSelected =
                        controller.appliedFilter().whiteColor.contains(color);
                    return FilterChip(
                      selectedColor: AppColors.kBEFFFC,
                      showCheckmark: false,
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.k1A47E8
                            : AppColors.k70777E.withOpacity(0.5),
                        width: 0.5.w,
                      ),
                      shadowColor: AppColors.k000000.withOpacity(0.1),
                      elevation: 3,
                      color: MaterialStatePropertyAll<Color>(
                        isSelected ? AppColors.kBEFFFC : AppColors.kffffff,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        side: BorderSide(
                          color: isSelected
                              ? AppColors.k1A47E8
                              : AppColors.k70777E.withOpacity(0.5),
                          width: 0.5.w,
                        ),
                      ),
                      label: Text(
                        ' ${color.toUpperCase()} ',
                      ),
                      onSelected: (bool value) {
                        vibrate();
                        controller.selectWhiteColor(color);
                      },
                      selected: isSelected,
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) =>
                      30.horizontalSpace,
                  itemCount: filterEntity.whiteColor.length,
                ),
              ),
            ),
            Visibility(
              visible: controller.diamondColor() == DiamondColor.fancy,
              child: SizedBox(
                height: 140.h,
                child: ListView.separated(
                  padding: REdgeInsets.symmetric(horizontal: 35),
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (BuildContext context, int index) {
                    var color = filterEntity.fancyColor[index];
                    var isSelected =
                        controller.appliedFilter().fancyColor.contains(color);
                    return FilterChip(
                      selectedColor: AppColors.kBEFFFC,
                      showCheckmark: false,
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.k1A47E8
                            : AppColors.k70777E.withOpacity(0.5),
                        width: 0.5.w,
                      ),
                      shadowColor: AppColors.k000000.withOpacity(0.1),
                      elevation: 3,
                      color: MaterialStatePropertyAll<Color>(
                        isSelected ? AppColors.kBEFFFC : AppColors.kffffff,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        side: BorderSide(
                          color: isSelected
                              ? AppColors.k1A47E8
                              : AppColors.k70777E.withOpacity(0.5),
                          width: 0.5.w,
                        ),
                      ),
                      label: Text(
                        color.toUpperCase(),
                      ),
                      onSelected: (bool value) {
                        vibrate();
                        controller.selectFancyColor(color);
                      },
                      selected: isSelected,
                    );
                  },
                  separatorBuilder: (BuildContext context, int index) =>
                      30.horizontalSpace,
                  itemCount: filterEntity.fancyColor.length,
                ),
              ),
            ),
            40.verticalSpace,
            Visibility(
              visible: controller.diamondColor() == DiamondColor.fancy,
              child: SizedBox(
                height: 250.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Intensity'.toUpperCase(),
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w900,
                        fontSize: 40.sp,
                        color: AppColors.k101C28,
                      ),
                    ).paddingOnly(left: 8),
                    40.verticalSpace,
                    Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          var color = filterEntity.fancyIntensity[index];
                          var isSelected = controller
                              .appliedFilter()
                              .fancyIntensity
                              .contains(color);
                          return FilterChip(
                            selectedColor: AppColors.kBEFFFC.withOpacity(0.1),
                            showCheckmark: false,
                            side: BorderSide(
                              color: isSelected
                                  ? AppColors.k1A47E8
                                  : AppColors.k70777E.withOpacity(0.5),
                              width: 0.5.w,
                            ),
                            shadowColor: AppColors.k000000.withOpacity(0.1),
                            elevation: 3,
                            color: MaterialStatePropertyAll<Color>(
                              isSelected
                                  ? AppColors.kBEFFFC
                                  : AppColors.kffffff,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24.r),
                              side: BorderSide(
                                color: isSelected
                                    ? AppColors.k1A47E8
                                    : AppColors.k70777E.withOpacity(0.5),
                                width: 0.5.w,
                              ),
                            ),
                            label: Text(
                              color.toUpperCase(),
                            ),
                            onSelected: (bool value) {
                              vibrate();
                              controller.selectFancyIntensity(color);
                            },
                            selected: isSelected,
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) =>
                            30.horizontalSpace,
                        itemCount: filterEntity.fancyIntensity.length,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            40.verticalSpace,
            Visibility(
              visible: controller.diamondColor() == DiamondColor.fancy,
              child: SizedBox(
                height: 250.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Overtone'.toUpperCase(),
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w900,
                        fontSize: 40.sp,
                        color: AppColors.k101C28,
                      ),
                    ).paddingOnly(left: 8),
                    40.verticalSpace,
                    Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (BuildContext context, int index) {
                          var color = filterEntity.fancyOvertone[index];
                          var isSelected = controller
                              .appliedFilter()
                              .fancyOvertone
                              .contains(color);
                          return FilterChip(
                            selectedColor: AppColors.kBEFFFC,
                            showCheckmark: false,
                            side: BorderSide(
                              color: isSelected
                                  ? AppColors.k1A47E8
                                  : AppColors.k70777E.withOpacity(0.5),
                              width: 0.5.w,
                            ),
                            shadowColor: AppColors.k000000.withOpacity(0.1),
                            elevation: 3,
                            color: MaterialStatePropertyAll<Color>(
                              isSelected
                                  ? AppColors.kBEFFFC
                                  : AppColors.kffffff,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(24.r),
                              side: BorderSide(
                                color: isSelected
                                    ? AppColors.k1A47E8
                                    : AppColors.k70777E.withOpacity(0.5),
                                width: 0.5.w,
                              ),
                            ),
                            label: Text(
                              color.toUpperCase(),
                            ),
                            onSelected: (bool value) {
                              vibrate();
                              controller.selectFancyOvertone(color);
                            },
                            selected: isSelected,
                          );
                        },
                        separatorBuilder: (BuildContext context, int index) =>
                            30.horizontalSpace,
                        itemCount: filterEntity.fancyOvertone.length,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );

  Widget _buildTitle({
    required String title,
    VoidCallback? onClearAll,
    IconData? icon,
    Widget? trailing,
    EdgeInsets? padding,
  }) =>
      CardTitle(
        padding: padding,
        title: title,
        icon: icon,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (trailing != null) trailing,
            // if (onClearAll != null)
            //   GestureDetector(
            //     onTap: () {
            //       onClearAll.call();
            //     },
            //     child: const Icon(
            //       Icons.clear,
            //       color: AppColors.k101C28,
            //     ),
            //   ),
          ],
        ),
      );

  InputDecoration _buildInputDecoration() => InputDecoration(
        isDense: true,
        fillColor: const Color(0xFFf1f3f9),
        filled: true,
        contentPadding: const EdgeInsets.all(12),
        hintText: 'Supplier name',
        hintStyle: GoogleFonts.montserrat(
          color: AppColors.k70777E,
          fontSize: 16,
        ),
        errorMaxLines: 3,
        enabledBorder: outlineInputBorderEnabled10Rad.copyWith(
          borderSide: BorderSide(
            color: Colors.grey.shade300,
          ),
        ),
        border: outlineInputBorderEnabled10Rad.copyWith(
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        focusedBorder: outlineInputBorderFocused15Rad,
        errorBorder: outlineInputBorderEnabled10Rad.copyWith(
          borderSide: const BorderSide(
            color: AppColors.k7C0B0B,
          ),
        ),
        focusedErrorBorder: outlineInputBorderFocused15Rad.copyWith(
          borderSide: const BorderSide(
            color: AppColors.k7C0B0B,
          ),
        ),
        // disabledBorder: _buildOutlineInputBorder(),
      );

  Widget _buildSegment(String text, int index) => Expanded(
        child: Obx(
          () => GestureDetector(
            onTap: () {
              controller.selectedindex(index);
              if (index == 0) {
                controller.setWhiteColor();
              } else if (index == 1) {
                controller.setFancyColor();
              }
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              decoration: BoxDecoration(
                color: controller.selectedindex() == index
                    ? AppColors.k70777E
                    : AppColors.kffffff,
                borderRadius: BorderRadius.circular(20),
              ),
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w500,
                  color: controller.selectedindex() == index
                      ? AppColors.kffffff
                      : AppColors.k101C28,
                ),
              ),
            ),
          ),
        ),
      );
}
