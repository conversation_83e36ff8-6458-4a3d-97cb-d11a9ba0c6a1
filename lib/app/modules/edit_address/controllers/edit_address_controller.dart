import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

/// EditAddressController
class EditAddressController extends GetxController {
  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// IsCheck
  RxBool isCheck = false.obs;

  /// select country for billing address
  RxString selectedCountryForBillingAddress = ''.obs;

  /// selected Country for billing address
  void selectCountryForBillingAddress(Country country) {
    selectedCountryForBillingAddress.value = country.name;
  }

  /// select country for shipping address
  RxString selectedCountryForShippingAddress = ''.obs;

  /// selected Country for billing address
  void selectCountryForShippingAddress(Country country) {
    selectedCountryForShippingAddress.value = country.name;
  }

  /// Checking isSame
  void isSame(value) {
    isCheck.value = value;
  }
}
