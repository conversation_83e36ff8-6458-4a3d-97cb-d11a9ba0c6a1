import 'dart:math';

import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/ui/components/add_owner_alert_dialog.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/int_ext.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../data/config/app_colors.dart';
import '../../../data/config/design_config.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/legal_status_organization_controller.dart';

/// LegalStatusOrganizationView
class LegalStatusOrganizationView
    extends GetView<LegalStatusOrganizationController> {
  /// LegalStatusOrganizationView
  const LegalStatusOrganizationView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildCommonAppbar(),
        bottomNavigationBar: _buildBottomButtons(context),
        body: Obx(
          () => IgnorePointer(
            ignoring: controller.isLoading(),
            child: _buildBody(context),
          ),
        ),
      );

  /// Build Bottom Buttons
  Widget _buildBottomButtons(BuildContext context) => Padding(
        padding: EdgeInsets.only(
          left: min(60.w, 15),
          right: min(60.w, 15),
          bottom: MediaQuery.of(context).viewInsets.bottom +
              MediaQuery.of(context).padding.bottom +
              60.h,
        ),
        child: controller.isEdit()
            ? Obx(
                () => AppButton.text(
                  isLoading: controller.isLoading(),
                  buttonText: LocaleKeys.legal_status_organization_save.tr,
                  onPressed: () {
                    if (UserProvider.currentUser?.isVerified ?? false) {
                      appSnackbar(
                        message: LocaleKeys
                            .messages_for_edit_profile_please_contact_to_admin
                            .tr,
                        snackBarState: SnackBarState.MESSAGE,
                      );
                    } else {
                      if (controller.orderAuthorizedpersonList.isEmpty) {
                        controller.ownerErrorMessage(
                            LocaleKeys.custom_error_please_add_owners.tr);
                        return;
                      } else {
                        controller.ownerErrorMessage('');
                        controller.updateProfileData();
                      }
                    }
                  },
                  buttonSize: Size(Get.width, 150.h),
                  backgroundColor: AppColors.k101C28,
                  borderRadius: 24.r,
                  borderColor: AppColors.k101C28,
                  buttonTextStyle: TextStyle(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.kBEFFFC,
                  ),
                ),
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () {
                        if (kIsWeb) {
                          DesignConfig.popItem();
                        } else {
                          Get.back();
                        }
                      },
                      buttonText:
                          LocaleKeys.legal_status_organization_previous.tr,
                      backgroundColor: AppColors.kEAEFF4,
                      borderColor: AppColors.kEAEFF4,
                      buttonTextColor: AppColors.k101C28,
                    ),
                  ),
                  //30.horizontalSpace,
                  SizedBox(width: min(30.w, 15)),
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () => controller.onClickNext(),
                      buttonText: LocaleKeys.legal_status_organization_next.tr,
                      buttonTextColor: AppColors.kBEFFFC,
                    ),
                  ),
                ],
              ),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => Container(
        height: Get.height,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: REdgeInsets.only(
              left: 57,
              right: 63,
            ),
            child: FormBuilder(
              key: controller.fbKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  70.verticalSpace,

                  /// Legal Status Organization Expansion Tile
                  _buildOrganizationStatus(context),

                  /// List of Owners Expansion Tile
                  _buildOwnersList(context),
                ],
              ),
            ),
          ),
        ),
      );

  /// Build Custom Owners List
  Widget _buildOwnersList(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: Obx(
          () => ExpansionTile(
            title: Text(
              LocaleKeys.legal_status_organization_list_of_owners.tr,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w900,
                color: AppColors.k101C28,
                letterSpacing: 5.sp,
              ),
            ),
            subtitle: AnimatedSize(
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOut,
              child: Visibility(
                visible: controller.ownerErrorMessage().isNotEmpty,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: REdgeInsets.only(left: 44, top: 28),
                    child: Text(
                      controller.ownerErrorMessage(),
                      style: AppFontStyles.skolaSans(
                        color: Colors.red,
                        fontSize: 38.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            onExpansionChanged: (bool value) =>
                controller.ownersExpansionToggle(value),
            initiallyExpanded: controller.ownersExpansionTile(),
            dense: true,
            visualDensity: const VisualDensity(
              horizontal: VisualDensity.minimumDensity,
              vertical: VisualDensity.minimumDensity,
            ),
            tilePadding: EdgeInsets.zero,
            trailing: Container(
              height: 100.h,
              width: min(100.w, 50),
              decoration: const BoxDecoration(
                color: AppColors.kEAEFF4,
                shape: BoxShape.circle,
              ),
              child: Icon(
                controller.ownersExpansionTile()
                    ? Icons.expand_less_sharp
                    : Icons.expand_more_sharp,
                color: AppColors.k101C28,
              ),
            ),
            children: <Widget>[
              60.verticalSpace,
              ListView.separated(
                shrinkWrap: true,
                itemCount:
                    // controller.isEdit()
                    //     ? AuthProvider.userEntity().orderAuthorizedPerson?.length ??
                    //         0
                    //     :
                    controller.orderAuthorizedpersonList.length,
                physics: const NeverScrollableScrollPhysics(),
                separatorBuilder: (BuildContext context, int index) =>
                    60.verticalSpace,
                itemBuilder: (BuildContext context, int index) => _buildListOfOwners(
                    index: index,
                    name:
                        '${controller.orderAuthorizedpersonList[index].firstName ?? ''} ${controller.orderAuthorizedpersonList[index].lastName ?? ''}',
                    phoneNumber:
                        '${controller.orderAuthorizedpersonList[index].mobileNo}',
                    designation:
                        '${controller.orderAuthorizedpersonList[index].designation}'),
              ),
              60.verticalSpace,
              Align(
                alignment: Alignment.topLeft,
                child: InkWell(
                  onTap: () {
                    if (controller.phoneCountryCode().isEmpty) {
                      controller.phoneCountryCode(controller
                              .newCompanyRegistrationController
                              .countryPhoneCode
                              .isEmpty
                          ? '1'
                          : '${controller.newCompanyRegistrationController.countryPhoneCode.toString()}');
                    }
                    showAddOwnersAlertDialog(
                        controller: controller, context: context);
                  },
                  borderRadius: BorderRadius.circular(500.r),
                  child: FittedBox(
                    child: Container(
                      height: 120.h,
                      //width: 335.w,
                      padding: REdgeInsets.symmetric(horizontal: 30),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.k101C28,
                          width: min(4.w, 2),
                        ),
                        borderRadius: BorderRadius.circular(500.r),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        LocaleKeys.legal_status_organization_add_owner.tr,
                        style: AppFontStyles.skolaSans(
                          color: AppColors.k101C28,
                          fontSize: 35.sp,
                          letterSpacing: 3.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              controller.ownersExpansionTile()
                  ? 100.verticalSpace
                  : 0.verticalSpace,
            ],
          ),
        ),
      );

  /// Build Organization Status
  Widget _buildOrganizationStatus(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Text(
            LocaleKeys
                .legal_status_organization_legal_status_of_organizations.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w900,
              color: AppColors.k101C28,
              letterSpacing: 5.sp,
            ),
          ),
          maintainState: true,
          onExpansionChanged: (bool value) =>
              controller.companyExpansionToggle(value),
          dense: true,
          // visualDensity: const VisualDensity(
          //   horizontal: VisualDensity.minimumDensity,
          //   vertical: VisualDensity.minimumDensity,
          // ),
          tilePadding: EdgeInsets.zero,
          initiallyExpanded: true,
          trailing: Container(
            height: 100.h,
            width: min(100.w, 50),
            decoration: const BoxDecoration(
              color: AppColors.kEAEFF4,
              shape: BoxShape.circle,
            ),
            child: Obx(
              () => Icon(
                controller.statusOrganizationExpansionTile.value
                    ? Icons.expand_less_sharp
                    : Icons.expand_more_sharp,
                color: AppColors.k101C28,
              ),
            ),
          ),
          children: <Widget>[
            76.verticalSpace,
            Align(
              alignment: Alignment.centerLeft,
              child: Row(
                children: <Widget>[
                  Text(
                    LocaleKeys.legal_status_organization_organization_type.tr,
                    style: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  10.horizontalSpace,
                  Text(
                    '*',
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w900,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),
            40.verticalSpace,
            // AppTextFormField(
            //   name: 'legal_organization_status_type',
            //   fieldType: FieldType.radioButton,
            // ),
            Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: controller.businessType
                    .asMap()
                    .entries
                    .map(
                      (MapEntry<int, String> e) => InkWell(
                        onTap: () =>
                            controller.selectBusinessIndex.value = e.key,
                        splashColor: Colors.transparent,
                        child: ListTile(
                          contentPadding: EdgeInsets.zero,
                          dense: true,
                          visualDensity: VisualDensity.compact,
                          leading: _buildAnimatedContainerForCustomError(e),
                          horizontalTitleGap: min(20.w, 8),
                          minVerticalPadding: 0,
                          title: Text(
                            _buildOrgType(e.value),
                            style: AppFontStyles.skolaSans(
                              color: AppColors.k101C28,
                              fontSize: 35.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
            Obx(
              () => AnimatedSize(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                child: Visibility(
                  visible: controller.errorMessage.value.isNotEmpty,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: REdgeInsets.only(left: 44, top: 28),
                      child: Text(
                        controller.errorMessage.value,
                        style: AppFontStyles.skolaSans(
                          color: Colors.red,
                          fontSize: 38.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            76.verticalSpace,
            _buildTextFormField(
              onTap: () {
                showCountryPicker(
                  context: context,
                  onSelect: (Country country) {
                    controller.selectedCountry(country);
                    controller
                        .fbKey
                        .currentState
                        ?.fields[RegistrationFormFields
                            .registrationOrIncorporationCountry]
                        ?.didChange(country.name);
                  },
                );
              },
              readOnly: true,
              initialValue: controller.isEdit()
                  ? AuthProvider.userEntity()
                          .legalOrganizationStatus
                          ?.registrationOrIncorporationCountry ??
                      ''
                  : controller.selectCountry(),
              name: RegistrationFormFields.registrationOrIncorporationCountry,
              keyboardType: TextInputType.name,
              autofillHints: const <String>[AutofillHints.countryName],
              labelText: LocaleKeys
                  .legal_status_organization_country_of_registration_or_incorporation
                  .tr,
              isRequired: true,
              validation: (String? value) {
                if (value?.trim().isEmpty ?? true) {
                  return LocaleKeys.validation_country_registration_is_empty.tr;
                }
                return null;
              },
            ),
            60.verticalSpace,
            Obx(
              () => DropDownFormField(
                isSearchable: true,
                hintText:
                    '${LocaleKeys.new_company_registration_search_state.tr}...',
                onChanged: (dynamic value) => controller.selectedState(value),
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                ),
                items: controller.incorporationStateList(),
                selectedValue: controller.selectedState().isEmpty
                    ? null
                    : controller.selectedState(),
                menuItemStyle: AppFontStyles.skolaSans(
                  fontSize: 45.sp,
                  color: AppColors.k101C28,
                  fontWeight: FontWeight.w400,
                ),
                decoration: InputDecoration(
                  contentPadding: REdgeInsets.only(
                    left: 50,
                    bottom: 30,
                    right: 0,
                  ),
                  constraints: BoxConstraints(
                    minHeight: 150.h,
                  ),
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Text(
                        LocaleKeys
                            .legal_status_organization_state_of_registration_or_incorporation
                            .tr,
                        style: AppFontStyles.skolaSans(
                          color: AppColors.k70777E,
                          fontSize: 45.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      Text(
                        ' *',
                        style: AppFontStyles.skolaSans(
                          fontSize: 35.sp,
                          fontWeight: FontWeight.w900,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  labelStyle: AppFontStyles.skolaSans(
                    color: AppColors.k70777E,
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w400,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      width: 1.w,
                      color: AppColors.k70777E,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      width: 1.w,
                      color: AppColors.k70777E,
                    ),
                  ),
                  errorStyle: const TextStyle(color: Colors.red),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(24.r),
                    borderSide: BorderSide(
                      width: 1.w,
                      color: AppColors.k70777E,
                    ),
                  ),
                ),
              ),
            ),
            // _buildTextFormField(
            //   onTap: () {
            //     showCountryPicker(
            //       context: context,
            //       onSelect: (Country country) {
            //         controller.selectedCountry(country);
            //         controller
            //             .fbKey
            //             .currentState
            //             ?.fields[RegistrationFormFields
            //                 .registrationOrIncorporationState]
            //             ?.didChange(country.name);
            //       },
            //     );
            //   },
            //   initialValue: controller.isEdit()
            //       ? AuthProvider.userEntity()
            //               .legalOrganizationStatus
            //               ?.registrationOrIncorporationState ??
            //           ''
            //       : '',
            //   name: RegistrationFormFields.registrationOrIncorporationState,
            //   keyboardType: TextInputType.name,
            //   autofillHints: const <String>[AutofillHints.addressState],
            //   labelText: LocaleKeys
            //       .legal_status_organization_state_of_registration_or_incorporation
            //       .tr,
            //   isRequired: true,
            //   validation: (String? value) {
            //     if (value?.trim().isEmpty ?? true) {
            //       return LocaleKeys.validation_state_registration_is_empty.tr;
            //     }
            //     return null;
            //   },
            // ),
            Obx(
              () => controller.statusOrganizationExpansionTile()
                  ? 100.verticalSpace
                  : 0.verticalSpace,
            ),
          ],
        ),
      );

  String _buildOrgType(String businessType) {
    if (businessType == 'Private Corp') {
      return LocaleKeys.legal_status_organization_private_corp.tr;
    } else if (businessType == 'Partnership') {
      return LocaleKeys.legal_status_organization_partnership.tr;
    } else if (businessType == 'Individual') {
      return LocaleKeys.legal_status_organization_individual.tr;
    } else {
      return '';
    }
  }

  /// Build AnimatedContainer For CustomError
  Widget _buildAnimatedContainerForCustomError(e) => AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        height: min(56.h, 20),
        width: min(56.w, 20),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          border: Border.all(
            width: controller.selectBusinessIndex() == e.key
                ? min(19.w, 6)
                : min(3.w, 2),
          ),
        ),
      );

  /// Common List Owners
  Widget _buildListOfOwners({
    required String name,
    required String designation,
    required String phoneNumber,
    required int index,
  }) =>
      Container(
        width: Get.width,
        decoration: BoxDecoration(
          border: Border.all(
            width: 1.w,
            color: AppColors.k70777E,
          ),
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Padding(
          padding: REdgeInsets.only(
            top: 40,
            bottom: 40,
            left: 60,
            right: 40,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Container(
                    height: 70.h,
                    //width: 115.w,
                    padding: REdgeInsets.symmetric(horizontal: 30.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.kEAEFF4,
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    child: Text(
                      '${(index + 1).withSuffix}',
                      style: AppFontStyles.skolaSans(
                        color: AppColors.k101C28.withOpacity(0.6),
                        fontSize: 30.sp,
                        letterSpacing: 3.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => controller.orderAuthorizedpersonList
                      ..removeAt(index)
                      ..refresh(),
                    padding: EdgeInsets.zero,
                    visualDensity: const VisualDensity(
                      horizontal: VisualDensity.minimumDensity,
                      vertical: VisualDensity.minimumDensity,
                    ),
                    icon: SizedBox(
                      height: 64.h,
                      width: min(64.w, 30),
                      child: AppImages.deleteLogo,
                    ),
                  ),
                ],
              ),
              33.verticalSpace,
              Text(
                name,
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
              33.verticalSpace,
              ListTile(
                contentPadding: EdgeInsets.only(right: min(60.w, 20)),
                horizontalTitleGap: min(10.w, 8),
                minVerticalPadding: 0,
                dense: true,
                visualDensity: VisualDensity.compact,
                leading: SizedBox(
                  height: 45.h,
                  width: min(45.w, 20),
                  child: AppImages.phoneIcon,
                ),
                title: Text(
                  phoneNumber,
                  style: AppFontStyles.skolaSans(
                    color: AppColors.k101C28,
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                trailing: Text(
                  designation,
                  style: AppFontStyles.skolaSans(
                    color: AppColors.k101C28,
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (controller.isLoading() == false) {
            controller.orderAuthorizedpersonList.refresh();
            if (kIsWeb) {
              DesignConfig.popItem();
            } else {
              Get.back();
            }
          } else {
            if (!controller.isLoading()) {
              if (kIsWeb) {
                DesignConfig.popItem();
              } else {
                Get.back();
              }
            }
          }
        },
        title: Text(
          controller.isEdit()
              ? LocaleKeys.legal_status_organization_edit_profile.tr
              : LocaleKeys
                  .legal_status_organization_legal_status_of_organization.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          if (!controller.isEdit())
            FittedBox(
              child: StatusChip(
                text: LocaleKeys.legal_status_organization_two_five.tr,
                textStyle: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  letterSpacing: 10.sp,
                  fontWeight: FontWeight.w400,
                ),
                color: AppColors.kEAEFF4,
              ),
            ),
          //30.horizontalSpace,
          SizedBox(width: min(30.w, 15)),
        ],
      );

  /// Common TextFormField
  AppTextFormField _buildTextFormField({
    required String name,
    required String? Function(String?)? validation,
    TextEditingController? controller,
    String? labelText,
    String? initialValue,
    bool isRequired = false,
    TextInputType? keyboardType,
    Widget? suffix,
    int? maxLines,
    Iterable<String>? autofillHints,
    void Function()? onTap,
    bool? readOnly,
  }) =>
      AppTextFormField(
        onTap: onTap ?? () {},
        readOnly: readOnly ?? false,
        initialValue: initialValue ?? '',
        controller: controller,
        name: name,
        keyboardType: keyboardType,
        autofillHints: autofillHints,
        validator: validation,
        labelText: labelText ?? '',
        isRequired: isRequired,
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        maxLines: maxLines ?? 1,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        suffixIcon: suffix,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48, bottom: 30),
      );

  /// Common App Button
  Widget _buildCommonAppButton({
    required String buttonText,
    Function? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
  }) =>
      AppButton.text(
        buttonText: buttonText,
        onPressed: () {
          onPressed!();
        },
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );
}
