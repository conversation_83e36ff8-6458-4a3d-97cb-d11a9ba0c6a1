import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

/// CartFirstAddressController
class CartFirstAddressController extends GetxController {
  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// IsCheck
  RxBool isCheck = false.obs;

  /// billing address phone
  RxString billingAddressPhoneCode = '+1'.obs;

  /// shipping address phone
  RxString shippingAddressPhoneCode = '+1'.obs;

  /// Checking isSame as Billing Address
  void isSameAsBillingAddress(value) {
    isCheck.value = value;
  }

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    //_preLoadAddress();
  }

  // void _preLoadAddress() {
  //   final AddressModel? addressModel = AddressModelHelper.get();
  //   if (addressModel != null) {
  //     billingAddressPhoneCode(
  //         '${addressModel.billingAddress.mobile?.split('-')[0]}');
  //     shippingAddressPhoneCode(
  //         '${addressModel.shippingAddress.mobile?.split('-')[0]}');
  //     fbKey.currentState?.patchValue({
  //       RegistrationFormFields.cartBillingAddressFirstName:
  //           addressModel.billingAddress.firstName ?? '',
  //       RegistrationFormFields.cartBillingAddressLastName:
  //           addressModel.billingAddress.lastName ?? '',
  //       RegistrationFormFields.email: addressModel.billingAddress.email ?? '',
  //       RegistrationFormFields.cartBillingAddressLineOne:
  //           addressModel.billingAddress.addressLineOne ?? '',
  //       RegistrationFormFields.cartBillingAddressLineTwo:
  //           addressModel.billingAddress.addressLineTwo ?? '',
  //       RegistrationFormFields.mailing_adress_country:
  //           addressModel.billingAddress.country ?? '',
  //       RegistrationFormFields.mailing_adress_zip_code:
  //           addressModel.billingAddress.zipCode ?? '',
  //       RegistrationFormFields.mailing_adress_city:
  //           addressModel.billingAddress.city ?? '',
  //       RegistrationFormFields.mailing_adress_state:
  //           addressModel.billingAddress.state ?? '',
  //       RegistrationFormFields.cartBillingAddressMobile:
  //           addressModel.billingAddress.mobile?.split('-')[1],
  //       RegistrationFormFields.cartShippingAddressLineOne:
  //           addressModel.shippingAddress.addressLineOne ?? '',
  //       RegistrationFormFields.cartShippingAddressLineTwo:
  //           addressModel.shippingAddress.addressLineTwo ?? '',
  //       RegistrationFormFields.physical_adress_country:
  //           addressModel.shippingAddress.country ?? '',
  //       RegistrationFormFields.physical_adress_zip_code:
  //           addressModel.shippingAddress.zipCode ?? '',
  //       RegistrationFormFields.physical_adress_city:
  //           addressModel.shippingAddress.city ?? '',
  //       RegistrationFormFields.physical_adress_state:
  //           addressModel.shippingAddress.state ?? '',
  //       RegistrationFormFields.cartPhysicalAddressMobile:
  //           addressModel.shippingAddress.mobile?.split('-')[1],
  //     });
  //   }
  // }

  /// select country for mailing address
  RxString selectedCountryForBillingAddress = ''.obs;

  /// selected Country
  void selectCountryForBillingAddress(Country country) {
    selectedCountryForBillingAddress.value = country.name;
  }

  /// select country for shipping address
  RxString selectedCountryForShippingAddress = ''.obs;

  /// selected Country
  void selectCountryForShippingAddress(Country country) {
    selectedCountryForShippingAddress.value = country.name;
  }

  /// confirm to pay
  void confirmToPay() {
    if (fbKey.currentState?.saveAndValidate() ?? false) {
      final Map<String, dynamic> data = <String, dynamic>{
        'billing_address': {
          'first_name': fbKey
              .currentState
              ?.fields[RegistrationFormFields.cartBillingAddressFirstName]
              ?.value,
          'last_name': fbKey
              .currentState
              ?.fields[RegistrationFormFields.cartBillingAddressLastName]
              ?.value,
          'email': fbKey
              .currentState?.fields[RegistrationFormFields.email]?.value
              .toString()
              .trim()
              .toLowerCase(),
          'address_line_one': fbKey.currentState
              ?.fields[RegistrationFormFields.cartBillingAddressLineOne]?.value,
          'address_line_two': fbKey.currentState
              ?.fields[RegistrationFormFields.cartBillingAddressLineTwo]?.value,
          'country': fbKey.currentState
              ?.fields[RegistrationFormFields.mailing_adress_country]?.value,
          'zip_code': fbKey.currentState
              ?.fields[RegistrationFormFields.mailing_adress_zip_code]?.value
              .toString(),
          'city': fbKey.currentState
              ?.fields[RegistrationFormFields.mailing_adress_city]?.value,
          'state': fbKey.currentState
              ?.fields[RegistrationFormFields.mailing_adress_state]?.value,
          'mobile':
              '${billingAddressPhoneCode()}-${fbKey.currentState?.fields[RegistrationFormFields.cartBillingAddressMobile]?.value}',
        },
        'is_address_same': isCheck(),
        if (!isCheck())
          'shipping_address': {
            'address_line_one': fbKey
                .currentState
                ?.fields[RegistrationFormFields.cartShippingAddressLineOne]
                ?.value,
            'address_line_two': fbKey
                .currentState
                ?.fields[RegistrationFormFields.cartShippingAddressLineTwo]
                ?.value,
            'country': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_country]?.value,
            'zip_code': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_zip_code]?.value
                .toString(),
            'city': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_city]?.value,
            'state': fbKey.currentState
                ?.fields[RegistrationFormFields.physical_adress_state]?.value,
            'mobile':
                '${shippingAddressPhoneCode()}-${fbKey.currentState?.fields[RegistrationFormFields.cartPhysicalAddressMobile]?.value}',
          }
      };
      // AddressModelHelper.add(
      //   AddressModel(
      //     billingAddress: IngAddress.fromMap(data['billing_address']),
      //     shippingAddress: data['is_address_same']
      //         ? IngAddress.fromMap(data['billing_address'])
      //         : IngAddress.fromMap(data['shipping_address']),
      //   ),
      // );
      Get.offNamed(Routes.CART_SECOND_ADDRESS);
    } else {
      logI(' ===================== Something Missing');
    }
  }
}
