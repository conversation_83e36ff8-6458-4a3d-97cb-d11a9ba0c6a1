import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/grid_stock_item.dart';
import 'package:diamond_company_app/app/ui/components/scroll_to_hide.dart';
import 'package:diamond_company_app/app/ui/components/shadow_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/view_all_controller.dart';

class ViewAllView extends GetView<ViewAllController> {
  const ViewAllView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
        bottomNavigationBar: _buildCart(),
      );

  Widget _buildCart() => Obx(
        () => Get.find<CartItemService>().diamonds().isEmpty
            ? const SizedBox.shrink()
            : ScrollToHide(
                scrollController: controller.scrollController,
                duration: const Duration(milliseconds: 100),
                child: Padding(
                  padding: EdgeInsets.only(
                      bottom:
                          MediaQuery.of(Get.context!).padding.bottom + 30.w),
                  child: ShadowButton(
                    onTap: () => controller.navigateToCart(),
                    height: 150.h,
                    width: Get.width.w,
                    buttonColor: AppColors.k101C28,
                    margin: REdgeInsets.only(left: 60, right: 60, bottom: 30),
                    padding: REdgeInsets.symmetric(horizontal: 40),
                    buttonName: 'cart',
                    borderRadius: BorderRadius.all(Radius.circular(24.r)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        SizedBox(
                          height: 80.h,
                          width: 80.w,
                          child: AppImages.cart,
                        ),
                        24.horizontalSpace,
                        Text(
                          'Cart',
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 45.sp,
                            color: AppColors.kBEFFFC,
                          ),
                        ),
                        Spacer(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: <Widget>[
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  'Pcs',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 30.sp,
                                    color: AppColors.k9FA4A9,
                                  ),
                                ),
                                20.horizontalSpace,
                                Center(
                                  child: Text(
                                    '${Get.find<CartItemService>().diamondSummary().totalPcs.toString().padLeft(2, '0')}',
                                    style: AppFontStyles.skolaSans(
                                      fontWeight: FontWeight.w400,
                                      fontSize: 35.sp,
                                      color: AppColors.kffffff,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            48.horizontalSpace,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  'Cts',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 30.sp,
                                    color: AppColors.k9FA4A9,
                                  ),
                                ),
                                20.horizontalSpace,
                                Text(
                                  '${Get.find<CartItemService>().diamondSummary().totalCts.toStringAsFixed(2)}',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 35.sp,
                                    color: AppColors.kffffff,
                                  ),
                                ),
                              ],
                            ),
                            48.horizontalSpace,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  'Price/ct',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 30.sp,
                                    color: AppColors.k9FA4A9,
                                  ),
                                ),
                                20.horizontalSpace,
                                Text(
                                  '\$${Get.find<CartItemService>().diamondSummary().avgPricePerCt.toStringAsFixed(2)}',
                                  style: AppFontStyles.skolaSans(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 35.sp,
                                    color: AppColors.kffffff,
                                  ),
                                ),
                              ],
                            ),
                            48.horizontalSpace,
                            SizedBox(
                              height: 70.h,
                              width: 70.w,
                              child: IconButton.filled(
                                onPressed: () {},
                                style: IconButton.styleFrom(
                                  backgroundColor: AppColors.kBEFFFC,
                                  shape: const CircleBorder(),
                                  fixedSize: Size(70.w, 70.h),
                                  padding: EdgeInsets.zero,
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                                icon: SizedBox(
                                  height: 38.h,
                                  width: 38.w,
                                  child: AppImages.arrowForward,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: Text(
          controller.isFeatured() ? 'Featured Items' : 'New Arrival Items',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        onBackPressed: () => Get.back(),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : controller.diamonds.isEmpty
                ? const SizedBox.shrink()
                : RefreshIndicator(
                    color: AppColors.k101C28,
                    onRefresh: () =>
                        controller.fetchDiamonds(skip: 0, limit: 10),
                    child: GetBuilder<ViewAllController>(
                      builder: (_) => GridView.builder(
                        clipBehavior: Clip.none,
                        physics: const AlwaysScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          // childAspectRatio: Get.width / Get.height,
                          crossAxisSpacing: 45.w,
                          mainAxisSpacing: 45.h,
                          mainAxisExtent: (Get.width * Get.height) /
                              (Get.width + Get.height),
                        ),
                        padding: EdgeInsets.only(
                            left: 60.w,
                            right: 60.w,
                            top: 60.h,
                            bottom: 60.h +
                                MediaQuery.of(Get.context!).padding.bottom),
                        controller: controller.scrollController,
                        itemCount: controller.diamonds.length + 1,
                        itemBuilder: (BuildContext context, int index) {
                          if (index == controller.diamonds.length) {
                            return controller.isLoadMore()
                                ? AppLoader()
                                : const SizedBox.shrink();
                          } else {
                            return GridStockItem(
                              diamond: controller.diamonds[index],
                              width: Get.width,
                              // height: 864.h,
                              index: index,
                              onFavourite: (bool isFav) {
                                controller.updateWishlistStock(
                                  isFav: isFav,
                                  id: controller.diamonds[index].id ?? '',
                                );
                              },
                              isAddedToCart: CartItemHelper.isDiamondAdded(
                                      controller.diamonds[index])
                                  .obs,
                              onTap: () => controller.navigateToDetails(index),
                            );
                          }
                        },
                      ),
                    ).paddingOnly(
                        bottom: MediaQuery.of(Get.context!).padding.bottom),
                  ),
      );
}
