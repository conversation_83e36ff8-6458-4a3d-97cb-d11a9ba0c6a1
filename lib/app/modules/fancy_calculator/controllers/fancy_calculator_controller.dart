import 'package:diamond_company_app/app/data/models/local_diamond.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FancyCalculatorController extends GetxController {
  static FancyCalculatorController get to =>
      Get.find<FancyCalculatorController>();

  FancyCalculatorController({
    LocalDiamond? diamond,
  }) : _diamond = diamond;

  final LocalDiamond? _diamond;

  // Text Editing Controllers
  final TextEditingController weightController =
      TextEditingController(text: '0');
  final TextEditingController pricePerCaratController =
      TextEditingController(text: '0');
  final TextEditingController finalPriceController =
      TextEditingController(text: '0');

  // Focus Nodes
  final FocusNode weightFocusNode = FocusNode();
  final FocusNode pricePerCaratFocusNode = FocusNode();
  final FocusNode finalPriceFocusNode = FocusNode();

  // Rx Variables
  RxNum pricePerCarat = RxNum(0);
  final RxNum finalPrice = RxNum(0);
  final RxNum weight = RxNum(0);

  LocalDiamond get diamond => LocalDiamond(
        weight: weight().toDouble(),
        perCarat: pricePerCarat().toDouble(),
        totalPrice: finalPrice().toDouble(),
      );

  void onWeightChange(String value) {
    if (value.isNotEmpty) {
      weight(double.parse(value));
      finalPrice(pricePerCarat() * weight());
      finalPriceController.text = moneyFormat.format(finalPrice());
    } else {
      weight(0);
      finalPrice(0);
      finalPriceController.text = '0';
    }
  }

  void onPerCaratChange(String value) {
    if (value.isNotEmpty) {
      pricePerCarat(double.parse(value));
      finalPrice(pricePerCarat() * weight());
      finalPriceController.text = moneyFormat.format(finalPrice());
    } else {
      pricePerCarat(0);
      finalPrice(0);
      finalPriceController.text = '0';
    }
  }

  void onFinalPriceChange(String value) {
    if (value.isNotEmpty) {
      finalPrice(double.parse(value));
      pricePerCarat(finalPrice() / weight());
      pricePerCaratController.text = moneyFormat.format(pricePerCarat());
    } else {
      finalPrice(0);
      pricePerCarat(0);
      pricePerCaratController.text = '0';
    }
  }

  void _setDiamond() {
    weight(_diamond?.weight);
    pricePerCarat(_diamond?.perCarat);
    finalPrice(_diamond?.totalPrice);
    weightController.text = _diamond?.weight.toString() ?? '0';
    pricePerCaratController.text = moneyFormat.format(_diamond?.perCarat);
    finalPriceController.text = moneyFormat.format(_diamond?.totalPrice);
  }

  @override
  void onInit() {
    _addFocusNodeListeners();
    if (_diamond != null) {
      _setDiamond();
    }
    super.onInit();
  }

  void _addFocusNodeListeners() {
    weightFocusNode.addListener(() {
      if (weightFocusNode.hasFocus) {
        weightController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: weightController.text.length,
        );
      }
    });
    pricePerCaratFocusNode.addListener(() {
      if (pricePerCaratFocusNode.hasFocus) {
        // Convert to double
        pricePerCaratController.text = pricePerCarat().toStringAsFixed(2);

        pricePerCaratController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: pricePerCaratController.text.length,
        );
      } else {
        pricePerCaratController.text = moneyFormat.format(pricePerCarat());
      }
    });
    finalPriceFocusNode.addListener(() {
      if (finalPriceFocusNode.hasFocus) {
        // Convert to double
        finalPriceController.text = finalPrice().toStringAsFixed(2);

        finalPriceController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: finalPriceController.text.length,
        );
      } else {
        finalPriceController.text = moneyFormat.format(finalPrice());
      }
    });
  }
}
