import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/local_diamond.dart';
import 'package:diamond_company_app/app/modules/calculator/button_props.dart';
import 'package:diamond_company_app/app/modules/calculator/calculator_repo.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_details_controller.dart';
import 'package:diamond_company_app/app/modules/fancy_calculator/controllers/fancy_calculator_controller.dart';
import 'package:diamond_company_app/app/modules/offer_details/controllers/offer_details_controller.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_stock_offer_details/controllers/vendor_stock_offer_details_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/input_formatters.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:diamond_company_app/app/utils/validate_decimal.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/services/text_formatter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../ui/components/app_button.dart';

class FancyCalculatorView extends GetView<FancyCalculatorController> {
  const FancyCalculatorView({
    required this.viewTag,
    this.btnProps = const ButtonProps(text: 'Calculate'),
    this.diamond,
    this.diamondColor,
    this.diamondClarity,
    this.diamondShape,
    this.dimondId,
    this.readOnly = false,
    this.isNewOffer = false,
    this.isCounterOffer = false,
    super.key,
  });

  final LocalDiamond? diamond;

  final String? diamondColor;
  final String? diamondClarity;
  final String? diamondShape;

  final String viewTag;

  final ButtonProps btnProps;

  final bool readOnly;
  final bool isNewOffer;
  final bool isCounterOffer;
  final String? dimondId;

  @override
  String get tag => viewTag;

  final OutlineInputBorder outlineInputBorderEnabled4Rad =
      const OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(4)),
    borderSide: BorderSide(
      color: Colors.grey,
      width: 1,
    ),
  );

  final OutlineInputBorder outlineInputBorderFocused4Rad =
      const OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(4)),
    borderSide: BorderSide(
      color: AppColors.k1A47E8,
      width: 2,
    ),
  );

  @override
  Widget build(BuildContext context) {
    FancyCalculatorController controller = Get.put<FancyCalculatorController>(
      FancyCalculatorController(
        diamond: diamond,
      ),
    );

    return Container(
      padding: REdgeInsets.all(50),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (isCounterOffer)
                Text(
                  'Counter Offer',
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w900,
                    color: AppColors.kA266D0,
                  ),
                ),
              if (isNewOffer)
                Text(
                  'Send Offer',
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w900,
                    color: AppColors.kA266D0,
                  ),
                ),
              if (isCounterOffer || isNewOffer) 30.verticalSpace,
              if (!readOnly) ...<Widget>[
                Text(
                  'Diamond Weight',
                  style: AppFontStyles.skolaSans(
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
                32.verticalSpace,
                _buildStoneWeight(controller),
                51.verticalSpace,
                _buildPickers(controller),
                51.verticalSpace,
              ] else ...<Widget>[
                Text(
                  LocaleKeys.diamond_details_view_diamond_details.tr,
                  style: AppFontStyles.skolaSans(
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
                32.verticalSpace,
                RichText(
                  text: TextSpan(
                    children: <InlineSpan>[
                      TextSpan(
                        text: '${diamond?.weight}',
                      ),
                      TextSpan(
                        text: 'CT,',
                        style: AppFontStyles.skolaSans(
                          fontSize: 56.sp,
                          fontWeight: FontWeight.w700,
                          color: AppColors.k101C28,
                        ),
                      ),
                      TextSpan(
                        text: ' ${diamondShape?.comaValidation ?? ''}'
                                ' ${diamondColor?.comaValidation ?? ''} '
                                '${diamondClarity ?? ''}'
                            .toUpperCase(),
                      ),
                    ],
                    style: AppFontStyles.skolaSans(
                      fontSize: 56.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.k101C28,
                    ),
                  ),
                ),
                51.verticalSpace,
                // _buildHorizontalDisc(controller),
                //51.verticalSpace,
              ],
              //_buildRapPrice(controller),
              51.verticalSpace,
              _buildYourPrice(controller),
            ],
          ),
          51.verticalSpace,
          if (isNewOffer || isCounterOffer)
            AppButton.text(
              buttonText: isNewOffer ? 'SUBMIT OFFER' : 'SUBMIT COUNTER OFFER',
              onPressed: () {
                Get.back();
                if (UserProvider.isTypeVendor) {
                  if (Get.isRegistered<VendorStockOfferDetailsController>()) {
                    Get.find<VendorStockOfferDetailsController>()
                        .submitCounterOffer(
                            offerPrice: controller.finalPrice().toDouble());
                  }
                } else {
                  if (Get.isRegistered<OfferDetailsController>() &&
                      isCounterOffer) {
                    Get.find<OfferDetailsController>().updateOffer(
                        offerPrice: controller.finalPrice().toDouble());
                  } else if (Get.isRegistered<DiamondDetailsController>(
                      tag: 'view_diamond_$dimondId')) {
                    Get.find<DiamondDetailsController>(
                            tag: 'view_diamond_$dimondId')
                        .createOfferApi(
                            offerPrice: controller.finalPrice().toDouble());
                  }
                }
              },
              borderColor: AppColors.k101C28,
              borderWidth: 4.w,
              padding: EdgeInsets.zero,
              buttonSize: isNewOffer ? Size(444.w, 100.h) : Size(510.w, 100.h),
              backgroundColor: AppColors.kffffff,
              borderRadius: 500.r,
              buttonTextStyle: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w700,
                fontSize: 35.sp,
                color: AppColors.k101C28,
              ),
            ),
          // if (btnProps.hide && !(isNewOffer || isCounterOffer))
          //   51.verticalSpace,
          // if (btnProps.hide && !(isNewOffer || isCounterOffer))
          //   Column(
          //     children: <Widget>[
          //       AppButton.text(
          //         buttonText: !readOnly
          //             ? 'UPDATE PRICE'
          //             : LocaleKeys.diamond_details_view_update_price_list.tr,
          //         onPressed: () {
          //           Get.isRegistered<DashboardController>()
          //               ? Get.find<DashboardController>()
          //                   .updatePriceList()
          //                   .then((value) => controller
          //                       .priceUpdatedAt(LocalStore.priceUpdatedAt()))
          //               : Get.find<VendorDashboardController>()
          //                   .updatePriceList()
          //                   .then((value) => controller
          //                       .priceUpdatedAt(LocalStore.priceUpdatedAt()));
          //         },
          //         borderColor: AppColors.k101C28,
          //         borderWidth: 4.w,
          //         padding: EdgeInsets.zero,
          //         buttonSize: Size(444.w, 80.h),
          //         backgroundColor: AppColors.kffffff,
          //         borderRadius: 500.r,
          //         buttonTextStyle: AppFontStyles.skolaSans(
          //           fontWeight: FontWeight.w700,
          //           fontSize: 35.sp,
          //           color: AppColors.k101C28,
          //         ),
          //       ),
          //       51.verticalSpace,
          //       Obx(
          //         () => RichText(
          //           text: TextSpan(
          //             children: <InlineSpan>[
          //               TextSpan(
          //                 text: LocaleKeys
          //                     .diamond_details_view_last_price_updated.tr,
          //                 style: AppFontStyles.skolaSans(
          //                   fontSize: 35.sp,
          //                   fontWeight: FontWeight.w700,
          //                   color: AppColors.k70777E,
          //                 ),
          //               ),
          //               TextSpan(
          //                 text: '${controller.priceUpdatedAt()}',
          //                 style: AppFontStyles.skolaSans(
          //                   fontSize: 35.sp,
          //                   fontWeight: FontWeight.w700,
          //                   color: AppColors.k101C28,
          //                 ),
          //               ),
          //             ],
          //           ),
          //         ),
          //       ),
          //       50.verticalSpace,
          //     ],
          //   ),
        ],
      ),
    ).marginOnly(bottom: MediaQuery.of(Get.context!).padding.bottom);
  }

  Row _buildYourPrice(FancyCalculatorController controller) => Row(
        children: <Widget>[
          Expanded(
            child: CalcTextField(
              focusNode: controller.pricePerCaratFocusNode,
              controller: controller.pricePerCaratController,
              labelText: LocaleKeys.diamond_details_view_price_ct.tr,
              suffixText: 'ct',
              onChanged: (String text) {
                if (text.isNotEmpty) {
                  controller.pricePerCarat(text.toDouble);
                } else {
                  controller.pricePerCarat(0.0);
                }
                controller.onPerCaratChange(text);
              },
              prefixText: '\$',
              textStyle: AppFontStyles.skolaSans(
                color: AppColors.k101C28,
                fontSize: 45.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          51.horizontalSpace,
          Expanded(
            child: CalcTextField(
              focusNode: controller.finalPriceFocusNode,
              controller: controller.finalPriceController,
              labelText: LocaleKeys.diamond_details_view_total_price.tr,
              onChanged: (String text) {
                if (text.isNotEmpty) {
                  controller.finalPrice(text.toDouble);
                } else {
                  controller.finalPrice(0.0);
                }
                controller.onFinalPriceChange(text);
              },
              prefixText: '\$',
              textStyle: AppFontStyles.skolaSans(
                color: AppColors.k101C28,
                fontSize: 45.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      );

  Widget _buildPickers(FancyCalculatorController controller) => ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Container(
          height: 400.h,
          width: Get.width,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.k70777E,
              width: 1.w,
            ),
            borderRadius: BorderRadius.circular(40.r),
          ),
          foregroundDecoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(40.r),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Container(
                    height: 400.h,
                    width: Get.width,
                    child: ScrollConfiguration(
                      behavior:
                          const ScrollBehavior().copyWith(overscroll: false),
                      child: CupertinoPicker(
                        backgroundColor: AppColors.kffffff,
                        itemExtent: 30,
                        selectionOverlay: Container(
                          height: 30.h,
                          margin: REdgeInsets.only(
                            left: 48,
                            bottom: 5,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.k000000.withOpacity(0.06),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(100.r),
                              bottomLeft: Radius.circular(100.r),
                            ),
                          ),
                        ),
                        children: showShapeList
                            .map(
                              (String e) => Padding(
                                padding: REdgeInsets.only(left: 48),
                                child: Center(
                                  child: Text(
                                    getShapeAbbreviation(e),
                                    style: AppFontStyles.skolaSans(
                                      fontSize: 45.sp,
                                      color: AppColors.k101C28,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 2.w,
                                    ),
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        onSelectedItemChanged: (int index) {
                          vibrate();
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 400.h,
                    child: ScrollConfiguration(
                      behavior:
                          const ScrollBehavior().copyWith(overscroll: false),
                      child: CupertinoPicker(
                        backgroundColor: AppColors.kffffff,
                        itemExtent: 30,
                        selectionOverlay: Container(
                          color: AppColors.k000000.withOpacity(0.06),
                          height: 30.h,
                          margin: REdgeInsets.only(
                            bottom: 5,
                          ),
                        ),
                        children: colorList
                            .map(
                              (String e) => Center(
                                child: Text(
                                  e.toUpperCase(),
                                  style: AppFontStyles.skolaSans(
                                    fontSize: 45.sp,
                                    color: AppColors.k101C28,
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: 2.w,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        onSelectedItemChanged: (int index) {
                          vibrate();
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 400.h,
                    child: ScrollConfiguration(
                      behavior:
                          const ScrollBehavior().copyWith(overscroll: false),
                      child: CupertinoPicker(
                        backgroundColor: AppColors.kffffff,
                        itemExtent: 30,
                        selectionOverlay: Container(
                          color: AppColors.k000000.withOpacity(0.06),
                          height: 30.h,
                          margin: REdgeInsets.only(
                            bottom: 5,
                          ),
                        ),
                        children: clarityList
                            .map(
                              (String e) => Center(
                                child: Text(
                                  e.toUpperCase(),
                                  style: AppFontStyles.skolaSans(
                                    fontSize: 45.sp,
                                    color: AppColors.k101C28,
                                    fontWeight: FontWeight.w500,
                                    letterSpacing: 2.w,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        onSelectedItemChanged: (int index) {
                          vibrate();
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  CalcTextField _buildStoneWeight(FancyCalculatorController controller) =>
      CalcTextField(
        focusNode: controller.weightFocusNode,
        label: 'DIAMOND WEIGHT',
        controller: controller.weightController,
        textStyle: AppFontStyles.skolaSans(
          fontSize: 56.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w700,
        ),
        borderColor: AppColors.kBEFFFC,
        hintText: 'eg. 1.32',
        cursorColor: AppColors.kFF9800,
        fillColor: AppColors.kBEFFFC,
        contentPadding: EdgeInsets.zero,
        prefixIcons: Padding(
          padding: REdgeInsets.symmetric(horizontal: 30, vertical: 40),
          child: Image(
            image: const AssetImage(AppImages.calculationDiamondPath),
            height: 70.h,
            width: 70.w,
          ),
        ),
        onChanged: (String text) {
          if (text.isNotEmpty) {
            controller.weight(text.toDouble);
            if (controller.weight() == 0) {
              controller.finalPrice(0);
            }
          } else {
            controller.weight(0.0);
            controller.finalPrice(0);
          }
          controller.onWeightChange(text);
        },
        suffixIcon: IconButton(
          onPressed: () {
            controller.weightController.text = '0';
            controller
              ..weight(0.0)
              // controller.resetCalculator();
              ..onWeightChange(controller.weightController.text);
            controller.weightFocusNode.requestFocus();
            controller.weightController.selection = TextSelection(
              baseOffset: 0,
              extentOffset: controller.weightController.text.length,
            );
          },
          icon: Icon(
            Icons.clear,
            size: 70.w,
          ),
        ),
      );
}

class CalcTextField extends StatelessWidget {
  const CalcTextField({
    this.label,
    this.labelText = '',
    this.cursorColor,
    this.borderColor,
    this.focusNode,
    this.controller,
    this.readOnly = false,
    this.onChanged,
    this.prefixIcon,
    this.prefixIcons,
    this.suffixIcon,
    this.suffixText,
    this.hintText,
    this.hintStyle,
    this.prefixText,
    this.textStyle,
    this.textAlign,
    this.fillColor,
    this.contentPadding,
    this.autofocus,
    this.inputFormatters,
  });

  final String? label;
  final String labelText;
  final Color? cursorColor;
  final Color? borderColor;
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final bool readOnly;
  final Function(String)? onChanged;
  final Widget? prefixIcon;
  final Widget? prefixIcons;
  final Widget? suffixIcon;
  final String? suffixText;
  final String? hintText;
  final TextStyle? hintStyle;
  final String? prefixText;
  final TextStyle? textStyle;
  final TextAlign? textAlign;
  final Color? fillColor;
  final EdgeInsets? contentPadding;
  final bool? autofocus;
  final List<TextInputFormatter>? inputFormatters;

  @override
  Widget build(BuildContext context) {
    Color cursorColor = this.cursorColor ?? AppColors.k111111;

    ///
    return TextField(
      focusNode: focusNode,
      style: textStyle ??
          AppFontStyles.skolaSans(
            fontWeight: FontWeight.bold,
          ),
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      readOnly: readOnly,
      onTap: vibrate,
      autofocus: autofocus ?? false,
      cursorColor: cursorColor,
      decoration: _buildInputDecoration(),
      textAlign: textAlign ?? TextAlign.start,
      onChanged: onChanged,
      inputFormatters: inputFormatters ??
          <TextInputFormatter>[
            DecimalTextInputFormatter(decimalRange: 2),
            twoDecimal,
          ],
      textInputAction: TextInputAction.done,
    );
  }

  InputDecoration _buildInputDecoration() => InputDecoration(
        contentPadding: contentPadding,
        isDense: true,
        fillColor: fillColor ?? AppColors.kffffff,
        filled: true,
        suffixStyle: AppFontStyles.skolaSans(
          color: AppColors.k101C28,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        prefixIcon: prefixIcons,
        prefixStyle: AppFontStyles.skolaSans(
          color: AppColors.k101C28,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        constraints: BoxConstraints(
          minHeight: 150.h,
          maxHeight: 150.h,
        ),
        labelStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        labelText: labelText,
        // label: Text(labelText),
        prefix: prefixIcon,
        suffixIcon: suffixIcon,
        suffixText: suffixText,
        hintText: hintText,
        hintStyle: hintStyle ??
            AppFontStyles.skolaSans(
              fontSize: 16,
            ),
        prefixText: prefixText,
        errorMaxLines: 3,
        border: _buildOutlineInputBorder(),
        disabledBorder: _buildOutlineInputBorder(),
        enabledBorder: _buildOutlineInputBorder(),
        errorBorder: _buildOutlineInputBorder(),
        focusedErrorBorder: _buildOutlineInputBorder(),
        focusedBorder: _buildOutlineInputBorder(),
      );

  OutlineInputBorder _buildOutlineInputBorder() => OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: borderColor ?? AppColors.k70777E,
          width: 1.w,
        ),
      );
}
