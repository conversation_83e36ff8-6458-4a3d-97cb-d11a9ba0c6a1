import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:get/get.dart';

/// MyAddressController
class MyAddressController extends GetxController {
  @override
  void onInit() {
    super.onInit();
  }

  /// edit address
  Future<void> editAddress() async {
    await Get.toNamed(Routes.NEW_COMPANY_REGISTRATION,
        arguments: {'isFromBuyRequest': true});
    update();
  }
}
