import 'dart:async';
import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/jewellery_showcase/jewellery_showcase.dart';
import 'package:diamond_company_app/app/modules/jewellery_showcase/controllers/jewellery_showcase_grid_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:readmore/readmore.dart';
import 'package:video_player/video_player.dart';

class ReelVideoPlayer extends StatefulWidget {
  final JewelleryShowcase video;
  final int index;
  final JewelleryShowcaseGridController controller;

  const ReelVideoPlayer({
    super.key,
    required this.video,
    required this.index,
    required this.controller,
  });

  @override
  State<ReelVideoPlayer> createState() => _ReelVideoPlayerState();
}

class _ReelVideoPlayerState extends State<ReelVideoPlayer> {
  VideoPlayerController? _controller;
  bool _isError = false;
  bool _isInitialized = false;
  bool _showControls = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    if (_isInitialized) return;

    if (!mounted) return;

    setState(() {
      _isError = false;
      _isInitialized = false;
    });

    try {
      // Dispose old controller if exists
      if (_controller != null) {
        await _controller?.dispose();
      }

      _controller = VideoPlayerController.networkUrl(
        Uri.parse(widget.video.videoUrl),
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
      );

      // Initialize and configure controller
      await _controller?.initialize();

      if (!mounted) return;

      setState(() => _isInitialized = true);
      await _controller?.setLooping(true);
      await _controller?.play();

      /// add to seen video ids
      if (Get.isRegistered<JewelleryShowcaseGridController>()) {
        unawaited(Get.find<JewelleryShowcaseGridController>()
            .addSeenJewelleryShowcase(jewelleryIds: [widget.video.id]));
      }
    } catch (e, stackTrace) {
      debugPrint('Video Error: $e\n$stackTrace');
      if (mounted) {
        setState(() {
          _isError = true;
          _isInitialized = false;
        });
      }
    }
  }

  @override
  void dispose() {
    try {
      if (_controller != null) {
        _controller?.dispose();
      }
    } catch (e) {
      debugPrint('Error disposing controller: $e');
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(ReelVideoPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.video.videoUrl != widget.video.videoUrl) {
      _initializeVideo();
    }
  }

  Widget _buildErrorWidget() => Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.white,
                size: 40,
              ),
              const SizedBox(height: 12),
              const Text(
                'Video unavailable',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: _initializeVideo,
                child: const Text(
                  'Retry',
                  style: TextStyle(color: Colors.blue),
                ),
              ),
            ],
          ),
        ),
      );

  @override
  Widget build(BuildContext context) {
    if (_isError) {
      return _buildErrorWidget();
    }

    return GestureDetector(
      onTap: _togglePlayPause,
      child: Container(
        height: Get.height,
        width: Get.width,
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(Get.context!).padding.bottom,
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Container(color: Colors.black),
            if (_isInitialized)
              FittedBox(
                child: SizedBox(
                  width: _controller?.value.size.width,
                  height: _controller?.value.size.height,
                  child: VideoPlayer(_controller!),
                ),
              ),
            if (!_isInitialized && !_isError)
              const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            if (_isInitialized) _buildPlayPauseOverlay(),
            if (_isInitialized) _buildProgressIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          //padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.black.withAlpha(180),
                Colors.transparent,
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black12.withValues(alpha: 0.3),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: ReadMoreText(
                        widget.video.description,
                        trimLines: 3,
                        colorClickableText: Colors.blue,
                        trimMode: TrimMode.Line,
                        trimCollapsedText: LocaleKeys.dashboard_read_more.tr,
                        trimExpandedText: LocaleKeys.dashboard_read_less.tr,
                        lessStyle: AppFontStyles.skolaSans(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.white54,
                          fontStyle: FontStyle.italic,
                        ),
                        moreStyle: AppFontStyles.skolaSans(
                          fontSize: 38.sp,
                          fontWeight: FontWeight.w400,
                          color: Colors.white54,
                          fontStyle: FontStyle.italic,
                        ),
                        style: AppFontStyles.skolaSans(
                          fontSize: 45.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.kffffff,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(width: min(15.w, 8)),
                    _buildActions(),
                  ],
                ),
              ),
              20.verticalSpace,
              if (widget.video.tags?.isNotEmpty ?? false) ...[
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    children: (widget.video.tags ?? [])
                        .map(
                          (String e) => StatusChip(
                            text: e,
                            textStyle: AppFontStyles.skolaSans(
                              fontSize: 32.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white54,
                            ),
                            color: Colors.white12,
                          ).paddingOnly(right: 10.w),
                        )
                        .toList(),
                  ),
                ),
                25.verticalSpace
              ],
              ValueListenableBuilder<VideoPlayerValue>(
                valueListenable: _controller!,
                builder: (BuildContext context, VideoPlayerValue value,
                        Widget? child) =>
                    TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1000),
                  tween: Tween<double>(
                    begin: 0,
                    end: _controller?.value.duration.inMilliseconds == 0
                        ? 0
                        : value.position.inMilliseconds /
                            _controller!.value.duration.inMilliseconds,
                  ),
                  curve: Curves.easeInOut,
                  builder:
                      (BuildContext context, double value, Widget? child) =>
                          LinearProgressIndicator(
                    value: value,
                    backgroundColor: Colors.white24,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white54,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ).paddingSymmetric(horizontal: 8),
            ],
          ),
        ),
      );

  Column _buildActions() => Column(
        children: [
          /// share jewellery
          Container(
            height: 120.h,
            width: min(120.w, 50),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: AppColors.kffffff.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => widget.controller.shareJewelleryVideo(),
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.kffffff.withValues(alpha: 0.2),
                fixedSize: Size(min(120.w, 50), 120.h),
                shape: const CircleBorder(),
              ),
              icon: Icon(
                Icons.share,
                color: AppColors.kffffff,
                size: min(60.sp, 20),
              ),
            ),
          ),

          SizedBox(height: min(25.h, 15)),

          /// like jewellery
          Obx(
            () => Container(
              height: 120.h,
              width: min(120.w, 50),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: AppColors.kffffff.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: () => widget.controller.addRemoveLikeJewellery(),
                style: IconButton.styleFrom(
                  backgroundColor: AppColors.kffffff.withValues(alpha: 0.2),
                  fixedSize: Size(min(120.w, 50), 120.h),
                  shape: const CircleBorder(),
                ),
                icon: Icon(
                  widget.controller.isVideoLiked()
                      ? Icons.favorite
                      : Icons.favorite_border_rounded,
                  color: AppColors.kffffff,
                  size: min(60.sp, 20),
                ),
              ),
            ),
          ),
        ],
      );

  Widget _buildPlayPauseOverlay() => AnimatedOpacity(
        opacity: _showControls ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          color: Colors.black26,
          child: Center(
            child: Icon(
              _controller?.value.isPlaying ?? false
                  ? Icons.pause_circle
                  : Icons.play_circle,
              size: 80,
              color: Colors.white.withAlpha(204),
            ),
          ),
        ),
      );

  void _togglePlayPause() {
    try {
      if (_controller == null) {
        return;
      }
      if (_controller?.value.isPlaying ?? false) {
        _controller?.pause();
      } else {
        _controller?.play();
      }
      setState(() {
        _showControls = true;
      });
      Future<void>.delayed(
        const Duration(seconds: 3),
        () => setState(() {
          _showControls = false;
        }),
      );
    } catch (e) {
      debugPrint('Play/Pause Error: $e');
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final String minutes = twoDigits(duration.inMinutes.remainder(60));
    final String seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
