import 'package:get/get.dart';

import '../controllers/jewellery_showcase_controller.dart';
import '../controllers/jewellery_showcase_grid_controller.dart';

class JewelleryShowcaseBinding extends Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<JewelleryShowcaseGridController>()) {
      Get.put(Get.find<JewelleryShowcaseGridController>());
    } else {
      Get.lazyPut<JewelleryShowcaseController>(
        () => JewelleryShowcaseController(),
      );
    }
  }
}
