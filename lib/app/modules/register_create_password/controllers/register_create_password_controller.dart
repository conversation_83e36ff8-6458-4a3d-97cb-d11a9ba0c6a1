import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

/// RegisterCreatePasswordController
class RegisterCreatePasswordController extends GetxController {
  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// New Password Controller
  TextEditingController newPasswordController = TextEditingController();

  /// Confirm Password Controller
  TextEditingController confirmPasswordController = TextEditingController();

  /// SetNewPasswordVisible
  RxBool setNewPasswordVisible = true.obs;

  /// ConfirmNewPasswordVisible
  RxBool confirmNewPasswordVisible = true.obs;

  /// Toggle Function for CreateNewPassword Visible
  void createNewPasswordVisibleToggle() {
    setNewPasswordVisible.value = !setNewPasswordVisible.value;
  }

  /// Toggle Function for ConfirmNewPassword Visible
  void confirmNewPasswordVisibleToggle() {
    confirmNewPasswordVisible.value = !confirmNewPasswordVisible.value;
  }

  /// email
  String email = '';

  /// otp
  String otp = '';

  /// isLoading
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    email = Get.arguments['email'] ?? '';
    otp = Get.arguments['otp'] ?? '';
  }

  ///create password
  Future<void> createPassword() async {
    try {
      isLoading(true);
      await AuthProvider.resetPassword(
        email: email.toString().toLowerCase().trim(),
        otp: otp,
        newPassword: newPasswordController.text,
      );
      appSnackbar(
        message: 'Password created successfully',
        snackBarState: SnackBarState.SUCCESS,
      );
      isLoading(false);
      await Get.offAllNamed(Routes.LOGIN, arguments: {
        'isFromRegister': true,
        'newPassword': newPasswordController.text,
        'email': email.toString().toLowerCase().trim(),
      });
    } on DioException catch (e) {
      isLoading(false);
      appSnackbar(
        message: 'Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
    }
  }
}
