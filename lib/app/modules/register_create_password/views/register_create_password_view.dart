import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/custom_back_button.dart';
import '../controllers/register_create_password_controller.dart';
import 'dart:math';

/// RegisterCreatePasswordView
class RegisterCreatePasswordView
    extends GetView<RegisterCreatePasswordController> {
  /// RegisterCreatePasswordView
  const RegisterCreatePasswordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: true,
        body: _buildBody(),
      );

  /// Build Body
  Widget _buildBody() => SafeArea(
        child: ListView(
          padding: EdgeInsets.only(
            left: min(60.w, 15),
            right: min(60.w, 15),
            top: 60.h,
            bottom: 60.h + MediaQuery.of(Get.context!).viewInsets.bottom,
          ),
          children: [
            FormBuilder(
              key: controller.fbKey,
              child: Column(
                children: <Widget>[
                  25.verticalSpace,
                  _buildHeaderPart(),
                  105.verticalSpace,
                  _buildText(),
                  125.verticalSpace,
                  _buildCreateNewPasswordTextFormField(),
                  80.verticalSpace,
                  _buildConfirmNewPasswordTextFormField(),
                  80.verticalSpace,
                  _buildAppButton(),
                ],
              ),
            ),
          ],
        ),
      );

  /// Build Confirm Pass Field
  Widget _buildConfirmNewPasswordTextFormField() => Obx(
        () => _buildTextFormField(
          controller: controller.confirmPasswordController,
          onPressed: () {
            controller.confirmNewPasswordVisibleToggle();
          },
          isShow: controller.confirmNewPasswordVisible.value,
          isVisible: controller.confirmNewPasswordVisible.value,
          name: LocaleKeys.create_password_confirm_new_password.tr,
          labelText: LocaleKeys.create_password_confirm_new_password.tr,
          obscureText: controller.confirmNewPasswordVisible.value,
          validator: (String? value) {
            if (value?.isEmpty ?? true) {
              return LocaleKeys.validation_password_is_empty.tr;
            } else if (value!.length < 6) {
              return LocaleKeys.validation_password_length.tr;
            } else if (value.trim() !=
                controller
                    .fbKey
                    .currentState
                    ?.fields[LocaleKeys.create_password_create_new_password.tr]
                    ?.value
                    .toString()
                    .trim()) {
              return LocaleKeys.validation_password_does_not_match.tr;
            }
            return null;
          },
        ),
      );

  /// Build Create Pass Field
  Widget _buildCreateNewPasswordTextFormField() => Obx(
        () => _buildTextFormField(
          controller: controller.newPasswordController,
          onPressed: () {
            controller.createNewPasswordVisibleToggle();
          },
          isShow: controller.setNewPasswordVisible.value,
          isVisible: controller.setNewPasswordVisible.value,
          name: LocaleKeys.create_password_create_new_password.tr,
          labelText: LocaleKeys.create_password_create_new_password.tr,
          obscureText: controller.setNewPasswordVisible.value,
          validator: (String? value) {
            final String password = value?.trim() ?? '';
            return password.isValidPassword();
          },
        ),
      );

  /// Build Text Field
  Widget _buildTextFormField({
    required String name,
    required TextEditingController controller,
    required String? Function(String?)? validator,
    required String labelText,
    required bool obscureText,
    required VoidCallback onPressed,
    required bool isShow,
    required bool isVisible,
    String? Function(String?)? onChange,
    String? Function(String?)? onSubmit,
  }) =>
      AppTextFormField(
        controller: controller,
        onSubmit: onSubmit,
        onChange: onChange,
        name: name,
        validator: validator,
        labelText: labelText,
        obscureText: obscureText,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: isVisible ? AppColors.k70777E : AppColors.k101C28,
          fontWeight: FontWeight.w400,
          letterSpacing: 10.w,
        ),
        suffixIcon: IconButton(
          onPressed: onPressed,
          icon: isShow
              ? SvgPicture.asset(
                  AppImages.visibilityOffPath,
                  height: 57.h,
                  width: 57.w,
                )
              : Icon(
                  Icons.visibility_outlined,
                  color: AppColors.k70777E,
                  size: 57.h,
                ),
        ),
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48),
      );

  /// Build AppButton
  Widget _buildAppButton() => Obx(
        () => AppButton.text(
          buttonText: LocaleKeys.create_password_set_password.tr,
          onPressed: () {
            if (controller.fbKey.currentState?.saveAndValidate() ?? false) {
              controller.createPassword();
            }
          },
          buttonSize: Size(Get.width, 150.h),
          backgroundColor: AppColors.k101C28,
          borderRadius: 24.r,
          isLoading: controller.isLoading(),
          borderColor: AppColors.k101C28,
          buttonTextStyle: TextStyle(
            fontSize: 45.sp,
            fontWeight: FontWeight.w700,
            color: AppColors.kBEFFFC,
          ),
        ),
      );

  /// Build Text
  Widget _buildText() => Column(
        children: <Widget>[
          Text(
            LocaleKeys.create_password_create_password.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 70.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
          50.verticalSpace,
          Text(
            LocaleKeys.create_password_change_password_both_are_same.tr,
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k70777E.withOpacity(0.6),
            ),
          ),
        ],
      );

  /// Build Header Part
  Widget _buildHeaderPart() => SizedBox(
        height: 200.h,
        width: Get.width,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: REdgeInsets.only(top: 45),
              child: CustomBackButton(onPressed: () {
                Get.back();
              }),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: SizedBox(
                height: 200.h,
                width: 200.w,
                child: AppImages.appLogo,
              ),
            ),
          ],
        ),
      );
}
