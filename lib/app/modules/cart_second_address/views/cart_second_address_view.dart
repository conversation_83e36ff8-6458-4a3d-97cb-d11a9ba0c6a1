import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/diamond_summary.dart';
import 'package:diamond_company_app/app/data/models/jewellery_summary.dart';
import 'package:diamond_company_app/app/data/models/shipment/shipment.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/ui/components/diamond_details.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:readmore/readmore.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/design_config.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../../../ui/components/dashed_divider.dart';
import '../controllers/cart_second_address_controller.dart';

/// CartSecondAddressView
class CartSecondAddressView extends GetView<CartSecondAddressController> {
  /// CartSecondAddressView
  const CartSecondAddressView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildCommonAppbar(),
        body: _buildBody(),
      );

  /// Build Body
  Widget _buildBody() {
    Rx<DiamondSummary> diamondSummary =
        Get.find<CartItemService>().diamondSummary;
    final Rx<JewellerySummary> jewellerySummary =
        Get.find<CartItemService>().jewellerySummary;
    final Rx<DiamondSummary> meleeSummary =
        Get.find<CartItemService>().meleeSummary;
    if (controller.buyRequestType == BuyRequestType.OFFER) {
      diamondSummary = DiamondSummary(
        totalPcs: 1,
        totalCts: controller.stockOffer?.stock?.weight ?? 0,
        avgDiscount: controller.stockOffer?.stock?.price?.discount ?? 0,
        avgPricePerCt: controller.stockOffer?.stock?.price?.pricePerCarat ?? 0,
        totalFinalPrice: controller.stockOffer?.offerPrice ?? 0,
      ).obs;
    }
    return Container(
      height: Get.height,
      width: Get.width,
      child: Stack(
        children: <Widget>[
          SingleChildScrollView(
            controller: controller.scrollController,
            physics: const BouncingScrollPhysics(),
            padding: REdgeInsets.only(
              left: 60,
              right: 60,
              bottom: 60,
            ),
            child: Obx(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  /// Billing Address
                  50.verticalSpace,
                  _buildHeaderPart(),
                  60.verticalSpace,
                  _buildBillingAddressDetails(),
                  80.verticalSpace,
                  _buildShippingAddressText(),
                  60.verticalSpace,
                  GetBuilder<CartSecondAddressController>(
                    builder: (_) =>
                        UserProvider.currentUser?.shippingAddress == null
                            ? _buildAddShippingAddress()
                            : _buildShippingAddressDetails(),
                  ),
                  80.verticalSpace,
                  if (controller.shipmentsList.isNotEmpty)
                    _buildShippingMethodText(),
                  if (controller.shipmentsList.isNotEmpty) 60.verticalSpace,
                  if (controller.shipmentsList.isNotEmpty)
                    _buildShippingMethodDetails(),
                  80.verticalSpace,
                  //if (controller.buyRequestType != BuyRequestType.OFFER)
                  _buildCartItemsDetails(
                    diamondSummary,
                    meleeSummary,
                    jewellerySummary,
                  ),

                  960.verticalSpace,
                ],
              ),
            ),
          ),
          _buildBottomCard(),
        ],
      ),
    );
  }

  Column _buildCartItemsDetails(
    Rx<DiamondSummary> diamondSummary,
    Rx<DiamondSummary> meleeSummary,
    Rx<JewellerySummary> jewellerySummary,
  ) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (diamondSummary().totalPcs != 0)
            _buildCartDetailsItem(
              title: LocaleKeys.cart_diamonds.tr,
              icon: AppImages.diamondCart,
              totalPcs: diamondSummary().totalPcs.toString().padLeft(2, '0'),
              totalPrice: diamondSummary().totalFinalPrice.toString().toPrice(),
              totalCts: diamondSummary().totalCts.toStringAsFixed(2),
              avgPricePerCarat:
                  diamondSummary().avgPricePerCt.toStringAsFixed(2),
              type: BuyRequestItemType.DIAMOND,
            ),
          if (diamondSummary().totalPcs != 0) 20.verticalSpace,
          if (meleeSummary().totalPcs != 0)
            _buildCartDetailsItem(
              title: LocaleKeys.cart_melee.tr,
              icon: AppImages.meleeCart,
              totalPcs: meleeSummary().totalPcs.toString().padLeft(2, '0'),
              totalPrice: meleeSummary().totalFinalPrice.toString().toPrice(),
              totalCts: meleeSummary().totalCts.toStringAsFixed(2),
              avgPricePerCarat: meleeSummary().avgPricePerCt.toStringAsFixed(2),
              type: BuyRequestItemType.MELEE,
            ),
          if (meleeSummary().totalPcs != 0) 20.verticalSpace,
          if (jewellerySummary().totalPcs != 0)
            _buildCartDetailsItem(
              title: LocaleKeys.cart_jewelry.tr,
              icon: AppImages.jewelleryCart,
              totalPcs: jewellerySummary().totalPcs.toString().padLeft(2, '0'),
              totalPrice:
                  jewellerySummary().totalFinalPrice.toString().toPrice(),
              totalCts: jewellerySummary().totalWeight.toStringAsFixed(2),
              avgPricePerCarat: '0',
              type: BuyRequestItemType.JEWELLERIES,
            ),
        ],
      );

  Container _buildCartDetailsItem({
    required String title,
    required String totalPcs,
    required String totalCts,
    required String avgPricePerCarat,
    required String totalPrice,
    required SvgPicture icon,
    required BuyRequestItemType type,
  }) =>
      Container(
        decoration: BoxDecoration(
          color: AppColors.k128807.withOpacity(0.10),
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
        ),
        child: Column(
          children: [
            ListTile(
              leading: SizedBox(
                child: icon,
                height: 70.h,
                width: min(70.w, 25),
              ),
              horizontalTitleGap: min(40.w, 15),
              minVerticalPadding: 0,
              title: Text(
                title,
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k101C28,
                ),
              ),
              trailing: Container(
                padding: REdgeInsets.symmetric(horizontal: 32, vertical: 10),
                decoration: BoxDecoration(
                  color: AppColors.kffffff,
                  borderRadius: BorderRadius.all(
                    Radius.circular(500.r),
                  ),
                ),
                child: Text(
                  totalPrice,
                  style: AppFontStyles.skolaSans(
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
              ),
            ),
            Container(
              width: Get.width,
              margin: REdgeInsets.symmetric(horizontal: 32),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24.r),
              ),
              child: Padding(
                padding: REdgeInsets.all(24),
                child: Row(
                  children: <Widget>[
                    24.horizontalSpace,
                    _buildProductDetails(
                      text: LocaleKeys.cart_second_address_pcs.tr,
                      value: totalPcs,
                    ),
                    const Spacer(),
                    if (type != BuyRequestItemType.JEWELLERIES)
                      _buildProductDetails(
                        text: LocaleKeys.cart_second_address_cts.tr,
                        value: totalCts,
                      ),
                    if (type != BuyRequestItemType.JEWELLERIES) const Spacer(),
                    if (type != BuyRequestItemType.JEWELLERIES)
                      _buildProductDetails(
                        text: LocaleKeys.cart_second_address_price_ct.tr,
                        value: avgPricePerCarat,
                      ),
                    if (type != BuyRequestItemType.JEWELLERIES) const Spacer(),
                  ],
                ),
              ),
            ),
            32.verticalSpace,
          ],
        ),
      );

  Column _buildAddShippingAddress() => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            height: 120.h,
            width: 120.w,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.kEAEFF4,
            ),
            alignment: Alignment.center,
            child: SizedBox(
              height: 80.h,
              width: 57.w,
              child: AppImages.locationLogo,
            ),
          ),
          34.verticalSpace,
          Text(
            LocaleKeys.cart_second_address_no_shipping_address_yet.tr,
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
          34.verticalSpace,
          InkWell(
            onTap: () => Get.back(),
            borderRadius: BorderRadius.circular(500.r),
            child: Container(
              height: 80.h,
              width: 335.w,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.k101C28,
                  width: 4.w,
                ),
                borderRadius: BorderRadius.circular(500.r),
              ),
              alignment: Alignment.center,
              child: Text(
                LocaleKeys.cart_second_address_add_address.tr,
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  letterSpacing: 3.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
        ],
      );

  /// Build Bottom Card
  Widget _buildBottomCard() => Positioned(
        bottom: 0,
        left: 0.w,
        right: 0.w,
        child: _buildDiamondDetails(),
      );

  ///  Build Shipping Method Details
  Widget _buildShippingMethodDetails() => Container(
        padding: REdgeInsets.symmetric(horizontal: 60, vertical: 50),
        decoration: BoxDecoration(
          border: Border.all(
            width: 1.w,
            color: AppColors.k70777E,
          ),
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Obx(
          () => ListView.separated(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const ClampingScrollPhysics(),
            itemCount: controller.shipmentsList.length,
            itemBuilder: (BuildContext context, int index) =>
                _buildShippingItem(controller.shipmentsList[index], index),
            separatorBuilder: (BuildContext context, int index) => Divider(
              color: AppColors.k70777E.withOpacity(0.7),
              thickness: 1.w,
              height: 80.h,
            ),
          ),
        ),
      );

  Widget _buildShippingItem(Shipment shipment, int index) => InkWell(
        onTap: () => controller.selectShippingMethodIndex(index),
        splashColor: Colors.transparent,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildAnimatedContainerForCustomError(index),
            //32.horizontalSpace,
            SizedBox(width: min(32.w, 10)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    shipment.title.toString(),
                    style: AppFontStyles.skolaSans(
                      fontSize: 35.sp,
                      fontWeight: FontWeight.w700,
                      color: AppColors.k101C28,
                    ),
                  ),
                  24.verticalSpace,
                  Container(
                    width: 679.w,
                    child: ReadMoreText(
                      shipment.description.toString(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k101C28.withOpacity(0.6),
                      ),
                      trimMode: TrimMode.Line,
                      trimLines: 1,
                      colorClickableText: Colors.pink,
                      trimCollapsedText:
                          '\n${LocaleKeys.dashboard_read_more.tr}',
                      trimExpandedText:
                          '\n${LocaleKeys.dashboard_read_less.tr}',
                      lessStyle: AppFontStyles.skolaSans(
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k101C28,
                        decoration: TextDecoration.underline,
                      ),
                      moreStyle: AppFontStyles.skolaSans(
                        fontSize: 30.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k101C28,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            //const Spacer(),
            Text(
              '${shipment.amount.toString().toPrice()}',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k1A47E8,
              ),
            ),
          ],
        ),
      );

  ///  Build Shipping Method Text
  Widget _buildShippingMethodText() => Text(
        LocaleKeys.cart_second_address_shipping_method.tr,
        style: AppFontStyles.skolaSans(
          fontSize: 40.sp,
          fontWeight: FontWeight.w900,
          color: AppColors.k101C28,
          letterSpacing: 5.sp,
        ),
      );

  ///  Build Shipping Address Details
  Widget _buildShippingAddressDetails() => Container(
        width: Get.width,
        padding: REdgeInsets.symmetric(vertical: 40, horizontal: 60),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.k70777E,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            16.verticalSpace,
            _buildPersonDetail(
              icon: AppImages.personSvg,
              text:
                  '${UserProvider.currentUser?.shippingAddress?.fullName ?? ''}',
            ),
            33.verticalSpace,
            _buildPersonDetail(
              icon: Icon(
                Icons.location_on_outlined,
                color: AppColors.k101C28,
                size: 50.sp,
              ),
              text:
                  '${UserProvider.currentUser?.shippingAddress?.street?.comaValidation ?? ''}\n'
                  '${UserProvider.currentUser?.shippingAddress?.addressLineTwo?.comaValidation ?? ''}\n'
                  '${UserProvider.currentUser?.shippingAddress?.zipCode?.comaValidation ?? ''} ${UserProvider.currentUser?.shippingAddress?.city?.comaValidation ?? ''}\n'
                  '${UserProvider.currentUser?.shippingAddress?.state?.comaValidation ?? ''} ${UserProvider.currentUser?.shippingAddress?.country?.comaValidation ?? ''}',
            ),
            33.verticalSpace,
            _buildPersonDetail(
                icon: AppImages.phoneIcon,
                text: UserProvider.currentUser?.phone ?? ''),
            33.verticalSpace,
            _buildPersonDetail(
                icon: AppImages.mailIconLogo,
                text: UserProvider.currentUser?.email ?? ''),
          ],
        ),
      );

  ///  Build Shipping Address Text
  Widget _buildShippingAddressText() => Text(
        LocaleKeys.cart_second_address_shipping_address.tr,
        style: AppFontStyles.skolaSans(
          fontSize: 40.sp,
          fontWeight: FontWeight.w900,
          color: AppColors.k101C28,
          letterSpacing: 5.sp,
        ),
      );

  ///  Build Billing Address Details
  Widget _buildBillingAddressDetails() =>
      GetBuilder<CartSecondAddressController>(
        builder: (_) => Container(
          width: Get.width,
          padding: REdgeInsets.symmetric(vertical: 40, horizontal: 60),
          decoration: BoxDecoration(
            border: Border.all(
              width: 1.w,
              color: AppColors.k70777E,
            ),
            borderRadius: BorderRadius.circular(24.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              16.verticalSpace,
              _buildPersonDetail(
                icon: AppImages.personSvg,
                text:
                    '${UserProvider.currentUser?.billingAddress?.fullName ?? ''}',
              ),
              33.verticalSpace,
              _buildPersonDetail(
                icon: Icon(
                  Icons.location_on_outlined,
                  color: AppColors.k101C28,
                  size: 50.sp,
                ),
                text:
                    '${UserProvider.currentUser?.billingAddress?.street?.comaValidation ?? ''}\n'
                    '${UserProvider.currentUser?.billingAddress?.addressLineTwo?.comaValidation ?? ''}\n'
                    '${UserProvider.currentUser?.billingAddress?.zipCode?.comaValidation ?? ''} ${UserProvider.currentUser?.billingAddress?.city?.comaValidation ?? ''}\n'
                    '${UserProvider.currentUser?.billingAddress?.state?.comaValidation ?? ''} ${UserProvider.currentUser?.billingAddress?.country?.comaValidation ?? ''}',
              ),
              33.verticalSpace,
              _buildPersonDetail(
                  icon: AppImages.phoneIcon,
                  text: UserProvider.currentUser?.phone ?? ''),
              33.verticalSpace,
              _buildPersonDetail(
                  icon: AppImages.mailIconLogo,
                  text: UserProvider.currentUser?.email ?? ''),
            ],
          ),
        ),
      );

  /// Build Header Part
  Widget _buildHeaderPart() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            LocaleKeys.cart_second_address_billing_address.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w900,
              color: AppColors.k101C28,
              letterSpacing: 5.sp,
            ),
          ),
          AppButton.text(
            onPressed: () => controller.editBuyRequestAddress(),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            buttonText: LocaleKeys.cart_second_address_edit.tr,
            borderColor: AppColors.k101C28,
            visualDensity: const VisualDensity(
              vertical: VisualDensity.minimumDensity,
              horizontal: VisualDensity.minimumDensity,
            ),
            borderWidth: min(2, 4.w),
            padding: EdgeInsets.zero,
            buttonSize: Size(min(180.w, 100), 80.h),
            backgroundColor: AppColors.kffffff,
            borderRadius: 500.r,
            buttonTextStyle: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 35.sp,
              color: AppColors.k101C28,
              letterSpacing: 3.w,
            ),
          ),
        ],
      );

  /// Build Diamond Details
  Widget _buildDiamondDetails() {
    DiamondSummary summary = Get.find<CartItemService>().diamondSummary();
    if (controller.buyRequestType == BuyRequestType.OFFER) {
      summary = DiamondSummary(
        totalPcs: 1,
        totalCts: controller.stockOffer?.stock?.weight ?? 0,
        avgDiscount: controller.stockOffer?.stock?.price?.discount ?? 0,
        avgPricePerCt: controller.stockOffer?.stock?.price?.pricePerCarat ?? 0,
        totalFinalPrice: controller.stockOffer?.offerPrice ?? 0,
      );
    }
    return AnimatedContainer(
      duration: const Duration(seconds: 2),
      decoration: BoxDecoration(
        color: AppColors.kEAEFF4,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24.r),
          topRight: Radius.circular(24.r),
        ),
      ),
      child: Obx(
        () => DiamondDetails(
          buttonText: LocaleKeys.cart_continue.tr,
          isLoading: controller.isLoading() || controller.isLoadingCredit(),
          onPressed: () => controller.onClickChoosePayment(),
          widget: Padding(
            padding: REdgeInsets.all(40),
            child: Column(
              children: <Widget>[
                _buildFullDetails(
                  text: LocaleKeys.cart_second_address_total_value.tr,
                  value: controller
                      .getTotalItemsAmount(summary: summary)
                      .toString()
                      .toPrice(),
                ),
                24.verticalSpace,
                _buildFullDetails(
                  text: LocaleKeys.cart_second_address_shipping_fees.tr,
                  value: controller.selectShippingMethodIndex() != -1
                      ? '${controller.shipmentsList[controller.selectShippingMethodIndex()].amount ?? 0}'
                          .toPrice()
                      : 'Free',
                ),
                if (!(UserProvider.currentUser?.isVerified ?? false))
                  24.verticalSpace,
                if (!(UserProvider.currentUser?.isVerified ?? false))
                  _buildFullDetails(
                    text: LocaleKeys.bill_summary_taxes.tr,
                    value: controller
                        .getTotalItemsAmount(summary: summary)
                        .toString()
                        .toTaxPrice(tax: controller.tax())
                        .toString()
                        .toPrice(),
                  ),
                24.verticalSpace,
                CustomDashedDivider(color: AppColors.k70777E.withOpacity(0.2)),
                24.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      LocaleKeys.cart_second_address_total.tr,
                      style: AppFontStyles.skolaSans(
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w900,
                        color: AppColors.k101C28,
                      ),
                    ),
                    Text(
                      controller
                          .getGrandTotal(summary: summary)
                          .toString()
                          .toPrice(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w900,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build PersonDetail
  Widget _buildPersonDetail({required Widget icon, required String text}) =>
      Row(
        children: <Widget>[
          SizedBox(
            height: 39.h,
            width: 45.w,
            child: icon,
          ),
          15.horizontalSpace,
          Text(
            text,
            style: AppFontStyles.skolaSans(
              color: AppColors.k101C28,
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.start,
          ),
        ],
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
        title: Text(
          LocaleKeys.cart_second_address_address.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
      );

  /// Build AnimatedContainer For CustomError
  Widget _buildAnimatedContainerForCustomError(int index) => Obx(
        () => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          height: 56.h,
          width: 56.w,
          margin: REdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            border: Border.all(
              width: controller.selectShippingMethodIndex() == index
                  ? min(19.w, 6)
                  : min(3.w, 2),
              color: AppColors.k101C28,
            ),
          ),
        ),
      );

  /// Build Full Details
  Widget _buildFullDetails({required String text, required String value}) =>
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            text,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
          Text(
            value,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );

  /// Build Product Details
  Widget _buildProductDetails({required String text, required String value}) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            text,
            style: AppFontStyles.skolaSans(
              fontSize: 30.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28.withOpacity(0.6),
            ),
          ),
          20.verticalSpace,
          Text(
            value,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );
}
