import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/data/models/crm_model/crm_model.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/modules/connectivity_service/controllers/connectivity_service_controller.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/providers/crm_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import '../../../data/config/logger.dart';
import '../../../routes/app_pages.dart';

/// LoginController
class LoginController extends GetxController {
  /// scaffold key
  final GlobalKey<ScaffoldState> scaffoldKey = new GlobalKey<ScaffoldState>();

  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// isUpdate
  final RxBool isFromRegister = false.obs;

  /// isLoginError
  RxBool isLoginError = false.obs;

  /// CRM details
  Rx<CRMModel> crmDetails = CRMModel().obs;

  /// isNavigatorActivated
  RxBool isNavigatorActivated = false.obs;

  @override
  void onReady() {
    super.onReady();
    if (Get.arguments != null) {
      isFromRegister(Get.arguments['isFromRegister']);
      isRegistrationComplete(isFromRegister());
      isRegistrationComplete.refresh();

      logE('EMAIL:: ${Get.arguments['email']}');
      logE('PASSWORD:: ${Get.arguments['newPassword']}');

      WidgetsBinding.instance.addPostFrameCallback(
        (Duration timeStamp) {
          fbKey.currentState?.fields['emailAddress']
              ?.didChange(Get.arguments['email']);
          fbKey.currentState?.fields['password']
              ?.didChange(Get.arguments['newPassword']);

          if (isFromRegister()) {
            login();
          }
        },
      );
    }
  }

  @override
  void onInit() {
    super.onInit();

    DesignConfig.updateSideMenuId = 2;

    ///  connectivity service controller
    Get.put(ConnectivityServiceController(), permanent: true);
    getCRMDetails();
  }

  /// IsVisible
  final RxBool isVisible = true.obs;

  /// IsLoading
  final RxBool isLoading = false.obs;

  /// Is registration complete
  final RxBool isRegistrationComplete = false.obs;

  /// Toggle Function for password visible
  void obsecureToggle() {
    isVisible.toggle();
  }

  /// login
  Future<void> login({String? email, String? password}) async {
    /*final Pay _payClient = Pay(<PayProvider, PaymentConfiguration>{
      PayProvider.apple_pay: PaymentConfiguration.fromJsonString('''{
  "provider": "apple_pay",
  "data": {
    "merchantIdentifier": "merchant.jascina.pay",
    "displayName": "Diamond Company",
    "merchantCapabilities": ["3DS", "debit", "credit"],
    "supportedNetworks": ["amex", "visa", "discover", "masterCard"],
    "countryCode": "US",
    "currencyCode": "USD",
    "requiredBillingContactFields": ["emailAddress", "name", "phoneNumber", "postalAddress"],
    "requiredShippingContactFields": []
  }
}'''),
    });

    if (await _payClient.userCanPay(PayProvider.apple_pay)) {
      await _payClient.showPaymentSelector(
        PayProvider.apple_pay,
        <PaymentItem>[
          const PaymentItem(
            label: 'Total',
            amount: '99.99',
            status: PaymentItemStatus.final_price,
          )
        ],
      );
    } else {
      appSnackbar(
        message: 'User cannot pay',
        snackBarState: SnackBarState.DANGER,
      );
      await _payClient.showPaymentSelector(
        PayProvider.apple_pay,
        <PaymentItem>[
          const PaymentItem(
            label: 'Total',
            amount: '99.99',
            status: PaymentItemStatus.final_price,
          )
        ],
      );
    }

    return;*/

    try {
      if (fbKey.currentState?.saveAndValidate() ?? false) {
        isLoading(true);
        isLoginError(false);
        final UserEntity? userEntity = await AuthProvider.login(
          email: email ??
              fbKey.currentState?.fields['emailAddress']?.value
                  .toString()
                  .toLowerCase()
                  .trim() ??
              '',
          password: password ??
              fbKey.currentState?.fields['password']?.value.toString().trim() ??
              '',
        );
        if (userEntity != null) {
          if (!(userEntity.isPhoneVerified ?? false)) {
            if (kIsWeb) {
              Get.toNamed(Routes.WEB_REGISTRATION);
              WidgetsBinding.instance.addPostFrameCallback(
                (Duration timeStamp) {
                  Get.routing.args = {'userEntity': userEntity};
                  Get.toNamed(
                    Routes.REGISTER_PHONE,
                    id: DesignConfig.listingSideMenuId,
                    arguments: {'userEntity': userEntity},
                  );
                },
              );
            } else {
              await Get.toNamed(
                Routes.REGISTER_PHONE,
                arguments: {'userEntity': userEntity},
              );
            }
          } else if ((userEntity.isPhoneVerified ?? false) &&
              !(userEntity.isEmailVerified ?? false)) {
            if (kIsWeb) {
              Get.toNamed(Routes.WEB_REGISTRATION);
              WidgetsBinding.instance.addPostFrameCallback(
                (Duration timeStamp) {
                  Get.routing.args = {'userEntity': userEntity};
                  Get.toNamed(
                    Routes.REGISTER_EMAIL,
                    id: DesignConfig.listingSideMenuId,
                    arguments: {'userEntity': userEntity},
                  );
                },
              );
            } else {
              await Get.toNamed(
                Routes.REGISTER_EMAIL,
                arguments: {'userEntity': userEntity},
              );
            }
          } else {
            appSnackbar(
              message: LocaleKeys.messages_user_loggedIn_successfully.tr,
              snackBarState: SnackBarState.SUCCESS,
            );
            await Get.offNamed(Routes.DASHBOARD);
          }
        }
      } else {
        appSnackbar(
          message: LocaleKeys.messages_please_enter_valid_email_and_password.tr,
          snackBarState: SnackBarState.DANGER,
        );
      }
    } on DioException catch (e) {
      isLoading(false);
      isLoginError(true);
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  /// check registration complete
  Future<void> checkRegistrationComplete() async {
    try {
      if (fbKey.currentState?.saveAndValidate() ?? false) {
        isLoading(true);
        final UserEntity? userEntity =
            await AuthProvider.checkRegistrationComplete(
          email: fbKey.currentState?.fields['emailAddress']?.value
                  .toString()
                  .toLowerCase()
                  .trim() ??
              '',
        );

        if (userEntity != null) {
          final bool isPasswordCreated = userEntity.isPasswordCreated ?? false;

          isRegistrationComplete(isPasswordCreated);
          if (isRegistrationComplete()) {
            WidgetsBinding.instance.addPostFrameCallback(
              (Duration timeStamp) {
                fbKey.currentState?.fields['password']?.focus();
              },
            );
          } else if (!isRegistrationComplete()) {
            if (kIsWeb) {
              Get.toNamed(Routes.WEB_REGISTRATION);
              WidgetsBinding.instance.addPostFrameCallback(
                (Duration timeStamp) {
                  Get.routing.args = {'userEntity': userEntity};
                  Get.toNamed(
                    Routes.REGISTER_PHONE,
                    id: DesignConfig.listingSideMenuId,
                    arguments: {'userEntity': userEntity},
                  );
                },
              );
            } else {
              await Get.toNamed(
                Routes.REGISTER_PHONE,
                arguments: {'userEntity': userEntity},
              );
            }
          }
        }
      }
    } on DioException catch (e) {
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  /// get crm details
  Future<void> getCRMDetails() async {
    try {
      final CRMModel? crm = await CrmProvider.getCRMDetails();
      if (crm != null) {
        crmDetails(crm);
        crmDetails.refresh();
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    }
  }

  void openEndDrawer() {
    scaffoldKey?.currentState?.openEndDrawer();
  }

  void closeEndDrawer() {
    if (scaffoldKey?.currentState?.isEndDrawerOpen ?? false) {
      scaffoldKey?.currentState?.closeEndDrawer();
    }
  }
}
