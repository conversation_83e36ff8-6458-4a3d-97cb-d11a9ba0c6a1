import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_bottom_sheets.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../controllers/login_controller.dart';

/// LoginView
class LoginView extends GetView<LoginController> {
  /// LoginView
  const LoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: true,
        body: _buildBody(context),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SingleChildScrollView(
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: Obx(
          () => SafeArea(
            child: FormBuilder(
              key: controller.fbKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  237.verticalSpace,
                  _buildImageLogo(),
                  237.verticalSpace,
                  _buildText(),
                  91.verticalSpace,
                  _buildEmailTextFormField(),
                  76.verticalSpace,
                  _buildPasswordTextFormField(),
                  76.verticalSpace,
                  _buildForgotPasswordButton(),
                  76.verticalSpace,

                  Obx(
                    () => _buildCommonAppButton(
                      isLoading: controller.isLoading(),
                      buttonText: controller.isRegistrationComplete()
                          ? LocaleKeys.login_login.tr
                          : 'Next',
                      onPressed: () => controller.isRegistrationComplete()
                          ? controller.login()
                          : controller.checkRegistrationComplete(),
                    ),
                  ),
                  30.verticalSpace,
                  _buildCommonAppButton(
                    buttonText: LocaleKeys.login_create_an_account.tr,
                    onPressed: () =>
                        Get.toNamed(Routes.NEW_COMPANY_REGISTRATION),
                    backgroundColor: AppColors.kEAEFF4,
                    borderColor: AppColors.kEAEFF4,
                    buttonTextColor: AppColors.k101C28,
                  ),
                  30.verticalSpace,
                  ListTile(
                    onTap: () => Get.toNamed(Routes.VENDOR_LOGIN),
                    titleAlignment: ListTileTitleAlignment.center,
                    title: Center(
                      child: RichText(
                        text: TextSpan(
                          text: 'Login as a ',
                          style: AppFontStyles.skolaSans(
                            fontSize: 40.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.k70777E,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: 'Vendor',
                              style: AppFontStyles.skolaSans(
                                decoration: TextDecoration.underline,
                                fontSize: 40.sp,
                                fontWeight: FontWeight.w700,
                                color: AppColors.k101C28,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  //20.verticalSpace,
                  TextButton(
                    onPressed: () {
                      showCustomerRelationBottomSheet();
                    },
                    child: Text(
                      'Do you have any questions?',
                      style: AppFontStyles.skolaSans(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ),
                  20.verticalSpace,
                  MediaQuery.of(context).padding.bottom.verticalSpace,
                ],
              ),
            ),
          ),
        ),
      );

  /// Build Password Textformfield
  Widget _buildPasswordTextFormField() => Visibility(
        visible: controller.isRegistrationComplete(),
        child: AppTextFormField(
          name: 'password',
          readOnly: controller.isLoading(),
          obscureText: controller.isVisible.value,
          onSubmit: (String? value) => controller.login(),
          validator: (String? value) {
            final String? trimmedValue = value?.trim();
            if (trimmedValue?.isEmpty ?? true) {
              return LocaleKeys.validation_password_is_empty.tr;
            } else if (trimmedValue!.length < 6) {
              return LocaleKeys.validation_password_length.tr;
            }
            return null;
          },
          labelText: LocaleKeys.login_password.tr,
          constraints: BoxConstraints(
            minHeight: 150.h,
          ),
          labelTextStyle: AppFontStyles.skolaSans(
            color: AppColors.k70777E,
            fontSize: 45.sp,
            fontWeight: FontWeight.w400,
          ),
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            color: controller.isVisible.value
                ? AppColors.k70777E
                : AppColors.k101C28,
            fontWeight: FontWeight.w400,
            letterSpacing: 10.w,
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24.r),
            borderSide: BorderSide(
              width: 1.w,
              color: AppColors.k70777E,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24.r),
            borderSide: BorderSide(
              width: 1.w,
              color: AppColors.k70777E,
            ),
          ),
          suffixIcon: IconButton(
            onPressed: () => controller.obsecureToggle(),
            icon: controller.isVisible.value
                ? SvgPicture.asset(
                    AppImages.visibilityOffPath,
                    height: 57.h,
                    width: 57.w,
                  )
                : Icon(
                    Icons.visibility_outlined,
                    color: AppColors.k70777E,
                    size: 57.h,
                  ),
          ),
          showBorder: true,
          fillColor: AppColors.k70777E,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24.r),
            borderSide: BorderSide(
              color: AppColors.k70777E,
              width: 1.w,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24.r),
            borderSide: BorderSide(
              color: AppColors.k70777E,
              width: 1.w,
            ),
          ),
          border: const OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.k70777E,
            ),
          ),
          contentPadding: REdgeInsets.only(left: 48),
        ),
      );

  /// Build Common App Button
  Widget _buildCommonAppButton({
    required String buttonText,
    required void Function() onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
    bool? isLoading,
  }) =>
      AppButton.text(
        isLoading: isLoading ?? false,
        buttonText: buttonText,
        onPressed: onPressed,
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );

  /// Build Forgot Password Button
  Widget _buildForgotPasswordButton() => Visibility(
        visible: controller.isRegistrationComplete(),
        child: InkWell(
          onTap: () {
            final String email = controller
                    .fbKey.currentState?.fields['emailAddress']?.value
                    .toString()
                    .toLowerCase()
                    .trim() ??
                '';
            Get.toNamed(Routes.FORGOT_PASSWORD,
                arguments: email.isNotEmpty ? email : null);
          },
          child: Text(
            LocaleKeys.login_forgot_password.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              color: AppColors.k101C28,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      );

  /// Build Email Textformfield
  Widget _buildEmailTextFormField() => AppTextFormField(
        name: 'emailAddress',
        readOnly: controller.isLoading(),
        keyboardType: TextInputType.emailAddress,
        textInputAction: TextInputAction.next,
        autofillHints: const <String>[AutofillHints.email],
        onSubmit: (_) {
          if (!controller.isRegistrationComplete()) {
            controller.checkRegistrationComplete();
          } else {
            controller.fbKey.currentState?.fields['password']?.focus();
          }
        },
        validator: (String? value) {
          final String? trimmedValue = value?.trim();
          if (trimmedValue?.isEmpty ?? true) {
            return LocaleKeys.validation_email_is_empty.tr;
          } else if (!trimmedValue!.isEmail) {
            return LocaleKeys.validation_email_is_invalid.tr;
          }

          return null;
        },
        labelText: LocaleKeys.login_email_address.tr,
        contentPadding: REdgeInsets.only(left: 48),
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: AppColors.k101C28,
          fontWeight: FontWeight.w400,
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
      );

  /// Build Text
  Widget _buildText() => Text(
        LocaleKeys.login_login.tr,
        style: AppFontStyles.skolaSans(
          fontSize: 100.sp,
          fontWeight: FontWeight.w700,
          color: AppColors.k101C28,
        ),
      );

  /// Build Image logo
  Widget _buildImageLogo() => Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: 614.h,
            maxWidth: 600.w,
          ),
          child: AppImages.appLogo,
        ),
      );
}
