import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:diamond_company_app/app/chat_service/chat_enum.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_details_controller.dart';
import 'package:diamond_company_app/app/modules/diamond_details/views/video_view.dart';
import 'package:diamond_company_app/app/modules/diamond_details/views/web_video_view.dart';
import 'package:diamond_company_app/app/modules/offer_details/controllers/offer_details_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/ui/components/diamond_details.dart';
import 'package:diamond_company_app/app/ui/components/share_diamonds_bottom_sheet.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../generated/locales.g.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../../../ui/components/common_ask_queries.dart';
import '../../../ui/components/status_chip.dart';

/// DiamondDetailsView
class DiamondDetailsView extends GetView<DiamondDetailsController> {
  /// DiamondDetailsView
  const DiamondDetailsView({
    required this.viewTag,
    super.key,
  });

  final String viewTag;

  @override
  String get tag => viewTag;

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  Widget _buildBody() => Container(
        width: Get.width,
        height: Get.height,
        child: Stack(
          children: <Widget>[
            Obx(
              () => controller.isLoading()
                  ? const LinearProgressIndicator(color: AppColors.k1A47E8)
                  : RefreshIndicator(
                      onRefresh: () => controller.fetchDiamondDetails(),
                      color: AppColors.k101C28,
                      child: CustomScrollView(
                        clipBehavior: Clip.none,
                        controller: controller.scrollController,
                        keyboardDismissBehavior:
                            ScrollViewKeyboardDismissBehavior.onDrag,
                        slivers: <Widget>[
                          // _buildHeader(controller.diamond()),
                          _buildDetails(controller.diamond()),
                        ],
                      ),
                    ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomSheet(),
            ),
          ],
        ),
      );

  /// Build Bottom Sheet
  Widget _buildBottomSheet() => Obx(
        () => DiamondDetails(
          isCustomButton: true,
          customButtonWidget: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                controller.addedToCart()
                    ? LocaleKeys.diamond_details_view_added_to_cart.tr
                    : LocaleKeys.diamond_details_view_add_to_cart.tr,
                style: TextStyle(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                  color: controller.addedToCart()
                      ? Colors.white
                      : AppColors.kBEFFFC,
                ),
              ),
              18.horizontalSpace,
              if (controller.addedToCart())
                Container(
                  height: 40.h,
                  width: min(40.w, 20),
                  padding: REdgeInsets.symmetric(horizontal: 10, vertical: 13),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Image(
                    image: AssetImage(
                      AppImages.verifyIconPath,
                    ),
                  ),
                ),
            ],
          ),
          onPressed: () {
            if (controller.diamond().isOfferCreated ?? false) {
              appSnackbar(
                message: LocaleKeys.messages_diamond_not_available.tr,
                snackBarState: SnackBarState.INFO,
              );
              return;
            }
            controller.toggleCart();
          },
          onCreateOffer: (Get.find<DashboardController>()
                      .dashboardMetrics()
                      .showOffersModule ??
                  false)
              ? () {
                  if (controller.addedToCart()) {
                    appSnackbar(
                      message:
                          LocaleKeys.messages_diamond_already_added_cart.tr,
                      snackBarState: SnackBarState.INFO,
                    );
                    return;
                  }
                  //showCreateOfferDialog(controller: controller);
                  if (!(controller.diamond().isOfferCreated ?? true)) {
                    if (controller.diamond().availability == 'AVAILABLE') {
                      controller.diamond()
                        ..isNewOffer = true
                        ..calculate.call();
                    } else {
                      appSnackbar(
                        message: LocaleKeys.messages_diamond_not_available.tr,
                        snackBarState: SnackBarState.INFO,
                      );
                    }
                  } else if (controller.diamond().offerId != null) {
                    if (Get.isRegistered<OfferDetailsController>()) {
                      if (kIsWeb) {
                        DesignConfig.popItem();
                      } else {
                        Get.back();
                      }
                    } else {
                      if (kIsWeb) {
                        Get.routing.args =
                            StockOffer(id: controller.diamond().offerId);
                        Get.toNamed(Routes.OFFER_DETAILS,
                            id: DesignConfig.listingSideMenuId);
                      } else {
                        Get.toNamed(
                          Routes.OFFER_DETAILS,
                          arguments: StockOffer(
                            id: controller.diamond().offerId,
                          ),
                        );
                      }
                    }
                  }
                }
              : null,
          isOfferCreating: controller.isOfferCreating(),
          diamond: controller.diamond(),
          backgroundColor:
              controller.addedToCart() ? AppColors.k128807 : AppColors.k101C28,
          borderColor:
              controller.addedToCart() ? AppColors.k128807 : AppColors.k101C28,
          buttonTextColor:
              controller.addedToCart() ? Colors.white : AppColors.kBEFFFC,
          widget: Obx(
            () => Column(
              children: <Widget>[
                Container(
                  width: Get.width,
                  padding: REdgeInsets.symmetric(horizontal: 40, vertical: 30),
                  decoration: BoxDecoration(
                    color: AppColors.kEAEFF4,
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: Row(
                    children: <Widget>[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            LocaleKeys.diamond_details_view_diamond_price.tr,
                            style: AppFontStyles.skolaSans(
                              fontSize: 30.sp,
                              fontWeight: FontWeight.w500,
                              color: AppColors.k101C28.withOpacity(0.6),
                            ),
                          ),
                          8.verticalSpace,
                          Row(
                            children: <Widget>[
                              Text(
                                '${controller.diamond().price?.pricePerCarat.toString().toPrice() ?? '-'}/CT',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 40.sp,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.k101C28,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const Spacer(),
                      Text(
                        '${controller.diamond().price?.finalPrice.toString().toPrice() ?? '-'}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 64.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  SliverToBoxAdapter _buildDetails(DiamondEntity diamond) => SliverToBoxAdapter(
        child: Column(
          children: <Widget>[
            50.verticalSpace,
            _buildSlider(),
            30.verticalSpace,
            Padding(
              padding: REdgeInsets.symmetric(horizontal: 60),
              child: Row(
                children: <Widget>[
                  StatusChip(
                    text: diamond.type.toString() ?? '',
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kEAEFF4,
                  ),
                  SizedBox(width: min(16.w, 8)),
                  // if (diamond.availability?.isNotEmpty ??
                  //     false || diamond.availability != null)
                  //   StatusChip(
                  //     text:
                  //         diamond.availability.toString().capitalizeFirst ?? '',
                  //     textStyle: AppFontStyles.skolaSans(
                  //       fontWeight: FontWeight.w500,
                  //       fontSize: 40.sp,
                  //       color: AppColors.kffffff,
                  //     ),
                  //     color: diamond.statusColor,
                  //   ),
                  // 16.horizontalSpace,
                  (diamond.country?.isEmpty ?? false || diamond.country == null)
                      ? const SizedBox.shrink()
                      : StatusChip(
                          text: diamond.country == null
                              ? ''
                              : diamond.country.toString().toUpperCase() ?? '',
                          color: Colors.white,
                          textStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.k101C28,
                          ),
                          border: Border.all(
                            color: AppColors.k000000.withOpacity(0.6),
                            width: min(2.w, 2),
                          ),
                        ),
                  const Spacer(),
                  IconButton.outlined(
                    constraints: BoxConstraints(
                      maxHeight: 120.h,
                      maxWidth: min(120.w, 50),
                    ),
                    style: OutlinedButton.styleFrom(
                      backgroundColor: AppColors.kEAEFF4,
                      side: BorderSide(color: AppColors.kEAEFF4, width: 1.w),
                      shape: const CircleBorder(),
                      padding: EdgeInsets.zero,
                      fixedSize: Size(min(120.w, 50), 120.h),
                    ),
                    onPressed: () {
                      showShareDiamondBottomSheet(
                          diamond: controller.diamond());
                    },
                    icon: const Icon(Icons.share, color: AppColors.k101C28),
                  ),
                  SizedBox(width: min(20.w, 8)),
                  StatefulBuilder(
                    builder: (BuildContext context, setState) => InkWell(
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () {
                        setState(() {
                          diamond.isWishlist = !diamond.isWishlist!;
                        });
                        controller.updateWishlistStock(
                          isFav: diamond.isWishlist ?? false,
                        );
                      },
                      child: CircleAvatar(
                        radius: 60.r,
                        backgroundColor: diamond.isWishlist ?? false
                            ? AppColors.k101C28
                            : AppColors.kEAEFF4,
                        child: diamond.isWishlist ?? false
                            ? AppImages.fillHeartSvg.paddingAll(33.r)
                            : AppImages.blackHeart,
                      ),
                    ),
                  ),
                  // InkWell(
                  //   onTap: () {},
                  //   borderRadius: BorderRadius.circular(100.r),
                  //   child: Container(
                  //     height: 120.h,
                  //     width: 120.w,
                  //     decoration: const BoxDecoration(
                  //       color: AppColors.kEAEFF4,
                  //       shape: BoxShape.circle,
                  //     ),
                  //     child: Container(
                  //       height: 64.h,
                  //       width: 64.w,
                  //       margin: REdgeInsets.all(28),
                  //       child: AppImages.blackHeart,
                  //     ),
                  //   ),
                  // ),
                  /*32.horizontalSpace,
                  InkWell(
                    onTap: () {},
                    borderRadius: BorderRadius.circular(100.r),
                    child: Container(
                      height: 120.h,
                      width: 120.w,
                      decoration: const BoxDecoration(
                        color: AppColors.kEAEFF4,
                        shape: BoxShape.circle,
                      ),
                      child: Container(
                        height: 72.h,
                        width: 72.w,
                        margin: REdgeInsets.all(28),
                        child: AppImages.inquiries,
                      ),
                    ),
                  ),*/
                ],
              ),
            ),
            Padding(
              padding: REdgeInsets.symmetric(horizontal: 60),
              child: ListTile(
                contentPadding: REdgeInsets.all(0),
                title: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Flexible(
                      child: Text(
                        '${diamond.displayShape?.toUpperCase() ?? '-'}, '
                                '${diamond.weight ?? '-'} ct, '
                            .toUpperCase(),
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w400,
                          fontSize: 64.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ),
                    Text(
                      '${diamond.finalColor ?? '-'} - ',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                    //30.horizontalSpace,
                    Text(
                      '${diamond.displayClarity?.toUpperCase() ?? '-'}',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ],
                ),
                subtitle: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      'Stock ID: ${diamond.skuPrefix?.toUpperCase() ?? ''}${diamond.skuNumber == null ? '' : diamond.skuNumber}'
                          .toUpperCase(),
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 64.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ],
                ),
                // trailing: SizedBox(
                //   height: 60.h,
                //   width: 182.w,
                //   child: AppImages.gia,
                // ),
              ),
            ),
            60.verticalSpace,
            _buildBasicDetails(diamond),
            350.verticalSpace,
          ],
        ),
      );

  Widget _buildBasicDetails(DiamondEntity diamond) {
    String fluoColor = diamond.displayFluorescenceColor?.toUpperCase() ?? '';

    if (fluoColor.isEmpty) {
      fluoColor = '-';
    }

    DiamondUtils.getFinishAbbreviation(diamond.displayCut);

    return Column(
      children: <Widget>[
        Container(
          margin: REdgeInsets.symmetric(horizontal: 60),
          padding: REdgeInsets.all(60),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.r),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 10.h),
                blurRadius: 104.r,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_certificate_number.tr,
                about: diamond.certificateNumber == ''
                    ? '-'
                    : diamond.certificateNumber?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_lab.tr,
                about:
                    diamond.lab == '' ? '-' : diamond.lab?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_cut.tr,
                about: diamond.displayCut == ''
                    ? '-'
                    : diamond.displayCut?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_polish.tr,
                about: diamond.displayPolish == ''
                    ? '-'
                    : diamond.displayPolish?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_symmetry.tr,
                about: diamond.displaySymmetry == ''
                    ? '-'
                    : diamond.displaySymmetry?.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_fluorescence.tr,
                about: diamond.displayIntensity == ''
                    ? '-'
                    : diamond.displayIntensity.toUpperCase() ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_fluorescence_color.tr,
                about: diamond.displayFluorescenceColor == ''
                    ? '-'
                    : diamond.displayFluorescenceColor?.toUpperCase() ?? '-',
              ),
              if (controller.diamond().isFancyColor)
                Column(
                  children: [
                    _buildAboutText(
                      text: LocaleKeys
                          .diamond_details_view_fluorescence_intensity.tr,
                      about: diamond.fancyColorIntensity == ''
                          ? '-'
                          : diamond.fancyColorIntensity?.toUpperCase() ?? '-',
                    ),
                    _buildAboutText(
                      text: LocaleKeys
                          .diamond_details_view_fluorescence_overtone.tr,
                      about: diamond.fancyColorOvertone == ''
                          ? '-'
                          : diamond.fancyColorOvertone?.toUpperCase() ?? '-',
                    ),
                  ],
                ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_measurements.tr,
                about: diamond.measurementWithUnit == ''
                    ? '-'
                    : diamond.measurementWithUnit ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_ratio.tr,
                about: diamond.lwRatio == '' ? '-' : diamond.lwRatio ?? '-',
              ),
              _buildAboutText(
                text: '${LocaleKeys.filter_Depth.tr} (%)',
                about: '${diamond.depth ?? '-'}',
              ),
              _buildAboutText(
                text: '${LocaleKeys.filter_Table.tr} (%)',
                about: '${diamond.table ?? '-'}',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_culet.tr,
                about: diamond.culetSize == '' || diamond.culetSize == null
                    ? '-'
                    : diamond.culetSize.toString().firstCharCapital ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_culet_condition.tr,
                about: diamond.culetCondition == '' || diamond.culetSize == null
                    ? '-'
                    : diamond.culetCondition.toString().firstCharCapital ?? '-',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_crown_angle.tr,
                about: '${diamond.crownAngle ?? '-'}',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_crown_height.tr,
                about: '${diamond.crownHeight ?? '-'}',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_pavilion_angle.tr,
                about: '${diamond.pavilionAngle ?? '-'}',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_pavilion_height.tr,
                about: '${diamond.pavilionDepth ?? '-'}',
              ),
              _buildAboutText(
                text: LocaleKeys.diamond_details_view_girdle.tr,
                about: diamond.girdle == '' ? '-' : diamond.girdle ?? '-',
              ),
            ],
          ),
        ),
        60.verticalSpace,
        controller.diamond().memberComment == null ||
                controller.diamond().memberComment == ''
            ? const SizedBox.shrink()
            : Column(
                children: [
                  Container(
                    padding: REdgeInsets.all(60),
                    margin: REdgeInsets.symmetric(horizontal: 60),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24.r),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          color: AppColors.k000000.withOpacity(0.1),
                          offset: Offset(0, 10.h),
                          blurRadius: 104.r,
                        ),
                      ],
                    ),
                    child: Column(
                      children: <Widget>[
                        Row(
                          children: [
                            StatusChip(
                              text: LocaleKeys.diamond_details_view_comment.tr,
                              textStyle: AppFontStyles.skolaSans(
                                fontWeight: FontWeight.w500,
                                fontSize: 40.sp,
                                color: AppColors.k101C28,
                              ),
                              color: AppColors.kEAEFF4,
                            ),
                          ],
                        ),
                        40.verticalSpace,
                        Text(
                          controller.diamond().memberComment ?? '',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                  ),
                  24.verticalSpace,
                ],
              ),
        controller.diamond().certificateComment == null ||
                controller.diamond().certificateComment == ''
            ? const SizedBox.shrink()
            : Column(
                children: [
                  Container(
                    width: Get.width,
                    padding: REdgeInsets.all(60),
                    margin: REdgeInsets.symmetric(horizontal: 60),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24.r),
                      border: Border.all(color: AppColors.k1A47E8, width: 2.w),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        RichText(
                          text: TextSpan(
                            text: LocaleKeys.diamond_details_view_lab_note.tr,
                            style: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w900,
                              color: AppColors.k101C28,
                              letterSpacing: 5.sp,
                            ),
                            children: <TextSpan>[
                              TextSpan(
                                text: ' 🔬',
                                style: AppFontStyles.skolaSans(
                                  fontSize: 48.sp,
                                  fontWeight: FontWeight.w900,
                                  color: AppColors.k101C28,
                                  letterSpacing: 5.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                        40.verticalSpace,
                        Text(
                          controller.diamond().certificateComment ?? '',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w400,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                  ),
                  60.verticalSpace,
                ],
              ),
        // Padding(
        //   padding: REdgeInsets.symmetric(horizontal: 60),
        //   child: Theme(
        //     data: Theme.of(Get.context!)
        //         .copyWith(dividerColor: Colors.transparent),
        //     child: ExpansionTile(
        //       title: Row(
        //         children: <Widget>[
        //           Text(
        //             LocaleKeys.diamond_details_view_certificate.tr,
        //             style: AppFontStyles.skolaSans(
        //               fontSize: 40.sp,
        //               fontWeight: FontWeight.w900,
        //               color: AppColors.k101C28,
        //               letterSpacing: 5.sp,
        //             ),
        //           ),
        //           const Spacer(),
        //           AppButton.text(
        //             buttonText: LocaleKeys.diamond_details_view_download.tr,
        //             onPressed: () {
        //               if (controller.diamond().haveCertificate) {
        //                 if (controller.diamond().haveCertificateImage) {
        //                   bool isPdf = controller.diamond().isCertificatePdf;
        //                   if (isPdf) {
        //                     controller.getCertificateUrl(
        //                       controller.diamond().certificateImage,
        //                     );
        //                   }
        //                   Get.to(
        //                     () => CertificateView(
        //                       viewTag: viewTag,
        //                       isWebView: !isPdf,
        //                     ),
        //                   );
        //                 } else {
        //                   controller.diamond().openNativeCertificate();
        //                 }
        //               }
        //             },
        //             borderColor: AppColors.k101C28,
        //             borderWidth: min(4.w, 1),
        //             padding: EdgeInsets.zero,
        //             buttonSize: Size(373.w, 80.h),
        //             backgroundColor: AppColors.kffffff,
        //             borderRadius: 500.r,
        //             buttonTextStyle: AppFontStyles.skolaSans(
        //               fontWeight: FontWeight.w700,
        //               fontSize: 35.sp,
        //               color: AppColors.k101C28,
        //             ),
        //           ),
        //         ],
        //       ),
        //       onExpansionChanged: (bool value) =>
        //           controller.certificateToggle(value),
        //       dense: true,
        //       visualDensity: const VisualDensity(
        //         horizontal: VisualDensity.minimumDensity,
        //         vertical: VisualDensity.minimumDensity,
        //       ),
        //       tilePadding: EdgeInsets.zero,
        //       trailing: Container(
        //         height: 100.h,
        //         width: 100.w,
        //         decoration: const BoxDecoration(
        //           color: AppColors.kEAEFF4,
        //           shape: BoxShape.circle,
        //         ),
        //         child: Obx(
        //           () => Icon(
        //             controller.certificateExpansionTile.value
        //                 ? Icons.expand_less_sharp
        //                 : Icons.expand_more_sharp,
        //             color: AppColors.k101C28,
        //           ),
        //         ),
        //       ),
        //       children: <Widget>[
        //         40.verticalSpace,
        //         Container(
        //           height: 735.h,
        //           width: Get.width,
        //           margin: REdgeInsets.only(right: 14, bottom: 14),
        //           decoration: BoxDecoration(
        //             color: Colors.white,
        //             borderRadius: BorderRadius.circular(24.r),
        //             boxShadow: <BoxShadow>[
        //               BoxShadow(
        //                 color: AppColors.k000000.withOpacity(0.25),
        //                 offset: Offset(5.w, 5.h),
        //                 blurRadius: 10.r,
        //               ),
        //             ],
        //           ),
        //           child: AppCachedImage(
        //             imageUrl:
        //                 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSot4yUKTPcV1QMYSizy25G-k7wMJAEIomb0g&s',
        //             // imageUrl: diamond.certificateImage.toString(),
        //             width: Get.width,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ),
        // ),
        // 60.verticalSpace,
        CommonAskQueries(
          //textTitle: LocaleKeys.diamond_details_view_chat_title.tr,
          onPressed: () {
            if (kIsWeb) {
              Get.routing.args = {
                'type': ChatType.DIAMOND_DETAILS,
                'diamond': diamond,
              };
              Get.toNamed(Routes.INQUIRY_MESSAGES,
                  id: DesignConfig.listingSideMenuId);
            } else {
              Get.toNamed(Routes.INQUIRY_MESSAGES, arguments: {
                'type': ChatType.DIAMOND_DETAILS,
                'diamond': diamond,
              });
            }
          },
        ),
        200.verticalSpace,
      ],
    );
  }

  /// Build About Text
  Widget _buildAboutText({required String text, String? about}) => Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Text(
              text,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28.withOpacity(0.6),
              ),
            ),
          ),
          20.verticalSpace,
          Expanded(
            flex: 2,
            child: Text(
              about ?? '-',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ],
      ).paddingOnly(bottom: 10.h);

  /// APP BAR
  CommonAppbar _buildAppBar() => CommonAppbar(
        titleSpacing: 0.w,
        title: Obx(
          () => controller.isLoading()
              ? const SizedBox.shrink()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      '${controller.diamond().displayShape.toUpperCase() ?? ''}, ${controller.diamond().weight ?? 'NONE'} ct, ${controller.diamond().finalColor ?? ''}, ${controller.diamond().displayClarity ?? ''}'
                          .toUpperCase(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.k101C28,
                      ),
                    ),
                    8.verticalSpace,
                    Text(
                      '${controller.diamond().displayCut ?? '-'} ${controller.diamond().displayPolish ?? ''} ${controller.diamond().displaySymmetry ?? ''} ${controller.diamond().displayIntensity ?? 'NONE'}'
                          .toUpperCase()
                          .trim(),
                      style: AppFontStyles.skolaSans(
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ],
                ),
        ),
        actions: <Widget>[
          Badge(
            backgroundColor: AppColors.k1A47E8,
            alignment: Alignment.topRight,
            label: Obx(
              () => Center(
                child: Text(
                  '${Get.find<CartItemService>().diamonds().length.toString()}',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    color: AppColors.kffffff,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            //offset: const Offset(-5, 3),
            child: IconButton.outlined(
              constraints: BoxConstraints(
                maxHeight: 120.h,
                maxWidth: min(120.w, 50),
              ),
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.kBEFFFC,
                side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
                shape: const CircleBorder(),
                padding: EdgeInsets.zero,
                fixedSize: Size(min(120.w, 50), 120.h),
              ),
              onPressed: () => Get.toNamed(Routes.CART_UPDATED,
                  id: DesignConfig.listingSideMenuId),
              icon: SizedBox(
                height: 64.h,
                width: 64.w,
                child: SvgPicture.asset(
                  AppImages.myCartOutlinePath,
                  color: AppColors.k101C28,
                ),
              ),
            ),
          ),
          IconButton.outlined(
            constraints: BoxConstraints(
              maxHeight: 120.h,
              maxWidth: min(120.w, 50),
            ),
            style: OutlinedButton.styleFrom(
              backgroundColor: AppColors.kBEFFFC,
              side: BorderSide(color: AppColors.kBEFFFC, width: 1.w),
              shape: const CircleBorder(),
              padding: EdgeInsets.zero,
              fixedSize: Size(min(120.w, 50), 120.h),
            ),
            onPressed: controller.diamond().calculate,
            icon: SizedBox(
              height: 64.h,
              width: 64.w,
              child: AppImages.calculationIcon,
            ),
          ),
        ],
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
      );

  /// Build Slider
  Widget _buildSlider() => Obx(
        () => Column(
          children: <Widget>[
            _buildSliderImages(diamond: controller.diamond()),
            30.verticalSpace,
            _buildSliderThumbnailImages(diamond: controller.diamond()),
            20.verticalSpace,
            //_buildSliderIndicator(diamond: controller.diamond()),
          ],
        ),
      );

  /// Build Slider Indicator
  Widget _buildSliderIndicator({required DiamondEntity diamond}) => Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List<Widget>.generate(
          diamond.images.length + 1,
          (int index) => GestureDetector(
            onTap: () {
              controller.carouselController.animateToPage(index);
              controller.current(index);
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 20.w,
              height: 20.h,
              margin: REdgeInsets.only(right: 20),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: controller.current() == index
                    ? AppColors.k101C28
                    : AppColors.kffffff,
                border: Border.all(
                  width: 2.w,
                  color: controller.current() == index
                      ? AppColors.k101C28
                      : AppColors.k9FA4A9,
                ),
              ),
            ),
          ),
        ),
      );

  /// Build Slider Images
  Widget _buildSliderImages({required DiamondEntity diamond}) {
    final List<String> sliderUrls = <String>[...diamond.media];
    if (sliderUrls.isEmpty) {
      sliderUrls.add('');
    }
    final List<Widget>? items =
        sliderUrls.map((String url) => _buildSliderItem(url)).toList();
    return sliderUrls.isEmpty
        ? const SizedBox.shrink()
        : Container(
            height: 1005.h,
            width: Get.width,
            child: CarouselSlider(
              items: items,
              carouselController: controller.carouselController,
              options: CarouselOptions(
                  enableInfiniteScroll: diamond.images.length > 1,
                  autoPlay: diamond.images.length > 1,
                  enlargeCenterPage: true,
                  aspectRatio: 1,
                  viewportFraction: 1,
                  onPageChanged:
                      (int index, CarouselPageChangedReason reason) =>
                          controller.current(index)),
            ),
          );
  }

  /// Build Slider Thumbnail Images
  Widget _buildSliderThumbnailImages({required DiamondEntity diamond}) {
    final List<String> sliderUrls = <String>[...diamond.media];
    if (sliderUrls.isEmpty) {
      sliderUrls.add('');
    }
    return sliderUrls.isEmpty
        ? const SizedBox.shrink()
        : Container(
            height: 150.h,
            width: Get.width,
            alignment: Alignment.center,
            child: ListView.separated(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: sliderUrls.length,
              itemBuilder: (BuildContext context, int index) => Obx(
                () => GestureDetector(
                  onTap: () {
                    controller.carouselController.animateToPage(index);
                    controller.current(index);
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24.r),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      height: min(150.h, 50),
                      width: min(150.w, 50),
                      decoration: BoxDecoration(
                        color: AppColors.kEAEFF4,
                        borderRadius: BorderRadius.circular(24.r),
                        border: controller.current() == index
                            ? Border.all(
                                color: AppColors.k101C28,
                                width: 1.5,
                              )
                            : null,
                      ),
                      child: _buildSliderThumbnailItem(sliderUrls[index]),
                    ),
                  ),
                ),
              ),
              separatorBuilder: (BuildContext context, int index) =>
                  SizedBox(width: min(20.w, 8)),
            ),
          );
  }

  Widget _buildSliderItem(String url) {
    if (url.isEmpty) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: SizedBox(
          height: 420.h,
          width: Get.width,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24.r),
            child: Image.asset(
              AppImages.diamondPlaceholderPngPath,
              fit: BoxFit.cover,
            ),
          ),
        ),
      );
    } else if (url.isImageUrl()) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24.r),
          child: SizedBox(
            height: 420.h,
            width: Get.width,
            child: PhotoView(
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              imageProvider: CachedNetworkImageProvider(url),
            ),
          ),
        ),
      );
    } else if (!url.isImageUrl()) {
      return Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: SizedBox(
          height: 420.h,
          width: Get.width,
          child: Stack(
            children: [
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(24.r),
                  child: WebVideoView(url: url),
                ),
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: PointerInterceptor(
                  child: IconButton(
                    onPressed: () {
                      if (kIsWeb) {
                        launchUrl(Uri.parse(url));
                      } else {
                        Get.to(
                          VideoView(
                            viewTag: viewTag,
                            url: url,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.open_in_new_outlined),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildSliderThumbnailItem(String url) {
    if (url.isEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: (controller.diamond().thumbnail?.isNotEmpty ?? false)
            ? CachedNetworkImage(
                imageUrl: controller.diamond().thumbnail ?? '',
                fit: BoxFit.cover,
                height: 150.h,
                width: 150.w,
              )
            : Image.asset(
                AppImages.diamondPlaceholderPngPath,
                fit: BoxFit.cover,
                height: 150.h,
                width: 150.w,
              ),
      );
    } else if (url.isImageUrl()) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: CachedNetworkImage(
          imageUrl: url ?? '',
          fit: BoxFit.cover,
          height: 150.h,
          width: 150.w,
        ),
      );
    } else if (!url.isImageUrl()) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        child: Stack(
          children: [
            Positioned.fill(
              child: (controller.diamond().thumbnail?.isNotEmpty ?? false)
                  ? CachedNetworkImage(
                      imageUrl: controller.diamond().thumbnail ?? '',
                      fit: BoxFit.cover,
                      height: 150.h,
                      width: 150.w,
                    )
                  : Image.asset(
                      AppImages.diamondPlaceholderPngPath,
                      fit: BoxFit.cover,
                    ),
            ),
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.k000000.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
            ),
            Positioned.fill(
              child: Icon(
                Icons.play_arrow_rounded,
                color: AppColors.kffffff,
                size: 90.sp,
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
