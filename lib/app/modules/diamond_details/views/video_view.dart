import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_details_controller.dart';
import 'package:diamond_company_app/app/modules/diamond_details/views/web_video_view.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_diamond_details/controllers/vendor_diamond_details_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class VideoView extends GetView<dynamic> {
  VideoView({
    required this.viewTag,
    required this.url,
    super.key,
  });

  final String viewTag;
  final String url;

  @override
  String get tag => viewTag;

  @override
  dynamic get controller =>
      Get.isRegistered<VendorDiamondDetailsController>(tag: viewTag)
          ? Get.put(VendorDiamondDetailsController(), tag: viewTag)
          : Get.put(DiamondDetailsController(), tag: viewTag);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: CommonAppbar(
          titleSpacing: 0.w,
          title: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text:
                      'Stock ID: ${controller.diamond().skuPrefix?.toUpperCase() ?? ''}${controller.diamond().skuNumber == null ? '' : controller.diamond().skuNumber}\n',
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.k101C28,
                  ),
                ),
                TextSpan(
                  text: 'Video',
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w400,
                    color: AppColors.k70777E,
                  ),
                ),
              ],
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k70777E,
              ),
            ),
          ),
          onBackPressed: () => Get.back(),
        ),
        body: WebVideoView(url: url),
      );
}
