import 'dart:typed_data';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/listing_value.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/remote/services/marketplace_service.dart';
import 'package:diamond_company_app/app/modules/cart/controllers/cart_controller.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/listing/controllers/marketplace_controller.dart';
import 'package:diamond_company_app/app/modules/view_all/controllers/view_all_controller.dart';
import 'package:diamond_company_app/app/modules/whishlist/controllers/whishlist_controller.dart';
import 'package:diamond_company_app/app/providers/offer_provider.dart';
import 'package:diamond_company_app/app/providers/wishlist_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart'
    show ActionChip, Curves, GlobalKey, PageController, ScrollController, Text;
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'diamond_args.dart';

/// DiamondDetailsController
class DiamondDetailsController extends GetxController
    with GetSingleTickerProviderStateMixin {
  /// isAdvanceExpanded
  final RxBool isAdvanceExpanded = false.obs;

  /// isLoading
  final RxBool isLoading = false.obs;

  /// sharePrice
  final RxBool sharePrice = true.obs;

  /// shareCertificate
  final RxBool shareCertificate = true.obs;

  /// shareSupplierDetails
  final RxBool shareSupplierDetails = true.obs;

  /// scrollController
  final ScrollController scrollController = ScrollController();

  /// diamond
  final Rx<DiamondEntity> diamond = DiamondEntity().obs;

  /// canCalculate
  final RxBool canCalculate = false.obs;

  /// addedToCart
  final RxBool addedToCart = false.obs;

  /// imagesController
  final PageController imagesController = PageController(
    initialPage: 0,
    keepPage: true,
  );

  /// isCertLoading
  final RxBool isCertLoading = false.obs;

  /// isOfferCreating
  final RxBool isOfferCreating = false.obs;

  /// certificateChecked
  bool certificateChecked = false;

  /// pdfImage
  Uint8List? pdfImage;

  /// videoLoading
  final RxBool videoLoading = true.obs;

  /// FormBuilder Key
  GlobalKey<FormBuilderState> offerFormKey = GlobalKey<FormBuilderState>();

  /// webviewController
  final WebViewController webviewController = WebViewController()
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setBackgroundColor(Get.theme.scaffoldBackgroundColor);

  @override
  void onInit() {
    fetchDiamondDetails();
    super.onInit();
  }

  /// dashboard controller
  DashboardController dashboardController = Get.find<DashboardController>();

  Future<void> updateWishlistStock({
    required bool isFav,
  }) async {
    try {
      final bool isUpdated = await WishlistProvider.addRemoveFromWishlist(
        stockId: diamond().id ?? '',
      );

      if (isUpdated) {
        final int featureIndex = dashboardController.featured
            .indexWhere((DiamondEntity element) => element.id == diamond().id);
        if (featureIndex > -1) {
          dashboardController.featured[featureIndex].isWishlist = isFav;
          dashboardController.featured.refresh();
        }

        final int newArrivalIndex = dashboardController.newArrival
            .indexWhere((DiamondEntity element) => element.id == diamond().id);
        if (newArrivalIndex > -1) {
          dashboardController.newArrival[newArrivalIndex].isWishlist = isFav;
          dashboardController.newArrival.refresh();
        }

        if (Get.isRegistered<ViewAllController>()) {
          final ViewAllController viewAllController =
              Get.find<ViewAllController>();
          final int viewAllIndex = viewAllController.diamonds.indexWhere(
              (DiamondEntity element) => element.id == diamond().id);
          if (viewAllIndex > -1) {
            viewAllController.diamonds[viewAllIndex].isWishlist = isFav;
            viewAllController.diamonds.refresh();
          }
        }

        if (Get.isRegistered<WhishlistController>()) {
          final WhishlistController whishlistController =
              Get.find<WhishlistController>();
          final int wishListIndex = whishlistController.diamond.indexWhere(
              (DiamondEntity element) => element.id == diamond().id);
          if (wishListIndex > -1) {
            whishlistController.diamond[wishListIndex].isWishlist = isFav;
            // whishlistController.diamond.removeAt(wishListIndex);
            whishlistController.diamond.refresh();
          }
        }

        if (Get.isRegistered<MarketplaceController>()) {
          final MarketplaceController marketplaceController =
              Get.find<MarketplaceController>();

          final RxList<ListingValue<dynamic>> diamondList =
              marketplaceController.listings;
          final int marketplaceIndex = diamondList.indexWhere(
              (ListingValue<dynamic> element) =>
                  element.value is DiamondEntity &&
                  (element.value as DiamondEntity).id == diamond().id);

          if (marketplaceIndex > -1) {
            marketplaceController.listings[marketplaceIndex].value.isWishlist =
                isFav;
            marketplaceController.listings.refresh();
          }
        }
      }
    } catch (e) {
      logE(e.toString());
    }
  }

  Future<void> fetchDiamondDetails() async {
    try {
      if (Get.arguments is DiamondArgs) {
        DiamondArgs args = Get.arguments as DiamondArgs;
        diamond(args.diamond);
        addedToCart(CartItemHelper.isDiamondAdded(diamond()));
        canCalculate(diamond().canCalculate());

        final bool isWishlist = diamond().isWishlist ?? false;

        diamond().isWishlist = isWishlist;
        isLoading(true);

        diamond((await MarketplaceService.diamondDetails(
                stockId: args.diamond?.stockId ?? '',
                adminId: args.diamond?.adminId,
                vendorId: args.diamond?.vendorId)) ??
            args.diamond);

        isAdvanceExpanded(diamond().images.isEmpty);

        diamond.refresh();

        isLoading(false);
      }
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.WARNING,
      );
      Get.back();
    } on Exception {
      appSnackbar(
        message: 'Something went wrong. Please try again later.',
        snackBarState: SnackBarState.WARNING,
      );
      Get.back();
    }
  }

  /// getCertificateUrl
  Future<void> getCertificateUrl([String? pdfUrl]) async {
    if (pdfImage == null &&
        isCertLoading.isFalse &&
        (diamond().certificateNumber?.isNotEmpty ?? false) &&
        (diamond().lab?.isNotEmpty ?? false) &&
        !certificateChecked) {
      try {
        certificateChecked = true;
        isCertLoading(true);

        if (pdfUrl == null) {
          var labType = diamond().labType;

          // TODO
          /*final CertificateRes certificate = await CertificateService.get(
            lab: labType,
            reportNumber: diamond().certificateNumber ?? '',
          );

          if (certificate.downloadUrl?.isNotEmpty ?? false) {
            String? url = certificate.downloadUrl;
            if (labType == LabType.gia) {
              url = await certificate.data.giaReport.downloadPdfUrl();
            }

            await _pdfToImage(url);
          }*/
        } else {
          await _pdfToImage(pdfUrl);
        }
      } finally {
        isCertLoading(false);
      }
    }
  }

  Future<void> _pdfToImage(String? certificateUrl) async {
    if (certificateUrl == null) {
      return;
    }

    // TODO
    /*var document = await PdfDocument.openData(
        await http.readBytes(Uri.parse(certificateUrl)));
    PdfPage pdfPage = await document.getPage(1);
    var pdfPageImage = await pdfPage.render(
      width: (pdfPage.width * 2).toInt(),
      height: (pdfPage.height * 2).toInt(),
    );
    var image = await pdfPageImage.createImageIfNotAvailable();
    pdfImage = (await image.toByteData(format: ImageByteFormat.png))!
        .buffer
        .asUint8List();*/
  }

  void _loginSnackbar({required String message}) {
    appSnackbar(
      message: message,
      snackBarState: SnackBarState.INFO,
      /*duration: const Duration(seconds: 3),
      trailing: ActionChip(
        label: Text(
          'Login',
          style: GoogleFonts.poppins(
            color: AppColors.primary,
          ),
        ),
        onPressed: () {
          vibrate();
          Get.toNamed<dynamic>(Routes.login)?.then((value) {
            if (value is bool && value) {
              _fetchDiamondDetails();
              if (Get.isRegistered<MarketplaceController>()) {
                var marketplaceCon = Get.find<MarketplaceController>();
                marketplaceCon.showPriceRevealBanner(false);
                double offset = marketplaceCon.scrollController.offset;
                marketplaceCon.fetchInventory().then((value) {
                  marketplaceCon.scrollController.animateTo(
                    offset,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                });
              }
            }
          });
        },
        backgroundColor: AppColors.kDFE5E8,
      ),*/
    );
  }

  /// shareDiamond
  void shareDiamond({
    required String viewTag,
  }) {
    /*Get.bottomSheet<void>(
      ShareDiamond(
        diamond: diamond(),
        viewTag: viewTag,
      ),
      persistent: false,
      isScrollControlled: true,
    );*/
  }

  /// toggleCart
  void toggleCart() {
    // TODO: Add to cart event
    vibrate();

    final cartService = Get.find<CartItemService>();

    logI('togglecart');
    logI(diamond.toJson());

    if (addedToCart.isTrue) {
      CartItemHelper.removeDiamond(diamond());
      if (Get.isRegistered<CartController>()) {
        Get.find<CartController>().removeDiamond(diamond());
      }
      addedToCart(false);
    } else if (cartService.canAddDiamond(diamond())) {
      CartItemHelper.addDiamond(diamond());
      addedToCart(true);
    }
    if (Get.isRegistered<MarketplaceController>()) {
      Get.find<MarketplaceController>().listings.refresh();
    }

    // TODO: check if the user is logged in or not before adding to cart
    /*if (UserProvider.isLoggedIn) {

    } else {
      _loginSnackbar(
        message: 'Please login to add to cart',
      );
    }*/
  }

  /// slider index
  RxInt current = 0.obs;

  /// carousel controller
  final CarouselController carouselController = CarouselController();

  /// StatusOrganizationExpansionTile
  RxBool certificateExpansionTile = RxBool(false);

  /// certificateToggle
  void certificateToggle(value) {
    certificateExpansionTile.value = value;
  }

  /// create offer api
  Future<void> createOfferApi({required double offerPrice}) async {
    try {
      logI('offer ccreating!!!!!!');
      isOfferCreating(true);
      final StockOffer? stockOffer = await OfferProvider.createOffer({
        'stock_id': diamond().id, // admin
        'offer_price': offerPrice
      });
      diamond().isOfferCreated = true;
      diamond().offerId = stockOffer?.id;
      diamond.refresh();
      appSnackbar(
        message: 'Offer created successfully!!!',
        snackBarState: SnackBarState.SUCCESS,
      );
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data?['message'] ?? 'Something went wrong!!!',
        snackBarState: SnackBarState.DANGER,
      );
    } finally {
      isOfferCreating(false);
    }
  }
}
