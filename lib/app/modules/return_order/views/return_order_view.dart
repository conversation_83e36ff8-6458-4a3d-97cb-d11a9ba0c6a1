import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/diamond_list_tile.dart';
import 'package:diamond_company_app/app/ui/components/download_invoice_dialog.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../controllers/return_order_controller.dart';

/// return order view
class ReturnOrderView extends GetView<ReturnOrderController> {
  /// return order view
  const ReturnOrderView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
        bottomNavigationBar: _buildButton(),
      );

  Widget _buildButton() => Obx(
        () => controller.returnOrderList.isNotEmpty || controller.isLoading()
            ? const SizedBox.shrink()
            : Container(
                padding: EdgeInsets.only(
                  left: 60.w,
                  right: 60.w,
                  bottom: MediaQuery.of(Get.context!).padding.bottom + 60.w,
                ),
                child: Obx(
                  () => AppButton.text(
                    onPressed: () => showReturnDiamondDialog(),
                    buttonText: LocaleKeys.return_order_continue_to_return.tr,
                    buttonSize: Size(Get.width, 150.h),
                    backgroundColor: AppColors.k101C28,
                    borderRadius: 24.r,
                    isLoading: controller.isLoading(),
                    borderColor: AppColors.k101C28,
                    buttonTextStyle: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                      color: AppColors.kBEFFFC,
                    ),
                  ),
                ),
              ),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : RefreshIndicator(
                color: AppColors.k101C28,
                onRefresh: () => controller.orderDetails(),
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: 60.w,
                    right: 60.w,
                    bottom: 60.h + MediaQuery.of(Get.context!).padding.bottom,
                  ),
                  children: <Widget>[
                    if (controller.returnOrderList.isEmpty) _buildRemoveList(),
                    if (controller.returnOrderList.isNotEmpty)
                      _buildReturnOrderList(),
                  ],
                ),
              ),
      );

  Column _buildReturnOrderList() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          40.verticalSpace,
          Text(
            '${LocaleKeys.return_order_return.tr} - ${controller.returnOrderList.length ?? 0}/${controller.userOrder().buyRequestDetails?.stockIds?.length ?? 0}'
                .toUpperCase(),
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w900,
              fontSize: 40.sp,
              color: AppColors.k101C28,
            ),
          ),
          60.verticalSpace,
          if (controller.returnOrderList.isNotEmpty) _buildReturnList(),
          60.verticalSpace,
        ],
      );

  Column _buildRemoveList() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          40.verticalSpace,
          Text(
            '${LocaleKeys.return_order_return.tr} - ${controller.returnDiamondList.length ?? 0}/${controller.userOrder().buyRequestDetails?.stockIds?.length ?? 0}'
                .toUpperCase(),
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w900,
              fontSize: 40.sp,
              color: AppColors.k101C28,
            ),
          ),
          60.verticalSpace,
          if (controller.userOrder().buyRequestDetails?.stockIds?.isNotEmpty ??
              false)
            _buildList(),
          60.verticalSpace,
        ],
      );

  ListView _buildReturnList() => ListView.separated(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          final ReturnOrder returnOrder = controller.returnOrderList[index];
          if (controller.userOrder().buyRequestDetails?.stockIds?.isNotEmpty ??
              false) {
            final int i = controller
                    .userOrder()
                    .buyRequestDetails
                    ?.stockIds
                    ?.indexWhere((StockId element) =>
                        element.stockId == returnOrder.stockId) ??
                -1;
            if (i != -1) {
              final DiamondEntity diamond = controller
                      .userOrder()
                      .buyRequestDetails
                      ?.stockIds?[i]
                      .stock ??
                  DiamondEntity();
              return GestureDetector(
                onTap: () => Get.toNamed(
                  Routes.DIAMOND_DETAILS,
                  arguments: DiamondArgs(
                    id: diamond.id ?? '',
                    diamond: diamond,
                  ),
                ),
                child: Container(
                  padding: REdgeInsets.all(40),
                  decoration: BoxDecoration(
                    color: AppColors.kffffff,
                    borderRadius: BorderRadius.circular(24.r),
                    boxShadow: <BoxShadow>[
                      BoxShadow(
                        color: AppColors.k000000.withOpacity(0.1),
                        offset: Offset(0, 10.h),
                        blurRadius: 104.r,
                      ),
                    ],
                  ),
                  child: DiamondListTile(
                    isFromOrder: true,
                    returnOrder: returnOrder,
                    isDelivered: controller.isDelivered(),
                    diamond: diamond,
                  ),
                ),
              );
            }
          }
          return const SizedBox.shrink();
        },
        separatorBuilder: (BuildContext context, int index) => 40.verticalSpace,
        itemCount: controller.returnOrderList.length,
      );

  ListView _buildList() => ListView.separated(
        shrinkWrap: true,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (BuildContext context, int index) => Container(
          padding: REdgeInsets.all(40),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.circular(24.r),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 10.h),
                blurRadius: 104.r,
              ),
            ],
          ),
          child: DiamondListTile(
            isFromOrder: true,
            isDelivered: controller.isDelivered(),
            diamond: controller
                    .userOrder()
                    .buyRequestDetails
                    ?.stockIds?[index]
                    .stock ??
                DiamondEntity(),
          ),
        ),
        separatorBuilder: (BuildContext context, int index) => 40.verticalSpace,
        itemCount:
            controller.userOrder().buyRequestDetails?.stockIds?.length ?? 0,
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        titleSpacing: 0,
        title: RichText(
          text: TextSpan(
            text:
                '${controller.isDelivered() ? '${LocaleKeys.return_order_return.tr} ' : ''} ${LocaleKeys.my_order_detail_order.tr}',
            style: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k70777E,
            ),
            children: <TextSpan>[
              TextSpan(
                text:
                    ' #${controller.userOrder().orderCode?.toUpperCase() ?? '789DEF'}',
                style: AppFontStyles.skolaSans(
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k101C28,
                ),
              ),
            ],
          ),
        ),
        actions: <Widget>[
          FittedBox(
            child: StatusChip(
              text: LocaleKeys.return_order_return.tr,
              color: AppColors.kBEFFFC,
              textStyle: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
          ),
          30.horizontalSpace,
        ],
        onBackPressed: () => Get.back(),
      );
}
