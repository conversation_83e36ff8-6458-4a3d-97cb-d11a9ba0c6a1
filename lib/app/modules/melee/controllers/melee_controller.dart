import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/filter_repository.dart';
import 'package:diamond_company_app/app/data/models/melee/melee_entity.dart';
import 'package:diamond_company_app/app/data/models/melle_attributes.dart';
import 'package:diamond_company_app/app/providers/melle_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/utils/debouncer.dart';
import 'package:diamond_company_app/app/utils/melee_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MeleeController extends GetxController {
  /// selected index
  final RxInt selectedindex = 0.obs;

  /// sscroll index
  final RxInt scrollIndex = 0.obs;

  /// melle
  Rx<MeleeEntity> selectedMeleeAttributes = MeleeEntity().obs;

  /// temp melle
  Rx<MeleeEntity> tempMeleeAttributes = MeleeEntity().obs;

  /// melle attributes
  Rx<MelleAttributes> melleAttributes = const MelleAttributes().obs;

  /// changed melle attributes
  Rx<MeleeEntity> changedMelleAttributes = MeleeEntity().obs;

  /// is loading
  RxBool isLoading = false.obs;

  /// is loading
  RxBool isLoadingPrice = false.obs;

  // The scroll controller for the CupertinoPicker
  FixedExtentScrollController measurementScrollController =
      FixedExtentScrollController();

  /// delaying for
  Rx<MeasurementType> delayingForType = MeasurementType.seiveSize.obs;

  /// growth type
  String? selectedGrowthType;

  /// shape
  String? selectedShape;

  /// white color
  String? selectedWhiteColor;

  /// clarity
  String? selectedClarity;

  /// is round melle
  RxBool isRoundMelle = false.obs;

  /// debouncer
  final Debouncer debouncer = Debouncer(milliseconds: 500);

  /// text editing controller
  TextEditingController? numberOfDiamondsController =
      TextEditingController(text: '1');

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      if (Get.arguments['is_round_melle'] != null) {
        isRoundMelle(Get.arguments['is_round_melle']);
        if (isRoundMelle()) {
          selectedMeleeAttributes().shape = 'round';
        }
      }
    }

    getMeleData();

    callApiToLoadData();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// set fancy color
  void setFancyColor() {
    selectedMeleeAttributes().colorType = DiamondColor.fancy;
    selectedMeleeAttributes().whiteColor = null;
    selectedMeleeAttributes.refresh();
  }

  /// navigate to cart
  Future<void> navigateToCart() async {
    await Get.toNamed(Routes.CART_UPDATED, arguments: 2);
    selectedMeleeAttributes.refresh();
  }

  /// set white color
  void setWhiteColor() {
    selectedindex(0);
    selectedMeleeAttributes().colorType = DiamondColor.white;
    selectedMeleeAttributes().fancyColor = null;
    selectedMeleeAttributes().fancyColor = null;
    selectedMeleeAttributes().fancyIntensity = null;
    selectedMeleeAttributes().fancyOvertone = null;
    selectedMeleeAttributes.refresh();
  }

  void selectWhiteColor(String color) {
    logI(selectedMeleeAttributes().whiteColor);
    if (selectedMeleeAttributes().whiteColor == color) {
      selectedMeleeAttributes().whiteColor = null;
    } else {
      selectedMeleeAttributes().whiteColor = color;
    }
    selectedMeleeAttributes.refresh();
    melleAttributes.refresh();
  }

  void selectFancyColor(String color) {
    selectedMeleeAttributes().fancyColor = color;
    selectedMeleeAttributes.refresh();
  }

  void selectFancyIntensity(String color) {
    selectedMeleeAttributes().fancyIntensity = color;
    selectedMeleeAttributes.refresh();
  }

  void selectFancyOvertone(String color) {
    selectedMeleeAttributes().fancyOvertone = color;
    selectedMeleeAttributes.refresh();
  }

  /// toggle cart
  void toggleCart() {
    selectedMeleeAttributes().perDiamondCarat =
        melleAttributes().perDiamondCaret;
    selectedMeleeAttributes().pricePerCarat = melleAttributes().pricePerCaret;
    selectedMeleeAttributes().totalPrice = melleAttributes().totalPrice;

    final bool isAdded = CartItemHelper.isMeleeAdded(selectedMeleeAttributes());

    if (isAdded) {
      CartItemHelper.removeMelee(selectedMeleeAttributes());
    } else {
      if ((melleAttributes().totalPrice ?? 0) > 0) {
        CartItemHelper.addMelee(selectedMeleeAttributes());
      }
    }
    selectedMeleeAttributes.refresh();
  }

  /// get mele data
  Future<void> getMeleData() async {
    try {
      isLoading(true);

      MelleAttributes? data = await MelleProvider.getMelleData(
        shape: selectedMeleeAttributes().shape,
        growthType: selectedMeleeAttributes().growthType,
        color: selectedMeleeAttributes().whiteColor,
        clarity: selectedMeleeAttributes().clarity,
      );
      if (data != null) {
        final double pricePerCaret = melleAttributes().pricePerCaret ?? 0;
        final double perDiamondCaret = melleAttributes().perDiamondCaret ?? 0;
        if (!isRoundMelle()) {
          data = data.copyWith(
              shape: (data.shape ?? [])
                  .where((String e) => e.toLowerCase() != 'round')
                  .toList());
        }
        melleAttributes(data);
        melleAttributes(melleAttributes().copyWith(
          perDiamondCaret: perDiamondCaret,
          pricePerCaret: pricePerCaret,
        ));
        melleAttributes(melleAttributes().copyWith(
            shape: (melleAttributes().shape ?? [])
                .where((String element) => element.toLowerCase() != 'round')
                .toList()));
        selectedMeleeAttributes().sieveSize =
            data.measurements?.firstOrNull?.sieveSize ?? '';
        selectedMeleeAttributes().mm =
            data.measurements?.firstOrNull?.milimeter ?? '';
        selectedMeleeAttributes().pointer =
            data.measurements?.firstOrNull?.pointer ?? '';
        selectedMeleeAttributes.refresh();
        melleAttributes.refresh();
        calculateTotalPrice();
        updatePointer(0);
        updatePerDiamondCaret(0);
      }
    } on DioException catch (e, t) {
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(
        'Error in get stock offers: $e here ====> $t',
      );
    } finally {
      isLoading(false);
    }
  }

  /// get mele data
  Future<void> getMellePricePerCaret() async {
    try {
      isLoadingPrice(true);
      isLoading(true);

      /// check if all data is provided
      if (!selectedMeleeAttributes().isAllDataProvided()) {
        return;
      }

      final Map<String, dynamic> requestObject = {
        // 'price_per_caret': '320',
        'shape': selectedMeleeAttributes().shape,
        'growth_type': selectedMeleeAttributes().growthType,
        'color': selectedMeleeAttributes().whiteColor,
        'clarity': selectedMeleeAttributes().clarity,
        'sieve_size': selectedMeleeAttributes().sieveSize,
        'milimeter': selectedMeleeAttributes().mm,
        'pointer': selectedMeleeAttributes().pointer
      };

      final double? data =
          await MelleProvider.getMellePricePerCaret(requestObject);
      if (data != null) {
        melleAttributes(melleAttributes().copyWith(pricePerCaret: data));
        melleAttributes.refresh();
        calculateTotalPrice();
      } else {
        appSnackbar(
          message: 'Price not found!!',
          snackBarState: SnackBarState.DANGER,
        );
      }
      isLoadingPrice(false);
      isLoading(false);
    } on DioException catch (e, t) {
      isLoading(false);
      isLoadingPrice(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(
        'Error in get stock offers: $e here ====> $t',
      );
    }
  }

  /// calculate total price
  void calculateTotalPrice() {
    melleAttributes(melleAttributes().copyWith(
        totalPrice: (melleAttributes().pricePerCaret ?? 0) *
            ((selectedMeleeAttributes().diamondQuantity ?? 1)) *
            (melleAttributes().perDiamondCaret ?? 0)));
    melleAttributes.refresh();
  }

  /// scroll list
  Future<void> scrollList(int index, MeasurementType type) async {
    if (measurementScrollController.positions.isNotEmpty) {
      await measurementScrollController.animateToItem(
        index, // Index to scroll to
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// skip scrolling
  Future<bool> skipScrolling(MeasurementType type) async {
    delayingForType(type);
    delayingForType.refresh();
    await Future<void>.delayed(
        const Duration(seconds: 10)); // Delay for 10 seconds
    return false; // Return false after the delay
  }

  /// update per diamond caret
  void updatePerDiamondCaret(int value) {
    if (melleAttributes().measurements?.isEmpty ?? true) {
      return;
    }
    final String? pointer = melleAttributes().measurements?[value].pointer;

    double perDiamondCaret = 0;

    if (pointer.toString().toLowerCase().contains('pt')) {
      perDiamondCaret = (pointer ?? '').toDigits / 100;
    } else if (pointer.toString().toLowerCase().contains('ct')) {
      perDiamondCaret = (pointer ?? '').toDigits;
    } else if (pointer.toString().toLowerCase().contains('-')) {
      perDiamondCaret =
          (pointer ?? '').averageFromRange.toStringAsFixed(2).toDigits;
    } else {
      perDiamondCaret = (pointer ?? '').toDigits / 100;
    }

    melleAttributes(
        melleAttributes().copyWith(perDiamondCaret: perDiamondCaret));

    melleAttributes.refresh();
  }

  /// update pointer
  void updatePointer(int value) {
    if (melleAttributes().measurements?.isEmpty ?? true) {
      return;
    }
    final String pointer = melleAttributes().measurements?[value].pointer ?? '';

    selectedMeleeAttributes().pointer = pointer;
    selectedMeleeAttributes.refresh();
  }

  /// get pointer
  String getPointer(String? value) {
    final String pointer = value ?? '';

    if (pointer.contains('-')) {
      return (pointer ?? '').averageFromRange.toStringAsFixed(2);
    } else {
      return (pointer ?? '').toDigits.toStringAsFixed(2);
    }
  }

  /// call api to load data
  void callApiToLoadData() {
    /// Adding an ever listener with a condition
    ever(selectedMeleeAttributes, (MeleeEntity selectedMelle) {
      if (selectedGrowthType != selectedMelle.growthType) {
        resetData();
        getMeleData();
      }
      if (selectedShape != selectedMelle.shape) {
        resetData();
        getMeleData();
      }
      if (selectedWhiteColor != selectedMelle.whiteColor) {
        resetData();
        getMeleData();
      }
      if (selectedClarity != selectedMelle.clarity) {
        resetData();
        getMeleData();
      }

      selectedGrowthType = selectedMelle.growthType;
      selectedShape = selectedMelle.shape;
      selectedWhiteColor = selectedMelle.whiteColor;
      selectedClarity = selectedMelle.clarity;

      if (selectedMelle.isAllDataProvided()) {
        final bool isAdded =
            CartItemHelper.isMeleeAdded(selectedMeleeAttributes());
        if (!isAdded) {
          debouncer.run(() => getMellePricePerCaret());
        }
      }
    });
  }

  /// reset data
  void resetData() {
    melleAttributes(
        melleAttributes().copyWith(pricePerCaret: null, measurements: null));
    melleAttributes.refresh();
  }
}
