import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/jewellery/jewellery.dart';
import 'package:diamond_company_app/app/data/models/jewellery_category/jewellery_category.dart';
import 'package:diamond_company_app/app/providers/jewellery_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class JewelleriesController extends GetxController {
  //TODO: Implement JewelleriesController

  /// isLoadMore
  RxBool isLoadMore = false.obs;

  /// limit
  int limit = 10;

  /// limit
  int skip = 0;

  /// This is the scroll controller for the JewelleriesView.
  ScrollController scrollController = ScrollController();

  /// is loading
  RxBool isLoading = false.obs;

  /// Jewellery categories
  RxList<Jewellery> jewellery = <Jewellery>[].obs;

  /// jewellery category
  JewelleryCategory? jewelleryCategory;

  @override
  void onInit() {
    super.onInit();

    scrollController.addListener(_scrollListener);

    if (Get.arguments != null) {
      jewelleryCategory = Get.arguments;
      fetchJewelleries();
    }
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      loadMoreData();
    }
  }

  /// load more
  Widget loadMoreData() {
    if (isLoadMore()) {
      if (!isLoading()) {
        fetchJewelleries(skip: skip, limit: limit);
      }
    }
    return const SizedBox.shrink();
  }

  /// Jewellery
  Future<void> fetchJewelleries({int? skip, int? limit}) async {
    try {
      if ((skip ?? 0) == 0) {
        this.skip = 0;
        isLoadMore(true);
        isLoading(true);
        jewellery.clear();
      }

      final List<Jewellery> jewelleries =
          await JewelleryProvider.getJewelleries(
        categoryId: jewelleryCategory?.id,
        skip: skip ?? 0,
        limit: limit ?? this.limit,
      );

      if (jewelleries.isEmpty || jewelleries.length < this.limit) {
        isLoadMore(false);
      } else {
        this.skip = this.skip + this.limit;
      }
      jewellery
        ..addAll(jewelleries)
        ..refresh();

      isLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
