import 'package:collection/collection.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/local_store.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/providers/stocks_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/utils/jewellery_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/local/db/app_drift.dart';

/// cart updated controller
class CartUpdatedController extends GetxController
    with GetSingleTickerProviderStateMixin {
  /// select tab index
  RxInt selectedTabIndex = 0.obs;

  /// tab controller
  TabController? tabController;

  /// count
  RxBool isDoNotAskAgain = false.obs;

  /// isLoading
  RxBool isLoading = false.obs;

  /// new diamond added
  RxInt updatedListLength = 0.obs;

  /// grouped items
  RxMap<bool, List<DiamondEntity>> groupedItems =
      <bool, List<DiamondEntity>>{}.obs;

  @override
  void onInit() {
    super.onInit();

    tabController = TabController(
      length: 3,
      vsync: this,
      animationDuration: const Duration(microseconds: 300),
    );
    getStocksWithAvailability();
  }

  @override
  void onReady() {
    super.onReady();

    tabController?.addListener(() {
      selectedTabIndex(tabController?.index ?? 0);
      isDoNotAskAgain(false);

      if (selectedTabIndex() == 0) {
        getStocksWithAvailability();
      }
    });

    if (Get.arguments != null) {
      selectedTabIndex(Get.arguments);
      tabController?.animateTo(Get.arguments);
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// get stock status
  Future<void> getStocksWithAvailability() async {
    try {
      isLoading(true);
      final RxList<DiamondEntity> diamonds =
          Get.find<CartItemService>().diamonds;

      if (diamonds.isNotEmpty) {
        final List<DiamondEntity> diamondListStatus =
            await StockProvider.getStockStatus(
                {'ids': diamonds.map((e) => e.id ?? '').toList()});
        // if (diamondListStatus.isNotEmpty) {
        final List<DiamondEntity> updatedList = diamonds.map(
          (e) {
            final int index = diamondListStatus
                .indexWhere((DiamondEntity element) => element.id == e.id);
            final DiamondEntity? diamond =
                index != -1 ? diamondListStatus[index] : null;
            e
              ..availability = diamond?.availability ?? 'HOLD'
              ..isAvailable = (diamond?.availability ?? 'HOLD') == 'AVAILABLE';
            return e;
          },
        ).toList();
        groupedItems.clear();
        groupedItems(groupBy(updatedList,
            (DiamondEntity diamond) => diamond.isAvailable ?? false));
        groupedItems.refresh();
        //}
      }
      isLoading(false);
    } on Exception catch (e) {
      logE(e);
      isLoading(false);
      appSnackbar(
        message: LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// returns true if unavailable diamonds are there
  bool isDiamondsUnAvailable() {
    if (groupedItems.isEmpty) {
      return false;
    } else {
      return groupedItems.entries
          .where((MapEntry<bool, List<DiamondEntity>> e) => !e.key)
          .isNotEmpty;
    }
  }

  /// remove diamond
  void removeDiamond(DiamondEntity diamond) {
    if (groupedItems.isNotEmpty) {
      if (groupedItems.entries.isNotEmpty) {
        for (final MapEntry<bool, List<DiamondEntity>> item
            in groupedItems.entries) {
          item.value
              .removeWhere((DiamondEntity element) => element.id == diamond.id);
        }
      }
      groupedItems
          .removeWhere((bool key, List<DiamondEntity> value) => value.isEmpty);
    }
    groupedItems.refresh();
    for (final MapEntry<bool, List<DiamondEntity>> item
        in groupedItems.entries) {
      updatedListLength = updatedListLength + item.value.length;
    }
  }

  /// on cart changed
  void onCartChanged() {
    getStocksWithAvailability();
  }

  /// navigate to cart
  Future<void> navigateToCart(DiamondEntity diamond) async {
    await Get.toNamed(
      Routes.DIAMOND_DETAILS,
      arguments: DiamondArgs(
        id: diamond.id ?? '',
        diamond: diamond,
      ),
    );
    await getStocksWithAvailability();
    // if (updatedListLength <= Get.find<CartService>().diamonds.length) {
    //   await getStocksWithAvailability();
    // }
  }

  /// remove all from cart
  void removeAllFromCart(List<DiamondEntity> diamonds) {
    for (final DiamondEntity diamond in diamonds) {
      CartItemHelper.removeDiamond(diamond);
    }
    groupedItems
      ..removeWhere((bool key, List<DiamondEntity> value) => !key)
      ..refresh();

    // for (final CartDiamond diamond in diamonds) {
    //   CartDiamondHelper.remove(diamond.diamond);
    //   removeDiamond(diamond.diamond);
    // }
  }

  /// reduce quantity
  Future<void> reduceQuantity(CartItem cartItem) async {
    if (cartItem.quantity == 1) {
      return;
    }
    //cartItem.quantity = cartItem.quantity - 1;
    await CartItemHelper.updateQuantity(
      quantity: cartItem.quantity - 1,
      jewelleryId: cartItem.jewelleryId,
    );
    Get.find<CartItemService>().calculateJewellerySummary();
    update();
  }

  /// add quantity
  Future<void> addQuantity(CartItem cartItem) async {
    if (!(cartItem.jewellery?.canIncreaseQuantity(cartItem) ?? false)) {
      appSnackbar(
        message: LocaleKeys.messages_max_quantity_reached.tr,
        snackBarState: SnackBarState.INFO,
      );
      return;
    }
    //cartItem.quantity = cartItem.quantity + 1;
    await CartItemHelper.updateQuantity(
      quantity: cartItem.quantity + 1,
      jewelleryId: cartItem.jewelleryId,
    );
    Get.find<CartItemService>().calculateJewellerySummary();
    update();
  }

  /// toggle soft remove
  void toggleDoNotAskAgain({bool? value}) {
    if (value ?? false) {
      isDoNotAskAgain(value);
    } else {
      isDoNotAskAgain.toggle();
    }
    if (selectedTabIndex() == 0) {
      LocalStore.smoothSelectedDiamondRemove(isDoNotAskAgain());
    } else if (selectedTabIndex() == 1) {
      LocalStore.smoothSelectedJewelleryRemove(isDoNotAskAgain());
    } else if (selectedTabIndex() == 2) {
      LocalStore.smoothSelectedMeleeRemove(isDoNotAskAgain());
    }
  }
}
