import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/diamond_summary.dart';
import 'package:diamond_company_app/app/modules/buy_request_details/controllers/buy_request_details_controller.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/dashed_divider.dart';
import 'package:diamond_company_app/app/ui/components/diamond_details.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_credit_card/flutter_credit_card.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/credit_card_payment_controller.dart';

/// CreditCardPaymentView
class CreditCardPaymentView extends GetView<CreditCardPaymentController> {
  /// CreditCardPaymentView
  const CreditCardPaymentView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: true,
        bottomNavigationBar: _buildBottomCard(),
        appBar: _buildCommonAppbar(),
        body: _buildBody(context),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          Get.back();
        },
        title: Text(
          'Payment with\nCredit Card',
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
      );

  /// Build Header Text
  Widget _buildHeaderText() => Text(
        'CARD DETAILS',
        style: AppFontStyles.skolaSans(
          fontSize: 40.sp,
          fontWeight: FontWeight.w900,
          color: AppColors.k101C28,
          letterSpacing: 5.sp,
        ),
      );

  /// Common TextFormField
  // AppTextFormField _buildTextFormField({
  //   required String name,
  //   required String? Function(String?)? validation,
  //   TextEditingController? controller,
  //   String? labelText,
  //   String? initialValue,
  //   bool isRequired = false,
  //   bool? readOnly,
  //   TextInputType? keyboardType,
  //   Widget? suffix,
  //   Widget? prefix,
  //   int? maxLines,
  //   Iterable<String>? autofillHints,
  //   List<TextInputFormatter>? inputFormatters,
  //   bool obscureText = false,
  //   void Function()? onTap,
  // }) =>
  //     AppTextFormField(
  //       onTap: onTap ?? () {},
  //       inputFormatters: inputFormatters,
  //       obscureText: obscureText,
  //       prefixIcon: prefix,
  //       autofillHints: autofillHints,
  //       initialValue: initialValue ?? '',
  //       readOnly: readOnly ?? false,
  //       controller: controller,
  //       name: name,
  //       keyboardType: keyboardType,
  //       validator: validation,
  //       labelText: labelText ?? '',
  //       isRequired: isRequired,
  //       constraints: BoxConstraints(
  //         minHeight: 150.h,
  //       ),
  //       maxLines: maxLines ?? 1,
  //       labelTextStyle: AppFontStyles.skolaSans(
  //         color: AppColors.k70777E,
  //         fontSize: 45.sp,
  //         fontWeight: FontWeight.w400,
  //       ),
  //       style: _buildCommonTextStyle(),
  //       errorBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(24.r),
  //         borderSide: BorderSide(
  //           width: 1.w,
  //           color: AppColors.k70777E,
  //         ),
  //       ),
  //       showBorder: true,
  //       fillColor: AppColors.k70777E,
  //       suffixIcon: suffix,
  //       focusedBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(24.r),
  //         borderSide: BorderSide(
  //           width: 1.w,
  //           color: AppColors.k70777E,
  //         ),
  //       ),
  //       enabledBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(24.r),
  //         borderSide: BorderSide(
  //           width: 1.w,
  //           color: AppColors.k70777E,
  //         ),
  //       ),
  //       focusedErrorBorder: OutlineInputBorder(
  //         borderRadius: BorderRadius.circular(24.r),
  //         borderSide: BorderSide(
  //           width: 1.w,
  //           color: AppColors.k70777E,
  //         ),
  //       ),
  //       border: const OutlineInputBorder(
  //         borderSide: BorderSide(
  //           color: AppColors.k70777E,
  //         ),
  //       ),
  //       contentPadding: REdgeInsets.only(left: 48, bottom: 30),
  //     );

  /// Build Bottom Card
  Widget _buildBottomCard() => Padding(
        padding: EdgeInsets.only(
          left: 0.w,
          right: 0.w,
          bottom: MediaQuery.of(Get.context!).padding.bottom + 0.w,
        ),
        child: _diamondDetails(),
      );

  /// Build Diamond Details
  Widget _diamondDetails() {
    DiamondSummary summary = Get.find<CartItemService>().diamondSummary();
    if (controller.stockOffer != null) {
      summary = DiamondSummary(
        totalPcs: 1,
        totalCts: controller.stockOffer?.stock?.weight ?? 0,
        avgDiscount: controller.stockOffer?.stock?.price?.discount ?? 0,
        avgPricePerCt: controller.stockOffer?.stock?.price?.pricePerCarat ?? 0,
        totalFinalPrice: controller.stockOffer?.offerPrice ?? 0,
      );
    }
    return Obx(
      () => DiamondDetails(
        buttonText: LocaleKeys.cart_first_address_confirm_to_pay.tr,
        onPressed: () {
          controller.confirmToPay();
        },
        isLoading: controller.isLoading(),
        widget: Get.isRegistered<BuyRequestDetailsController>()
            ? const SizedBox.shrink()
            : Padding(
                padding: REdgeInsets.all(40),
                child: Column(
                  children: <Widget>[
                    // Container(
                    //   width: Get.width,
                    //   decoration: BoxDecoration(
                    //     color: Colors.white,
                    //     borderRadius: BorderRadius.circular(24.r),
                    //   ),
                    //   child: Padding(
                    //     padding: REdgeInsets.all(24),
                    //     child: Row(
                    //       children: <Widget>[
                    //         24.horizontalSpace,
                    //         _buildProductDetails(
                    //           text: LocaleKeys.cart_first_address_pcs.tr,
                    //           value:
                    //               '${summary.totalPcs.toString().padLeft(2, '0')}',
                    //         ),
                    //         const Spacer(),
                    //         _buildProductDetails(
                    //           text: LocaleKeys.cart_first_address_cts.tr,
                    //           value: '${summary.totalCts.toStringAsFixed(2)}',
                    //         ),
                    //         const Spacer(),
                    //         _buildProductDetails(
                    //           text: LocaleKeys.cart_first_address_price_ct.tr,
                    //           value:
                    //               '${summary.avgPricePerCt.toString().toPrice()}',
                    //         ),
                    //         const Spacer(),
                    //         // _buildProductDetails(
                    //         //   text: LocaleKeys.cart_first_address_value.tr,
                    //         //   value:
                    //         //       '${summary.totalFinalPrice.toString().toPrice()}',
                    //         // ),
                    //         // const Spacer(),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                    // 40.verticalSpace,
                    // _buildFullDetails(
                    //   text: LocaleKeys.cart_first_address_total_quantity_selected.tr,
                    //   value: '${summary.totalPcs.toString().padLeft(2, '0')}',
                    // ),
                    // 24.verticalSpace,
                    _buildFullDetails(
                      text: LocaleKeys.cart_first_address_total_value.tr,
                      value: controller
                          .getTotalItemsAmount(summary: summary)
                          .toString()
                          .toPrice(),
                    ),
                    24.verticalSpace,
                    _buildFullDetails(
                      text: LocaleKeys.cart_first_address_shipping_fees.tr,
                      value: controller.getShippingFees().toString().toPrice(),
                    ),
                    if (!(UserProvider.currentUser?.isVerified ?? false))
                      24.verticalSpace,
                    if (!(UserProvider.currentUser?.isVerified ?? false))
                      _buildFullDetails(
                        text: LocaleKeys.bill_summary_taxes.tr,
                        value: controller
                            .getTaxAmount(summary: summary)
                            .toString()
                            .toPrice(),
                      ),
                    24.verticalSpace,
                    CustomDashedDivider(
                        color: AppColors.k70777E.withOpacity(0.2)),
                    // _buildFullDetails(
                    //   text: 'Taxes',
                    //   value: '\$100',
                    // ),
                    24.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Text(
                          LocaleKeys.cart_second_address_total.tr,
                          style: AppFontStyles.skolaSans(
                            fontSize: 40.sp,
                            fontWeight: FontWeight.w900,
                            color: AppColors.k101C28,
                          ),
                        ),
                        Text(
                          controller
                              .getGrandTotal(summary: summary)
                              .toString()
                              .toPrice(),
                          style: AppFontStyles.skolaSans(
                            fontSize: 40.sp,
                            fontWeight: FontWeight.w900,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ],
                    ),
                    // RichText(
                    //   text: TextSpan(
                    //     text: LocaleKeys.cart_first_address_grand_total_is.tr,
                    //     style: AppFontStyles.skolaSans(
                    //       fontSize: 48.sp,
                    //       fontWeight: FontWeight.w700,
                    //       color: AppColors.k101C28,
                    //     ),
                    //     children: <InlineSpan>[
                    //       TextSpan(
                    //         text:
                    //             '${(summary.totalFinalPrice + (controller.cartSecondAddressController.shipmentsList[controller.cartSecondAddressController.selectShippingMethodIndex()].amount ?? 0)).toString().toPrice()}',
                    //         // text: '\$26543',
                    //         style: AppFontStyles.skolaSans(
                    //           fontSize: 48.sp,
                    //           fontWeight: FontWeight.w900,
                    //           color: AppColors.k101C28,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
      ),
    );
  }

  /// Build Full Details
  Widget _buildFullDetails({required String text, required String value}) =>
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            text,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
          Text(
            value,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );

  /// Build Product Details
  Widget _buildProductDetails({required String text, required String value}) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            text,
            style: AppFontStyles.skolaSans(
              fontSize: 30.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28.withOpacity(0.6),
            ),
          ),
          20.verticalSpace,
          Text(
            value,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        // padding: REdgeInsets.only(
        //   left: 60,
        //   right: 60,
        //   bottom: MediaQuery.of(context).viewInsets.bottom,
        // ),
        child: FormBuilder(
          key: controller.fbKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              60.verticalSpace,
              Padding(
                padding: REdgeInsets.only(left: 60),
                child: _buildHeaderText(),
              ),
              30.verticalSpace,
              CreditCardForm(
                cardNumber: controller.cardNumber(),
                expiryDate: controller.expiryDate(),
                cardHolderName: controller.cardHolderName(),
                cvvCode: controller.cvvNumber(),
                onCreditCardModelChange: (value) {
                  controller.cardHolderName.value = value.cardHolderName;
                  controller.cardNumber.value = value.cardNumber;
                  controller.cvvNumber.value = value.cvvCode;
                  controller.expiryDate.value = value.expiryDate;
                },
                inputConfiguration: InputConfiguration(
                  cardNumberDecoration:
                      _buildCommonInputDecoration(labelText: 'Card number'),
                  cardNumberTextStyle: _buildCommonTextStyle(),
                  expiryDateDecoration: _buildCommonInputDecoration(
                      labelText: 'Exp. Date', hintText: 'MM/YY'),
                  expiryDateTextStyle: _buildCommonTextStyle(),
                  cvvCodeDecoration:
                      _buildCommonInputDecoration(labelText: 'CVV'),
                  cvvCodeTextStyle: _buildCommonTextStyle(),
                  cardHolderDecoration: _buildCommonInputDecoration(
                      labelText: 'Card holder name'),
                  cardHolderTextStyle: _buildCommonTextStyle(),
                ),
                formKey: controller.globalKey,
                obscureCvv: true,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                cardNumberValidator: (String? value) {
                  if (value?.trim() == null ||
                      value.toString().trim().isEmpty) {
                    return 'Please enter a card number';
                  } else if (value?.trim().length != 19) {
                    return 'Credit card number should be 16 digits';
                  }
                  return null;
                },
                cardHolderValidator: (String? value) {
                  if (value?.trim() == null ||
                      value.toString().trim().isEmpty) {
                    return 'Please enter a valid name';
                  }
                  return null;
                },
                cvvValidator: (String? value) {
                  if (value?.trim() == null ||
                      value.toString().trim().isEmpty) {
                    return 'Please enter a CVV number';
                  } else if (value?.toString().trim().length != 3) {
                    return 'CVV number should be 3 digits';
                  }
                  return null;
                },
              ).paddingSymmetric(horizontal: 15.h)
            ],
          ),
          // Column(
          //   crossAxisAlignment: CrossAxisAlignment.start,
          //   children: <Widget>[
          //     /// Billing Address
          //     60.verticalSpace,
          //     _buildHeaderText(),
          //     80.verticalSpace,
          //     _buildTextFormField(
          //       name: 'card holder name',
          //       labelText: 'Card holder name',
          //       isRequired: true,
          //       keyboardType: TextInputType.name,
          //       autofillHints: <String>[AutofillHints.creditCardGivenName],
          //       validation: (String? value) {
          //         final String? trimmedValue = value?.trim();
          //         if (trimmedValue?.isEmpty ?? true) {
          //           return 'Please enter card holder name';
          //         }
          //         return null;
          //       },
          //     ),
          //     76.verticalSpace,
          //     _buildTextFormField(
          //       name: 'card number',
          //       labelText: 'Card number',
          //       isRequired: true,
          //       keyboardType: TextInputType.number,
          //       autofillHints: <String>[AutofillHints.creditCardNumber],
          //       validation: (String? value) {
          //         final String? trimmedValue = value?.trim();
          //         if (trimmedValue?.isEmpty ?? true) {
          //           return 'Please enter your card number';
          //         } else if (trimmedValue!.length != 16) {
          //           return 'Credit card number should be 16 digits';
          //         }
          //         return null;
          //       },
          //     ),
          //     76.verticalSpace,
          //     Row(
          //       crossAxisAlignment: CrossAxisAlignment.start,
          //       children: <Widget>[
          //         Expanded(
          //           child: _buildTextFormField(
          //             name: 'exp date',
          //             labelText: 'Exp.Date',
          //             isRequired: true,
          //             keyboardType: TextInputType.datetime,
          //             autofillHints: <String>[
          //               AutofillHints.creditCardExpirationDate
          //             ],
          //             validation: (String? value) {
          //               final String? trimmedValue = value?.trim();
          //               if (trimmedValue?.isEmpty ?? true) {
          //                 return 'Please enter expiration date';
          //               }
          //               return null;
          //             },
          //           ),
          //         ),
          //         40.horizontalSpace,
          //         Expanded(
          //           child: _buildTextFormField(
          //             name: 'cvv',
          //             labelText: 'CVV',
          //             obscureText: true,
          //             autofillHints: <String>[
          //               AutofillHints.creditCardSecurityCode
          //             ],
          //             isRequired: true,
          //             validation: (String? value) {
          //               final String? trimmedValue = value?.trim();
          //               if (trimmedValue?.isEmpty ?? true) {
          //                 return LocaleKeys.validation_last_name_is_empty.tr;
          //               }
          //               return null;
          //             },
          //           ),
          //         ),
          //       ],
          //     ),
          //   ],
          // ),
        ),
      );

  /// Common TextStyle
  TextStyle _buildCommonTextStyle() => AppFontStyles.skolaSans(
        fontSize: 45.sp,
        color: AppColors.k101C28,
        fontWeight: FontWeight.w400,
      );

  /// Common InputDecoration
  InputDecoration _buildCommonInputDecoration(
          {required String labelText, String? hintText}) =>
      InputDecoration(
        labelText: labelText,
        errorMaxLines: 2,
        contentPadding: REdgeInsets.symmetric(horizontal: 48),
        hintText: hintText ?? '',
        hintStyle: AppFontStyles.skolaSans(
          fontSize: 35.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k70777E,
        ),
        labelStyle: AppFontStyles.skolaSans(
          fontSize: 35.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k70777E,
        ),
        floatingLabelStyle: AppFontStyles.skolaSans(
          fontSize: 35.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k70777E,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
      );
}

// import 'package:flutter/services.dart';
// inputFormatters: <TextInputFormatter>[
// FilteringTextInputFormatter.digitsOnly,
// LengthLimitingTextInputFormatter(16),
// CreditCardNumberFormatter(),
// ],
// /// CreditCardNumberFormatter
// class CreditCardNumberFormatter extends TextInputFormatter {
//   @override
//   TextEditingValue formatEditUpdate(
//       TextEditingValue oldValue, TextEditingValue newValue) {
//     // Remove any characters that are not digits
//     final String digitsOnly = newValue.text.replaceAll(RegExp(r'\D'), '');
//
//     // Add space after every four digits
//     final StringBuffer buffer = StringBuffer();
//     for (int i = 0; i < digitsOnly.length; i++) {
//       if (i % 4 == 0 && i != 0) {
//         buffer.write(' ');
//       }
//       buffer.write(digitsOnly[i]);
//     }
//
//     // Get the new string with spaces
//     final String formattedString = buffer.toString();
//
//     // Calculate the new cursor position
//     int newCursorPosition = newValue.selection.baseOffset +
//         (formattedString.length - newValue.text.length);
//     if (newCursorPosition > formattedString.length) {
//       newCursorPosition = formattedString.length;
//     }
//
//     return TextEditingValue(
//       text: formattedString,
//       selection: TextSelection.collapsed(offset: newCursorPosition),
//     );
//   }
// }
