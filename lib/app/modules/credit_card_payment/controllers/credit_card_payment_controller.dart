import 'dart:developer';

import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/diamond_summary.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/modules/cart_second_address/controllers/cart_second_address_controller.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/providers/authorize_payment_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../data/config/design_config.dart';

/// CreditCardPaymentController
class CreditCardPaymentController extends GetxController {
  /// Form Builder Controller
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// Form Key
  GlobalKey<FormState> globalKey = GlobalKey<FormState>();

  /// card holder name
  RxString cardHolderName = ''.obs;

  /// card number
  RxString cardNumber = ''.obs;

  /// cvv number
  RxString cvvNumber = ''.obs;

  /// expiry date
  RxString expiryDate = ''.obs;

  /// buy request id
  RxString buyRequestId = ''.obs;

  /// is loading
  RxBool isLoading = false.obs;

  /// stock offer
  StockOffer? stockOffer;

  /// CartSecondAddressController
  CartSecondAddressController cartSecondAddressController =
      Get.put(CartSecondAddressController());

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      if (Get.arguments is String) {
        buyRequestId(Get.arguments);
      } else if (Get.arguments is StockOffer) {
        stockOffer = Get.arguments;
        buyRequestId(stockOffer?.buyRequestId);
      }
    }
  }

  /// confirm to pay
  Future<void> confirmToPay() async {
    if (globalKey.currentState?.validate() ?? false) {
      log(' ===================== Confirm to Pay');

      final Map<String, dynamic> data = {
        'card_number': cardNumber.replaceAll(' ', ''),
        'card_holder_name': cardHolderName.toUpperCase(),
        'exp_date': DateFormat('yyyy-MM').format(DateTime(
            int.parse(convertToFullYear(expiryDate.split('/')[1])),
            int.parse(expiryDate.split('/')[0]))),
        'cvv': cvvNumber
      };

      if (kIsWeb) {
        DesignConfig.listingSideMenuKey?.currentState?.pop(data);
      } else {
        Get.back(result: data);
      }
      /////
    } else {
      log(' ===================== Something Missing');
    }
  }

  /// convert to full year
  String convertToFullYear(String year) {
    // Convert the input year string to an integer
    int inputTwoDigitYear = int.parse(year);

    // Determine the full year based on the input
    int fullYear = inputTwoDigitYear;
    fullYear += 2000;
    return fullYear.toString();

    // Check if the year is in the future relative to the current year's last two digits
    int currentYearLastTwoDigits = DateTime.now().year % 100;
    if (fullYear <= currentYearLastTwoDigits) {
      fullYear += 2000;
    } else {
      fullYear += 1900;
    }

    // Return the full year as a string
    return fullYear.toString();
  }

  double getShippingFees() {
    if (cartSecondAddressController.shipmentsList[
            cartSecondAddressController.selectShippingMethodIndex()] !=
        -1) {
      return double.parse((cartSecondAddressController
                  .shipmentsList[
                      cartSecondAddressController.selectShippingMethodIndex()]
                  .amount ??
              0)
          .toString());
    }
    return 0;
  }

  double getTaxAmount({DiamondSummary? summary}) {
    final double taxPrice = getTotalItemsAmount(summary: summary)
        .toString()
        .toTaxPrice(tax: cartSecondAddressController.tax());
    return taxPrice;
  }

  /// get total amount
  double getTotalItemsAmount({DiamondSummary? summary}) {
    if (stockOffer != null) {
      return summary?.totalFinalPrice ?? 0;
    }
    final double diamondTotal =
        Get.find<CartItemService>().diamondSummary().totalFinalPrice;
    final double jewelleryTotal =
        Get.find<CartItemService>().jewellerySummary().totalFinalPrice;
    final double meleeTotal =
        Get.find<CartItemService>().meleeSummary().totalFinalPrice;
    return diamondTotal + jewelleryTotal + meleeTotal;
  }

  /// get grand total
  double getGrandTotal({DiamondSummary? summary}) {
    final double total = getTotalItemsAmount(summary: summary);
    final double taxPrice = getTotalItemsAmount(summary: summary)
        .toString()
        .toTaxPrice(tax: cartSecondAddressController.tax());

    return total + taxPrice + getShippingFees();
  }

  /// create customer profile
  Future<void> createCustomerProfile() async {
    try {
      isLoading(true);
      final String? customerProfileId =
          await AuthorizePaymentProvider.createCustomerProfile({
        'card_holder_name': cardHolderName,
        'card_number': cardNumber,
        'exp_date': expiryDate
      });
      await UserProvider.onLogin(
          user: UserEntity.fromJson({
            ...?UserProvider.currentUser?.toJson(),
            'customer_profile_id': customerProfileId
          }),
          userAuthToken: UserProvider.authToken ?? '');
      isLoading(false);
    } on DioException catch (e) {
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// authorize credit card
  Future<void> authorizeCreditCard() async {
    try {
      isLoading(true);
      final Map<String, dynamic>? result =
          await AuthorizePaymentProvider.authorizeCreditCard({
        'card_number': cardNumber,
        'exp_date': expiryDate,
        'cvv': cvvNumber,
        'buy_request_id': buyRequestId()
      });

      isLoading(false);
      if (result != null) {
        Get.back(result: result);
      }
    } on DioException catch (e) {
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// capture amount
  Future<void> captureAmount() async {
    try {
      await AuthorizePaymentProvider.captureAuthorizedAmount(
          {'buy_request_id': buyRequestId()});
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// charge credit card
  Future<void> chargeCreditCard() async {
    try {
      await AuthorizePaymentProvider.chargeCreditCard({
        'card_number': cardNumber,
        'card_holder_name': cardNumber,
        'exp_date': expiryDate,
        'cvv': cvvNumber,
        'buy_request_id': buyRequestId()
      });
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

//////
}
