import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:diamond_company_app/app/ui/components/empty_screens.dart';
import 'package:diamond_company_app/app/ui/components/stock_item_tile.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/inquiries_controller.dart';

/// inquiries view
class InquiriesView extends GetView<InquiriesController> {
  /// inquiries view
  const InquiriesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppbar(),
        body: _buildBody(),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : controller.inquiryData.isEmpty
                ? EmptyScreen(
                    icon: AppImages.emptyInquiries,
                    title: 'No Inquiries',
                    subtitle: "Got questions or need assistance? Our\n"
                        "support team is here to help! Feel free\n"
                        "to reach out with any inquiries, and\n"
                        "we'll get back to you as soon as possible.")
                : RefreshIndicator(
                    onRefresh: () => controller.getUserInquiry(),
                    color: AppColors.k101C28,
                    child: ListView.separated(
                      physics: const AlwaysScrollableScrollPhysics(),
                      controller: controller.scrollController,
                      padding: EdgeInsets.only(
                        left: 60.w,
                        right: 60.w,
                        top: 60.h,
                        bottom:
                            60.h + MediaQuery.of(Get.context!).padding.bottom,
                      ),
                      itemBuilder: (BuildContext context, int index) {
                        if (index < controller.inquiryData.length) {
                          return StockItemTile(
                              fromInquiries: true,
                              width: Get.width,
                              inquiryData: controller.inquiryData[index]);
                        } else {
                          return AppLoader();
                        }
                      },
                      separatorBuilder: (BuildContext context, int index) =>
                          30.verticalSpace,
                      itemCount: controller.inquiryData.length +
                          (controller.isPagination() ? 1 : 0),
                    ),
                  ),
      );

  CommonAppbar _buildAppbar() => CommonAppbar(
        title: Text(
          LocaleKeys.inquiries_title.tr,
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w500,
            fontSize: 45.sp,
            color: AppColors.k101C28,
          ),
        ),
        // actions: <Widget>[
        //   IconButton.filled(
        //     onPressed: () {},
        //     icon: SizedBox(
        //       child: AppImages.searchInquiries,
        //       height: 64.h,
        //       width: 64.w,
        //     ),
        //     style: IconButton.styleFrom(
        //       backgroundColor: AppColors.kBEFFFC,
        //       shape: const CircleBorder(),
        //       fixedSize: Size(120.w, 120.h),
        //       padding: EdgeInsets.zero,
        //     ),
        //     constraints: BoxConstraints(
        //       minHeight: 120.h,
        //       minWidth: 120.w,
        //     ),
        //   ),
        // ],
        centerTitle: true,
        onBackPressed: () => Get.back(),
      );
}
