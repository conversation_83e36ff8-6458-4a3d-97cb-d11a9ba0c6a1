import 'dart:math';

import 'package:diamond_company_app/app/ui/components/documnet_preview_dialog.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/design_config.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/usa_patriot_act_controller.dart';

/// UsaPatriotActView
class UsaPatriotActView extends GetView<UsaPatriotActController> {
  /// UsaPatriotActView
  const UsaPatriotActView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildCommonAppbar(),
        bottomNavigationBar: _buildBottomButtons(context),
        body: _buildBody(),
      );

  /// Build Body
  Widget _buildBody() => Padding(
        padding: REdgeInsets.symmetric(horizontal: 60),
        child: Column(
          children: <Widget>[
            70.verticalSpace,

            /// Description Text
            _buildDescriptionText(),
            60.verticalSpace,
            _buildChooseFileContainer(),
            Obx(
              () => AnimatedSize(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                child: Visibility(
                  visible: controller.chooseFileError().isNotEmpty,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: REdgeInsets.only(left: 44, top: 28),
                      child: Text(
                        controller.filePickerResult().files.isEmpty
                            ? LocaleKeys.custom_error_please_select_file.tr
                            : controller.chooseFileError().isNotEmpty
                                ? LocaleKeys
                                    .custom_error_file_size_should_be_less_than_5mb
                                    .tr
                                : '',
                        style: AppFontStyles.skolaSans(
                          color: Colors.red,
                          fontSize: 38.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  /// Build ChooseFile
  Widget _buildChooseFileContainer() => Obx(
        () => Container(
          height: 640.h,
          width: Get.width,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.k70777E,
              width: 1.w,
            ),
            borderRadius: BorderRadius.circular(24.r),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              InkWell(
                onTap: () => showDocumentPickerDialog(controller: controller),
                borderRadius: BorderRadius.circular(500.r),
                child: FittedBox(
                  child: Container(
                    height: 100.h,
                    //width: 535.w,
                    padding: REdgeInsets.symmetric(horizontal: 40),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: AppColors.k101C28,
                        width: min(4.w, 1),
                      ),
                      borderRadius: BorderRadius.circular(500.r),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      LocaleKeys.usa_patriot_act_choose_file.tr,
                      style: AppFontStyles.skolaSans(
                        color: AppColors.k101C28,
                        fontSize: 35.sp,
                        letterSpacing: 3.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
              ),
              48.verticalSpace,
              controller.filePickerResult().files.isEmpty
                  ? const SizedBox.shrink()
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            controller
                                    .filePickerResult()
                                    .files
                                    .firstOrNull
                                    ?.name ??
                                '',
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: AppFontStyles.skolaSans(
                              fontSize: 40.sp,
                              fontWeight: FontWeight.w400,
                              color: AppColors.k101C28,
                            ),
                          ).paddingSymmetric(horizontal: 60.w),
                          20.verticalSpace,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: () {
                                  controller.filePickerResult().files.clear();
                                  controller.filePickerResult.refresh();
                                },
                                icon: const Icon(Icons.delete_outline_outlined),
                              ),
                              IconButton(
                                onPressed: () {
                                  showDocumentPreviewDialogRegistration(
                                    controller: controller,
                                  );
                                },
                                icon: const Icon(Icons.remove_red_eye_outlined),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
              controller.filePickerResult().files.isEmpty
                  ? const SizedBox.shrink()
                  : 20.verticalSpace,
              Text(
                '${LocaleKeys.usa_patriot_act_allowed_file_types.tr}\n${LocaleKeys.usa_patriot_act_file_type.tr}',
                textAlign: TextAlign.center,
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.k70777E,
                ),
              ),
            ],
          ),
        ),
      );

  /// Build Bottom Buttons
  Widget _buildBottomButtons(BuildContext context) => Obx(
        () => Padding(
          padding: EdgeInsets.only(
            left: min(60.w, 15),
            right: min(60.w, 15),
            bottom: MediaQuery.of(context).viewInsets.bottom +
                MediaQuery.of(context).padding.bottom +
                60.h,
          ),
          child: controller.isKYCResubmission()
              ? _buildCommonAppButton(
                  onPressed: () => controller.resubmitDocuments(),
                  buttonText: LocaleKeys.dashboard_upload.tr,
                  buttonTextColor: AppColors.kBEFFFC,
                  isLoading: controller.isLoading(),
                )
              : Row(
                  children: <Widget>[
                    Expanded(
                      child: _buildCommonAppButton(
                        onPressed: () {
                          if (kIsWeb) {
                            DesignConfig.popItem();
                          } else {
                            Get.back();
                          }
                        },
                        buttonText: LocaleKeys.usa_patriot_act_previous.tr,
                        backgroundColor: AppColors.kEAEFF4,
                        borderColor: AppColors.kEAEFF4,
                        buttonTextColor: AppColors.k101C28,
                      ),
                    ),
                    //30.horizontalSpace,
                    SizedBox(width: min(30.w, 15)),
                    Expanded(
                      child: _buildCommonAppButton(
                        onPressed: () => controller.onClickNext(),
                        buttonText: LocaleKeys.usa_patriot_act_next.tr,
                        buttonTextColor: AppColors.kBEFFFC,
                      ),
                    ),
                  ],
                ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
        title: Text(
          LocaleKeys.usa_patriot_act_to_comply_with_the_usa_patriot_act.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
        actions: <Widget>[
          FittedBox(
            child: StatusChip(
              text: LocaleKeys.usa_patriot_act_three_five.tr,
              textStyle: AppFontStyles.skolaSans(
                color: AppColors.k101C28,
                fontSize: 35.sp,
                letterSpacing: 10.sp,
                fontWeight: FontWeight.w400,
              ),
              color: AppColors.kEAEFF4,
            ),
          ),
          //30.horizontalSpace,
          SizedBox(width: min(30.w, 15)),
        ],
      );

  /// Text Description
  Widget _buildDescriptionText() => Column(
        children: <Widget>[
          Center(
            child: Text(
              LocaleKeys.usa_patriot_act_description_text.tr,
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k70777E,
              ),
            ),
          ),
          Center(
            child: Text(
              LocaleKeys.usa_patriot_act_example.tr,
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ],
      );

  /// Common App Button
  Widget _buildCommonAppButton({
    required String buttonText,
    Function? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
    bool? isLoading,
  }) =>
      AppButton.text(
        buttonText: buttonText,
        onPressed: () {
          onPressed!();
        },
        buttonSize: Size(Get.width, 150.h),
        isLoading: isLoading ?? false,
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );
}
