import 'dart:async';
import 'dart:io';

import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/data/remote/api_service/init_api_service.dart';
import 'package:diamond_company_app/app/modules/credit_agreement/controllers/credit_agreement_controller.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart' hide MultipartFile, FormData, Response;
import 'package:http_parser/http_parser.dart' as httpParser;

import '../../../routes/app_pages.dart';
import '../../../ui/components/add_trade_references_confirmation_dialog.dart';
import '../../trade_references/controllers/trade_references_controller.dart';

/// UsaPatriotActController
class UsaPatriotActController extends GetxController {
  /// registration
  Rx<UserEntity> registration = const UserEntity().obs;

  /// file picker result
  Rx<FilePickerResult> filePickerResult = const FilePickerResult([]).obs;

  /// selected file error message
  RxString chooseFileError = ''.obs;

  /// is KYC re submission
  RxBool isKYCResubmission = false.obs;

  /// isLoading
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    logI('registration data Get.arguments ${Get.arguments}');

    if (Get.arguments != null) {
      if (Get.arguments is bool) {
        isKYCResubmission(Get.arguments);
      } else {
        registration(Get.arguments);
      }
      logI('registration data');
      logI(registration.toJson());
    }
  }

  /// pick file
  // Future<void> pickFile() async {
  //   final FilePickerResult? result = await FilePicker.platform.pickFiles(
  //     type: FileType.custom,
  //     allowedExtensions: <String>['jpg', 'pdf', 'png', 'gif', 'jpeg'],
  //   );
  //   if (result != null) {
  //     chooseFileError('');
  //     filePickerResult(result);
  //     logI('file name');
  //     logI(filePickerResult().files.firstOrNull?.name);
  //     filePickerResult.refresh();
  //   }
  // }

  Future<void> pickFile() async {
    final FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: <String>['jpg', 'pdf', 'png', 'gif', 'jpeg'],
    );
    if (result != null) {
      final PlatformFile file = result.files.first;
      const int maxFileSize = 5 * 1024 * 1024;

      filePickerResult(result);
      filePickerResult.refresh();

      if (file.size > maxFileSize) {
        chooseFileError(
            LocaleKeys.custom_error_file_size_should_be_less_than_5mb.tr);
        chooseFileError.refresh();
      } else {
        chooseFileError('');
        filePickerResult(result);
        logI('file name');
        logI(filePickerResult().files.firstOrNull?.name);
        filePickerResult.refresh();
      }
    }
  }

  /// pick file camera
  Future<void> pickFileCamera() async {
    final XFile? result =
        await ImagePicker().pickImage(source: ImageSource.camera);

    if (result != null) {
      const int maxFileSize = 5 * 1024 * 1024;
      final bytes = await result.readAsBytes();

      if (bytes.length > maxFileSize) {
        chooseFileError(
            LocaleKeys.custom_error_file_size_should_be_less_than_5mb.tr);
        chooseFileError.refresh();
      } else {
        chooseFileError('');
        filePickerResult(FilePickerResult([
          PlatformFile(
              path: result.path,
              name: result.name,
              size: bytes.length,
              bytes: bytes)
        ]));
        filePickerResult.refresh();
        logI('file name');
        logI(filePickerResult().files.firstOrNull?.name);
        filePickerResult.refresh();
      }
    }
  }

  /// resubmit documents
  Future<void> resubmitDocuments() async {
    try {
      if (filePickerResult().files.isEmpty || chooseFileError().isNotEmpty) {
        chooseFileError(LocaleKeys.custom_error_please_select_file.tr);
        return;
      }

      isLoading(true);
      final String? documentName = await uploadDocument();
      if (documentName == null) {
        throw Exception(LocaleKeys.custom_error_document_is_empty.tr);
      }

      await AuthProvider.updateProfile({'document_name': documentName});

      appSnackbar(
        message: LocaleKeys.messages_document_updated_successfully.tr,
        snackBarState: SnackBarState.SUCCESS,
      );

      isLoading(false);
      if (kIsWeb) {
        DesignConfig.popItem();
      } else {
        Get.back();
      }
    } on DioException catch (e) {
      isLoading(false);
      logE(e);
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  /// upload document
  Future<String?> uploadDocument() async {
    try {
      if (filePickerResult().files.firstOrNull?.path == null) {
        return null;
      }

      final PlatformFile? platformFile = filePickerResult().files.firstOrNull;

      var uploadDocumentData = <String, dynamic>{
        'document': MultipartFile.fromBytes(
          kIsWeb
              ? platformFile?.bytes ?? []
              : File(filePickerResult().files.firstOrNull?.path ?? '')
                  .readAsBytesSync(),
          contentType: httpParser.MediaType(
            'document',
            filePickerResult().files.firstOrNull?.extension.toString() ?? '',
          ),
          filename: filePickerResult().files.firstOrNull?.name,
        ),
      };

      final Response<Map<String, dynamic>?>? result = await APIService.post(
        path: ApiConstants.uploadUserDocument,
        data: FormData.fromMap(uploadDocumentData),
      );

      if (result?.statusCode == 200) {
        if (result?.data != null) {
          logI(result?.data?['file']);
          return result?.data?['file'];
        }
      }
    } on DioException catch (e) {
      logE(e);
      rethrow;
    }
    return null;
  }

  /// on click next
  Future<void> onClickNext() async {
    if (filePickerResult().files.isEmpty || chooseFileError().isNotEmpty) {
      chooseFileError('please select file');
    } else {
      final bool isAdding =
          await showTradeReferecesConfirmationsDialog(controller: this);

      if (isAdding) {
        if (Get.isRegistered<TradeReferencesController>()) {
          Get.find<TradeReferencesController>()
            ..registration(registration())
            ..filePickerResult(filePickerResult())
            ..refresh();
        }

        if (kIsWeb) {
          Get.routing.args = <Object>[
            registration(),
            filePickerResult(),
          ];
          return Get.toNamed(
            Routes.TRADE_REFERENCES,
            id: DesignConfig.listingSideMenuId,
            arguments: <Object>[
              registration(),
              filePickerResult(),
            ],
          );
        } else {
          return Get.toNamed(
            Routes.TRADE_REFERENCES,
            arguments: <Object>[
              registration(),
              filePickerResult(),
            ],
          );
        }
      } else {
        if (Get.isRegistered<CreditAgreementController>()) {
          Get.find<CreditAgreementController>()
            ..registration(registration())
            ..filePickerResult(filePickerResult())
            ..refresh();
        }

        if (kIsWeb) {
          Get.routing.args = <Object>[
            registration(),
            filePickerResult(),
          ];
          return Get.toNamed(
            Routes.CREDIT_AGREEMENT,
            id: DesignConfig.listingSideMenuId,
            arguments: <Object>[
              registration(),
              filePickerResult(),
            ],
          );
        } else {
          return Get.toNamed(
            Routes.CREDIT_AGREEMENT,
            arguments: <Object>[
              registration(),
              filePickerResult(),
            ],
          );
        }
      }
    }
  }
}
