import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

/// MyAccountController
class MyAccountController extends GetxController {
  /// Is Company Detail Edit
  bool isCompanyDetailEdit = true;

  /// Open Mail
  void openMail() async {
    final Uri emailLaunchUri = Uri(
      scheme: 'mailto',
      path: UserProvider.currentUser?.email ?? '',
      query:
          'subject=demo Email&body=Hello, this is a demo email.', // add subject and body here
    );
    if (await canLaunchUrl(emailLaunchUri)) {
      await launchUrl(emailLaunchUri);
    } else {
      print('Could not launch $emailLaunchUri');
    }
  }

  /// Open Instagram
  void openInstagram() async {
    logI(UserProvider.currentUser?.instagramId); // null
    final Uri instagramUri = Uri.parse(
        '${UserProvider.currentUser?.instagramId ?? 'https://www.instagram.com/'}');
    if (await canLaunchUrl(instagramUri)) {
      await launchUrl(instagramUri);
    } else {
      print('Could not launch $instagramUri');
    }
  }

  /// Open Facebook
  void openFacebook() async {
    final Uri facebookUri = Uri.parse(
        '${UserProvider.currentUser?.facebookId ?? 'https://www.facebook.com/'}');
    if (await canLaunchUrl(facebookUri)) {
      await launchUrl(facebookUri);
    } else {
      print('Could not launch $facebookUri');
    }
  }

  /// get user details
  Future<void> getUserDetails() async {
    await AuthProvider.getUserDetails();
  }
}
