import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/providers/buy_request_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/config/logger.dart';
import '../../../ui/components/app_snackbar.dart';

///BuyRequestController
class BuyRequestController extends GetxController {
  /// pending buy requests
  RxList<BuyRequest> pendingBuyRequests = <BuyRequest>[].obs;

  /// requested buy requests
  RxList<BuyRequest> requestedBuyRequests = <BuyRequest>[].obs;

  /// isLoading
  RxBool isLoading = false.obs;

  /// Is buy request updated
  RxBool isBuyRequestUpdated = false.obs;

  /// isLoading closed buyRequest
  RxBool isLoadingClosed = false.obs;

  /// isLoadMore
  RxBool isLoadMore = false.obs;

  /// limit
  int limit = 10;

  /// limit
  int skip = 0;

  /// scroll controller
  final ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();

    scrollController.addListener(_scrollListener);

    fetchBuyRequest();
  }

  void _scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      loadMoreData();
    }
  }

  /// buy request
  Future<void> fetchBuyRequest() async {
    try {
      isLoading(true);
      pendingBuyRequests().clear();
      pendingBuyRequests(
          await BuyRequestProvider.buyRequest(status: 'pending,updated'));
      pendingBuyRequests.refresh();

      await fetchClosedBuyRequest(skip: 0, limit: limit);
      isLoading(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  /// load more
  Widget loadMoreData() {
    if (isLoadMore()) {
      if (!isLoadingClosed()) {
        fetchClosedBuyRequest(skip: skip, limit: limit);
      }
    }
    return const SizedBox.shrink();
  }

  /// buy request
  Future<void> fetchClosedBuyRequest({int? skip, int? limit}) async {
    try {
      isLoadingClosed(true);

      if (skip == 0) {
        this.skip = 0;
        isLoadMore(true);
        requestedBuyRequests.clear();
      }

      final List<BuyRequest> buyRequests = await BuyRequestProvider.buyRequest(
        status: 'accepted,canceled,auto-canceled',
        limit: limit,
        skip: skip,
      );
      if (buyRequests.isEmpty || buyRequests.length < this.limit) {
        isLoadMore(false);
      } else {
        this.skip = this.skip + this.limit;
      }
      requestedBuyRequests
        ..addAll(buyRequests)
        ..refresh();
      isLoadingClosed(false);
    } on DioException catch (e) {
      appSnackbar(
        message: e.response?.data['message'] ??
            LocaleKeys.messages_something_went_wrong.tr,
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoadingClosed(false);
    }
  }

  /// navigate to buy request
  Future<void> navigateToBuyRequest(BuyRequest buyRequest) async {
    isBuyRequestUpdated(false);
    if (kIsWeb) {
      Get.routing.args = buyRequest;
      await Get.toNamed(Routes.BUY_REQUEST_DETAILS,
          id: DesignConfig.listingSideMenuId);
    } else {
      await Get.toNamed(
        Routes.BUY_REQUEST_DETAILS,
        arguments: buyRequest,
      );
    }
    if (isBuyRequestUpdated()) {
      await fetchBuyRequest();
    }
  }
}
