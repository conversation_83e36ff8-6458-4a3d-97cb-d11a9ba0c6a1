import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/custom_back_button.dart';
import '../controllers/register_email_controller.dart';

/// RegisterEmailView
class RegisterEmailView extends GetView<RegisterEmailController> {
  /// RegisterEmailView
  const RegisterEmailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        resizeToAvoidBottomInset: false,
        body: _buildBody(context),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SafeArea(
        child: Padding(
          padding: REdgeInsets.symmetric(horizontal: 60),
          child: FormBuilder(
            key: controller.fbKey,
            child: Column(
              children: <Widget>[
                25.verticalSpace,
                _buildHeaderPart(),
                105.verticalSpace,
                _buildText(),
                150.verticalSpace,
                _buildPinPut(),
                _buildCustomErrorMessage(context),
                80.verticalSpace,
                Obx(() => _buildCommonAppButton(
                      buttonText: LocaleKeys.verification_email_continue.tr,
                      isLoading: controller.verifyingEmail(),
                      onPressed: () => controller.onContinue(),
                    )),
                30.verticalSpace,
                Obx(
                  () => controller.start() == 0
                      ? _buildCommonAppButton(
                          buttonText: LocaleKeys.verification_email_resend.tr,
                          onPressed: () => controller.sendVerificationOTP(),
                          loaderColor: AppColors.k101C28,
                          isLoading: controller.sendingOTP(),
                          backgroundColor: AppColors.kEAEFF4,
                          borderColor: AppColors.kEAEFF4,
                          buttonTextColor: AppColors.k101C28,
                        )
                      : _buildBottomText().paddingOnly(top: 80.h),
                ),
              ],
            ),
          ),
        ),
      );

  /// Build CommonAppButton
  Widget _buildCommonAppButton({
    required String buttonText,
    Function()? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
    Color? loaderColor,
    bool isLoading = false,
  }) =>
      AppButton.text(
        buttonText: buttonText,
        loaderColor: loaderColor ?? Colors.white,
        onPressed: onPressed,
        buttonSize: Size(Get.width, 150.h),
        isLoading: isLoading,
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );

  /// Build Custom Error Message
  Widget _buildCustomErrorMessage(BuildContext context) => Obx(
        () => AnimatedSize(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
          child: Visibility(
            visible: controller.errorMessage.value.isNotEmpty,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: REdgeInsets.only(left: 44, top: 28),
                child: Text(
                  controller.errorMessage.value,
                  style: AppFontStyles.skolaSans(
                    color: Colors.red,
                    fontSize: 38.sp,
                  ),
                ),
              ),
            ),
          ),
        ),
      );

  /// Build Pin Put
  Widget _buildPinPut() => Container(
        height: 150.h,
        width: Get.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        child: Pinput(
          controller: controller.otpController,
          preFilledWidget: Text(
            '-',
            style: TextStyle(
              color: AppColors.k101C28,
              fontSize: 45.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          defaultPinTheme: PinTheme(
            width: 100.w,
            textStyle: TextStyle(
              fontSize: 56.sp,
              color: AppColors.k101C28,
            ),
          ),
          length: 6,
          onCompleted: (String value) => controller.onContinue(),
          onTap: () {},
        ),
      );

  /// Build Text
  Widget _buildText() => Column(
        children: <Widget>[
          Text(
            LocaleKeys.verification_email_verification_email.tr,
            style: AppFontStyles.skolaSans(
              fontSize: 70.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
          50.verticalSpace,
          Text(
            LocaleKeys.verification_email_code_sent_to_your_registered_phone.tr,
            textAlign: TextAlign.center,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k70777E.withOpacity(0.6),
            ),
          ),
          20.verticalSpace,
          Obx(
            () => Text(
              controller.userEntity().email ?? '',
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ],
      );

  /// Build Header Part
  Widget _buildHeaderPart() => SizedBox(
        height: 200.h,
        width: Get.width,
        child: Stack(
          children: <Widget>[
            Padding(
              padding: REdgeInsets.only(top: 45),
              child: CustomBackButton(onPressed: () {
                Get.back();
              }),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: SizedBox(
                height: 200.h,
                width: 200.w,
                child: AppImages.appLogo,
              ),
            ),
          ],
        ),
      );

  /// Bottom Text
  Widget _buildBottomText() => Obx(
        () => Text(
          'Resend code in ${controller.start()} secs',
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k101C28,
          ),
        ),
      );
}
