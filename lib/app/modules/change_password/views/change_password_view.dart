import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_form_field.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../../../ui/components/dashed_divider.dart';
import '../controllers/change_password_controller.dart';

/// ChangePasswordView
class ChangePasswordView extends GetView<ChangePasswordController> {
  /// ChangePasswordView
  const ChangePasswordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildCommonAppbar(),
        body: _buildBody(context),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: REdgeInsets.only(
              left: 60,
              right: 60,
              bottom: MediaQuery.of(context).viewInsets.bottom),
          child: FormBuilder(
            key: controller.fbKey,
            child: Column(
              children: <Widget>[
                45.verticalSpace,
                _buildHeaderText(),
                100.verticalSpace,
                _buildCurrentPasswordTextFormField(),
                80.verticalSpace,
                CustomDashedDivider(color: AppColors.k70777E.withOpacity(0.5)),
                80.verticalSpace,
                _buildCreateNewPasswordTextFormField(),
                80.verticalSpace,
                _buildConfirmNewPasswordTextFormField(),
                80.verticalSpace,
                _buildAppButton(),
              ],
            ),
          ),
        ),
      );

  /// Build Header Text
  Widget _buildHeaderText() => Align(
        child: Text(
          LocaleKeys.change_password_change_password_both_are_same.tr,
          style: AppFontStyles.skolaSans(
            fontSize: 40.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k70777E.withOpacity(0.6),
          ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          Get.back();
        },
        title: Text(
          LocaleKeys.change_password_change_password.tr,
          textAlign: TextAlign.center,
          style: AppFontStyles.skolaSans(
            fontSize: 45.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        centerTitle: true,
      );

  /// Build Create Pass Field
  Widget _buildCurrentPasswordTextFormField() => Obx(
        () => _buildTextFormField(
          controller: controller.currentPasswordController,
          onPressed: () {
            controller.currentPasswordVisibleToggle();
          },
          onSubmit: (String? p0) {
            controller.fbKey.currentState
                ?.fields[LocaleKeys.change_password_new_password]
                ?.focus();
            return null;
          },
          isShow: controller.currentPasswordVisible.value,
          isVisible: controller.currentPasswordVisible.value,
          name: LocaleKeys.change_password_current_password,
          labelText: LocaleKeys.change_password_current_password.tr,
          obscureText: controller.currentPasswordVisible.value,
          validator: (String? value) {
            if (value?.trim().isEmpty ?? true) {
              return LocaleKeys.validation_password_is_empty.tr;
            } else if (value!.trim().length < 6) {
              return LocaleKeys.validation_password_length.tr;
            }
            return null;
          },
        ),
      );

  /// Build Create Pass Field
  Widget _buildCreateNewPasswordTextFormField() => Obx(
        () => _buildTextFormField(
          controller: controller.newPasswordController,
          onPressed: () {
            controller.createNewPasswordVisibleToggle();
          },
          onSubmit: (p0) {
            controller.fbKey.currentState
                ?.fields[LocaleKeys.change_password_confirm_new_password]
                ?.focus();
            return null;
          },
          isShow: controller.setNewPasswordVisible.value,
          isVisible: controller.setNewPasswordVisible.value,
          name: LocaleKeys.change_password_new_password,
          labelText: LocaleKeys.change_password_new_password.tr,
          obscureText: controller.setNewPasswordVisible.value,
          validator: (String? value) {
            final String password = value?.trim() ?? '';
            return password.isValidPassword();
          },
        ),
      );

  /// Build Confirm Pass Field
  Widget _buildConfirmNewPasswordTextFormField() => Obx(
        () => _buildTextFormField(
          controller: controller.confirmPasswordController,
          onPressed: () {
            controller.confirmNewPasswordVisibleToggle();
          },
          onSubmit: (String? p0) {
            controller.changePassword();
            return null;
          },
          isShow: controller.confirmNewPasswordVisible.value,
          isVisible: controller.confirmNewPasswordVisible.value,
          name: LocaleKeys.change_password_confirm_new_password,
          labelText: LocaleKeys.change_password_confirm_new_password.tr,
          obscureText: controller.confirmNewPasswordVisible.value,
          validator: (String? value) {
            if (value?.trim().isEmpty ?? true) {
              return LocaleKeys.validation_password_is_empty.tr;
            } else if (value!.trim().length < 6) {
              return LocaleKeys.validation_password_length.tr;
            } else if (controller.confirmPasswordController.text !=
                controller.newPasswordController.text) {
              return LocaleKeys.validation_password_does_not_match.tr;
            }
            return null;
          },
        ),
      );

  /// Build AppButton
  Widget _buildAppButton() => Obx(
        () => AppButton.text(
          isLoading: controller.isLoading(),
          buttonText: LocaleKeys.change_password_change_password.tr,
          onPressed: () => controller.changePassword(),
          buttonSize: Size(Get.width, 150.h),
          backgroundColor: AppColors.k101C28,
          borderRadius: 24.r,
          borderColor: AppColors.k101C28,
          buttonTextStyle: TextStyle(
            fontSize: 45.sp,
            fontWeight: FontWeight.w700,
            color: AppColors.kBEFFFC,
          ),
        ),
      );

  /// Build Text Field
  Widget _buildTextFormField({
    required String name,
    required TextEditingController controller,
    required String? Function(String?)? validator,
    required String labelText,
    required bool obscureText,
    required VoidCallback onPressed,
    required bool isShow,
    required bool isVisible,
    String? Function(String?)? onChange,
    String? Function(String?)? onSubmit,
    TextInputAction? textInputAction,
  }) =>
      AppTextFormField(
        controller: controller,
        onSubmit: onSubmit,
        onChange: onChange,
        name: name,
        validator: validator,
        labelText: labelText,
        obscureText: obscureText,
        textInputAction: textInputAction,
        labelTextStyle: AppFontStyles.skolaSans(
          color: AppColors.k70777E,
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          color: isVisible ? AppColors.k70777E : AppColors.k101C28,
          fontWeight: FontWeight.w400,
          letterSpacing: 10.w,
        ),
        suffixIcon: IconButton(
          onPressed: onPressed,
          icon: isShow
              ? SvgPicture.asset(
                  AppImages.visibilityOffPath,
                  height: 57.h,
                  width: 57.w,
                )
              : Icon(
                  Icons.visibility_outlined,
                  color: AppColors.k70777E,
                  size: 57.h,
                ),
        ),
        constraints: BoxConstraints(
          minHeight: 150.h,
        ),
        showBorder: true,
        fillColor: AppColors.k70777E,
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            width: 1.w,
            color: AppColors.k70777E,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.k70777E,
          ),
        ),
        contentPadding: REdgeInsets.only(left: 48),
      );
}
