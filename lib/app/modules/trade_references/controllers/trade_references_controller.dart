import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/local/user_provider.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/modules/legal_status_organization/controllers/legal_status_organization_controller.dart';
import 'package:diamond_company_app/app/modules/new_company_registration/controllers/new_company_registration_controller.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

/// TradeReferencesController
class TradeReferencesController extends GetxController {
  /// legal status organization controller
  LegalStatusOrganizationController legalStatusOrganizationController =
      Get.put(LegalStatusOrganizationController());

  /// new company registration controller
  NewCompanyRegistrationController newCompanyRegistrationController =
      Get.find<NewCompanyRegistrationController>();

  /// FormBuilder Key
  GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();

  /// registration
  Rx<UserEntity> registration = const UserEntity().obs;

  /// file picker result
  Rx<FilePickerResult> filePickerResult = const FilePickerResult([]).obs;

  /// trade references list
  RxList<TradeReference> tradeReferencesList = <TradeReference>[].obs;

  /// error message
  RxBool showErrorMessage = false.obs;

  /// phone Prefix
  RxString phoneCountryCode = ''.obs;

  /// selected Country code for phone
  void selectPhoneCountryCode(Country country) {
    phoneCountryCode(country.phoneCode);
  }

  /// isUpdate
  RxBool isEdit = false.obs;

  /// isLoading
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments is List<dynamic>) {
      registration(Get.arguments[0]);
      filePickerResult(Get.arguments[1]);
    }

    if (Get.arguments is Map<String, dynamic>) {
      if (Get.arguments['isEdit'] != null) {
        isEdit(Get.arguments['isEdit']);
        tradeReferencesList(
            [...AuthProvider.userEntity().tradeReferences ?? []]);
        tradeReferencesList.refresh();
      }
    }
  }

  /// Compare As
  String? compareAs(TradeReferencesController controller,
          {required String key}) =>
      controller.newCompanyRegistrationController.fbKey.currentState
          ?.fields[key]?.value
          .toString()
          .trim();

  /// Update Profile Data
  Future<void> updateProfileData() async {
    try {
      isLoading(true);
      if (tradeReferencesList.length >= 3) {
        showErrorMessage(false);
        final Map<String, dynamic> data = {
          //'id': UserProvider.currentUser?.id,
          'trade_references': tradeReferencesList
              .map((TradeReference e) => {
                    'company_name': e.companyName,
                    'contact_person_name': e.contactPersonName,
                    if (e.email?.isNotEmpty ?? false) 'email': e.email,
                    'phone': e.phone,
                  })
              .toList(),
        };

        final bool response = await AuthProvider.updateProfile(data);

        if (response) {
          appSnackbar(
            message: 'Updated successfully',
            snackBarState: SnackBarState.SUCCESS,
          );
          Get.back();
        }
      } else {
        showErrorMessage(true);
      }
      isLoading(false);
    } on DioException catch (e) {
      isLoading(false);
      appSnackbar(
        message: e.response?.data['message'] ?? 'Oops! Something went wrong',
        snackBarState: SnackBarState.DANGER,
      );
      logE(e);
    } finally {
      isLoading(false);
    }
  }

  /// add trade references
  void addTradeReferences(TradeReferencesController controller) {
    if (controller.fbKey.currentState?.saveAndValidate() ?? false) {
      Future<void>.delayed(
        const Duration(milliseconds: 100),
        () {
          controller.tradeReferencesList.add(
            TradeReference(
              companyName: controller
                  .fbKey
                  .currentState
                  ?.fields[RegistrationFormFields.tradeReferenceCompanyName]
                  ?.value,
              contactPersonName: controller
                  .fbKey
                  .currentState
                  ?.fields[
                      RegistrationFormFields.tradeReferenceContactPersonName]
                  ?.value,
              email: controller.fbKey.currentState
                  ?.fields[RegistrationFormFields.tradeReferenceEmail]?.value
                  ?.toLowerCase()
                  ?.trim(),
              phone:
                  '+${phoneCountryCode.isEmpty ? newCompanyRegistrationController.countryPhoneCode.isEmpty ? 1 : newCompanyRegistrationController.countryPhoneCode.toString() : phoneCountryCode}-${fbKey.currentState?.fields[RegistrationFormFields.tradeReferencePhone]?.value}',
            ),
          );
          controller.tradeReferencesList.refresh();
          showErrorMessage(false);
          Get.back();
        },
      );
    } else {
      logI('missed something');
    }
  }

  /// onclick next
  Future<void> onClickNext() async {
    if (tradeReferencesList.isEmpty) {
      showErrorMessage(true);
    } else if (tradeReferencesList.length < 3) {
      showErrorMessage(true);
    } else {
      final UserEntity updatedRegistrationData =
          registration().copyWith(tradeReferences: tradeReferencesList());
      await Get.toNamed(
        Routes.CREDIT_AGREEMENT,
        arguments: [updatedRegistrationData, filePickerResult()],
      );
    }
  }

  /// save button pressed
  void saveButtonPressed() {
    if (UserProvider.currentUser?.isVerified ?? false) {
      appSnackbar(
        message: 'For edit profile, please contact to admin',
        snackBarState: SnackBarState.MESSAGE,
      );
    } else {
      if (tradeReferencesList.isEmpty) {
        showErrorMessage(true);
        return;
      } else {
        showErrorMessage(false);
        updateProfileData();
      }
    }
  }
}
