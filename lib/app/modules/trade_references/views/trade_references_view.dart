import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/ui/components/add_trade_reference_alert_dialog.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/int_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../generated/locales.g.dart';
import '../../../data/config/app_colors.dart';
import '../../../data/config/app_images.dart';
import '../../../ui/components/app_button.dart';
import '../../../ui/components/app_text_style.dart';
import '../../../ui/components/common_appbar.dart';
import '../controllers/trade_references_controller.dart';

/// TradeReferencesView
class TradeReferencesView extends GetView<TradeReferencesController> {
  /// TradeReferencesView
  const TradeReferencesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => Obx(
        () => PopScope(
          canPop: !controller.isLoading(),
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: _buildCommonAppbar(),
            bottomNavigationBar: _buildBottomButtons(context),
            body: IgnorePointer(
              ignoring: controller.isLoading(),
              child: _buildBody(context),
            ),
          ),
        ),
      );

  /// Build Body
  Widget _buildBody(BuildContext context) => SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: REdgeInsets.only(
          left: 60,
          right: 60,
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Column(
          children: <Widget>[
            60.verticalSpace,
            _buildShowTradeReferences(),
            80.verticalSpace,
            Align(
              alignment: Alignment.topLeft,
              child: InkWell(
                onTap: () {
                  showAddTradeReferenceAlertDialog(
                      controller: controller, context: context);
                },
                borderRadius: BorderRadius.circular(500.r),
                child: Container(
                  height: 100.h,
                  width: 530.w,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppColors.k101C28,
                      width: 4.w,
                    ),
                    borderRadius: BorderRadius.circular(500.r),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    LocaleKeys.trade_references_add_trade_preference.tr,
                    style: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 35.sp,
                      letterSpacing: 3.sp,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            ),
            60.verticalSpace,
            Obx(
              () => AnimatedSize(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                child: Visibility(
                  visible: controller.showErrorMessage(),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: REdgeInsets.only(left: 44, top: 28),
                      child: Text(
                        'Please add at-least 3 trade reference',
                        style: AppFontStyles.skolaSans(
                          color: Colors.red,
                          fontSize: 38.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  /// Build Show Trade References
  Widget _buildShowTradeReferences() => Obx(
        () => ListView.separated(
          shrinkWrap: true,
          itemCount: controller.tradeReferencesList.length,
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (BuildContext context, int index) =>
              60.verticalSpace,
          itemBuilder: (BuildContext context, int index) =>
              _buildTradeReferences(
                  controller.tradeReferencesList[index], index),
        ),
      );

  /// Build Bottom Buttons
  Widget _buildBottomButtons(BuildContext context) => Padding(
        padding: EdgeInsets.only(
          left: 60.w,
          right: 60.w,
          bottom: MediaQuery.of(context).viewInsets.bottom +
              MediaQuery.of(context).padding.bottom +
              60.h,
        ),
        child: controller.isEdit()
            ? Obx(
                () => AppButton.text(
                  isLoading: controller.isLoading(),
                  buttonText: 'Save',
                  onPressed: controller.saveButtonPressed,
                  buttonSize: Size(Get.width, 150.h),
                  backgroundColor: AppColors.k101C28,
                  borderRadius: 24.r,
                  borderColor: AppColors.k101C28,
                  buttonTextStyle: TextStyle(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.kBEFFFC,
                  ),
                ),
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () => Get.back(),
                      buttonText: LocaleKeys.trade_references_previous.tr,
                      backgroundColor: AppColors.kEAEFF4,
                      borderColor: AppColors.kEAEFF4,
                      buttonTextColor: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () => controller.onClickNext(),
                      buttonText: LocaleKeys.trade_references_next.tr,
                      buttonTextColor: AppColors.kBEFFFC,
                    ),
                  ),
                ],
              ),
      );

  /// Build Trade References
  Widget _buildTradeReferences(TradeReference tradeReferences, int index) =>
      Container(
        width: Get.width,
        decoration: BoxDecoration(
          border: Border.all(
            width: 1.w,
            color: AppColors.k70777E,
          ),
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Padding(
          padding: REdgeInsets.only(top: 40, bottom: 40, left: 60, right: 33),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Container(
                    height: 70.h,
                    width: 115.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: AppColors.kEAEFF4,
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    child: Text(
                      '${(index + 1).withSuffix}',
                      // '1ST',
                      style: AppFontStyles.skolaSans(
                        color: AppColors.k101C28.withOpacity(0.6),
                        fontSize: 30.sp,
                        letterSpacing: 3.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () =>
                        controller.tradeReferencesList.removeAt(index),
                    padding: EdgeInsets.zero,
                    visualDensity: const VisualDensity(
                      horizontal: VisualDensity.minimumDensity,
                      vertical: VisualDensity.minimumDensity,
                    ),
                    icon: SizedBox(
                      height: 64.h,
                      width: 64.w,
                      child: AppImages.deleteLogo,
                    ),
                  ),
                ],
              ),
              33.verticalSpace,
              Text(
                '${tradeReferences.contactPersonName.toString().toUpperCase()}',
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
              33.verticalSpace,
              Row(
                children: <Widget>[
                  SizedBox(
                    height: 45.h,
                    width: 45.w,
                    child: AppImages.phoneIcon,
                  ),
                  15.horizontalSpace,
                  Text(
                    '${tradeReferences.phone}',
                    style: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 40.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              if (tradeReferences.email?.isNotEmpty ?? false) 33.verticalSpace,
              if (tradeReferences.email?.isNotEmpty ?? false)
                Row(
                  children: <Widget>[
                    SizedBox(
                      height: 45.h,
                      width: 45.w,
                      child: AppImages.mailIconLogo,
                    ),
                    15.horizontalSpace,
                    Text(
                      '${tradeReferences.email}',
                      style: AppFontStyles.skolaSans(
                        color: AppColors.k101C28,
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              33.verticalSpace,
              Row(
                children: <Widget>[
                  SizedBox(
                    height: 45.h,
                    width: 45.w,
                    child: AppImages.companyIconLogo,
                  ),
                  15.horizontalSpace,
                  Text(
                    '${tradeReferences.companyName}',
                    style: AppFontStyles.skolaSans(
                      color: AppColors.k101C28,
                      fontSize: 40.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );

  /// Common AppBar
  CommonAppbar _buildCommonAppbar() => CommonAppbar(
        onBackPressed: () {
          if (!controller.isLoading()) {
            Get.back();
          }
        },
        title: Column(
          children: <Widget>[
            Text(
              controller.isEdit()
                  ? 'Edit ${LocaleKeys.trade_references_trade_references.tr}'
                  : LocaleKeys.trade_references_trade_references.tr,
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
            20.verticalSpace,
            _buildDescriptionText(),
          ],
        ),
        centerTitle: true,
        actions: <Widget>[
          if (!controller.isEdit())
            FittedBox(
              child: StatusChip(
                text: LocaleKeys.trade_references_four_five.tr,
                textStyle: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  letterSpacing: 10.sp,
                  fontWeight: FontWeight.w400,
                ),
                color: AppColors.kEAEFF4,
              ),
            ),
          30.horizontalSpace,
        ],
      );

  /// Text Description
  Widget _buildDescriptionText() => RichText(
        text: TextSpan(
          text: LocaleKeys.trade_references_list_at_least.tr,
          style: AppFontStyles.skolaSans(
            fontSize: 30.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k70777E,
          ),
          children: <InlineSpan>[
            TextSpan(
              text: LocaleKeys.trade_references_3_references.tr,
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
            TextSpan(
              text: ' ${LocaleKeys.trade_references_please.tr}',
              style: AppFontStyles.skolaSans(
                fontSize: 30.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k70777E,
              ),
            ),
          ],
        ),
      );

  /// Common App Button
  Widget _buildCommonAppButton({
    required String buttonText,
    Function? onPressed,
    Color? backgroundColor,
    Color? borderColor,
    Color? buttonTextColor,
  }) =>
      AppButton.text(
        buttonText: buttonText,
        onPressed: () {
          onPressed!();
        },
        buttonSize: Size(Get.width, 150.h),
        backgroundColor: backgroundColor ?? AppColors.k101C28,
        borderColor: borderColor ?? AppColors.k101C28,
        borderRadius: 24.r,
        buttonTextStyle: TextStyle(
          fontSize: 45.sp,
          fontWeight: FontWeight.w700,
          color: buttonTextColor ?? AppColors.kBEFFFC,
        ),
      );
}
