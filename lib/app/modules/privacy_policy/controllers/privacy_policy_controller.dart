import 'dart:async';

import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/static_pages/static_pages.dart';
import 'package:diamond_company_app/app/providers/static_pages_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class PrivacyPolicyController extends GetxController {
  Rx<StaticPagesType> staticPageKey = StaticPagesType.agreement.obs;

  Rx<StaticPages> staticPage = StaticPages().obs;

  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      staticPageKey(Get.arguments);
      getStaticPage();
    }
  }

  ///
  String translateTitle(StaticPagesType pageType) {
    if (pageType == StaticPagesType.agreement) {
      return LocaleKeys.profile_agreement.tr;
    } else if (pageType == StaticPagesType.return_policy) {
      return LocaleKeys.profile_return_policy.tr;
    } else if (pageType == StaticPagesType.privacy_policy) {
      return LocaleKeys.profile_privacy_policy.tr;
    } else if (pageType == StaticPagesType.help_and_support) {
      return LocaleKeys.profile_help_and_support.tr;
    } else if (pageType == StaticPagesType.legal_policy) {
      return LocaleKeys.profile_legal_policy.tr;
    } else if (pageType == StaticPagesType.terms_and_conditions) {
      return LocaleKeys.profile_terms_and_conditions.tr;
    } else if (pageType == StaticPagesType.shipping_policy) {
      return LocaleKeys.profile_shipping_policy.tr;
    } else if (pageType == StaticPagesType.vendor_terms_and_conditions) {
      return LocaleKeys.profile_vendor_terms_and_conditions.tr;
    } else {
      return '';
    }
  }

  /// get static page
  Future<void> getStaticPage() async {
    try {
      isLoading(true);
      staticPage(await StaticPagesProvider.getStaticPage(
          {'slug': staticPageKey().name}));
      staticPageKey.refresh();
      isLoading(false);
    } on DioException catch (e) {
      logE(e);
      isLoading(false);
      appSnackbar(
        message: e.response?.data?['message'] ?? 'Something went wrong!!!',
        snackBarState: SnackBarState.DANGER,
      );
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  /// tap url
  Future<bool> onTapUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      print('Could not launch $uri');
    }
    return true;
  }
}
