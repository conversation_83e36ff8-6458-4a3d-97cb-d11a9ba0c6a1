import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/ui/components/app_loader.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_appbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';

import '../../../data/config/design_config.dart';
import '../controllers/privacy_policy_controller.dart';

/// privacy policy
class PrivacyPolicyView extends GetView<PrivacyPolicyController> {
  /// privacy policy
  const PrivacyPolicyView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
      );

  Widget _buildBody() => Obx(
        () => controller.isLoading()
            ? AppLoader()
            : RefreshIndicator(
                color: AppColors.k101C28,
                onRefresh: () => controller.getStaticPage(),
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: REdgeInsets.all(60),
                  shrinkWrap: true,
                  children: [
                    Column(
                      children: [
                        HtmlWidget(
                          controller.staticPage().content ?? '',
                          onTapUrl: controller.onTapUrl,
                        ),
                      ],
                    ),
                  ],
                ).paddingOnly(
                  bottom: MediaQuery.of(Get.context!).padding.bottom,
                ),
              ),
      );

  CommonAppbar _buildAppBar() => CommonAppbar(
        title: Obx(
          () => Text(
            controller.translateTitle(controller.staticPageKey()),
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w500,
              fontSize: 45.sp,
              color: AppColors.k101C28,
            ),
          ),
        ),
        centerTitle: true,
        onBackPressed: () {
          if (kIsWeb) {
            DesignConfig.popItem();
          } else {
            Get.back();
          }
        },
      );
}
