import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

/// ConnectivityServiceController
class ConnectivityServiceController extends GetxController {
  /// check initial connectivity
  Future<void> checkInitialConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      Get.back();
    }
  }
}
