import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/local/local_store.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/jewellery/jewellery.dart';
import 'package:diamond_company_app/app/data/models/melee/melee_entity.dart';
import 'package:diamond_company_app/app/modules/buy_request_details/controllers/buy_request_details_controller.dart';
import 'package:diamond_company_app/app/modules/cart_updated/controllers/cart_updated_controller.dart';
import 'package:diamond_company_app/app/modules/my_order/controllers/my_order_controller.dart';
import 'package:diamond_company_app/app/modules/my_order/models/filter_order.dart';
import 'package:diamond_company_app/app/modules/return_order/controllers/return_order_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/diamond_list_tile.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/ui/components/jewellery_cart_item.dart';
import 'package:diamond_company_app/app/ui/components/melee_cart_item.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///   showCancelBuyRequestDialog
void showCancelBuyRequestDialog() {
  final BuyRequestDetailsController controller =
      Get.find<BuyRequestDetailsController>();
  controller.selectedCancelBuyRequestReason('');
  Get.dialog(_buildCancelBuyRequestWidget(controller: controller));
}

SimpleDialog _buildCancelBuyRequestWidget(
        {required BuyRequestDetailsController controller}) =>
    SimpleDialog(
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      contentPadding: REdgeInsets.symmetric(horizontal: 50),
      titlePadding: EdgeInsets.zero,
      children: <Widget>[
        Obx(
          () => FormBuilder(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            key: controller.cancelBuyRequestKey,
            child: Column(
              children: <Widget>[
                90.verticalSpace,
                Center(
                  child: Text(
                    'Cancel Request',
                    style: TextStyle(
                      fontSize: 70.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.k101C28,
                    ),
                  ),
                ),
                100.verticalSpace,
                _buildDropDownFormField(),
                (controller.selectedCancelBuyRequestReason() == 'Other' &&
                        controller.selectedCancelBuyRequestReason().isNotEmpty)
                    ? 80.verticalSpace
                    : const SizedBox.shrink(),
                (controller.selectedCancelBuyRequestReason() == 'Other' &&
                        controller.selectedCancelBuyRequestReason().isNotEmpty)
                    ? _buildOtherReasonField(controller)
                    : const SizedBox.shrink(),
                50.verticalSpace,
                _buildBuyRequestActions(controller),
                50.verticalSpace,
              ],
            ),
          ),
        ),
      ],
    );

Row _buildBuyRequestActions(BuyRequestDetailsController controller) => Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: 'Cancel',
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: 'Send',
            onPressed: () => controller.submitCancelBuyRequest(),
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );

Row _buildDeclineActions(bool? isFromOrder) => Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: 'Cancel',
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: 'Continue',
            onPressed: () {
              if (isFromOrder ?? false) {
                if (Get.find<ReturnOrderController>()
                        .returnWithReasonKey
                        .currentState
                        ?.saveAndValidate() ??
                    false) {
                  Get.back(
                      result: Get.find<ReturnOrderController>()
                          .returnWithReasonKey
                          .currentState
                          ?.fields['other']
                          ?.value);
                }
              }
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );

Row _buildRemoveCartActions({
  required CartUpdatedController cartController,
  DiamondEntity? diamond,
  MeleeEntity? melee,
  Jewellery? jewellery,
}) =>
    Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.my_orders_cancel.tr,
            onPressed: () => Get.back(),
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.cart_remove.tr,
            onPressed: () {
              Get.back();
              if (cartController.selectedTabIndex() == 0) {
                CartItemHelper.removeDiamond(diamond ?? DiamondEntity());
                Get.find<CartUpdatedController>()
                    .removeDiamond(diamond ?? DiamondEntity());
              } else if (cartController.selectedTabIndex() == 1) {
                CartItemHelper.removeJewelery(jewellery ?? Jewellery());
              } else if (cartController.selectedTabIndex() == 2) {
                CartItemHelper.removeMelee(melee ?? MeleeEntity());
              }
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );

Row _buildFilterStatusActions(MyOrderController orderController) => Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.my_orders_cancel.tr,
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.my_orders_apply.tr,
            onPressed: () {
              Get.back();
              orderController.fetchUserOrder(
                  skip: 0, limit: orderController.limit());
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );

Widget _buildDeclineReasonField(bool isFromOrder) => FormBuilder(
      autovalidateMode: AutovalidateMode.onUserInteraction,
      key: isFromOrder
          ? Get.find<ReturnOrderController>().returnWithReasonKey
          : Get.find<BuyRequestDetailsController>().cancelBuyRequestKey,
      child: AppTextFormField(
        name: 'other',
        labelText: LocaleKeys.my_order_detail_give_a_reason.tr,
        hintText: LocaleKeys.my_order_detail_write_here.tr,
        minLines: 6,
        maxLines: 7,
        textInputAction: TextInputAction.newline,
        keyboardType: TextInputType.multiline,
        validator: (String? value) {
          if (value == null || value.trim().isEmpty) {
            return 'Please enter some text';
          }
          final List<String> words = value.trim().split(RegExp(r'\s+'));
          if (words.length < 15) {
            return 'The text must contain at least 15 words';
          }
          return null;
        },
        hintTextStyle: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.kCFD2D4,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k101C28,
        ),
        contentPadding: REdgeInsets.symmetric(horizontal: 48, vertical: 50),
        labelTextStyle: AppFontStyles.skolaSans(
          fontSize: 35.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k70777E,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
      ),
    );

Widget _buildOtherReasonField(BuyRequestDetailsController controller) =>
    AppTextFormField(
      name: 'Other',
      labelText: LocaleKeys.my_order_detail_give_a_reason.tr,
      hintText: LocaleKeys.my_order_detail_write_here.tr,
      minLines: 3,
      maxLines: 4,
      hintTextStyle: AppFontStyles.skolaSans(
        fontSize: 45.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.kCFD2D4,
      ),
      style: AppFontStyles.skolaSans(
        fontSize: 45.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.k101C28,
      ),
      validator: (String? value) {
        if (value?.trim().isEmpty ?? true) {
          return 'Please enter reason';
        }
        return null;
      },
      contentPadding: REdgeInsets.symmetric(horizontal: 48, vertical: 50),
      labelTextStyle: AppFontStyles.skolaSans(
        fontSize: 35.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.k70777E,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
    );

DropDownFormField _buildDropDownFormField() {
  final RxList<String> declineReason =
      Get.find<BuyRequestDetailsController>().cancelBuyRequestReasons;
  return DropDownFormField(
    menuItemStyle: AppFontStyles.skolaSans(
      fontSize: 45.sp,
      fontWeight: FontWeight.w400,
      color: AppColors.k101C28,
    ),
    decoration: InputDecoration(
      labelText: LocaleKeys.my_order_detail_select_reason.tr,
      contentPadding: REdgeInsets.symmetric(horizontal: 48),
      labelStyle: AppFontStyles.skolaSans(
        fontSize: 35.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.k70777E,
      ),
      floatingLabelStyle: AppFontStyles.skolaSans(
        fontSize: 35.sp,
        fontWeight: FontWeight.w400,
        color: AppColors.k70777E,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          color: AppColors.k70777E,
          width: 1.w,
        ),
      ),
    ),
    items: declineReason,
    onChanged: (dynamic value) => Get.find<BuyRequestDetailsController>()
        .selectedCancelBuyRequestReason(value ?? ''),
  );
}

///   showDeclineWithReasonDialog
Future<String?> showDeclineWithReasonDialog({bool? isFromOrder}) async =>
    Get.dialog(_buildDeclinedWithReasonWidget(isFromOrder: isFromOrder));

SimpleDialog _buildDeclinedWithReasonWidget({bool? isFromOrder}) =>
    SimpleDialog(
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      contentPadding: REdgeInsets.symmetric(horizontal: 50),
      titlePadding: EdgeInsets.zero,
      children: <Widget>[
        90.verticalSpace,
        Center(
          child: Text(
            (isFromOrder ?? false)
                ? LocaleKeys.return_order_return_with_reason.tr
                : LocaleKeys.my_order_detail_declined_with_reason.tr,
            style: TextStyle(
              fontSize: 70.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
        ),
        Center(
          child: Text(
            '(${LocaleKeys.my_order_detail_min_15_words_required.tr})',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w400,
              fontSize: 35.sp,
              color: AppColors.k1A47E8,
            ),
          ),
        ),
        90.verticalSpace,
        _buildDeclineReasonField(isFromOrder ?? false),
        50.verticalSpace,
        _buildDeclineActions(isFromOrder),
        50.verticalSpace,
      ],
    );

///   showRemoveCartDialog
Future<void> showRemoveCartDialog(
    {DiamondEntity? diamond, MeleeEntity? melee, Jewellery? jewellery}) async {
  final CartUpdatedController cartController =
      Get.find<CartUpdatedController>();

  if (cartController.selectedTabIndex() == 0) {
    if (LocalStore.smoothSelectedDiamondRemove() ?? false) {
      CartItemHelper.removeDiamond(diamond ?? DiamondEntity());
      cartController.removeDiamond(diamond ?? DiamondEntity());
      return;
    }
  } else if (cartController.selectedTabIndex() == 1) {
    if (LocalStore.smoothSelectedJewelleryRemove() ?? false) {
      CartItemHelper.removeJewelery(jewellery ?? Jewellery());
      return;
    }
  } else if (cartController.selectedTabIndex() == 2) {
    if (LocalStore.smoothSelectedMeleeRemove() ?? false) {
      CartItemHelper.removeMelee(melee ?? MeleeEntity());
      return;
    }
  }
  await Get.dialog(
    _buildRemoveCartWidget(
      cartController: cartController,
      diamond: diamond,
      jewellery: jewellery,
      melee: melee,
    ),
  );
}

SimpleDialog _buildRemoveCartWidget({
  required CartUpdatedController cartController,
  DiamondEntity? diamond,
  MeleeEntity? melee,
  Jewellery? jewellery,
}) =>
    SimpleDialog(
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24.r)),
      contentPadding: REdgeInsets.symmetric(horizontal: 50),
      titlePadding: EdgeInsets.zero,
      children: <Widget>[
        100.verticalSpace,
        Center(
          child: Text(
            LocaleKeys.cart_confirm_remove_dialog_title.tr,
            style: TextStyle(
              fontSize: 70.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        50.verticalSpace,
        if (diamond != null)
          Container(
            padding: REdgeInsets.symmetric(horizontal: 40, vertical: 30),
            decoration: BoxDecoration(
              color: AppColors.kffffff,
              borderRadius: BorderRadius.circular(24.r),
              boxShadow: <BoxShadow>[
                BoxShadow(
                  color: AppColors.k000000.withOpacity(0.1),
                  offset: Offset(0, 10.h),
                  blurRadius: 104.r,
                ),
              ],
            ),
            child: DiamondListTile(
              isFromCart: true,
              diamond: diamond,
            ),
          ),
        if (melee != null)
          MeleeCartItem(
            isFromRemoveCart: true,
            melee: melee,
          ),
        if (jewellery != null)
          JewelleryCartItem(
            isFromRemoveCart: true,
            cartItem: CartItem(
              jewellery: jewellery,
              type: CartItemType.jewellery,
            ),
          ),
        50.verticalSpace,
        _buildDonNotShow(cartController),
        50.verticalSpace,
        _buildRemoveCartActions(
          cartController: cartController,
          diamond: diamond,
          melee: melee,
          jewellery: jewellery,
        ),
        50.verticalSpace,
      ],
    );

Widget _buildDonNotShow(CartUpdatedController cartController) => Obx(
      () => InkWell(
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () => cartController.toggleDoNotAskAgain(),
        child: Row(
          children: <Widget>[
            Checkbox(
              value: cartController.isDoNotAskAgain(),
              visualDensity: VisualDensity.compact,
              activeColor: AppColors.k101C28,
              side: BorderSide(
                color: AppColors.k101C28,
                width: 3.w,
              ),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: AppColors.k101C28,
                  width: 3.w,
                ),
                borderRadius: BorderRadius.circular(15.r),
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              onChanged: (bool? value) =>
                  cartController.toggleDoNotAskAgain(value: value),
            ),
            Text(
              'Do not ask again',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w400,
                fontSize: 35.sp,
                color: AppColors.k101C28,
              ),
            ),
          ],
        ),
      ),
    );
// CheckboxListTile(
//   value: cartController.isDoNotAskAgain(),
//   title: Text(
//     'Do not ask again',
//     style: AppFontStyles.skolaSans(
//       fontWeight: FontWeight.w400,
//       fontSize: 35.sp,
//       color: AppColors.k101C28,
//     ),
//   ),
//   visualDensity: VisualDensity.compact,
//   activeColor: AppColors.k101C28,
//   side: BorderSide(
//     color: AppColors.k101C28,
//     width: 3.w,
//   ),
//   checkColor: AppColors.k101C28,
//   shape: RoundedRectangleBorder(
//     side: BorderSide(
//       color: AppColors.k101C28,
//       width: 3.w,
//     ),
//     borderRadius: BorderRadius.circular(15.r),
//   ),
//   materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
//   onChanged: (bool? value) {
//     cartController.isDoNotAskAgain(value);
//     LocalStore.smoothSelectedDiamondRemove(value);
//   },
// );

///   showFilterDialog
void showFilterOrderDialog() {
  final MyOrderController orderController = Get.find<MyOrderController>();
  Get.dialog(
    SimpleDialog(
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24.r)),
      contentPadding: REdgeInsets.symmetric(horizontal: 50),
      titlePadding: EdgeInsets.zero,
      children: <Widget>[
        50.verticalSpace,
        Text(
          LocaleKeys.my_orders_filter.tr,
          style: TextStyle(
            fontSize: 70.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        50.verticalSpace,
        _buildFilterStatusListWidget(orderController),
        20.verticalSpace,
        _buildFilterStatusActions(orderController),
        50.verticalSpace,
      ],
    ),
  );
}

Column _buildFilterStatusListWidget(MyOrderController orderController) =>
    Column(
      children: orderController.filterList
          .asMap()
          .entries
          .map((MapEntry<int, FilterModel> e) => Obx(
                () => ListTile(
                  contentPadding: EdgeInsets.zero,
                  visualDensity: VisualDensity.compact,
                  dense: true,
                  minVerticalPadding: 0,
                  horizontalTitleGap: 24.w,
                  onTap: () {
                    orderController.currentFilterIndex(e.key);
                  },
                  leading: IconButton(
                    onPressed: null,
                    padding: EdgeInsets.zero,
                    visualDensity: VisualDensity.compact,
                    constraints: const BoxConstraints(),
                    style: OutlinedButton.styleFrom(
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap),
                    icon: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      height: 56.h,
                      width: 56.w,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white,
                        border: Border.all(
                          width: orderController.currentFilterIndex() == e.key
                              ? 19.w
                              : 3.w,
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    e.value.title ?? '',
                    style: AppFontStyles.skolaSans(
                      fontWeight:
                          (orderController.currentFilterIndex() == e.key)
                              ? FontWeight.w700
                              : FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                ),
              ))
          .toList() as List<Widget>,
    );
