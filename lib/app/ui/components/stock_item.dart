import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/providers/wishlist_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_cached_image.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

/// stock item
class StockItem extends StatelessWidget {
  /// stock item constructor
  StockItem({
    required this.diamond,
    required this.isAddedToCart,
    this.isFromVendor,
    this.margin,
    this.height,
    this.width,
    this.border,
    this.onFavourite,
    this.boxShadow,
    this.onTap,
  });

  /// is from vendor
  final bool? isFromVendor;

  final Function()? onTap;

  final DiamondEntity diamond;

  /// margin
  final EdgeInsetsGeometry? margin;

  /// height
  final double? height;

  /// width
  final double? width;

  /// box border
  final BoxBorder? border;

  /// box shadow
  final List<BoxShadow>? boxShadow;

  RxBool isAddedToCart = false.obs;

  final Function(bool)? onFavourite;

  @override
  Widget build(BuildContext context) => InkWell(
        borderRadius: BorderRadius.circular(24.r),
        onTap: onTap,
        child: Container(
          height: height, // ?? 450.h,
          width: width ?? 900.w,
          margin: margin,
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            border: border,
            borderRadius: BorderRadius.circular(24.r),
            boxShadow: boxShadow ??
                <BoxShadow>[
                  BoxShadow(
                    color: AppColors.k000000.withOpacity(0.1),
                    offset: Offset(0, 30.r),
                    blurRadius: 104.r,
                  ),
                ],
          ),
          child: Row(
            children: <Widget>[
              Container(
                //height: height ?? 450.h,
                width: 430.w,
                padding: REdgeInsets.all(10),
                child: Stack(
                  children: <Widget>[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(24.r),
                      child: AppCachedImage(
                        width: 430.w,
                        height: height ?? 430.h,
                        imageUrl: diamond.thumbnail ?? '',
                        fit: BoxFit.cover,
                        errorWidget:
                            (BuildContext context, String url, dynamic error) =>
                                Image.asset(
                          AppImages.diamondPlaceholderPngPath,
                          fit: BoxFit.cover,
                          width: 430.w,
                          height: height ?? 430.h,
                        ),
                        placeholder: (BuildContext context, String url) =>
                            Shimmer.fromColors(
                          baseColor: Colors.transparent,
                          highlightColor: Colors.grey.withOpacity(0.2),
                          child: Image.asset(
                            AppImages.diamondPlaceholderPngPath,
                            fit: BoxFit.cover,
                            width: 430.w,
                            height: height ?? 430.h,
                          ),
                        ),
                      ),
                    ),
                    if (isAddedToCart())
                      Positioned(
                        left: 20.w,
                        bottom: 20.h,
                        child: CircleAvatar(
                          radius: 50.r,
                          backgroundColor: Colors.white,
                          child: SizedBox(
                            height: 53.h,
                            width: 52.w,
                            child: FittedBox(
                              fit: BoxFit.cover,
                              child: AppImages.cartBlue,
                            ),
                          ),
                        ),
                      ),
                    // Positioned(
                    //   top: 4,
                    //   left: 4,
                    //   child: StatusChip(
                    //     text: '${diamond.type ?? ''}',
                    //     padding: REdgeInsets.symmetric(
                    //       horizontal: 30,
                    //       vertical: 8,
                    //     ),
                    //     color: AppColors.kEAEFF4,
                    //     textStyle: AppFontStyles.skolaSans(
                    //       fontSize: 30.sp,
                    //       fontWeight: FontWeight.w400,
                    //       color: AppColors.k101C28,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              35.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    40.verticalSpace,
                    Row(
                      children: <Widget>[
                        // CircleAvatar(
                        //   radius: 26.5.r,
                        //   backgroundColor: diamond.statusColor,
                        //   child: Text(
                        //     diamond.status.firstChar,
                        //     style: AppFontStyles.skolaSans(
                        //       fontWeight: FontWeight.w700,
                        //       fontSize: 30.sp,
                        //       color: AppColors.kffffff,
                        //     ),
                        //   ),
                        // ),
                        StatusChip(
                          text: '${diamond.type ?? ''}',
                          textStyle: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w700,
                            fontSize: 30.sp,
                            color: AppColors.k101C28,
                          ),
                          padding: REdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 10,
                          ),
                          color: AppColors.kEAEFF4,
                        ),
                        20.horizontalSpace,
                        diamond.lab?.isEmpty ?? false
                            ? const SizedBox.shrink()
                            : StatusChip(
                                text: diamond.lab?.toUpperCase() ?? '',
                                textStyle: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 30.sp,
                                  color: AppColors.k101C28,
                                ),
                                padding: REdgeInsets.symmetric(
                                  horizontal: 30,
                                  vertical: 10,
                                ),
                                color: AppColors.kEAEFF4,
                              ),
                        // SizedBox(
                        //   height: 53.h,
                        //   width: 122.w,
                        //   child: FittedBox(
                        //     fit: BoxFit.cover,
                        //     child: AppImages.gia,
                        //   ),
                        // ),
                        const Spacer(),
                        if (!(diamond.isCounterOffer ?? false))
                          StatefulBuilder(
                            builder: (BuildContext context, setState) =>
                                InkWell(
                              onTap: () {
                                setState(() {
                                  diamond.isWishlist =
                                      !(diamond.isWishlist ?? false);
                                });
                                if (onFavourite == null) {
                                  WishlistProvider.addRemoveFromWishlist(
                                      stockId: diamond.id ?? '');
                                } else {
                                  if (diamond.isWishlist != null) {
                                    onFavourite
                                        ?.call(diamond.isWishlist ?? false);
                                  }
                                }
                              },
                              child: diamond.isWishlist ?? false
                                  ? CircleAvatar(
                                      radius: 34.r,
                                      backgroundColor: AppColors.k101C28,
                                      child: AppImages.fillHeartSvg
                                          .paddingAll(17.r),
                                    )
                                  : AppImages.blackHeart,
                            ),
                          ),
                      ],
                    ),
                    20.verticalSpace,
                    Text(
                      _buildListToString().toUpperCase(),
                      // 'Round, 1.00ct, K, VVS2 EX EX EX STG',
                      maxLines: 2,
                      overflow: TextOverflow.clip,
                      style: AppFontStyles.skolaSans(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k101C28,
                      ),
                    ),
                    20.verticalSpace,
                    Text(
                      isFromVendor ?? false
                          ? '${diamond.price?.finalPriceOri.toString().toPrice()}'
                          : '${diamond.price?.finalPrice.toString().toPrice()}',
                      maxLines: 1,
                      overflow: TextOverflow.clip,
                      style: AppFontStyles.skolaSans(
                        fontSize: 50.sp,
                        fontWeight: FontWeight.w700,
                        color: AppColors.k101C28,
                      ),
                    ),
                    // const Spacer(),
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            isFromVendor ?? false
                                ? '${diamond.price?.pricePerCaratOri.round().toString().toPrice()} /ct'
                                    .toUpperCase()
                                : '${diamond.price?.pricePerCarat.round().toString().toPrice()} /ct'
                                    .toUpperCase(),
                            style: AppFontStyles.skolaSans(
                              fontSize: 35.sp,
                              fontWeight: FontWeight.w700,
                              color: AppColors.k70777E,
                            ),
                          ),
                        ),
                        Text(
                          isFromVendor ?? false
                              ? '${diamond.price?.discountOri}%'
                              : '${diamond.price?.discount}%',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w700,
                            color: AppColors.k1A47E8,
                          ),
                        ),
                      ],
                    ),
                    40.verticalSpace,
                  ],
                ),
              ),
              40.horizontalSpace,
            ],
          ),
        ),
      );

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          diamond.shape.toString().capitalizeFirst,
          '${diamond.weight}Ct',
          ((diamond.color?.isNotEmpty ?? false) && !diamond.isFancyColor)
              ? '${diamond.color?.wordCapital}'
              : (diamond.isFancyColor &&
                      (diamond.fancyColorMainBody?.isNotEmpty ?? false))
                  ? ' ${diamond.fancyColorMainBody?.wordCapital}'
                  : '',
          '${diamond.clarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}\n${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(diamond.cut),
          DiamondUtils.getFinishAbbreviation(diamond.polish),
          DiamondUtils.getFinishAbbreviation(diamond.symmetry),
          DiamondUtils.getFluoAbbreviation(diamond.fluorescenceIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';
}
