import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// CustomDashedDivider
class CustomDashedDivider extends StatelessWidget {
  /// CustomDashedDivider
  const CustomDashedDivider(
      {Key? key, this.height = 1, this.color = Colors.black})
      : super(key: key);
  final double height;
  final Color color;

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final double boxWidth = constraints.constrainWidth();
          final double dashWidth = 10.w;
          final double dashHeight = height;
          final int dashCount = (boxWidth / (2 * dashWidth)).floor();
          return Flex(
            children: List.generate(
              dashCount,
              (_) => SizedBox(
                width: dashWidth,
                height: dashHeight,
                child: DecoratedBox(
                  decoration: BoxDecoration(color: color),
                ),
              ),
            ),
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            direction: Axis.horizontal,
          );
        },
      );
}
