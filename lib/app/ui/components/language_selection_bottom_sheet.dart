import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/local/locale_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

// Function to show the CupertinoActionSheet
void showLanguageSelection(BuildContext context) {
  showCupertinoModalPopup(
    context: context,
    builder: (BuildContext context) => LanguageSelectionSheet(
      onLanguageSelected: (Locale local) {
        // Handle the selected language code
        // You can update the app language here
        LocaleProvider.changeLocale(local);
        Get.forceAppUpdate();
      },
    ),
  );
}

class LanguageSelectionSheet extends StatelessWidget {
  final void Function(Locale) onLanguageSelected;

  const LanguageSelectionSheet({Key? key, required this.onLanguageSelected})
      : super(key: key);

  @override
  Widget build(BuildContext context) => CupertinoActionSheet(
        title: Text(
          'Select Language',
          style: AppFontStyles.skolaSans(
            fontSize: 48.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28,
          ),
        ),
        message: Text(
          'Choose your preferred language.',
          style: AppFontStyles.skolaSans(
            fontSize: 35.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k101C28,
          ),
        ),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              onLanguageSelected(const Locale('en_US')); // 'en' for English
              Get.back();
            },
            child: Text(
              'English',
              style: AppFontStyles.skolaSans(
                fontSize: 48.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              onLanguageSelected(const Locale('es_ES')); // 'es' for Spanish
              Get.back();
            },
            child: Text(
              'Español',
              style: AppFontStyles.skolaSans(
                fontSize: 48.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          isDefaultAction: true,
          onPressed: () {
            Get.back();
          },
          child: Text(
            'Cancel',
            style: AppFontStyles.skolaSans(
              fontSize: 48.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
        ),
      );
}
