import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class AppCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final Widget Function(BuildContext, String, Object)? errorWidget;
  final Widget Function(BuildContext, String)? placeholder;

  bool get isBase64 => imageUrl.startsWith('data:image');

  const AppCachedImage(
      {required this.imageUrl,
      this.height,
      this.width,
      this.fit,
      this.errorWidget,
      this.placeholder});

  @override
  Widget build(BuildContext context) {
    logI('Image URL: $imageUrl');
    logI('Is Base64: $isBase64');
    return isBase64
        ? Image.memory(base64Decode(imageUrl.split(',').lastOrNull ?? ''),
            fit: BoxFit.cover)
        : CachedNetworkImage(
            imageUrl: imageUrl,
            height: height,
            width: width,
            fit: fit ?? BoxFit.cover,
            placeholder: placeholder ??
                (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        color: Colors.white,
                      ),
                    ),
            errorWidget: errorWidget ??
                (context, url, error) => Icon(Icons.error_outline),
          );
  }
}
