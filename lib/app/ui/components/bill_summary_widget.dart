import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/buy_request_details/controllers/buy_request_details_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/buy_request_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// bill summary widget
class BillSummaryWidget extends StatelessWidget {
  /// Constructor for BillSummaryWidget
  BillSummaryWidget({
    super.key,
    this.isFromOrder = false,
    this.buyRequest = const BuyRequest(),
    this.userOrder = const UserOrder(),
    this.onAccept,
    this.onDecline,
  });

  /// onAccept
  Function()? onAccept;

  /// onDecline
  Function()? onDecline;

  /// isFromOrder
  bool isFromOrder;

  /// isTileExpanded
  RxBool isTileExpanded = false.obs;

  /// isReasonTileExpanded
  RxBool isReasonTileExpanded = false.obs;

  /// buyRequest
  BuyRequest buyRequest = const BuyRequest();

  /// user order
  UserOrder userOrder = const UserOrder();

  @override
  Widget build(BuildContext context) => Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(Get.context!).padding.bottom),
        child: Container(
          //margin: REdgeInsets.symmetric(horizontal: 60),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.all(Radius.circular(24.r)),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 10.h),
                blurRadius: 104.r,
              ),
            ],
          ),
          child: Column(
            children: <Widget>[
              Obx(
                () => Container(
                  padding: REdgeInsets.symmetric(horizontal: 40),
                  decoration: BoxDecoration(
                    color: AppColors.kEAEFF4,
                    border: Border.all(
                      color: AppColors.kffffff,
                      width: 10.w,
                    ),
                    borderRadius: BorderRadius.all(Radius.circular(24.r)),
                  ),
                  child: AnimatedSize(
                    alignment: Alignment.topCenter,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeIn,
                    child: Column(
                      children: <Widget>[
                        24.verticalSpace,
                        InkWell(
                          onTap: () => isTileExpanded.toggle(),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                LocaleKeys.bill_summary_title.tr,
                                style: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 35.sp,
                                  color: AppColors.k101C28,
                                ),
                              ),
                              24.horizontalSpace,
                              SizedBox(
                                height: 30.h,
                                width: 30.w,
                                child: isTileExpanded()
                                    ? AppImages.arrowDown
                                    : AppImages.arrowUp,
                              ),
                            ],
                          ),
                        ),
                        24.verticalSpace,
                        isTileExpanded()
                            ? _buildSummaryItem()
                            : SizedBox(
                                width: Get.width,
                              ),
                      ],
                    ),
                  ),
                ),
              ),

              _buildAcceptDeclineCard(),

              isFromOrder ? _buildTotalPaid() : const SizedBox.shrink(),
              !isFromOrder
                  ? buyRequest.status != BuyRequestStatus.UPDATED
                      ? _buildTotalPaid()
                      : const SizedBox.shrink()
                  : const SizedBox.shrink(),
              // userOrder.paymentStatus == PaymentStatus.PAID
              //     ? 40.verticalSpace
              //     : const SizedBox.shrink(),
              // userOrder.paymentStatus != PaymentStatus.PAID
              //     ? const SizedBox.shrink()
              //     : _totalPaidMessage(),
              // userOrder.paymentStatus != PaymentStatus.PAID
              //     ? const SizedBox.shrink()
              //     : 40.verticalSpace,
              !isFromOrder
                  ? buyRequest.status == BuyRequestStatus.CANCELED
                      ? _buildCancellationReason()
                      : const SizedBox.shrink()
                  : const SizedBox.shrink(),
            ],
          ),
        ),
      );

  Widget _buildAcceptDeclineCard() {
    if (isFromOrder) {
      return const SizedBox.shrink();
    }
    if (buyRequest.status == BuyRequestStatus.UPDATED) {
      return Container(
        decoration: BoxDecoration(
          color: AppColors.kBEFFFC,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24.r),
            bottomRight: Radius.circular(24.r),
          ),
        ),
        child: Column(
          children: <Widget>[
            48.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  'Total Payable',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w500,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                Text(
                  '${(buyRequest.grandTotal ?? 0) + (buyRequest.taxPrice ?? 0)}'
                      .toPrice(),
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 45.sp,
                    color: AppColors.k1A47E8,
                  ),
                ),
              ],
            ).paddingSymmetric(horizontal: 48.w),
            Container(
              padding: REdgeInsets.all(40),
              margin: REdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(16.r)),
                color: AppColors.kffffff,
                border: Border.all(
                  color: AppColors.k000000.withOpacity(0.20),
                  width: 1.w,
                ),
              ),
              child: Column(
                children: <Widget>[
                  Text(
                    "Some of the diamonds could not be made available. Please review and 'Accept' to move forward, 'Decline' to discard.",
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                    textAlign: TextAlign.center,
                  ).paddingSymmetric(horizontal: 80.w),
                  32.verticalSpace,
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Obx(
                          () => AppButton.text(
                            buttonText: 'ACCEPT',
                            onPressed: onAccept,
                            loaderHeight: 50.h,
                            loaderWidth: 50.w,
                            strokeWidth: 8.w,
                            isLoading: Get.find<BuyRequestDetailsController>()
                                .isAccepting(),
                            buttonSize: Size(Get.width.w, 100.h),
                            buttonTextStyle: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color: AppColors.kffffff,
                            ),
                            borderRadius: 500.r,
                            backgroundColor: AppColors.k128807,
                            borderColor: AppColors.k128807,
                          ),
                        ),
                      ),
                      24.horizontalSpace,
                      Expanded(
                        child: Obx(
                          () => AppButton.text(
                            buttonText: 'DECLINE',
                            onPressed: onDecline,
                            loaderHeight: 50.h,
                            loaderWidth: 50.w,
                            strokeWidth: 8.w,
                            isLoading: Get.find<BuyRequestDetailsController>()
                                .isDeclining(),
                            buttonSize: Size(Get.width.w, 100.h),
                            buttonTextStyle: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 35.sp,
                              color: AppColors.kffffff,
                            ),
                            borderRadius: 500.r,
                            backgroundColor: AppColors.k70777E,
                            borderColor: AppColors.k70777E,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildCancellationReason() => (buyRequest.rejectReason ?? '').isEmpty
      ? const SizedBox.shrink()
      : ClipRRect(
          borderRadius:
              BorderRadius.circular(isReasonTileExpanded() ? 30.r : 60.r),
          child: Theme(
            data: ThemeData(dividerColor: Colors.transparent),
            child: ExpansionTile(
              backgroundColor: AppColors.kEAEFF4,
              initiallyExpanded: true,
              dense: true,
              expansionAnimationStyle: AnimationStyle(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeIn,
              ),
              tilePadding: REdgeInsets.symmetric(horizontal: 50),
              visualDensity: VisualDensity.compact,
              iconColor: AppColors.k101C28,
              collapsedIconColor: AppColors.k101C28,
              collapsedBackgroundColor: AppColors.kEAEFF4,
              onExpansionChanged: (bool value) => isReasonTileExpanded(value),
              title: Text(
                LocaleKeys.my_order_detail_see_cancellation_reason.tr,
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  fontSize: 35.sp,
                  color: isReasonTileExpanded()
                      ? AppColors.k101C28
                      : AppColors.k1A47E8,
                ),
              ),
              children: <Widget>[
                Container(
                  width: Get.width,
                  margin: REdgeInsets.only(
                    left: 50,
                    right: 50,
                    bottom: 50,
                  ),
                  padding: REdgeInsets.symmetric(
                    horizontal: 40,
                    vertical: 30,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.kffffff,
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: Text(
                    buyRequest.rejectReason ?? '',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                )
              ],
            ),
          ),
        ).paddingAll(40.r);

  ClipRRect _totalPaidMessage() => ClipRRect(
        borderRadius: BorderRadius.circular(isTileExpanded() ? 24.r : 60.r),
        child: Theme(
          data: ThemeData(dividerColor: Colors.transparent),
          child: ExpansionTile(
            backgroundColor: AppColors.kEAEFF4,
            initiallyExpanded: true,
            dense: true,
            expansionAnimationStyle: AnimationStyle(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeIn,
            ),
            tilePadding: REdgeInsets.symmetric(horizontal: 50),
            visualDensity: VisualDensity.compact,
            collapsedBackgroundColor: AppColors.kEAEFF4,
            iconColor: AppColors.k101C28,
            collapsedIconColor: AppColors.k101C28,
            onExpansionChanged: (bool value) {
              isTileExpanded.value = value;
            },
            title: Text(
              LocaleKeys.bill_summary_total_paid.tr,
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 35.sp,
                color: isTileExpanded() ? AppColors.k101C28 : AppColors.k1A47E8,
              ),
            ),
            children: <Widget>[
              Container(
                width: Get.width,
                margin: REdgeInsets.only(
                  left: 50,
                  right: 50,
                  bottom: 50,
                ),
                padding: REdgeInsets.symmetric(
                  horizontal: 40,
                  vertical: 30,
                ),
                decoration: BoxDecoration(
                  color: AppColors.kffffff,
                  borderRadius: BorderRadius.circular(24.r),
                ),
                child: Text(
                  "Customer changed their mind about the diamond's size and decided to opt for a larger one, leading to the return process.",
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
              )
            ],
          ).paddingSymmetric(horizontal: 40.w),
        ),
      );

  Container _buildTotalPaid() => Container(
        decoration: BoxDecoration(
          color: AppColors.kffffff.withOpacity(0.15),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24.r),
            bottomRight: Radius.circular(24.r),
          ),
          border: Border(
            left: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
            right: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
            bottom: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(18.r),
            bottomRight: Radius.circular(18.r),
          ),
          child: AppButton.custom(
            onPressed: () => isTileExpanded.toggle(),
            backgroundColor: AppColors.k101C28,
            padding: EdgeInsets.zero,
            borderWidth: 0,
            buttonSize: Size(Get.width, 150.h),
            child: RichText(
                text: TextSpan(
              text: 'Total ',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.kBEFFFC,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: _totalPaidAmount(),
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.kBEFFFC,
                  ),
                ),
              ],
            )),
            borderRadius: 0,
            buttonTextStyle: AppFontStyles.skolaSans(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
            borderColor: AppColors.k101C28,
          ),
        ),
      );

  Container _buildPayNow() => Container(
        decoration: BoxDecoration(
          color: AppColors.kFF9800.withOpacity(0.15),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24.r),
            bottomRight: Radius.circular(24.r),
          ),
          border: Border(
            left: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
            right: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
            bottom: BorderSide(
              color: AppColors.kffffff,
              width: 10.w,
            ),
          ),
        ),
        child: ListTile(
          contentPadding: REdgeInsets.symmetric(horizontal: 48),
          visualDensity: VisualDensity.compact,
          dense: true,
          title: Text(
            LocaleKeys.bill_summary_total_payable.tr,
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w500,
              fontSize: 35.sp,
              color: AppColors.k70777E,
            ),
          ),
          subtitle: Text(
            _grandTotal(),
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 45.sp,
              color: AppColors.k101C28,
            ),
          ),
          trailing: AppButton.text(
            buttonText: LocaleKeys.bill_summary_pay_now.tr,
            onPressed: () {},
            borderRadius: 50.r,
            buttonSize: Size(276.w, 101.h),
            backgroundColor: AppColors.kFF9800,
            borderColor: AppColors.kFF9800,
            buttonTextStyle: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 35.sp,
              color: AppColors.kffffff,
            ),
          ),
        ),
      );

  Column _buildSummaryItem() => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if ((userOrder.unifiedOrderType ?? UnifiedOrderType.DIAMOND) ==
              UnifiedOrderType.DIAMOND)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Expanded(
                  child: StatusChip(
                    text: _totalPCS(),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(500.r),
                      bottomLeft: Radius.circular(500.r),
                    ),
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kffffff,
                  ),
                ),
                8.horizontalSpace,
                Expanded(
                  child: StatusChip(
                    text: _totalCTS(),
                    borderRadius: BorderRadius.zero,
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kffffff,
                  ),
                ),
                8.horizontalSpace,
                Expanded(
                  child: StatusChip(
                    text: _pricePerCarat(),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(500.r),
                      bottomRight: Radius.circular(500.r),
                    ),
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kffffff,
                  ),
                ),
              ],
            ),
          if ((userOrder.unifiedOrderType ?? UnifiedOrderType.DIAMOND) ==
              UnifiedOrderType.DIAMOND)
            24.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                text: TextSpan(
                  text: _totalCT(),
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: 'ct',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 35.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                    TextSpan(
                      text: ' X ',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 35.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                    TextSpan(
                      text: _pricePerCaratWOCt(),
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 35.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                    TextSpan(
                      text: '/ct',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 35.sp,
                        color: AppColors.k70777E,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                _totalAmount(),
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 35.sp,
                  color: AppColors.k101C28,
                ),
              ),
            ],
          ),
          24.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.bill_summary_shipping_fees.tr,
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 35.sp,
                  color: AppColors.k101C28,
                ),
              ),
              Text(
                _shippingPrice(),
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 35.sp,
                  color: AppColors.k101C28,
                ),
              ),
            ],
          ),
          if (_isTaxApplied()) 24.verticalSpace,
          if (_isTaxApplied())
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocaleKeys.bill_summary_taxes.tr,
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                Text(
                  _taxPrice(),
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w400,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
              ],
            ),
          24.verticalSpace,
        ],
      );

  String _totalCTS() {
    if (isFromOrder) {
      return '${userOrder.totalCts?.toStringAsFixed(2) ?? '0'} cts';
    } else {
      return '${buyRequest.totalCts?.toStringAsFixed(2) ?? '0'} cts';
    }
  }

  String _totalCT() {
    if (isFromOrder) {
      return '${userOrder.totalCts?.toStringAsFixed(2) ?? '0'}';
    } else {
      return '${buyRequest.totalCts?.toStringAsFixed(2) ?? '0'}';
    }
  }

  String _pricePerCaratWOCt() {
    if (isFromOrder) {
      return '${userOrder.pricePerCarat?.toString().toPrice() ?? 0}';
    } else {
      return '${buyRequest.pricePerCarat?.toString().toPrice() ?? 0}';
    }
  }

  String _pricePerCarat() {
    if (isFromOrder) {
      return '${userOrder.pricePerCarat?.toString().toPrice() ?? 0}/ct';
    } else {
      return '${buyRequest.pricePerCarat?.toString().toPrice() ?? 0}/ct';
    }
  }

  String _totalAmount() {
    if (isFromOrder) {
      return '${userOrder.totalAmount?.toString().toPrice() ?? 0}';
    } else {
      return '${buyRequest.totalAmount?.toString().toPrice() ?? 0}';
    }
  }

  String _shippingPrice() {
    if (isFromOrder) {
      return '${userOrder.shippingPrice?.toString().toPrice() ?? 0}';
    } else {
      return '${buyRequest.shippingPrice?.toString().toPrice() ?? 0}';
    }
  }

  String _taxPrice() {
    if (isFromOrder) {
      return '${userOrder.taxPrice?.toString().toPrice() ?? 0}';
    } else {
      return '${buyRequest.taxPrice?.toString().toPrice() ?? 0}';
    }
  }

  bool _isTaxApplied() {
    if (isFromOrder) {
      return (userOrder.taxPrice ?? 0) > 0;
    } else {
      return (buyRequest.taxPrice?.toPrecision(2) ?? 0) > 0;
    }
  }

  String _totalPCS() {
    if (isFromOrder) {
      return '${userOrder.buyRequestDetails?.stockIds?.where((StockId stockIds) => BuyRequestUtils.isStockAvailable(stockIds)).length ?? 0} pcs';
    } else {
      return '${buyRequest.stockIds?.where((StockId stockIds) => BuyRequestUtils.isStockAvailable(stockIds)).length ?? 0} pcs';
    }
  }

  String _totalPaidAmount() {
    if (isFromOrder) {
      return '${userOrder.grandTotal?.toString().toPrice()}';
    } else {
      return '${buyRequest.grandTotal?.toString().toPrice()}';
    }
  }

  String _grandTotal() {
    if (isFromOrder) {
      return '${userOrder.grandTotal?.toString().toPrice()}';
    } else {
      return '${buyRequest.grandTotal?.toString().toPrice()}';
    }
  }
}
