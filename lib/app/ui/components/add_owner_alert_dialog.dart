import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/modules/legal_status_organization/controllers/legal_status_organization_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_country_picker.dart';
import 'package:diamond_company_app/app/ui/components/drop_down_form_field.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../../data/config/app_colors.dart';

/// ShowAddOwnersAlertDialog
void showAddOwnersAlertDialog({
  required LegalStatusOrganizationController controller,
  required BuildContext context,
}) {
  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.7),
    SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: Get.height,
          minWidth: Get.width,
        ),
        child: IntrinsicHeight(
          child: Container(
            margin: EdgeInsets.symmetric(
              horizontal: 60.w,
            ),
            child: FormBuilder(
              key: controller.alertFBKey,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              child: _buildAddOwnersAlertDialog(controller, context),
            ),
          ),
        ),
      ),
    ),
  );
}

/// Build Add Owners Alert Dialog
Widget _buildAddOwnersAlertDialog(
        LegalStatusOrganizationController controller, BuildContext context) =>
    Dialog(
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      insetPadding: REdgeInsets.only(
        left: 50.w,
        right: 50.w,
      ),
      backgroundColor: Colors.white,
      child: Container(
        margin: EdgeInsets.only(
          left: 50.w,
          right: 50.w,
          top: 100.h,
          bottom: 50.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              LocaleKeys.legal_status_organization_add_owners.tr.wordCapital,
              style: AppFontStyles.skolaSans(
                fontSize: 70.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
            100.verticalSpace,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Expanded(
                  child: _buildTextFormField(
                    name: RegistrationFormFields.orderAuthorizedPersonFirstName,
                    keyboardType: TextInputType.name,
                    //autofillHints: <String>[AutofillHints.name],
                    labelText:
                        LocaleKeys.legal_status_organization_first_name.tr,
                    isRequired: true,
                    validation: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_first_name_is_empty.tr;
                      }
                      return null;
                    },
                  ),
                ),
                40.horizontalSpace,
                Expanded(
                  child: _buildTextFormField(
                    name: RegistrationFormFields.orderAuthorizedPersonLastName,
                    keyboardType: TextInputType.name,
                    //autofillHints: <String>[AutofillHints.name],
                    labelText:
                        LocaleKeys.legal_status_organization_last_name.tr,
                    isRequired: true,
                    validation: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_last_name_is_empty.tr;
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            76.verticalSpace,
            DropDownFormField(
              onChanged: (dynamic value) {
                controller.designationSelect(value);
              },
              items: controller.designationsList(),
              menuItemStyle: AppFontStyles.skolaSans(
                color: AppColors.k70777E,
                fontSize: 45.sp,
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                contentPadding: REdgeInsets.only(
                  left: 48,
                  bottom: 30,
                  right: 48,
                ),
                constraints: BoxConstraints(
                  minHeight: 150.h,
                ),
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      LocaleKeys
                          .legal_status_organization_designation.tr.wordCapital,
                    ),
                    Text(
                      ' *',
                      style: AppFontStyles.skolaSans(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w900,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
                labelStyle: AppFontStyles.skolaSans(
                  color: AppColors.k70777E,
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w400,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
                errorStyle: const TextStyle(color: Colors.red),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24.r),
                  borderSide: BorderSide(
                    width: 1.w,
                    color: AppColors.k70777E,
                  ),
                ),
              ),
            ),
            76.verticalSpace,
            Obx(
              () => _buildTextFormField(
                name: RegistrationFormFields.orderAuthorizedPersonPhone,
                autofillHints: <String>[AutofillHints.telephoneNumber],
                labelText: LocaleKeys.legal_status_organization_phone.tr,
                keyboardType: TextInputType.phone,
                isRequired: true,
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.digitsOnly,
                ],
                prefix: InkWell(
                  onTap: () {
                    commonShowCountryPicker(
                      context,
                      onSelect: (Country country) {
                        controller.selectPhoneCountryCode(country);
                      },
                    );
                    // showCountryPicker(
                    //   context: context,
                    //   onSelect: (Country country) {
                    //     controller.selectPhoneCountryCode(country);
                    //   },
                    // );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: AppColors.k70777E,
                          width: 1.w,
                        ),
                      ),
                    ),
                    margin: REdgeInsets.only(right: 30, left: 10),
                    padding: REdgeInsets.symmetric(horizontal: 30),
                    child: Text(
                      controller.phoneCountryCode().startsWith('+')
                          ? controller.phoneCountryCode()
                          : '+${controller.phoneCountryCode()}',
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
                validation: (String? value) {
                  final String? trimmedValue = value?.trim();
                  if (trimmedValue?.isEmpty ?? true) {
                    return LocaleKeys.validation_phone_is_empty.tr;
                  } else if (trimmedValue!.length < 7) {
                    return LocaleKeys.validation_phone_length_less.tr;
                  } else if (trimmedValue.length > 14) {
                    return LocaleKeys.validation_phone_length_more.tr;
                  }
                  return null;
                },
              ),
            ),
            50.verticalSpace,
            Row(
              children: <Widget>[
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () => Get.back(),
                    buttonText: LocaleKeys.legal_status_organization_cancel.tr,
                    backgroundColor: AppColors.kEAEFF4,
                    borderColor: AppColors.kEAEFF4,
                    buttonTextColor: AppColors.k101C28,
                  ),
                ),
                30.horizontalSpace,
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () => controller.addOwner(),
                    buttonText: LocaleKeys.legal_status_organization_add.tr,
                    buttonTextColor: AppColors.kBEFFFC,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

/// Common TextFormField
Widget _buildTextFormField({
  required String name,
  required String? Function(String?)? validation,
  TextEditingController? controller,
  String? labelText,
  bool isRequired = false,
  TextInputType? keyboardType,
  Widget? suffix,
  Widget? prefix,
  int? maxLines,
  List<TextInputFormatter>? inputFormatters,
  Iterable<String>? autofillHints,
}) =>
    AppTextFormField(
      controller: controller,
      name: name,
      keyboardType: keyboardType,
      autofillHints: autofillHints,
      validator: validation,
      prefixIcon: prefix,
      labelText: labelText ?? '',
      isRequired: isRequired,
      inputFormatters: inputFormatters,
      constraints: BoxConstraints(
        minHeight: 150.h,
      ),
      maxLines: maxLines ?? 1,
      labelTextStyle: AppFontStyles.skolaSans(
        color: AppColors.k70777E,
        fontSize: 45.sp,
        fontWeight: FontWeight.w400,
      ),
      style: AppFontStyles.skolaSans(
        fontSize: 45.sp,
        color: AppColors.k101C28,
        fontWeight: FontWeight.w400,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          width: 1.w,
          color: AppColors.k70777E,
        ),
      ),
      showBorder: true,
      fillColor: AppColors.k70777E,
      suffixIcon: suffix,
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          width: 1.w,
          color: AppColors.k70777E,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          width: 1.w,
          color: AppColors.k70777E,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(24.r),
        borderSide: BorderSide(
          width: 1.w,
          color: AppColors.k70777E,
        ),
      ),
      border: const OutlineInputBorder(
        borderSide: BorderSide(
          color: AppColors.k70777E,
        ),
      ),
      contentPadding: REdgeInsets.only(left: 48, bottom: 30),
    );

/// Common App Button
Widget _buildCommonAppButton({
  required String buttonText,
  Function? onPressed,
  Color? backgroundColor,
  Color? borderColor,
  Color? buttonTextColor,
}) =>
    AppButton.text(
      buttonText: buttonText,
      onPressed: () {
        onPressed!();
      },
      buttonSize: Size(Get.width, 150.h),
      backgroundColor: backgroundColor ?? AppColors.k101C28,
      borderColor: borderColor ?? AppColors.k101C28,
      borderRadius: 24.r,
      buttonTextStyle: TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w700,
        color: buttonTextColor ?? AppColors.kBEFFFC,
      ),
    );
