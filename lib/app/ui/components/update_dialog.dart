import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/web_dialog_widget.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///  show update dialog
void showUpdateDialog() {
  Get.dialog(WebDialog(
    child: SimpleDialog(
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(24.r),
        ),
      ),
      alignment: Alignment.center,
      contentPadding: REdgeInsets.all(50),
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      children: <Widget>[
        Column(
          children: <Widget>[
            50.verticalSpace,
            Text(
              LocaleKeys.update_update_dialog_title.tr,
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 70.sp,
                color: AppColors.k101C28,
              ),
            ),
            50.verticalSpace,
            Text(
              'Recommends that you update to the latest version 2.5.1. is cover the latest features and improvements.',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 45.sp,
                color: AppColors.k70777E,
              ),
              textAlign: TextAlign.center,
            ),
            50.verticalSpace,
            Text(
              LocaleKeys.update_read_more_about.tr,
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 45.sp,
                color: AppColors.k5173ED,
                decorationColor: AppColors.k5173ED,
                decoration: TextDecoration.underline,
              ),
            ),
            50.verticalSpace,
            _buildUpdateActions(),
          ],
        ),
      ],
    ),
  ));
}

Row _buildUpdateActions() => Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.update_remind_me_later.tr,
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.update_title.tr,
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );
