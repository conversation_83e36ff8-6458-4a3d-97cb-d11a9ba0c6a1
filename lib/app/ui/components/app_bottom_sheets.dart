import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/crm_model/crm_model.dart';
import 'package:diamond_company_app/app/modules/dashboard/controllers/dashboard_controller.dart';
import 'package:diamond_company_app/app/modules/login/controllers/login_controller.dart';
import 'package:diamond_company_app/app/ui/components/crm_widgets.dart';
import 'package:diamond_company_app/app/ui/components/shadow_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Show the customer relation bottom sheet
void showCustomerRelationBottomSheet() {
  final dynamic controller = Get.isRegistered<LoginController>()
      ? Get.find<LoginController>()
      : Get.find<DashboardController>();
  Get.bottomSheet(
    backgroundColor: AppColors.k101C28,
    elevation: 0,
    shape: const LinearBorder(),
    _buildCustomerRelationBottomSheetWidget(controller.crmDetails())
        .paddingOnly(bottom: MediaQuery.of(Get.context!).padding.bottom),
  );
}

SizedBox _buildCustomerRelationBottomSheetWidget(CRMModel crmModel) => SizedBox(
      //height: 1020.h,
      child: SingleChildScrollView(
        child: Stack(
          children: <Widget>[
            Positioned(
              right: 0,
              top: 0,
              child: IconButton(
                onPressed: () => Get.back(),
                icon: Icon(
                  Icons.close,
                  color: AppColors.kffffff,
                  size: 72.sp,
                ),
              ),
            ),
            CRMCard(crmModel: crmModel),
          ],
        ),
      ),
    );

ShadowButton _buildCustomerRelationSheetButton({
  required String title,
  required Widget icon,
  required Function()? onTap,
}) =>
    ShadowButton(
      onTap: onTap,
      height: 234.h,
      width: Get.width,
      buttonColor: AppColors.kffffff,
      buttonName: 'WhatsApp',
      borderRadius: BorderRadius.all(Radius.circular(24.r)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          icon,
          Text(
            title,
            style: TextStyle(
              fontSize: 40.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
        ],
      ).paddingSymmetric(vertical: 46.h),
    );
