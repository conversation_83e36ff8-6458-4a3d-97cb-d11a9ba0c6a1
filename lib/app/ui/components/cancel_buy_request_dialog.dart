import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/buy_request_details/controllers/buy_request_details_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_dialogs.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///   showDownloadInvoiceDialog
void showCancelBuyRequestMenu() {
  Get.dialog(
    SimpleDialog(
      contentPadding: EdgeInsets.zero,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      children: <Widget>[
        _buildCancelBuyRequestActions(),
      ],
    ),
  );
}

Container _buildCancelBuyRequestActions() => Container(
      padding: REdgeInsets.all(50),
      decoration: BoxDecoration(
        color: AppColors.kffffff,
        borderRadius: BorderRadius.all(Radius.circular(24.r)),
      ),
      child: Column(
        children: <Widget>[
          AppButton.text(
            buttonText: '${LocaleKeys.bill_summary_cancel_buy_request.tr}'
                .toUpperCase(),
            onPressed: () {
              Get.back();
              showCancelBuyRequestDialog();
            },
            borderColor: AppColors.k101C28,
            borderWidth: 4.w,
            borderRadius: 500.r,
            padding: EdgeInsets.zero,
            buttonSize: Size(Get.width, 150.h),
            backgroundColor: AppColors.kffffff,
            buttonTextStyle: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 35.sp,
              color: AppColors.k101C28,
            ),
          ),
        ],
      ),
    );
