import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/diamond_status_indicator.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class DiamondTile extends StatelessWidget {
  const DiamondTile({
    required this.diamond,
    this.seen = false,
    this.hideLab = false,
    this.hidePrice = false,
    this.hideSupplierDetails = false,
    this.showAddedToCart = false,
    this.onTap,
    this.margin,
    this.leading,
    this.constraints,
    super.key,
  });

  final DiamondEntity diamond;
  final bool seen;
  final VoidCallback? onTap;
  final EdgeInsets? margin;
  final bool hideLab;
  final bool hidePrice;
  final bool hideSupplierDetails;
  final Widget? leading;
  final BoxConstraints? constraints;
  final bool showAddedToCart;

  @override
  Widget build(BuildContext context) {
    Color bgColor = Get.theme.cardColor;

    Color fgColor = AppColors.k111111;

    bool addedToCart = false;

    if (showAddedToCart) {
      addedToCart = CartItemHelper.isDiamondAdded(diamond);
    }

    return GestureDetector(
      onTap: () {
        onTap?.call();
        Get.toNamed(
          Routes.DIAMOND_DETAILS,
          arguments: DiamondArgs(
            id: diamond.id ?? '',
            diamond: diamond,
          ),
        );
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        constraints: constraints,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: diamond.statusColor.withOpacity(0.6),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: diamond.statusColor.withOpacity(0.1),
              offset: const Offset(-2, 2),
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        margin: margin ?? const EdgeInsets.only(bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: diamond.statusColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
              ),
              child: Row(
                children: [
                  /*Visibility(
                    visible: diamondData.status == 'AVAILABLE',
                    replacement: const SizedBox(width: 40, height: 40),
                    child: Checkbox(
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      onChanged: (value) {
                        */ /*controller.diamonds[index].isSelected =
                            !controller.diamonds[index].isSelected;
                        controller.diamonds.refresh();*/ /*
                        // controller.selected(controller.selected() + 1);
                      },
                      value: diamondData.isSelected,
                    ),
                  ),*/
                  if (seen && !(showAddedToCart && addedToCart)) ...[
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Center(
                        child: leading ??
                            Icon(
                              Icons.remove_red_eye_outlined,
                              /*color: ThemeProvider.isDarkModeOn
                              ? Colors.grey.shade700
                              : Colors.grey.shade400,*/
                              color: fgColor.withOpacity(0.3),
                            ),
                      ),
                    ),
                  ] else if (showAddedToCart && addedToCart) ...<Widget>[
                    SizedBox(
                      height: 40,
                      width: 40,
                      child: Center(
                        child: leading ??
                            Icon(
                              Icons.shopping_cart_outlined,
                              color: fgColor.withOpacity(0.3),
                            ),
                      ),
                    ),
                  ] else ...[
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: leading,
                    ),
                  ],
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text:
                                '${DiamondUtils.getShapeAbbreviation(diamond.shape).toUpperCase()}, ',
                          ),
                          TextSpan(
                            text:
                                '${diamond.weight?.toStringAsFixed(2) ?? 0}Ct',
                            //'10.49Ct, K, VVS2',
                            style: GoogleFonts.montserrat(
                              color: fgColor,
                            ),
                          ),
                          if ((diamond.color?.isNotEmpty ?? false) &&
                              !diamond.isFancyColor)
                            TextSpan(
                              text: ', ${diamond.color?.wordCapital}',
                              style: GoogleFonts.montserrat(
                                color: fgColor,
                              ),
                            ),
                          if (diamond.isFancyColor &&
                              (diamond.fancyColorMainBody?.isNotEmpty ?? false))
                            TextSpan(
                              text:
                                  ', ${diamond.fancyColorMainBody?.wordCapital}',
                              style: GoogleFonts.montserrat(
                                color: fgColor,
                              ),
                            ),
                          if (diamond.clarity?.isNotEmpty ?? false)
                            TextSpan(
                              text: ', ${diamond.clarity?.toUpperCase()}',
                              //'10.49Ct, K, VVS2',
                              style: GoogleFonts.montserrat(
                                color: fgColor,
                              ),
                            ),
                          TextSpan(
                            text: StringUtils.listToString(
                              [
                                '\n${diamond.stockId?.toUpperCase() ?? ''}'
                                    .toUpperCase(),
                                diamond.type,
                              ],
                              separator: ', ',
                            ),
                            style: GoogleFonts.montserrat(
                              color: fgColor.withOpacity(0.5),
                              fontSize: 13,
                            ),
                          ),
                          if (diamond.treatmentLabel != null) ...[
                            TextSpan(
                              text: ', ',
                              style: GoogleFonts.montserrat(
                                color: fgColor.withOpacity(0.5),
                                fontSize: 13,
                              ),
                            ),
                            TextSpan(
                              text: diamond.treatmentLabel,
                              style: GoogleFonts.montserrat(
                                color: AppColors.k7C0B0B,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ],
                        style: GoogleFonts.montserrat(
                          color: AppColors.k1A47E8,
                          fontSize: 17,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.kffffff,
                      borderRadius: BorderRadius.circular(50),
                      border: Border.all(
                        color: Colors.black12,
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    child: Text(
                      diamond.discountLabel(hide: hidePrice),
                      style: GoogleFonts.montserrat(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
              ),
            ),
            Divider(
              height: 0,
              color: diamond.statusColor,
            ),
            Row(
              children: [
                DiamondStatusIndicator(
                  status: diamond.status.firstChar,
                ),
                Text(
                  StringUtils.listToString(
                    [
                      if (!hideLab) ...[diamond.lab],
                      DiamondUtils.getFinishAbbreviation(diamond.cut),
                      DiamondUtils.getFinishAbbreviation(diamond.polish),
                      DiamondUtils.getFinishAbbreviation(diamond.symmetry),
                      DiamondUtils.getFluoAbbreviation(
                          diamond.fluorescenceIntensity),
                    ],
                    separator: ', ',
                    upperCase: true,
                  ),
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  diamond.pricePerCaratLabel(hide: hidePrice),
                  style: GoogleFonts.montserrat(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
            const Divider(height: 0),
            Row(
              children: [
                Container(
                  width: 40,
                  alignment: Alignment.center,
                  child: (diamond.isLabGrown == 'Y')
                      ? Text(
                          'LAB',
                          style: GoogleFonts.monda(
                            fontSize: 8,
                            color: AppColors.k128807,
                          ),
                        )
                      : Text(
                          'MIN\nED',
                          style: GoogleFonts.monda(
                            fontSize: 8,
                            color: AppColors.k1A47E8,
                          ),
                        ),
                ),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (diamond.measLabel?.isNotEmpty ?? false)
                        Text.rich(
                          TextSpan(
                            text: diamond.measLabel,
                            children: [
                              TextSpan(
                                text: diamond.isFancyShape &&
                                        diamond.lwRatio != null
                                    ? '・${diamond.lwRatio}'
                                    : '',
                                style: GoogleFonts.montserrat(
                                  fontWeight: FontWeight.bold,
                                  color: fgColor.withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                          style: GoogleFonts.montserrat(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      if (!hideSupplierDetails)
                        Text(
                          diamond.supplierName,
                          style: GoogleFonts.montserrat(),
                        ),
                    ],
                  ),
                ),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          const TextSpan(
                            text: 'TTL ',
                          ),
                          TextSpan(
                            text: ' ${diamond.priceLabel(hide: hidePrice)}',
                            style: GoogleFonts.montserrat(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                        style: GoogleFonts.montserrat(
                          color: AppColors.k111111,
                        ),
                      ),
                    ),
                    if (diamond.location.isNotEmpty)
                      Text(
                        diamond.location,
                        style: GoogleFonts.montserrat(),
                      ),
                  ],
                ),
              ],
            ).paddingOnly(right: 8, bottom: 8, top: 4),
          ],
        ),
      ),
    );
  }
}
