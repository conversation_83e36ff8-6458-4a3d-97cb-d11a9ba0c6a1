import 'package:country_picker/country_picker.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/user/login/user_entity.dart';
import 'package:diamond_company_app/app/modules/trade_references/controllers/trade_references_controller.dart';
import 'package:diamond_company_app/app/providers/auth_provider.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/common_country_picker.dart';
import 'package:diamond_company_app/app/utils/registration_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../generated/locales.g.dart';
import '../../data/config/app_colors.dart';

/// Shoe Add Trade Reference Alert Dialog
void showAddTradeReferenceAlertDialog(
    {required controller, required BuildContext context}) {
  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.7),
    SingleChildScrollView(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: Get.height,
          minWidth: Get.width,
        ),
        child: IntrinsicHeight(
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 60.w),
            child: _buildAddTradeReferenceAlertDialog(controller, context),
          ),
        ),
      ),
    ),
  );
}

/// Build Add Trade Reference Alert Dialog
Widget _buildAddTradeReferenceAlertDialog(
        TradeReferencesController controller, BuildContext context) =>
    Dialog(
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      insetPadding: REdgeInsets.only(
        left: 50.w,
        right: 50.w,
      ),
      backgroundColor: Colors.white,
      child: Container(
        margin: EdgeInsets.only(
          left: 50.w,
          right: 50.w,
          top: 100.h,
          bottom: 50.h,
        ),
        child: FormBuilder(
          key: controller.fbKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                LocaleKeys.trade_references_add_reference.tr,
                style: AppFontStyles.skolaSans(
                  fontSize: 70.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.k101C28,
                ),
              ),
              100.verticalSpace,
              Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  AppTextFormField(
                    name: RegistrationFormFields.tradeReferenceCompanyName,
                    labelText: LocaleKeys.trade_references_company_name.tr,
                    keyboardType: TextInputType.name,
                    autofillHints: const <String>[AutofillHints.name],
                    validator: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_company_name_is_empty.tr;
                      } else if (trimmedValue ==
                              controller.compareAs(controller,
                                  key: RegistrationFormFields
                                      .companyLegalName) ||
                          trimmedValue ==
                              AuthProvider.userEntity()
                                  .legalRegisteredName
                                  .toString()
                                  .trim()) {
                        return 'Company name already exists in the registration form';
                      }
                      return null;
                    },
                    isRequired: true,
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                    ),
                    labelTextStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                      fontWeight: FontWeight.w400,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    showBorder: true,
                    fillColor: AppColors.k70777E,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.k70777E,
                      ),
                    ),
                    contentPadding: REdgeInsets.only(left: 48, bottom: 30),
                  ),
                  76.verticalSpace,
                  AppTextFormField(
                    name:
                        RegistrationFormFields.tradeReferenceContactPersonName,
                    labelText:
                        LocaleKeys.trade_references_contact_person_name.tr,
                    keyboardType: TextInputType.name,
                    autofillHints: const <String>[AutofillHints.name],
                    validator: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return LocaleKeys.validation_contact_person_is_empty.tr;
                      }
                      return null;
                    },
                    isRequired: true,
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                    ),
                    labelTextStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                      fontWeight: FontWeight.w400,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    showBorder: true,
                    fillColor: AppColors.k70777E,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.k70777E,
                      ),
                    ),
                    contentPadding: REdgeInsets.only(left: 48, bottom: 30),
                  ),
                  76.verticalSpace,
                  Obx(
                    () => AppTextFormField(
                      name: RegistrationFormFields.tradeReferencePhone,
                      labelText: LocaleKeys.trade_references_phone.tr,
                      autofillHints: <String>[AutofillHints.telephoneNumber],
                      inputFormatters: <TextInputFormatter>[
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      isRequired: true,
                      prefixIcon: InkWell(
                        onTap: () {
                          logD(
                              'NUMBER:::${controller.legalStatusOrganizationController.orderAuthorizedpersonList[0].mobileNo}');
                          commonShowCountryPicker(
                            context,
                            onSelect: (Country country) {
                              controller.selectPhoneCountryCode(country);
                            },
                          );
                          // showCountryPicker(
                          //   context: context,
                          //   onSelect: (Country country) {
                          //     controller.selectPhoneCountryCode(country);
                          //   },
                          // );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border(
                              right: BorderSide(
                                color: AppColors.k70777E,
                                width: 1.w,
                              ),
                            ),
                          ),
                          margin: REdgeInsets.only(right: 30, left: 10),
                          padding: REdgeInsets.symmetric(horizontal: 30),
                          child: Text(
                            controller.phoneCountryCode().isEmpty
                                ? controller.newCompanyRegistrationController
                                        .countryPhoneCode.isEmpty
                                    ? '+1'
                                    : '+${controller.newCompanyRegistrationController.countryPhoneCode.toString()}'
                                : '+${controller.phoneCountryCode.toString()}',
                            style: AppFontStyles.skolaSans(
                              fontSize: 45.sp,
                              color: AppColors.k101C28,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (String? value) {
                        final String? trimmedValue = value?.trim();
                        if (trimmedValue?.isEmpty ?? true) {
                          return LocaleKeys.validation_phone_is_empty.tr;
                        } else if (!GetUtils.isPhoneNumber(
                            trimmedValue ?? '')) {
                          return 'Invalid phone number';
                        } else if (controller.legalStatusOrganizationController
                            .orderAuthorizedpersonList
                            .any((OrderAuthorizedPerson element) =>
                                element.mobileNo ==
                                '+${controller.phoneCountryCode.isEmpty ? controller.newCompanyRegistrationController.countryPhoneCode.isEmpty ? '1' : controller.newCompanyRegistrationController.countryPhoneCode.toString() : controller.phoneCountryCode.toString()}-$trimmedValue')) {
                          return 'Phone number should not be same as authorized person phone';
                        } else if (controller.tradeReferencesList.any(
                            (TradeReference element) =>
                                element.phone ==
                                '+${controller.phoneCountryCode.toString()}-$trimmedValue')) {
                          return 'References number can not be same';
                        }
                        return null;
                      },
                      constraints: BoxConstraints(
                        minHeight: 150.h,
                      ),
                      labelTextStyle: AppFontStyles.skolaSans(
                        color: AppColors.k70777E,
                        fontSize: 45.sp,
                        fontWeight: FontWeight.w400,
                      ),
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                        fontWeight: FontWeight.w400,
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        borderSide: BorderSide(
                          width: 1.w,
                          color: AppColors.k70777E,
                        ),
                      ),
                      showBorder: true,
                      fillColor: AppColors.k70777E,
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        borderSide: BorderSide(
                          width: 1.w,
                          color: AppColors.k70777E,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        borderSide: BorderSide(
                          width: 1.w,
                          color: AppColors.k70777E,
                        ),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24.r),
                        borderSide: BorderSide(
                          width: 1.w,
                          color: AppColors.k70777E,
                        ),
                      ),
                      border: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: AppColors.k70777E,
                        ),
                      ),
                      contentPadding: REdgeInsets.only(
                        left: 48,
                        /**/
                        bottom: 30,
                      ),
                    ),
                  ),
                  76.verticalSpace,
                  AppTextFormField(
                    name: RegistrationFormFields.tradeReferenceEmail,
                    labelText: LocaleKeys.trade_references_email_address.tr,
                    keyboardType: TextInputType.emailAddress,
                    autofillHints: const <String>[AutofillHints.email],
                    isRequired: false,
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                    ),
                    validator: (String? value) {
                      final String? trimmedValue = value?.trim();
                      if (trimmedValue?.isEmpty ?? true) {
                        return null;
                      } else {
                        if (!trimmedValue!.isEmail) {
                          return LocaleKeys.validation_email_is_invalid.tr;
                        } else if (trimmedValue ==
                                controller.compareAs(controller,
                                    key: RegistrationFormFields.email) ||
                            trimmedValue ==
                                AuthProvider.userEntity()
                                    .email
                                    .toString()
                                    .trim()) {
                          return 'Email already exists in the registration form';
                        } else if (controller.tradeReferencesList.any(
                            (TradeReference element) =>
                                element.email == trimmedValue)) {
                          return 'References email can not be same';
                        }
                      }
                      return null;
                    },
                    labelTextStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 45.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                      fontWeight: FontWeight.w400,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    showBorder: true,
                    fillColor: AppColors.k70777E,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.k70777E,
                      ),
                    ),
                    contentPadding: REdgeInsets.only(
                      left: 48,
                      bottom: 30,
                    ),
                  ),
                ],
              ),
              50.verticalSpace,
              Row(
                children: <Widget>[
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () => Get.back(),
                      buttonText: LocaleKeys.trade_references_cancel.tr,
                      backgroundColor: AppColors.kEAEFF4,
                      borderColor: AppColors.kEAEFF4,
                      buttonTextColor: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Expanded(
                    child: _buildCommonAppButton(
                      onPressed: () =>
                          controller.addTradeReferences(controller),
                      buttonText: LocaleKeys.trade_references_add.tr,
                      buttonTextColor: AppColors.kBEFFFC,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

/// Common App Button
Widget _buildCommonAppButton({
  required String buttonText,
  Function? onPressed,
  Color? backgroundColor,
  Color? borderColor,
  Color? buttonTextColor,
}) =>
    AppButton.text(
      buttonText: buttonText,
      onPressed: () {
        onPressed!();
      },
      buttonSize: Size(Get.width, 150.h),
      backgroundColor: backgroundColor ?? AppColors.k101C28,
      borderColor: borderColor ?? AppColors.k101C28,
      borderRadius: 24.r,
      buttonTextStyle: TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w700,
        color: buttonTextColor ?? AppColors.kBEFFFC,
      ),
    );
