import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../data/config/app_colors.dart';

/// For Add Owner Alert
void showAddOwnerAlertDialog() {
  _buildAddOwnerAlertDialog();
}

Dialog _buildAddOwnerAlertDialog() => Dialog(
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      insetPadding: REdgeInsets.only(
        left: 50.w,
        right: 50.w,
      ),
      backgroundColor: Colors.white,
      child: Container(
        margin: EdgeInsets.only(
          left: 50.w,
          right: 50.w,
          top: 100.h,
          bottom: 50.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              'Add owner',
              style: AppFontStyles.skolaSans(
                fontSize: 70.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
            100.verticalSpace,
            Row(
              children: <Widget>[
                Expanded(
                  child: AppTextFormField(
                    name: 'First name',
                    labelText: 'First name',
                    validator: (String? value) {
                      if (value?.trim().isNotEmpty ?? false) {
                        return null;
                      }
                      return null;
                    },
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                    ),
                    labelTextStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 35.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                      fontWeight: FontWeight.w400,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    showBorder: true,
                    fillColor: AppColors.k70777E,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.k70777E,
                      ),
                    ),
                    contentPadding: REdgeInsets.only(
                      left: 48,
                      bottom: 30,
                    ),
                  ),
                ),
                40.horizontalSpace,
                Expanded(
                  child: AppTextFormField(
                    name: 'Last name',
                    labelText: 'Last name',
                    validator: (String? value) {
                      if (value?.trim().isNotEmpty ?? false) {
                        return null;
                      }
                      return null;
                    },
                    constraints: BoxConstraints(
                      minHeight: 150.h,
                    ),
                    labelTextStyle: AppFontStyles.skolaSans(
                      color: AppColors.k70777E,
                      fontSize: 35.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    style: AppFontStyles.skolaSans(
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                      fontWeight: FontWeight.w400,
                    ),
                    errorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    showBorder: true,
                    fillColor: AppColors.k70777E,
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24.r),
                      borderSide: BorderSide(
                        width: 1.w,
                        color: AppColors.k70777E,
                      ),
                    ),
                    border: const OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColors.k70777E,
                      ),
                    ),
                    contentPadding: REdgeInsets.only(left: 48, bottom: 30),
                  ),
                ),
              ],
            ),
            76.verticalSpace,
            AppTextFormField(
              name: 'Designation',
              labelText: 'Designation',
              constraints: BoxConstraints(
                minHeight: 150.h,
              ),
              labelTextStyle: AppFontStyles.skolaSans(
                color: AppColors.k70777E,
                fontSize: 35.sp,
                fontWeight: FontWeight.w400,
              ),
              suffixIcon: IconButton(
                onPressed: () {},
                icon: Icon(
                  Icons.expand_more_sharp,
                  size: 32.h,
                  color: AppColors.k101C28,
                ),
              ),
              style: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                color: AppColors.k101C28,
                fontWeight: FontWeight.w400,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              showBorder: true,
              fillColor: AppColors.k70777E,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              border: const OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColors.k70777E,
                ),
              ),
              contentPadding: REdgeInsets.only(
                left: 48,
                bottom: 30,
              ),
            ),
            76.verticalSpace,
            AppTextFormField(
              // controller: controller.phoneController,
              name: 'Phone',
              labelText: 'Phone',
              isRequired: true,
              validator: (String? value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'please enter your Phone';
                } else if (value!.length < 10) {
                  return 'phone number should be at-least 10 digit';
                }
                return null;
              },
              constraints: BoxConstraints(
                minHeight: 150.h,
              ),
              labelTextStyle: AppFontStyles.skolaSans(
                color: AppColors.k70777E,
                fontSize: 35.sp,
                fontWeight: FontWeight.w400,
              ),
              style: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                color: AppColors.k101C28,
                fontWeight: FontWeight.w400,
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              showBorder: true,
              fillColor: AppColors.k70777E,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              border: const OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColors.k70777E,
                ),
              ),
              contentPadding: REdgeInsets.only(
                left: 48,
                bottom: 30,
              ),
            ),
            50.verticalSpace,
            Row(
              children: <Widget>[
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () => Get.back(),
                    buttonText: 'Cancel',
                    backgroundColor: AppColors.kEAEFF4,
                    borderColor: AppColors.kEAEFF4,
                    buttonTextColor: AppColors.k101C28,
                  ),
                ),
                30.horizontalSpace,
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () {},
                    buttonText: 'Add',
                    buttonTextColor: AppColors.kBEFFFC,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

/// Common App Button
Widget _buildCommonAppButton({
  required String buttonText,
  Function? onPressed,
  Color? backgroundColor,
  Color? borderColor,
  Color? buttonTextColor,
}) =>
    AppButton.text(
      buttonText: buttonText,
      onPressed: () {
        onPressed!();
      },
      buttonSize: Size(Get.width, 150.h),
      backgroundColor: backgroundColor ?? AppColors.k101C28,
      borderColor: borderColor ?? AppColors.k101C28,
      borderRadius: 24.r,
      buttonTextStyle: TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w700,
        color: buttonTextColor ?? AppColors.kBEFFFC,
      ),
    );
