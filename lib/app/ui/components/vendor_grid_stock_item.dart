import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/ui/components/app_cached_image.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

/// stock item
class VendorGridStockItem extends StatelessWidget {
  /// stock item constructor
  VendorGridStockItem({
    required this.index,
    required this.diamond,
    this.height,
    this.width,
    required this.onTap,
    required this.isAddedToCart,
    this.onFavourite,
  });

  final DiamondEntity diamond;

  Function()? onTap;

  /// index
  int index;

  /// height
  final double? height;

  /// width
  final double? width;

  RxBool isAddedToCart = false.obs;

  Function(bool)? onFavourite;

  @override
  Widget build(BuildContext context) => InkWell(
        onTap: onTap,
        child: Container(
          height: height ?? 430.h,
          width: width ?? 900.w,
          // margin: REdgeInsets.only(bottom: 60),
          padding: REdgeInsets.only(bottom: 60),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.circular(24.r),
            border: Border.all(
              color: AppColors.k000000.withOpacity(0.2),
              width: 0.5.r,
            ),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 30.h),
                blurRadius: 100.r,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Flexible(
                child: Container(
                  padding: REdgeInsets.all(10),
                  child: Stack(
                    children: <Widget>[
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24.r),
                          child: AppCachedImage(
                            height: height ?? 430.h,
                            width: width ?? 900.w,
                            imageUrl: diamond.thumbnail ?? '',
                            fit: BoxFit.cover,
                            errorWidget: (BuildContext context, String url,
                                    dynamic error) =>
                                Image.asset(
                              AppImages.diamondPlaceholderPngPath,
                              fit: BoxFit.cover,
                              height: height ?? 430.h,
                              width: width ?? 900.w,
                            ),
                            placeholder: (BuildContext context, String url) =>
                                Shimmer.fromColors(
                              baseColor: Colors.transparent,
                              highlightColor: Colors.grey.withOpacity(0.2),
                              child: Image.asset(
                                AppImages.diamondPlaceholderPngPath,
                                height: height ?? 430.h,
                                width: width ?? 900.w,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                      ),
                      // Positioned(
                      //   left: 20.w,
                      //   bottom: 20.h,
                      //   child: SizedBox(
                      //     height: 53.h,
                      //     width: 122.w,
                      //     child: FittedBox(
                      //       fit: BoxFit.cover,
                      //       child: AppImages.gia,
                      //     ),
                      //   ),
                      // child: Obx(
                      //   () => isAddedToCart()
                      //       ? CircleAvatar(
                      //           radius: 50.r,
                      //           backgroundColor: AppColors.kffffff,
                      //           child: SizedBox(
                      //             child: AppImages.cartBlue,
                      //             height: 53.h,
                      //             width: 53.w,
                      //           ),
                      //         )
                      //       : const SizedBox.shrink(),
                      // ),
                      // ),
                      // Positioned(
                      //   top: 4,
                      //   left: 4,
                      //   child: FittedBox(
                      //     child: StatusChip(
                      //       text: '${diamond.type ?? ''}'.toUpperCase(),
                      //       padding: REdgeInsets.symmetric(
                      //         horizontal: 30,
                      //         vertical: 8,
                      //       ),
                      //       color: AppColors.kEAEFF4,
                      //       textStyle: AppFontStyles.skolaSans(
                      //         fontSize: 30.sp,
                      //         fontWeight: FontWeight.w400,
                      //         color: AppColors.k101C28,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                    ],
                  ),
                ),
              ),
              18.verticalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        // CircleAvatar(
                        //   radius: 26.5.r,
                        //   backgroundColor: diamond.statusColor,
                        //   child: Text(
                        //     diamond.status.firstChar,
                        //     style: AppFontStyles.skolaSans(
                        //       fontWeight: FontWeight.w700,
                        //       fontSize: 30.sp,
                        //       color: AppColors.kffffff,
                        //     ),
                        //   ),
                        // ),
                        FittedBox(
                          child: StatusChip(
                            text: '${diamond.type ?? ''}'.toUpperCase(),
                            textStyle: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 30.sp,
                              color: AppColors.k101C28,
                            ),
                            padding: REdgeInsets.symmetric(
                              horizontal: 30,
                              vertical: 10,
                            ),
                            color: AppColors.kEAEFF4,
                          ),
                        ),
                        20.horizontalSpace,
                        diamond.lab?.isEmpty ?? false
                            ? const SizedBox.shrink()
                            : StatusChip(
                                text: diamond.lab?.toUpperCase() ?? '',
                                textStyle: AppFontStyles.skolaSans(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 30.sp,
                                  color: AppColors.k101C28,
                                ),
                                padding: REdgeInsets.symmetric(
                                  horizontal: 30,
                                  vertical: 10,
                                ),
                                color: AppColors.kEAEFF4,
                              ),
                        // SizedBox(
                        //   height: 53.h,
                        //   width: 122.w,
                        //   child: FittedBox(
                        //     fit: BoxFit.cover,
                        //     child: AppImages.gia,
                        //   ),
                        // ),
                        const Spacer(),
                      ],
                    ),
                    10.verticalSpace,
                    Text(
                      _buildListToString().toUpperCase(),
                      // 'Round, 1.00ct, K, VVS2 EX EX EX STG',
                      maxLines: 2,
                      overflow: TextOverflow.clip,
                      style: AppFontStyles.skolaSans(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w400,
                        color: AppColors.k101C28,
                      ),
                    ),
                    10.verticalSpace,
                    Text(
                      '${diamond.price?.finalPriceOri.toString().toPrice()}',
                      maxLines: 1,
                      overflow: TextOverflow.clip,
                      style: AppFontStyles.skolaSans(
                        fontSize: 50.sp,
                        fontWeight: FontWeight.w700,
                        color: AppColors.k101C28,
                      ),
                    ),
                    //  12.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Text(
                          '${diamond.price?.pricePerCaratOri.round().toString().toPrice()} /ct'
                              .toUpperCase(),
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w700,
                            color: AppColors.k70777E,
                          ),
                        ),
                        Text(
                          '${diamond.price?.discountOri}%',
                          style: AppFontStyles.skolaSans(
                            fontSize: 35.sp,
                            fontWeight: FontWeight.w700,
                            color: AppColors.k1A47E8,
                          ),
                        ),
                      ],
                    ),
                  ],
                ).paddingSymmetric(horizontal: 32.w),
              ),
              //90.verticalSpace,
            ],
          ),
        ),
      );

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          diamond.shape.toString().capitalizeFirst,
          '${diamond.weight}Ct',
          ((diamond.color?.isNotEmpty ?? false) && !diamond.isFancyColor)
              ? '${diamond.color?.wordCapital}'
              : (diamond.isFancyColor &&
                      (diamond.fancyColorMainBody?.isNotEmpty ?? false))
                  ? ' ${diamond.fancyColorMainBody?.wordCapital}'
                  : '',
          '${diamond.clarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}\n${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(diamond.cut),
          DiamondUtils.getFinishAbbreviation(diamond.polish),
          DiamondUtils.getFinishAbbreviation(diamond.symmetry),
          DiamondUtils.getFluoAbbreviation(diamond.fluorescenceIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';
}
