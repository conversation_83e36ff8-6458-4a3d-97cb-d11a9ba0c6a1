import 'dart:io';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/usa_patriot_act/controllers/usa_patriot_act_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/web_dialog_widget.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pdfrx/pdfrx.dart';

/// registration document preview dialog
void showDocumentPreviewDialogRegistration({
  required UsaPatriotActController controller,
}) {
  Get.dialog(WebDialog(
    child: SimpleDialog(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(24.r))),
      insetPadding: REdgeInsets.all(60),
      titlePadding: EdgeInsets.zero,
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      contentPadding: REdgeInsets.all(60),
      title: ListTile(
        contentPadding: REdgeInsets.only(left: 60),
        visualDensity: VisualDensity.compact,
        dense: true,
        title: Text(
          controller.filePickerResult().files.firstOrNull?.name ?? '',
          style: AppFontStyles.skolaSans(
            color: AppColors.k101C28,
            fontSize: 40.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          '${((controller.filePickerResult().files.firstOrNull?.size ?? 0) / 1024).toStringAsFixed(2)} kb',
          style: AppFontStyles.skolaSans(
            color: AppColors.k101C28,
            fontSize: 40.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
      ),
      children: [
        ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (controller.filePickerResult().files.firstOrNull?.extension ==
                  'pdf')
                Container(
                  height: Get.height * 0.6,
                  width: Get.width,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: Get.height * 0.6,
                      minWidth: Get.width,
                    ),
                    child: PdfViewer.file(
                        controller.filePickerResult().files.firstOrNull?.path ??
                            ''),
                  ),
                ),
              if (controller.filePickerResult().files.firstOrNull?.extension !=
                  'pdf')
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: Get.height * 0.6,
                    minWidth: Get.width,
                  ),
                  child: kIsWeb
                      ? Image.memory(
                          controller
                              .filePickerResult()
                              .files
                              .firstOrNull!
                              .bytes!,
                          fit: BoxFit.contain,
                        )
                      : Image.file(
                          File(controller
                                  .filePickerResult()
                                  .files
                                  .firstOrNull
                                  ?.path ??
                              ''),
                          fit: BoxFit.contain,
                        ),
                )
            ],
          ),
        ),
      ],
    ),
  ));
}

/// sow document picker options
Future<void> showDocumentPickerDialog({required dynamic controller}) async {
  await Get.dialog(WebDialog(
    child: SimpleDialog(
      contentPadding: EdgeInsets.zero,
      children: [
        Container(
          // height: 500.h,
          width: Get.width,
          padding: REdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.all(Radius.circular(24.r)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ListTile(
                onTap: () {
                  Get.back();
                  controller.pickFileCamera();
                },
                leading: Icon(
                  Icons.camera_alt_outlined,
                  color: AppColors.k101C28,
                  size: 80.sp,
                ),
                minVerticalPadding: 0,
                title: Text(
                  LocaleKeys.legal_status_organization_camera.tr,
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.k101C28,
                  ),
                ),
              ),
              Divider(color: AppColors.k70777E.withOpacity(0.5))
                  .paddingSymmetric(horizontal: 50.w),
              ListTile(
                onTap: () {
                  Get.back();
                  controller.pickFile();
                },
                leading: Icon(
                  Icons.image_outlined,
                  color: AppColors.k101C28,
                  size: 80.sp,
                ),
                minVerticalPadding: 0,
                title: Text(
                  LocaleKeys.legal_status_organization_gallery.tr,
                  style: AppFontStyles.skolaSans(
                    fontSize: 40.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.k101C28,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  ));
}
