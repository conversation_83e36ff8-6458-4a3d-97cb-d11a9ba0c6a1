import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/inquiry/inquiry.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/date_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../chat_service/chat_enum.dart';

/// stock item tile
class StockItemTile extends StatelessWidget {
  /// stock item tile
  StockItemTile({
    this.height,
    this.width,
    this.fromInquiries = false,
    super.key,
    required this.inquiryData,
  });

  /// from inquiries
  final bool fromInquiries;

  /// width
  final double? width;

  /// height
  final double? height;

  final InquiryData inquiryData;

  @override
  Widget build(BuildContext context) => Container(
        width: width ?? 915.w,
        height: height ?? 220.h,
        margin: fromInquiries ? null : REdgeInsets.symmetric(vertical: 40),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          borderRadius: BorderRadius.circular(24.r),
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
        ),
        child: ListTile(
          onTap: () => Get.toNamed(Routes.INQUIRY_MESSAGES, arguments: {
            'inquiry': inquiryData,
            'type': getChatTypeEnum(inquiryData.inquiry?.type),
          }),
          contentPadding: REdgeInsets.symmetric(horizontal: 60),
          minTileHeight: 220.h,
          horizontalTitleGap: 30.w,
          // leading: CircleAvatar(
          //   radius: 80.r,
          //   backgroundImage: const AssetImage(AppImages.diamondImagePath),
          // ),
          title: Text(
            '${inquiryData.inquiry?.title ?? ''}'.toUpperCase(),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.k101C28,
            ),
          ),
          subtitle: Text(
            '${inquiryData.lastMessage ?? ''}',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: AppFontStyles.skolaSans(
              fontSize: 35.sp,
              fontWeight: (inquiryData.userUnread ?? 0) > 0
                  ? FontWeight.bold
                  : FontWeight.w400,
              color: (inquiryData.userUnread ?? 0) > 0
                  ? AppColors.k000000
                  : AppColors.k70777E,
            ),
          ),
          trailing: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              CircleAvatar(
                radius: 15.r,
                backgroundColor: ((inquiryData.userUnread ?? 0) > 0)
                    ? AppColors.k128807
                    : AppColors.kffffff,
                child: ((inquiryData.userUnread ?? 0) > 0)
                    ? const SizedBox.shrink()
                    : Text(
                        '', // '${inquiryData.userUnread ?? 0}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 35.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.kffffff,
                        ),
                      ),
              ),
              //const Spacer(),
              Text(
                '${inquiryData.lastMessageAt?.dmyy ?? ''}',
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w400,
                  color: AppColors.k70777E,
                ),
              ),
            ],
          ),
        ),
      );
}
