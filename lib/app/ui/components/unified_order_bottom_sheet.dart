import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/diamond_summary.dart';
import 'package:diamond_company_app/app/data/models/jewellery_summary.dart';
import 'package:diamond_company_app/app/modules/cart_second_address/controllers/cart_second_address_controller.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

/// show unified order bottom sheet
Future<void> showApplePayUnifiedOrderBottomSheet() async {
  final CartSecondAddressController controller =
      Get.find<CartSecondAddressController>();
  await Get.bottomSheet(
    _buildBody(controller),
    backgroundColor: AppColors.kffffff,
    isDismissible: false,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(),
  );
  await controller.continueAfterApplePayUnifiedOrder();
}

Obx _buildBody(CartSecondAddressController controller) => Obx(
      () => AnimatedSize(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
        child: Container(
          width: Get.width,
          color: AppColors.kffffff,
          padding: REdgeInsets.only(left: 50, right: 50, top: 100, bottom: 130),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              SizedBox(
                height: 140.h,
                width: 140.w,
                child: Icon(
                  Icons.apple_outlined,
                  color: AppColors.k101C28,
                  size: 120.sp,
                ),
              ),
              50.verticalSpace,
              Text(
                'Proceed to payment',
                overflow: TextOverflow.clip,
                //maxLines: 4,
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  fontSize: 45.sp,
                  color: AppColors.k70777E,
                ),
                textAlign: TextAlign.center,
              ).paddingSymmetric(horizontal: 50.w),
              50.verticalSpace,
              _buildCartItemsDetails(controller),
              80.verticalSpace,
              _buildButton(controller),
              MediaQuery.of(Get.context!).padding.bottom.verticalSpace,
            ],
          ),
        ),
      ),
    );

Column _buildCartItemsDetails(CartSecondAddressController controller) {
  final Rx<DiamondSummary> diamondSummary =
      Get.find<CartItemService>().diamondSummary;
  final Rx<JewellerySummary> jewellerySummary =
      Get.find<CartItemService>().jewellerySummary;
  final Rx<DiamondSummary> meleeSummary =
      Get.find<CartItemService>().meleeSummary;
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      if (diamondSummary().totalPcs != 0)
        _buildCartDetailsItem(
          title: 'Diamonds',
          icon: AppImages.diamondCart,
          totalPcs: diamondSummary().totalPcs.toString().padLeft(2, '0'),
          totalPrice: diamondSummary().totalFinalPrice.toString().toPrice(),
          totalCts: diamondSummary().totalCts.toStringAsFixed(2),
          avgPricePerCarat: diamondSummary().avgPricePerCt.toStringAsFixed(2),
          type: BuyRequestItemType.DIAMOND,
          controller: controller,
          isSuccess: (controller.applePayDiamondOrderState() ==
                  ApplePayOrderState.SUCCESS)
              .obs,
          showCheckoutButton: (controller.applePayDiamondOrderState() ==
                  ApplePayOrderState.PENDING)
              .obs,
          showError: (controller.applePayDiamondOrderState() ==
                  ApplePayOrderState.ERROR)
              .obs,
          isLoading: (controller.applePayDiamondOrderState() ==
                  ApplePayOrderState.CREATING)
              .obs,
          onPressed: () {
            controller.proceedApplePayOrder(
                applePayType: BuyRequestItemType.DIAMOND);
          },
        ),
      if (diamondSummary().totalPcs != 0) 20.verticalSpace,
      if (meleeSummary().totalPcs != 0)
        _buildCartDetailsItem(
          title: 'Melee',
          icon: AppImages.meleeCart,
          totalPcs: meleeSummary().totalPcs.toString().padLeft(2, '0'),
          totalPrice: meleeSummary().totalFinalPrice.toString().toPrice(),
          totalCts: meleeSummary().totalCts.toStringAsFixed(2),
          avgPricePerCarat: meleeSummary().avgPricePerCt.toStringAsFixed(2),
          type: BuyRequestItemType.MELEE,
          controller: controller,
          isSuccess: (controller.applePayMelleOrderState() ==
                  ApplePayOrderState.SUCCESS)
              .obs,
          showCheckoutButton: (controller.applePayMelleOrderState() ==
                  ApplePayOrderState.PENDING)
              .obs,
          showError:
              (controller.applePayMelleOrderState() == ApplePayOrderState.ERROR)
                  .obs,
          isLoading: (controller.applePayMelleOrderState() ==
                  ApplePayOrderState.CREATING)
              .obs,
          onPressed: () {
            controller.proceedApplePayOrder(
                applePayType: BuyRequestItemType.MELEE);
          },
        ),
      if (meleeSummary().totalPcs != 0) 20.verticalSpace,
      if (jewellerySummary().totalPcs != 0)
        _buildCartDetailsItem(
          title: 'Jewelleries',
          icon: AppImages.jewelleryCart,
          totalPcs: jewellerySummary().totalPcs.toString().padLeft(2, '0'),
          totalPrice: jewellerySummary().totalFinalPrice.toString().toPrice(),
          totalCts: jewellerySummary().totalWeight.toStringAsFixed(2),
          avgPricePerCarat: '0',
          type: BuyRequestItemType.JEWELLERIES,
          controller: controller,
          isSuccess: (controller.applePayJewelleryOrderState() ==
                  ApplePayOrderState.SUCCESS)
              .obs,
          showCheckoutButton: (controller.applePayJewelleryOrderState() ==
                  ApplePayOrderState.PENDING)
              .obs,
          showError: (controller.applePayJewelleryOrderState() ==
                  ApplePayOrderState.ERROR)
              .obs,
          isLoading: (controller.applePayJewelleryOrderState() ==
                  ApplePayOrderState.CREATING)
              .obs,
          onPressed: () {
            controller.proceedApplePayOrder(
                applePayType: BuyRequestItemType.JEWELLERIES);
          },
        ),
    ],
  );
}

Widget _buildButton(CartSecondAddressController controller) => AppButton.text(
      buttonText: 'Continue',
      onPressed: () {
        Get.back();
      },
      buttonSize: Size(Get.width, 150.h),
      backgroundColor: AppColors.kEAEFF4,
      borderRadius: 24.r,
      borderColor: AppColors.kEAEFF4,
      buttonTextStyle: TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w700,
        color: AppColors.k101C28,
      ),
    );

String _buildSubTitle(BuyRequestProgress progress) {
  if (progress == BuyRequestProgress.CREATING) {
    return 'Please do not go back or close the app';
  } else if (progress == BuyRequestProgress.SUCCESS) {
    return 'The order has been created. The Diamond company team will soon get back to you';
  } else {
    return 'There’s been a small snag while creating a buy request for you.'
        'Kindly review the payment information and try again';
  }
}

String _buildTitle(BuyRequestProgress progress) {
  if (progress == BuyRequestProgress.CREATING) {
    return 'Creating Buy request ...';
  } else if (progress == BuyRequestProgress.SUCCESS) {
    return 'Thank you';
  } else {
    return 'Oops! Try again';
  }
}

Widget _buildCartDetailsItem({
  required String title,
  required String totalPcs,
  required String totalCts,
  required String avgPricePerCarat,
  required String totalPrice,
  required SvgPicture icon,
  required BuyRequestItemType type,
  required CartSecondAddressController controller,
  required RxBool showCheckoutButton,
  required RxBool showError,
  required RxBool isSuccess,
  required Function()? onPressed,
  RxBool? isLoading,
}) =>
    Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.k128807.withOpacity(0.10),
              borderRadius: BorderRadius.all(Radius.circular(24.r)),
            ),
            child: ListTile(
              leading: SizedBox(
                child: icon,
                height: 70.h,
                width: 70.w,
              ),
              horizontalTitleGap: 40.w,
              title: Text(
                title,
                style: AppFontStyles.skolaSans(
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k101C28,
                ),
              ),
              subtitle: Text(
                totalPrice,
                style: AppFontStyles.skolaSans(
                  fontSize: 40.sp,
                  fontWeight: FontWeight.w700,
                  color: AppColors.k1A47E8,
                ),
              ),
              trailing: isSuccess()
                  ? const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    )
                  : AppButton.text(
                      buttonText: 'Checkout',
                      onPressed: onPressed,
                      loaderHeight: 50.h,
                      loaderWidth: 50.w,
                      strokeWidth: 8.w,
                      isLoading: isLoading?.value ?? false,
                      backgroundColor: AppColors.k101C28,
                      borderColor: AppColors.k101C28,
                      buttonTextStyle: AppFontStyles.skolaSans(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w700,
                        color: AppColors.kffffff,
                      ),
                    ),
            ),
          ),
          if (showError())
            Text(
              'There’s been a small snag while creating an order for you. Please try again',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
        ],
      ),
    );

/// Build Product Details
Widget _buildProductDetails({required String text, required String value}) =>
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          text,
          style: AppFontStyles.skolaSans(
            fontSize: 30.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.k101C28.withOpacity(0.6),
          ),
        ),
        20.verticalSpace,
        Text(
          value,
          style: AppFontStyles.skolaSans(
            fontSize: 35.sp,
            fontWeight: FontWeight.w400,
            color: AppColors.k101C28,
          ),
        ),
      ],
    );

SvgPicture _buildIcon(BuyRequestProgress progress) {
  if (progress == BuyRequestProgress.CREATING) {
    return AppImages.buyRequestCreateBottomSheetSvg;
  } else if (progress == BuyRequestProgress.SUCCESS) {
    return AppImages.thankYouBottomSheetSvg;
  } else {
    return AppImages.tryAgainBottomSheetSvg;
  }
}
