import 'dart:math';

import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/modules/return_order/controllers/return_order_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_dialogs.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/diamond_ext.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/return_order_status.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../data/config/design_config.dart';

/// DiamondListTile
class DiamondListTile extends StatelessWidget {
  /// ConstrucondListTile
  DiamondListTile({
    super.key,
    this.isFromCart = false,
    this.isAlternative = false,
    this.isFromOrder = false,
    this.returnOrder,
    this.isDelivered = false,
    this.diamond,
    this.isFromVendor,
    this.isActionTaken,
    this.isAvailable,
  });

  /// diamond
  final DiamondEntity? diamond;

  /// isAlternative
  final bool isAlternative;

  /// isDelivered
  final bool isDelivered;

  /// isFromVendor
  final bool? isFromVendor;

  /// isActionTaken
  final bool? isActionTaken;

  /// isAvailable
  final bool? isAvailable;

  /// isFromCart
  final bool isFromCart;

  /// isFromCart
  final bool isFromOrder;

  /// isFromReturnOrder
  final ReturnOrder? returnOrder;

  @override
  Widget build(BuildContext context) {
    logI('DiamondListTile');
    logI(diamond?.toJson());
    return GestureDetector(
      onTap: () {
        if (kIsWeb) {
          Get.routing.args = DiamondArgs(
            id: diamond?.id ?? '',
            diamond: diamond,
          );
          Get.toNamed(Routes.DIAMOND_DETAILS,
              id: DesignConfig.listingSideMenuId);
        } else {
          Get.toNamed(
            Routes.DIAMOND_DETAILS,
            arguments: DiamondArgs(
              id: diamond?.id ?? '',
              diamond: diamond,
            ),
          );
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              if (diamond?.status == 'R') ...[
                StatusChip(
                  padding: REdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 6,
                  ),
                  text: 'RETURNED',
                  color: AppColors.k128807,
                ),
                //20.horizontalSpace,
                SizedBox(width: min(20.w, 10)),
              ],
              isAlternative
                  ? const SizedBox.shrink()
                  : isFromCart
                      ? CircleAvatar(
                          radius: 26.5.r,
                          backgroundColor:
                              // diamond?.diamond.statusColor,
                              (diamond?.isAvailable ?? false)
                                  ? diamond?.statusColor
                                  : AppColors.kFF0000,
                          child: Text(
                            diamond?.isAvailable ?? false
                                ? diamond?.status.firstChar ?? ''
                                : 'A',
                            style: AppFontStyles.skolaSans(
                              fontWeight: FontWeight.w700,
                              fontSize: 30.sp,
                              color: AppColors.kffffff,
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
              isAlternative || isFromCart
                  ? SizedBox(width: min(20.w, 10))
                  : const SizedBox.shrink(),
              StatusChip(
                text: diamond?.type?.toUpperCase() ?? '',
                padding: REdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 8,
                ),
                textStyle: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 30.sp,
                  color: AppColors.k101C28,
                ),
                color: AppColors.kEAEFF4,
              ),
              //20.horizontalSpace,
              SizedBox(width: min(20.w, 10)),
              if (diamond?.skuNumber != null)
                Row(
                  children: [
                    Text(
                      'Stock ID: ',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 38.sp,
                        color: AppColors.k1A47E8,
                      ),
                    ),
                    Text(
                      '${diamond?.skuPrefix ?? ''}${diamond?.skuNumber ?? 0}'
                          .toUpperCase(),
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w600,
                        fontSize: 38.sp,
                        color: AppColors.k1A47E8,
                      ),
                    ),
                  ],
                ),
              // SizedBox(
              //   height: 53.h,
              //   width: 122.w,
              //   child: FittedBox(
              //     fit: BoxFit.cover,
              //     child: AppImages.gia,
              //   ),
              // ),
              const Spacer(),
              (isFromVendor ?? false) && (isActionTaken ?? false)
                  ? StatusChip(
                      text: (isAvailable ?? false) ? 'Approved' : 'Rejected',
                      padding: REdgeInsets.symmetric(
                        horizontal: 30,
                        vertical: 8,
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(8.r)),
                      textStyle: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w500,
                        fontSize: 35.sp,
                        color: isAvailable ?? false
                            ? AppColors.k101C28
                            : AppColors.kffffff,
                      ),
                      color: isAvailable ?? false
                          ? AppColors.kFFF3E0
                          : AppColors.k7C0B0B,
                    )
                  : const SizedBox.shrink(),
              isFromOrder &&
                      !isDelivered &&
                      returnOrder == null &&
                      diamond?.status != 'R'
                  ? GetBuilder<ReturnOrderController>(
                      builder: (ReturnOrderController controller) => SizedBox(
                        height: 56.h,
                        width: min(56.w, 15),
                        child: Checkbox(
                          value: diamond?.isReturn ?? false,
                          visualDensity: VisualDensity.compact,
                          activeColor: AppColors.k101C28,
                          side: BorderSide(
                            color: AppColors.k101C28,
                            width: min(3.w, 1),
                          ),
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              color: AppColors.k101C28,
                              width: min(3.w, 1),
                            ),
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          onChanged: (bool? value) =>
                              onClickCheck(isCheck: value ?? false),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
              _buildReturnOrderStatus(),
              isAlternative
                  ? IconButton.outlined(
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.k70777E, width: 1.w),
                        shape: const CircleBorder(),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      onPressed: () {
                        if (kIsWeb) {
                          Get.routing.args = DiamondArgs(
                            id: diamond?.id ?? '',
                            diamond: diamond,
                          );
                          Get.toNamed(Routes.DIAMOND_DETAILS,
                              id: DesignConfig.listingSideMenuId);
                        } else {
                          Get.toNamed(
                            Routes.DIAMOND_DETAILS,
                            arguments: DiamondArgs(
                              id: diamond?.id ?? '',
                              diamond: diamond,
                            ),
                          );
                        }
                      },
                      padding: EdgeInsets.zero,
                      visualDensity: VisualDensity.compact,
                      constraints: BoxConstraints(
                        minWidth: 80.w,
                        minHeight: 80.h,
                      ),
                      icon: SizedBox(
                        width: 16.w,
                        height: 26.h,
                        child: AppImages.arrowForward,
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
          isFromCart ? 35.verticalSpace : 32.verticalSpace,
          if (isFromVendor ?? false)
            InkWell(
              onTap: () => Get.toNamed(
                Routes.VENDOR_DIAMOND_DETAILS,
                arguments: DiamondArgs(
                  id: diamond?.id ?? '',
                  diamond: diamond,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    'Stock ID: ',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 40.sp,
                      color: AppColors.k1A47E8,
                    ),
                  ),
                  Text(
                    '${diamond?.stockId ?? ''}'.toUpperCase(),
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w600,
                      fontSize: 40.sp,
                      color: AppColors.k1A47E8,
                    ),
                  ),
                ],
              ),
            ),
          if (isFromVendor ?? false) 32.verticalSpace,
          Text(
            _buildListToString().toUpperCase(),
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w400,
              fontSize: 40.sp,
              color: AppColors.k101C28,
            ),
          ),
          20.verticalSpace,
          isFromCart ? 35.verticalSpace : 32.verticalSpace,
          Row(
            children: <Widget>[
              Text(
                (isFromVendor ?? false)
                    ? '${diamond?.price?.finalPriceOri.toString().toPrice()}'
                    : '${diamond?.price?.finalPrice.toString().toPrice()}',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  fontSize: 45.sp,
                  color: AppColors.k101C28,
                ),
              ),
              //30.horizontalSpace,
              SizedBox(width: min(30.w, 15)),
              Text(
                (isFromVendor ?? false)
                    ? '${diamond?.price?.pricePerCaratOri.toString().toPrice()} /ct'
                    : '${diamond?.price?.pricePerCarat.toString().toPrice()} /ct',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w400,
                  fontSize: 35.sp,
                  color: AppColors.k101C28,
                ),
              ),
              if (isFromVendor ?? false) SizedBox(width: min(30.w, 15)),
              if (isFromVendor ?? false)
                Text(
                  (isFromVendor ?? false)
                      ? ' ${diamond?.price?.discountOri?.toStringAsFixed(2)}%'
                      : ' ${diamond?.price?.discount?.toStringAsFixed(2)}%',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 35.sp,
                    color: AppColors.k1A47E8,
                  ),
                )
            ],
          ),
          isFromOrder ? 32.verticalSpace : 0.verticalSpace,
          isFromOrder
              ? diamond?.reason != null
                  ? _buildReasonWidget(reason: diamond?.reason ?? '')
                  : const SizedBox.shrink()
              : const SizedBox.shrink(),
          returnOrder != null ? 32.verticalSpace : 0.verticalSpace,
          returnOrder != null
              ? _buildReasonWidget(reason: returnOrder?.reason ?? '')
              : const SizedBox.shrink(),
          (returnOrder != null && returnOrder?.rejectReason != null)
              ? 32.verticalSpace
              : 0.verticalSpace,
          (returnOrder != null && returnOrder?.rejectReason != null)
              ? _buildReasonWidget(
                  reason: returnOrder?.rejectReason ?? '',
                  isRejected: true,
                )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildReturnOrderStatus() {
    if (returnOrder != null) {
      if (returnOrder?.isActionTaken ?? false) {
        if (returnOrder?.isAccepted ?? false) {
          if (returnOrder?.vendorId != null) {
            return getReturnOrderStatusWidget(
                returnOrder?.status ?? ReturnOrderStatus.PENDING);
          }
          return const StatusChip(
            text: 'Accepted',
            color: AppColors.k128807,
          );
        } else {
          return const StatusChip(
            text: 'Rejected',
            color: AppColors.kFF0000,
          );
        }
      }
    }
    return const SizedBox.shrink();
  }

  Container _buildReasonWidget(
          {required String reason, bool isRejected = false}) =>
      Container(
        width: Get.width,
        padding: REdgeInsets.all(40),
        decoration: BoxDecoration(
          color: isRejected ? AppColors.kFFE4E4 : AppColors.kffffff,
          boxShadow: [
            BoxShadow(
              color: AppColors.k101C28.withOpacity(0.1),
              offset: Offset(0, 4.h),
              blurRadius: 4.r,
            )
          ],
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Text(
          reason ?? '',
          style: AppFontStyles.skolaSans(
            fontWeight: FontWeight.w400,
            fontSize: 35.sp,
            color: AppColors.k101C28,
          ),
        ),
      );

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          diamond?.displayShape.toString().capitalizeFirst,
          '${diamond?.weight} ct.',
          ((diamond?.color?.isNotEmpty ?? false) &&
                  !(diamond?.isFancyColor ?? false))
              ? '${diamond?.color?.wordCapital}'
              : ((diamond?.isFancyColor ?? false) &&
                      (diamond?.fancyColorMainBody?.isNotEmpty ?? false))
                  ? ' ${diamond?.fancyColorMainBody?.wordCapital}'
                  : '',
          '${diamond?.displayClarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}\n${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(diamond?.displayCut),
          DiamondUtils.getFinishAbbreviation(diamond?.displayPolish),
          DiamondUtils.getFinishAbbreviation(diamond?.displaySymmetry),
          DiamondUtils.getFluoAbbreviation(diamond?.displayIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';

  /// on check click
  Future<void> onClickCheck({bool isCheck = false}) async {
    final ReturnOrderController returnOrderController =
        Get.find<ReturnOrderController>();

    if (isCheck) {
      logI('isCheck');
      logI(isCheck);
      final String? reason =
          await showDeclineWithReasonDialog(isFromOrder: true);
      if (reason != null) {
        diamond?.isReturn = isCheck;
        diamond?.reason = reason;
        final int index = returnOrderController.returnDiamondList
            .indexWhere((DiamondEntity element) => element.id == diamond?.id);
        if (index == -1) {
          returnOrderController.returnDiamondList.add(diamond!);
        }
        returnOrderController.returnDiamondList.refresh();
      }
    } else {
      diamond?.isReturn = isCheck;
      diamond?.reason = null;
      returnOrderController.returnDiamondList
          .removeWhere((DiamondEntity element) => element.id == diamond?.id);
      returnOrderController.returnDiamondList.refresh();
    }
    returnOrderController.update();
  }
}
