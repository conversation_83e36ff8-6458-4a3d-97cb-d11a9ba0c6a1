import 'package:cached_network_image/cached_network_image.dart';
import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/cart_updated/controllers/cart_updated_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// jewellery order item
class JewelleryOrderItem extends StatelessWidget {
  /// jewellery order item
  JewelleryOrderItem({super.key, required this.jewelleryArrayItem});

  CartUpdatedController controller = Get.isRegistered<CartUpdatedController>()
      ? Get.find<CartUpdatedController>()
      : CartUpdatedController();

  final JewelleryArray jewelleryArrayItem;

  @override
  Widget build(BuildContext context) => Container(
        padding: REdgeInsets.all(30),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          boxShadow: <BoxShadow>[
            BoxShadow(
              color: AppColors.k000000.withOpacity(0.1),
              offset: Offset(0, 10.h),
              blurRadius: 104.r,
            ),
          ],
          borderRadius: BorderRadius.all(
            Radius.circular(24.r),
          ),
        ),
        child: Row(
          children: [
            Container(
              height: 150.h,
              width: 150.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(12.r),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.all(
                  Radius.circular(12.r),
                ),
                child: CachedNetworkImage(
                  imageUrl: jewelleryArrayItem.data?.thumbnail ?? '',
                  height: 150.h,
                  width: 150.w,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            40.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    jewelleryArrayItem.data?.selectedVariant?.title ?? '',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  20.verticalSpace,
                  Row(
                    children: [
                      Text(
                        'Size: ${jewelleryArrayItem.data?.selectedVariant?.selectedSize ?? ''}',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 40.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                      40.horizontalSpace,
                      Text(
                        '${jewelleryArrayItem.data?.selectedVariant?.selectedPurity ?? ''}, ${jewelleryArrayItem.data?.selectedVariant?.selectedMetal ?? ''}',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 40.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  20.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${jewelleryArrayItem.totalAmount}',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w700,
                          fontSize: 45.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                      40.horizontalSpace,
                      Text(
                        int.parse(jewelleryArrayItem.quantity ?? '0') > 1
                            ? '${int.parse(jewelleryArrayItem.quantity ?? '0')} Items'
                            : '${int.parse(jewelleryArrayItem.quantity ?? '0')} Item',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w500,
                          fontSize: 35.sp,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Column _buildItemSpec({
    required String title,
    required String subTitle,
  }) =>
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w500,
              fontSize: 30.sp,
              color: AppColors.k67707A,
            ),
          ),
          Text(
            subTitle,
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w400,
              fontSize: 35.sp,
              color: AppColors.k101C28,
            ),
          ),
        ],
      );
}
