import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_utils/get_utils.dart';

/// A dropdown form field that validates the selected value.
class DropDownFormField extends StatelessWidget {
  /// Constructor for DropDownFormField
  DropDownFormField({
    required this.items,
    required this.menuItemStyle,
    super.key,
    this.onChanged,
    this.decoration,
    this.selectedValue,
    this.style,
    this.hintStyle,
    this.hintText,
    this.isSearchable = false,
    this.suffixPadding,
    this.validate,
    this.isEnable = true,
  });

  /// isSearchable
  final bool isSearchable;

  /// items
  final List<dynamic> items;

  /// onChanged
  final Function(dynamic)? onChanged;

  /// decoration
  final InputDecoration? decoration;

  /// drop down item widget
  final TextStyle menuItemStyle;

  /// selected value
  dynamic? selectedValue;

  /// selected item style
  final TextStyle? style;

  /// hint style
  final TextStyle? hintStyle;

  /// hint text
  final String? hintText;

  /// suffix padding
  final EdgeInsets? suffixPadding;

  /// Validate
  final String? Function(dynamic)? validate;

  /// Is enable
  final bool isEnable;

  @override
  Widget build(BuildContext context) =>
      isSearchable ? _buildDropDownSearchSelection() : _buildBody();

  Widget _buildDropDownSearchSelection() => DropdownSearch<dynamic>(
        items: (String filter, LoadProps? loadProps) => items,
        onChanged: (_) => onChanged?.call(_ ?? ''),
        selectedItem: selectedValue,
        compareFn: (dynamic item1, dynamic item2) => true,
        suffixProps: const DropdownSuffixProps(
          dropdownButtonProps: DropdownButtonProps(
            iconOpened: Icon(
              Icons.keyboard_arrow_up,
              color: AppColors.k101C28,
            ),
            iconClosed: Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.k101C28,
            ),
            color: AppColors.k101C28,
          ),
        ),
        decoratorProps: DropDownDecoratorProps(
          decoration: decoration ?? const InputDecoration(),
          baseStyle: AppFontStyles.skolaSans(
            color: AppColors.k101C28,
            fontSize: 45.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        popupProps: PopupPropsMultiSelection.dialog(
          searchFieldProps: TextFieldProps(
            style: AppFontStyles.skolaSans(
              fontSize: 40.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.k101C28,
            ),
            decoration: InputDecoration(
              prefixIcon: const Icon(
                Icons.search,
                color: AppColors.k101C28,
              ),
              hintText: hintText ?? 'Search Location...',
              hintStyle: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
              fillColor: AppColors.kffffff,
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  color: AppColors.k101C28,
                  width: 1.w,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24.r),
                borderSide: BorderSide(
                  color: AppColors.k101C28,
                  width: 1.w,
                ),
              ),
            ),
            autofocus: true,
          ),
          showSearchBox: true,
          dialogProps: DialogProps(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            backgroundColor: AppColors.kffffff,
          ),
          emptyBuilder: (BuildContext context, String searchEntry) => Center(
            child: Text(
              searchEntry.isNotEmpty
                  ? LocaleKeys.legal_status_organization_not_found.tr
                  : LocaleKeys.legal_status_organization_search_location.tr,
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 40.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.k101C28,
              ),
            ),
          ),
          itemBuilder: (BuildContext context, dynamic item, bool isDisabled,
                  bool isSelected) =>
              Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.kF6F6F6,
                  width: 1.w,
                ),
              ),
            ),
            child: ListTile(
              leading: const Icon(
                Icons.location_on_outlined,
                color: AppColors.k101C28,
              ),
              title: Text(
                item,
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 45.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
      );

  Widget _buildBody() => DropdownButtonFormField<dynamic>(
        decoration: decoration,
        hint: hintText != null
            ? Text(
                hintText ?? '',
                style: hintStyle,
              )
            : null,
        isExpanded: true,
        value: selectedValue,
        icon: Padding(
          padding: suffixPadding ?? EdgeInsets.zero,
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: SvgPicture.asset(
              AppImages.arrowDownPath,
              width: 20.w,
              height: 20.h,
            ),
          ),
        ),
        style: style,
        onChanged: (dynamic value) => onChanged?.call(value ?? ''),
        validator: validate ??
            (dynamic value) {
              if (value == null || value.isEmpty) {
                return LocaleKeys.validation_please_select_an_option.tr;
              }
              return null;
            },
        items: items
            .map<DropdownMenuItem<dynamic>>(
              (dynamic value) => DropdownMenuItem<dynamic>(
                value: value,
                child: Text(
                  value is String ? value : value?.value ?? '',
                  style: menuItemStyle,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            )
            .toList(),
      );
}
