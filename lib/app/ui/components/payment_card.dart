import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// Payment Card
class PaymentCard extends StatelessWidget {
  /// Payment Card Constructor
  const PaymentCard({required this.buyRequest, super.key});

  /// Buy Request
  final Rx<BuyRequest> buyRequest;

  @override
  Widget build(BuildContext context) {
    logI('payment card');
    logI(buyRequest().toJson());
    if (buyRequest().paymentMode == PaymentMode.CREDIT_CARD) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPaymentText().paddingSymmetric(horizontal: 60.w),
          60.verticalSpace,
          Container(
            width: Get.width,
            margin: REdgeInsets.only(
              left: 60,
              right: 60,
            ),
            padding: REdgeInsets.symmetric(horizontal: 48, vertical: 50),
            decoration: BoxDecoration(
              color: AppColors.kEAEFF4,
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: [
                    RichText(
                      text: TextSpan(
                        text: 'xxxx',
                        style: AppFontStyles.skolaSans(
                          fontWeight: FontWeight.w900,
                          fontSize: 64.sp,
                          color: AppColors.k70777E,
                          letterSpacing: 5.w,
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text:
                                ' ${buyRequest().cardNumber?.substring((buyRequest().cardNumber?.length ?? 4) - 4) ?? ''}',
                            style: AppFontStyles.skolaSans(
                              fontSize: 45.sp,
                              color: AppColors.k101C28,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: REdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 17,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Image.asset(
                        AppImages.masterCardIconPath,
                        width: 80.w,
                      ),
                    ),
                  ],
                ),
                40.verticalSpace,
                Row(
                  children: <Widget>[
                    Text(
                      buyRequest().cardHolderName ?? '',
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      buyRequest().expDate?.replaceAll('-', '/') ?? '',
                      style: AppFontStyles.skolaSans(
                        fontSize: 45.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          80.verticalSpace,
        ],
      );
    } else if (buyRequest().paymentMode == PaymentMode.APPLE_PAY) {
      // return Column(
      //   crossAxisAlignment: CrossAxisAlignment.start,
      //   children: [
      //     _buildPaymentText().paddingSymmetric(horizontal: 60.w),
      //     60.verticalSpace,
      //     _buildCommonPaymentMethod(
      //       paymentMode: 'Apple Pay',
      //       transaction: 'Transaction ID',
      //       transactionID: buyRequest().transId ?? '',
      //       image: AppImages.applePayIconPath,
      //     ),
      //     80.verticalSpace,
      //   ],
      // );
    } else if (buyRequest().paymentMode == PaymentMode.CREDIT_LIMIT) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPaymentText().paddingSymmetric(horizontal: 60.w),
          60.verticalSpace,
          _buildCreditLimitContainer(),
        ],
      );
    }
    return const SizedBox.shrink();
  }

  /// Credit Limit Container
  Container _buildCreditLimitContainer() => Container(
        width: Get.width,
        margin: REdgeInsets.only(
          left: 60,
          right: 60,
          bottom: 80,
        ),
        padding: REdgeInsets.symmetric(horizontal: 48, vertical: 24),
        decoration: BoxDecoration(
          color: AppColors.kEAEFF4,
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Row(
          children: [
            Text(
              'Credit limit',
              style: AppFontStyles.skolaSans(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
            const Spacer(),
            Image.asset(
              AppImages.creditLimitIconPath,
              height: 120.h,
              width: 120.w,
            ),
          ],
        ),
      );

  Text _buildPaymentText() => Text(
        'PAYMENT METHOD',
        style: AppFontStyles.skolaSans(
          fontSize: 40.sp,
          fontWeight: FontWeight.w900,
          color: AppColors.k101C28,
          letterSpacing: 5.w,
        ),
      );

  Container _buildCommonPaymentMethod({
    required String paymentMode,
    String? transaction,
    String? transactionID,
    String? image,
  }) =>
      Container(
        width: Get.width,
        margin: REdgeInsets.only(
          left: 60,
          right: 60,
        ),
        padding: REdgeInsets.symmetric(horizontal: 48, vertical: 50),
        decoration: BoxDecoration(
          color: AppColors.kEAEFF4,
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  paymentMode,
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    fontWeight: FontWeight.w700,
                    color: AppColors.k101C28,
                  ),
                ),
                40.verticalSpace,
                Text(
                  transaction ?? '',
                  style: AppFontStyles.skolaSans(
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                32.verticalSpace,
                Text(
                  transactionID ?? '',
                  style: AppFontStyles.skolaSans(
                    fontSize: 45.sp,
                    color: AppColors.k101C28,
                  ),
                ),
              ],
            ),
            const Spacer(),
            image == null
                ? const SizedBox.shrink()
                : Image.asset(
                    image,
                    height: 120.h,
                    width: 120.w,
                  ),
          ],
        ),
      );
}
