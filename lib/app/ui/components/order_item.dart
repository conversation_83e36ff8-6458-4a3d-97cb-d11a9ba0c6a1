import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/config/app_images.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/modules/my_order/controllers/my_order_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/unified_order_type_widget.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/order_status.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/src/intl/date_format.dart';

/// order item
class OrderItem extends StatelessWidget {
  /// order item
  const OrderItem({
    Key? key,
    this.userOrder,
    this.height,
    this.width,
    this.toMyOrderScreen = false,
    this.index = 0,
    this.onTap,
  }) : super(key: key);

  /// User Order
  final UserOrder? userOrder;

  /// onclick order item
  final Function()? onTap;

  /// formMyOrderScreen
  final bool toMyOrderScreen;

  /// index
  final int index;

  /// width
  final double? width;

  /// height
  final double? height;

  /// controller
  MyOrderController get controller => Get.find<MyOrderController>();

  @override
  Widget build(BuildContext context) => InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(24.r),
        child: Container(
          width: width ?? 915.w,
          height: height,
          padding: REdgeInsets.all(toMyOrderScreen ? 40 : 30),
          decoration: BoxDecoration(
            color: (userOrder?.orderStatus == OrdersStatus.DELIVERED ||
                    userOrder?.orderStatus == OrdersStatus.CANCELED)
                ? AppColors.kEAEFF4
                : AppColors.kffffff,
            borderRadius: BorderRadius.circular(24.r),
            border: toMyOrderScreen
                ? (userOrder?.orderStatus == OrdersStatus.DELIVERED ||
                        userOrder?.orderStatus == OrdersStatus.CANCELED)
                    ? null
                    : Border.all(
                        color: AppColors.k70777E,
                        width: 1.w,
                      )
                : null,
            boxShadow: toMyOrderScreen
                ? <BoxShadow>[]
                : <BoxShadow>[
                    BoxShadow(
                      color: AppColors.k000000.withOpacity(0.1),
                      offset: Offset(0, 10.h),
                      blurRadius: 104.r,
                    ),
                  ],
          ),
          child: Column(
            children: <Widget>[
              Row(
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        LocaleKeys.dashboard_order_id.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k70777E,
                        ),
                      ),
                      Text(
                        userOrder?.orderCode.toString().toUpperCase() ?? '',
                        style: AppFontStyles.skolaSans(
                          fontSize: toMyOrderScreen ? 35.sp : 30.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        LocaleKeys.dashboard_order_date.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k70777E,
                        ),
                      ),
                      Text(
                        '${DateFormat('dd/MM/yy').format(userOrder?.updatedAt ?? DateTime.now())}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        LocaleKeys.dashboard_diamonds.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k70777E,
                        ),
                      ),
                      Text(
                        userOrder?.buyRequestDetails?.stockIds?.length
                                .toString()
                                .padLeft(2, '0')
                                .toString() ??
                            '',
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                ],
              ),
              Divider(
                color: AppColors.k101C28.withOpacity(0.05),
                height: 80.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        '${userOrder?.grandTotal?.toString().toPrice()}',
                        style: AppFontStyles.skolaSans(
                          fontSize: toMyOrderScreen ? 64.sp : 40.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k101C28,
                        ),
                      ),
                      Text(
                        LocaleKeys.dashboard_total_payment.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: toMyOrderScreen ? 35.sp : 30.sp,
                          fontWeight: FontWeight.w400,
                          color: toMyOrderScreen
                              ? AppColors.k101C28
                              : AppColors.k70777E,
                        ),
                      ),
                    ],
                  ),
                  getStatusWidget(
                      userOrder?.orderStatus ?? OrdersStatus.DELIVERED),
                ],
              ),
              // (userOrder?.orderStatus == OrdersStatus.DELIVERED ||
              //         userOrder?.orderStatus == OrdersStatus.CANCELED)
              //     ? const SizedBox.shrink()
              //     : Divider(
              //         color: AppColors.k101C28.withOpacity(0.05),
              //       ),
              // (userOrder?.orderStatus == OrdersStatus.DELIVERED ||
              //         userOrder?.orderStatus == OrdersStatus.CANCELED)
              //     ? const SizedBox.shrink()
              //     : RichText(
              //         text: TextSpan(
              //           text: LocaleKeys.dashboard_expected_delivery_on.tr,
              //           style: AppFontStyles.skolaSans(
              //             fontSize: toMyOrderScreen ? 35.sp : 30.sp,
              //             fontWeight: FontWeight.w400,
              //             color: AppColors.k101C28,
              //           ),
              //           children: <TextSpan>[
              //             TextSpan(
              //               text: ' Apr 6, 2024',
              //               style: AppFontStyles.skolaSans(
              //                 fontSize: toMyOrderScreen ? 35.sp : 30.sp,
              //                 fontWeight: FontWeight.w700,
              //                 color: AppColors.k101C28,
              //               ),
              //             ),
              //           ],
              //         ),
              //       ),
              Divider(
                color: AppColors.k101C28.withOpacity(0.05),
                height: 80.h,
              ),
              UnifiedOrderTypeWidget(userOrder?.unifiedOrderType),
              (userOrder?.orderStatus == OrdersStatus.DELIVERED &&
                      (userOrder?.returnOrders?.isNotEmpty ?? false))
                  ? 40.verticalSpace
                  : const SizedBox.shrink(),
              (userOrder?.orderStatus == OrdersStatus.DELIVERED &&
                      (userOrder?.returnOrders?.isNotEmpty ?? false))
                  ? _buildReturnDiamondsTile(userOrder)
                  : const SizedBox.shrink()
            ],
          ),
        ),
      );

  Widget _buildReturnDiamondsTile(UserOrder? userOrder) => InkWell(
        onTap: () => Get.toNamed(Routes.RETURN_ORDER, arguments: userOrder),
        child: Container(
          padding: REdgeInsets.symmetric(
            horizontal: 50,
            vertical: 43,
          ),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.circular(500.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '${userOrder?.returnOrders?.length ?? 0} ${(userOrder?.returnOrders?.length ?? 0) > 1 ? 'Diamonds' : 'Diamond'} Return',
                style: AppFontStyles.skolaSans(
                  fontWeight: FontWeight.w500,
                  fontSize: 35.sp,
                  color: AppColors.k1A47E8,
                ),
              ),
              SizedBox(
                width: 20.w,
                height: 32.h,
                child: AppImages.arrowForward,
              ),
            ],
          ),
        ),
      );
}
