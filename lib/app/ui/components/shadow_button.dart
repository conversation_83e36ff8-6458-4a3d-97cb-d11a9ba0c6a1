import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ShadowButton extends StatefulWidget {
  /// Shadow button constructor
  const ShadowButton({
    required this.buttonName,
    this.onTap,
    this.margin,
    this.width,
    this.height,
    this.buttonColor,
    this.borderSide,
    this.borderRadius,
    this.boxShadow,
    this.shadowColor,
    this.child,
    this.animationDuration,
    this.padding,
    this.textStyle,
  });

  /// Text
  final String buttonName;

  /// On tap
  final Function()? onTap;

  /// Margin
  final EdgeInsetsGeometry? margin;

  /// Padding
  final EdgeInsetsGeometry? padding;

  /// Width
  final double? width;

  /// Height
  final double? height;

  /// Button color
  final Color? buttonColor;

  /// shadow color
  final Color? shadowColor;

  /// Border side
  final BorderSide? borderSide;

  /// Border radius
  final BorderRadiusGeometry? borderRadius;

  /// Box shadow
  final BoxShadow? boxShadow;

  /// Child widget
  final Widget? child;

  /// Animation duration
  final Duration? animationDuration;

  /// Button textStyle
  final TextStyle? textStyle;

  @override
  State<ShadowButton> createState() => _ShadowButtonState();
}

class _ShadowButtonState extends State<ShadowButton>
    with TickerProviderStateMixin {
  /// Animation controller
  late AnimationController _controller;

  /// Is animation complete
  bool isAnimationComplete = false;

  // Init state method
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration ?? const Duration(milliseconds: 150),
      vsync: this,
    );
  }

  // Dispose method
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // On animation forward call
    void onForward() {
      _controller.forward();
      setState(
        () {
          isAnimationComplete = true;
        },
      );
    }

    // On animation reverse call
    void onReverse() {
      // To fix a bug on crashlytics
      if (_controller != null) {
        _controller.reverse();
        setState(
          () {
            isAnimationComplete = false;
          },
        );
      }
    }

    return GestureDetector(
      onTap: () {
        //AppUtils.lightImpact();
        onForward();
        Future.delayed(
          widget.animationDuration ?? const Duration(milliseconds: 150),
          () {
            onReverse();
            widget.onTap?.call();
          },
        );
      },
      onLongPress: () {
        //AppUtils.vibrate();
        onForward();
      },
      onLongPressEnd: (details) {
        onReverse();
        widget.onTap?.call();
      },
      child: ScaleTransition(
        scale: Tween<double>(
          begin: 1.0,
          end: 0.97,
        ).animate(_controller),
        child: AnimatedContainer(
          duration:
              widget.animationDuration ?? const Duration(milliseconds: 150),
          transformAlignment: Alignment.center,
          curve: Curves.easeIn,
          //alignment: Alignment.topCenter,
          margin: widget.margin ?? const EdgeInsets.only(bottom: 20).r,
          padding: widget.padding ?? EdgeInsets.zero,
          width: widget.width ?? 145.w,
          height: widget.height ?? 44.h,
          decoration: ShapeDecoration(
            color: widget.buttonColor,
            shape: RoundedRectangleBorder(
              side: widget.borderSide ?? BorderSide.none,
              borderRadius: widget.borderRadius ?? BorderRadius.circular(10).r,
            ),
            shadows: isAnimationComplete
                ? []
                : [
                    widget.boxShadow ??
                        BoxShadow(
                          color: widget.shadowColor ?? AppColors.kCECECE,
                          offset: const Offset(4, 4),
                        )
                  ],
          ),
          child: widget.child ??
              Center(
                child: Text(
                  widget.buttonName,
                  style: widget.textStyle,
                ),
              ),
        ),
      ),
    );
  }
}
