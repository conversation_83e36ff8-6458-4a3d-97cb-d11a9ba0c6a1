import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CardTitle extends StatelessWidget {
  const CardTitle({
    super.key,
    required this.title,
    this.icon,
    this.subtitle,
    this.trailing,
    this.padding,
  });

  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? trailing;
  final EdgeInsets? padding;

  @override
  Widget build(BuildContext context) => Padding(
        padding: padding ?? const EdgeInsets.only(left: 10, right: 10, top: 10),
        child: Row(
          children: <Widget>[
            if (icon != null) ...[
              Icon(
                icon,
                color: AppColors.k1A47E8,
                size: 40.sp,
              ),
              10.horizontalSpace
            ],
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title.toUpperCase(),
                    style: TextStyle(
                      fontWeight: FontWeight.w900,
                      fontSize: 40.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  if (subtitle != null)
                    Text(
                      subtitle!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey,
                      ),
                    ),
                ],
              ),
            ),
            if (trailing != null) 10.horizontalSpace,
            trailing ?? const SizedBox.shrink(),
            10.horizontalSpace,
          ],
        ),
      );
}
