import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_order/controllers/vendor_order_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

/// show accept decline offer Alert
void showShipVendorOrderConfirmationDialog() {
  Get.dialog(
    barrierColor: Colors.black.withOpacity(0.7),
    Container(
      margin: EdgeInsets.symmetric(
        horizontal: 60.w,
      ),
      child: _buildAcceptDeclineAlertDialog(),
      // showLogoutAlertDialog(),
    ),
  );
}

/// Logout Alert Dialog
Widget _buildAcceptDeclineAlertDialog() => Dialog(
      surfaceTintColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24.r),
      ),
      insetPadding: REdgeInsets.only(
        left: 50.w,
        right: 50.w,
      ),
      backgroundColor: Colors.white,
      child: Container(
        margin: EdgeInsets.only(
          left: 50.w,
          right: 50.w,
          top: 100.h,
          bottom: 50.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              'Are you sure you want to mark orders as Shipped?',
              textAlign: TextAlign.center,
              style: AppFontStyles.skolaSans(
                fontSize: 70.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.k101C28,
              ),
            ),
            // 50.verticalSpace,
            // Text(
            //   LocaleKeys.profile_description_text.tr,
            //   textAlign: TextAlign.center,
            //   style: AppFontStyles.skolaSans(
            //     fontSize: 45.sp,
            //     fontWeight: FontWeight.w500,
            //     color: AppColors.k70777E,
            //   ),
            // ),
            50.verticalSpace,
            Row(
              children: <Widget>[
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () {},
                    buttonText: LocaleKeys.profile_cancel.tr,
                    backgroundColor: AppColors.kEAEFF4,
                    borderColor: AppColors.kEAEFF4,
                    buttonTextColor: AppColors.k101C28,
                  ),
                ),
                30.horizontalSpace,
                Expanded(
                  child: _buildCommonAppButton(
                    onPressed: () {
                      Get.find<VendorOrderController>().callApiShipOrders();
                    },
                    buttonText: 'Mark as Shipped',
                    buttonTextColor: AppColors.kBEFFFC,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

/// Common App Button
Widget _buildCommonAppButton({
  required String buttonText,
  Function? onPressed,
  Color? backgroundColor,
  Color? borderColor,
  Color? buttonTextColor,
}) =>
    AppButton.text(
      buttonText: buttonText,
      onPressed: () {
        Get.back();
        onPressed!();
      },
      buttonSize: Size(Get.width, 150.h),
      backgroundColor: backgroundColor ?? AppColors.k101C28,
      borderColor: borderColor ?? AppColors.k101C28,
      borderRadius: 24.r,
      buttonTextStyle: TextStyle(
        fontSize: 45.sp,
        fontWeight: FontWeight.w700,
        color: buttonTextColor ?? AppColors.kBEFFFC,
      ),
    );
