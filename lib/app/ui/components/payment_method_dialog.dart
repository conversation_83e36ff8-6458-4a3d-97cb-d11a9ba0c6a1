import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/app/modules/cart_second_address/controllers/cart_second_address_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///   showChoosePaymentDialog
void showChoosePaymentDialog() {
  final CartSecondAddressController controller =
      Get.find<CartSecondAddressController>();
  Get.dialog(
    SimpleDialog(
      contentPadding: EdgeInsets.zero,
      insetPadding: REdgeInsets.symmetric(horizontal: 60),
      backgroundColor: AppColors.kffffff,
      surfaceTintColor: AppColors.kffffff,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(
          Radius.circular(24.r),
        ),
      ),
      title: Text(
        'Choose Payment \nMethod',
        style: AppFontStyles.skolaSans(
          fontWeight: FontWeight.w500,
          fontSize: 70.sp,
          color: AppColors.k101C28,
        ),
        textAlign: TextAlign.center,
      ),
      children: <Widget>[
        Column(
          children: <Widget>[
            50.verticalSpace,
            Container(
              margin: REdgeInsets.symmetric(horizontal: 50),
              padding: REdgeInsets.symmetric(horizontal: 60, vertical: 45),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(
                  Radius.circular(24.r),
                ),
                border: Border.all(
                  width: 1.w,
                  color: AppColors.k70777E,
                ),
              ),
              child: Column(
                children: <Widget>[
                  _buildAuthorizePaymentTile(controller),
                  controller.isCreditAvailable()
                      ? const SizedBox.shrink()
                      : 25.verticalSpace,
                  controller.isCreditAvailable()
                      ? Divider(
                          thickness: 1.h,
                          color: AppColors.k70777E,
                          height: 64.h,
                        )
                      : const SizedBox.shrink(),
                  controller.isCreditAvailable()
                      ? _buildCreditTile(controller)
                      : _buildZeroCredit(controller),
                  controller.isCreditAvailable()
                      ? const SizedBox.shrink()
                      : 15.verticalSpace,
                ],
              ),
            ),
            _buildChoosePaymentDialogActions(controller),
          ],
        ),
      ],
    ),
  );
}

Container _buildZeroCredit(CartSecondAddressController controller) => Container(
      width: Get.width,
      padding: REdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppColors.kEAEFF4,
        borderRadius: BorderRadius.all(
          Radius.circular(24.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'No available credit to use.',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w700,
              fontSize: 35.sp,
              color: AppColors.k101C28,
            ),
          ),
          24.verticalSpace,
          Text(
            'your available credit is insufficient',
            style: AppFontStyles.skolaSans(
              fontWeight: FontWeight.w400,
              fontSize: 35.sp,
              color: AppColors.k101C28,
            ),
          ),
          32.verticalSpace,
          _buildCreditChip(controller, AppColors.kffffff),
        ],
      ),
    );

Container _buildCreditChip(
        CartSecondAddressController controller, Color? color) =>
    Container(
      padding: REdgeInsets.symmetric(
        horizontal: 40,
        vertical: 16,
      ),
      decoration: BoxDecoration(
        color: color ?? AppColors.kffffff,
        borderRadius: BorderRadius.all(
          Radius.circular(500.r),
        ),
      ),
      child: RichText(
        text: TextSpan(
          text: controller.availableCredit() == 0
              ? '0'
              : '${controller.availableCredit().toString().toPrice()}',
          style: AppFontStyles.skolaSans(
            fontSize: 35.sp,
            fontWeight: FontWeight.w700,
            color: AppColors.k2F5EEB,
          ),
          children: <TextSpan>[
            TextSpan(
              text: ' Credit',
              style: AppFontStyles.skolaSans(
                fontSize: 35.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
          ],
        ),
      ),
    );

Widget _buildCreditTile(CartSecondAddressController controller) => Obx(
      () => InkWell(
        onTap: () => controller.selectedPaymentType(PaymentMode.CREDIT_LIMIT),
        borderRadius: BorderRadius.all(Radius.circular(24.r)),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: 56.h,
              width: 56.w,
              margin: REdgeInsets.only(top: 8),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(
                  width: controller.selectedPaymentType() ==
                          PaymentMode.CREDIT_LIMIT
                      ? 19.w
                      : 3.w,
                  color: AppColors.k101C28,
                ),
              ),
            ),
            32.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Use Available Credit',
                  style: AppFontStyles.skolaSans(
                    fontWeight: FontWeight.w700,
                    fontSize: 35.sp,
                    color: AppColors.k101C28,
                  ),
                ),
                24.verticalSpace,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'Utilize your available credit balance.',
                      style: AppFontStyles.skolaSans(
                        fontWeight: FontWeight.w400,
                        fontSize: 30.sp,
                        color: AppColors.k101C28,
                      ),
                    ),
                    24.verticalSpace,
                    _buildCreditChip(controller, AppColors.kBEFFFC),
                    24.verticalSpace,
                  ],
                )
              ],
            )
          ],
        ).paddingOnly(top: 20.h),
      ),
    );

// Widget _buildAuthorizePaymentTile(CartSecondAddressController controller) =>
//     Obx(
//       () => InkWell(
//         onTap: () =>
//             controller.selectedPaymentType(PaymentType.AUTHORIZE_PAYMENT),
//         borderRadius: BorderRadius.all(Radius.circular(24.r)),
//         child: Padding(
//           padding: REdgeInsets.all(30),
//           child: Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: <Widget>[
//               AnimatedContainer(
//                 duration: const Duration(milliseconds: 300),
//                 curve: Curves.easeInOut,
//                 height: 56.h,
//                 width: 56.w,
//                 margin: REdgeInsets.only(top: 8),
//                 decoration: BoxDecoration(
//                   shape: BoxShape.circle,
//                   color: Colors.white,
//                   border: Border.all(
//                     width: controller.selectedPaymentType() ==
//                             PaymentType.AUTHORIZE_PAYMENT
//                         ? 19.w
//                         : 3.w,
//                     color: AppColors.k101C28,
//                   ),
//                 ),
//               ),
//               32.horizontalSpace,
//               Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   Text(
//                     'Authorize Payment',
//                     style: AppFontStyles.skolaSans(
//                       fontWeight: FontWeight.w700,
//                       fontSize: 35.sp,
//                       color: AppColors.k101C28,
//                     ),
//                   ),
//                   24.verticalSpace,
//                   FittedBox(
//                     fit: BoxFit.scaleDown,
//                     child: Text(
//                       'Pay directly using your preferred payment\nmethod.',
//                       style: AppFontStyles.skolaSans(
//                         fontWeight: FontWeight.w400,
//                         fontSize: 30.sp,
//                         color: AppColors.k101C28,
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         ),
//       ),
//     );

Widget _buildAuthorizePaymentTile(CartSecondAddressController controller) =>
    Obx(
      () => Column(
        children: List.generate(
          controller.paymentMode.length,
          (int index) => _buildOtherButtons(controller, index),
        ),
      ),
    );

Widget _buildOtherButtons(CartSecondAddressController controller, int index) =>
    Column(
      children: [
        InkWell(
          borderRadius: BorderRadius.all(Radius.circular(24.r)),
          onTap: () {
            controller
                .selectedPaymentType(controller.paymentMode[index].paymentType);
            controller.selectProgramIndex(index);
          },
          splashColor: Colors.transparent,
          child: Row(
            children: <Widget>[
              _buildAnimatedContainerForPayment(
                isSelected: controller.selectedPaymentType() ==
                    controller.paymentMode[index].paymentType,
              ),
              32.horizontalSpace,
              Text(
                controller.paymentMode[index].title ?? '',
                style: AppFontStyles.skolaSans(
                  color: AppColors.k101C28,
                  fontSize: 35.sp,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const Spacer(),
              SizedBox(
                height: 80.h,
                width: 80.w,
                child: controller.paymentMode[index].image ??
                    const SizedBox.shrink(),
              ),
            ],
          ).paddingSymmetric(vertical: 15.h),
        ),
        if (index < controller.paymentMode.length - 1)
          Divider(
            thickness: 1.h,
            color: AppColors.k70777E,
            height: 64.h,
          ),
      ],
    );

Widget _buildAnimatedContainerForPayment({bool isSelected = false}) =>
    AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: 56.h,
      width: 56.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        border: Border.all(
          width: isSelected ? 19.w : 3.w,
        ),
      ),
    );

Container _buildChoosePaymentDialogActions(
        CartSecondAddressController controller) =>
    Container(
      padding: REdgeInsets.all(50),
      decoration: BoxDecoration(
        color: AppColors.kffffff,
        borderRadius: BorderRadius.all(Radius.circular(24.r)),
      ),
      child: Row(
        children: <Widget>[
          Expanded(
            child: AppButton.text(
              buttonText: 'Cancel',
              onPressed: () => Get.back(),
              buttonSize: Size(Get.width, 150.h),
              backgroundColor: AppColors.kEAEFF4,
              borderColor: AppColors.kEAEFF4,
              buttonTextStyle: TextStyle(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.k101C28,
              ),
            ),
          ),
          30.horizontalSpace,
          Expanded(
            child: AppButton.text(
              buttonText: 'Continue',
              onPressed: () {
                Get.back();
                controller.continueBuyRequest();
                // if (controller.selectProgramIndex() != -1) {
                //   Get.toNamed(Routes.CREDIT_CARD_PAYMENT);
                // } else {
                //   appSnackbar(
                //     message: 'Please choose payment method',
                //     snackBarState: SnackBarState.INFO,
                //   );
                // }
              },
              buttonSize: Size(Get.width, 150.h),
              backgroundColor: AppColors.k101C28,
              borderColor: AppColors.k101C28,
              borderRadius: 24.r,
              buttonTextStyle: TextStyle(
                fontSize: 45.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.kBEFFFC,
              ),
            ),
          ),
        ],
      ),
    );
