import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/vendor_order/vendor_order.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_args.dart';
import 'package:diamond_company_app/app/modules/vendor/vendor_order/controllers/vendor_order_controller.dart';
import 'package:diamond_company_app/app/routes/app_pages.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:diamond_company_app/app/utils/number_format_ext.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/src/intl/date_format.dart';

/// vendor order item
class VendorOrderItem extends StatelessWidget {
  /// vendor order item
  const VendorOrderItem({
    Key? key,
    this.vendorOrder,
    this.height,
    this.width,
    this.toMyOrderScreen = false,
    this.index = 0,
    this.onTap,
    this.showStatusChangeCheckBox,
    this.showUploadInvoice,
  }) : super(key: key);

  /// Vendor Order
  final VendorOrder? vendorOrder;

  /// onclick order item
  final Function()? onTap;

  /// formMyOrderScreen
  final bool toMyOrderScreen;

  /// show status change checkbox
  final bool? showStatusChangeCheckBox;

  /// show upload invoice
  final bool? showUploadInvoice;

  /// index
  final int index;

  /// width
  final double? width;

  /// height
  final double? height;

  /// controller
  VendorOrderController get controller => Get.find<VendorOrderController>();

  @override
  Widget build(BuildContext context) => InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(24.r),
        child: Container(
          width: width ?? 915.w,
          height: height,
          padding: REdgeInsets.all(toMyOrderScreen ? 40 : 30),
          decoration: BoxDecoration(
            color: AppColors.kffffff,
            borderRadius: BorderRadius.circular(24.r),
            border: Border.all(
              color: AppColors.k70777E,
              width: 1.w,
            ),
            boxShadow: <BoxShadow>[
              BoxShadow(
                color: AppColors.k000000.withOpacity(0.1),
                offset: Offset(0, 10.h),
                blurRadius: 104.r,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        LocaleKeys.dashboard_order_id.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k70777E,
                        ),
                      ),
                      Text(
                        vendorOrder?.order?.orderCode
                                .toString()
                                .toUpperCase() ??
                            '',
                        style: AppFontStyles.skolaSans(
                          fontSize: toMyOrderScreen ? 35.sp : 30.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        LocaleKeys.dashboard_order_date.tr,
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w500,
                          color: AppColors.k70777E,
                        ),
                      ),
                      Text(
                        '${DateFormat('dd/MM/yy').format(vendorOrder?.order?.updatedAt ?? DateTime.now())}',
                        style: AppFontStyles.skolaSans(
                          fontSize: 30.sp,
                          fontWeight: FontWeight.w400,
                          color: AppColors.k101C28,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  StatusChip(
                    borderRadius: BorderRadius.all(Radius.circular(24.r)),
                    text: '${vendorOrder?.order?.type}',
                    textStyle: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                    color: AppColors.kEAEFF4,
                  ),
                  if (showStatusChangeCheckBox ?? false) 30.horizontalSpace,
                  (showStatusChangeCheckBox ?? false)
                      ? GetBuilder<VendorOrderController>(
                          builder: (VendorOrderController controller) =>
                              SizedBox(
                            height: 56.h,
                            width: 56.w,
                            child: Checkbox(
                              value: controller.selectedIdsForShipped
                                  .contains(vendorOrder?.id),
                              visualDensity: VisualDensity.compact,
                              activeColor: AppColors.k101C28,
                              side: BorderSide(
                                color: AppColors.k101C28,
                                width: 3.w,
                              ),
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  color: AppColors.k101C28,
                                  width: 3.w,
                                ),
                                borderRadius: BorderRadius.circular(15.r),
                              ),
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                              onChanged: (bool? value) =>
                                  controller.onClickCheckPendingItem(
                                      vendorOrderId: vendorOrder?.id ?? ''),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                  // const Spacer(),
                ],
              ),
              Divider(
                color: AppColors.k101C28.withOpacity(0.05),
                height: 80.h,
              ),
              Column(
                children: [
                  InkWell(
                    onTap: () => Get.toNamed(
                      Routes.VENDOR_DIAMOND_DETAILS,
                      arguments: DiamondArgs(
                        id: vendorOrder?.stock?.id ?? '',
                        diamond: vendorOrder?.stock,
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Stock Id: '.toUpperCase(),
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.k1A47E8,
                          ),
                        ),
                        Text(
                          '${vendorOrder?.stock?.stockId ?? ''}'.toUpperCase(),
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w600,
                            fontSize: 40.sp,
                            color: AppColors.k1A47E8,
                          ),
                        ),
                      ],
                    ),
                  ),
                  20.verticalSpace,
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _buildListToString().toUpperCase(),
                          style: AppFontStyles.skolaSans(
                            fontWeight: FontWeight.w400,
                            fontSize: 40.sp,
                            color: AppColors.k101C28,
                          ),
                        ),
                      ),
                      if (showUploadInvoice ?? false)
                        _buildInvoiceUpload(vendorOrder),
                    ],
                  ),
                ],
              ),
              Divider(
                color: AppColors.k101C28.withOpacity(0.05),
                height: 80.h,
              ),
              Row(
                children: <Widget>[
                  Text(
                    '${vendorOrder?.stock?.price?.finalPriceOri.toString().toPrice()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w500,
                      fontSize: 45.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Text(
                    ' \$/CT: ${vendorOrder?.stock?.price?.pricePerCaratOri.toString().toPrice()}',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w400,
                      fontSize: 35.sp,
                      color: AppColors.k101C28,
                    ),
                  ),
                  30.horizontalSpace,
                  Text(
                    ' ${vendorOrder?.stock?.price?.discountOri?.toStringAsFixed(2)}%',
                    style: AppFontStyles.skolaSans(
                      fontWeight: FontWeight.w700,
                      fontSize: 35.sp,
                      color: AppColors.k1A47E8,
                    ),
                  ),
                  const Spacer(),
                ],
              ),
              if (((vendorOrder?.isInvoiceActionTaken == true) &&
                      (vendorOrder?.isInvoiceAccepted == false)) &&
                  controller.isDeliveredTab()) ...[
                30.verticalSpace,
                _buildReasonWidget(reason: vendorOrder?.rejectReason ?? '')
              ],
            ],
          ),
        ),
      );

  Container _buildReasonWidget({required String reason}) => Container(
        width: Get.width,
        padding: REdgeInsets.all(40),
        decoration: BoxDecoration(
          color: AppColors.kffffff,
          boxShadow: [
            BoxShadow(
              color: AppColors.k101C28.withOpacity(0.1),
              offset: Offset(0, 4.h),
              blurRadius: 4.r,
            )
          ],
          borderRadius: BorderRadius.circular(24.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Rejected',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w500,
                fontSize: 35.sp,
                color: AppColors.kFF0000,
              ),
            ),
            20.verticalSpace,
            Text(
              reason ?? '',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w400,
                fontSize: 35.sp,
                color: AppColors.k101C28,
              ),
            ),
          ],
        ),
      );

  Widget _buildInvoiceUpload(VendorOrder? vendorOrder) => AppButton.custom(
        onPressed: () => controller.onClickInvoiceButton(vendorOrder),
        borderColor: AppColors.k101C28,
        borderWidth: 4.w,
        borderRadius: 500.r,
        backgroundColor: AppColors.kffffff,
        isLoading: vendorOrder?.isUploadingInvoice ?? false,
        loaderColor: AppColors.k101C28,
        strokeWidth: 8.w,
        loaderHeight: 50.h,
        loaderWidth: 50.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              _buildIcon(vendorOrder),
              color: AppColors.k128807,
              size: 50.sp,
            ),
            15.horizontalSpace,
            Text(
              'Invoice',
              style: AppFontStyles.skolaSans(
                fontWeight: FontWeight.w700,
                fontSize: 35.sp,
                color: AppColors.k101C28,
              ),
            ),
          ],
        ),
      );

  IconData _buildIcon(VendorOrder? vendorOrder) {
    if (vendorOrder?.invoiceUrl != null &&
        vendorOrder?.isInvoiceActionTaken == false) {
      return Icons.update_outlined;
    } else if (vendorOrder?.invoiceUrl != null &&
        vendorOrder?.isInvoiceActionTaken == true &&
        vendorOrder?.isInvoiceAccepted == true) {
      return Icons.check_circle;
    } else {
      return Icons.cloud_upload_rounded;
    }
  }

  String _buildListToString() => '${StringUtils.listToString(
        <String?>[
          vendorOrder?.stock?.shape.toString().capitalizeFirst,
          '${vendorOrder?.stock?.weight}Ct',
          ((vendorOrder?.stock?.color?.isNotEmpty ?? false) &&
                  !(vendorOrder?.stock?.isFancyColor ?? false))
              ? '${vendorOrder?.stock?.color?.wordCapital}'
              : ((vendorOrder?.stock?.isFancyColor ?? false) &&
                      (vendorOrder?.stock?.fancyColorMainBody?.isNotEmpty ??
                          false))
                  ? ' ${vendorOrder?.stock?.fancyColorMainBody?.wordCapital}'
                  : '',
          '${vendorOrder?.stock?.clarity?.toUpperCase()}',
        ],
        separator: ', ',
      )}\n${StringUtils.listToString(
        <String?>[
          DiamondUtils.getFinishAbbreviation(vendorOrder?.stock?.cut),
          DiamondUtils.getFinishAbbreviation(vendorOrder?.stock?.polish),
          DiamondUtils.getFinishAbbreviation(vendorOrder?.stock?.symmetry),
          DiamondUtils.getFluoAbbreviation(
              vendorOrder?.stock?.fluorescenceIntensity),
        ],
        separator: ' ',
        upperCase: true,
      )}';
}
