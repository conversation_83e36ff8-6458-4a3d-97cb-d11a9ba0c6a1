import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/modules/diamond_details/controllers/diamond_details_controller.dart';
import 'package:diamond_company_app/app/modules/offer_details/controllers/offer_details_controller.dart';
import 'package:diamond_company_app/app/ui/components/app_button.dart';
import 'package:diamond_company_app/app/ui/components/app_text_form_field.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/web_dialog_widget.dart';
import 'package:diamond_company_app/app/utils/string_ext.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

///   show create Offer dialog
void showCreateOfferDialog({dynamic controller, StockOffer? stockOffer}) {
  Get.dialog(
    WebDialog(
      child: SimpleDialog(
        contentPadding: EdgeInsets.zero,
        backgroundColor: AppColors.kffffff,
        surfaceTintColor: AppColors.kffffff,
        insetPadding: REdgeInsets.symmetric(horizontal: 60),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(24.r),
          ),
        ),
        children: <Widget>[
          Column(
            children: [
              100.verticalSpace,
              Center(
                child: Text(
                  LocaleKeys.offers_make_an_offer.tr,
                  style: TextStyle(
                    fontSize: 70.sp,
                    fontWeight: FontWeight.w500,
                    color: AppColors.k101C28,
                  ),
                ),
              ),
              80.verticalSpace,
              _buildOfferPriceField(controller),
              50.verticalSpace,
              _buildOfferPriceActions(controller, stockOffer),
              50.verticalSpace,
            ],
          ).paddingSymmetric(horizontal: 50.w),
        ],
      ),
    ),
  );
}

Widget _buildOfferPriceField(dynamic controller) => FormBuilder(
      key: controller.offerFormKey,
      child: AppTextFormField(
        name: 'offer_price',
        labelText: LocaleKeys.offers_offer_price.tr.wordCapital,
        hintText: LocaleKeys.offers_offer_price.tr.wordCapital,
        keyboardType: TextInputType.number,
        hintTextStyle: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.kCFD2D4,
        ),
        style: AppFontStyles.skolaSans(
          fontSize: 45.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k101C28,
        ),
        validator: (String? value) {
          if (value?.trim().isEmpty ?? true) {
            return LocaleKeys.offers_please_enter_offer_price.tr;
          }
          return null;
        },
        contentPadding: REdgeInsets.symmetric(horizontal: 48, vertical: 50),
        labelTextStyle: AppFontStyles.skolaSans(
          fontSize: 35.sp,
          fontWeight: FontWeight.w400,
          color: AppColors.k70777E,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24.r),
          borderSide: BorderSide(
            color: AppColors.k70777E,
            width: 1.w,
          ),
        ),
      ),
    );

Row _buildOfferPriceActions(dynamic controller, StockOffer? stockOffer) => Row(
      children: <Widget>[
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.offers_cancel.tr,
            onPressed: () {
              Get.back();
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.kEAEFF4,
            borderColor: AppColors.kEAEFF4,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.k101C28,
            ),
          ),
        ),
        30.horizontalSpace,
        Expanded(
          child: AppButton.text(
            buttonText: LocaleKeys.offers_send.tr,
            onPressed: () {
              if (controller is OfferDetailsController) {
                //controller.updateOffer(offerPrice: 0, offerId: stockOffer?.offerId??'');
              } else if (controller is DiamondDetailsController) {
                //controller.createOfferApi();
              }
            },
            buttonSize: Size(Get.width / 2, 150.h),
            backgroundColor: AppColors.k101C28,
            borderColor: AppColors.k101C28,
            borderRadius: 24.r,
            buttonTextStyle: TextStyle(
              fontSize: 45.sp,
              fontWeight: FontWeight.w700,
              color: AppColors.kBEFFFC,
            ),
          ),
        ),
      ],
    );
