import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

class DashboardShimmer extends StatelessWidget {
  const DashboardShimmer({super.key});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          80.verticalSpace,
          SizedBox(
            width: Get.width,
            height: 500.h,
            child: Shimmer.fromColors(
              baseColor: Colors.grey.shade300,
              highlightColor: Colors.grey.shade200,
              child: Container(
                width: Get.width,
                height: 500.h,
                margin: REdgeInsets.symmetric(horizontal: 60),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: Colors.white,
                ),
              ),
            ),
          ),
          80.verticalSpace,
          ShimmerHorizontalList(),
          ShimmerHorizontalList(),
          ShimmerHorizontalList(),
          ShimmerHorizontalList(),
        ],
      );
}

class ShimmerHorizontalList extends StatelessWidget {
  @override
  Widget build(BuildContext context) => SizedBox(
        width: Get.width,
        height: 400.h,
        child: ListView.builder(
          itemCount: 5,
          scrollDirection: Axis.horizontal,
          padding: REdgeInsets.symmetric(horizontal: 60),
          itemBuilder: (BuildContext context, int index) => Container(
            width: 915.w,
            margin: REdgeInsets.only(right: 40),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Shimmer Placeholder for Photo Image
                SizedBox(
                  width: 430.w,
                  height: 340.h,
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey.shade300,
                    highlightColor: Colors.grey.shade200,
                    child: Container(
                      width: 430.w,
                      height: 430.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(14.r),
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                30.horizontalSpace,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      30.verticalSpace,
                      // Shimmer for Photo Title
                      Shimmer.fromColors(
                        baseColor: Colors.grey.shade300,
                        highlightColor: Colors.grey.shade200,
                        child: Container(
                          height: 40.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                      30.verticalSpace,
                      // Shimmer for Description
                      Shimmer.fromColors(
                        baseColor: Colors.grey.shade300,
                        highlightColor: Colors.grey.shade200,
                        child: Container(
                          height: 40.h,
                          width: Get.width * 0.5 / 2,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                      30.verticalSpace,
                      // Shimmer for Description
                      Shimmer.fromColors(
                        baseColor: Colors.grey.shade300,
                        highlightColor: Colors.grey.shade200,
                        child: Container(
                          height: 40.h,
                          width: Get.width * 0.4 / 2,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                      30.verticalSpace,
                      // Shimmer for Another Description
                      Shimmer.fromColors(
                        baseColor: Colors.grey.shade300,
                        highlightColor: Colors.grey.shade200,
                        child: Container(
                          height: 40.h,
                          width: Get.width * 0.3 / 2,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      );
}
