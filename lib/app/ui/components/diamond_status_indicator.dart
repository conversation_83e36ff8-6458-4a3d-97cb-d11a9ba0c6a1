import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:diamond_company_app/app/utils/diamond_utils.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DiamondStatusIndicator extends StatelessWidget {
  const DiamondStatusIndicator({
    required this.status,
    this.margin,
    this.textStyle,
    super.key,
  });

  final String status;
  final EdgeInsets? margin;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) => Tooltip(
        message: _statusTooltip(),
        triggerMode: TooltipTriggerMode.tap,
        decoration: const BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.all(Radius.circular(6)),
        ),
        onTriggered: vibrate,
        textStyle: GoogleFonts.monda(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
        child: Container(
          decoration: BoxDecoration(
            color: _statusColor.withOpacity(0.2),
            borderRadius: const BorderRadius.all(Radius.circular(6)),
            border: Border.all(
              color: _statusColor.withOpacity(0.5),
            ),
          ),
          margin:
              margin ?? const EdgeInsets.symmetric(horizontal: 9, vertical: 8),
          constraints: const BoxConstraints(
            minHeight: 22,
            minWidth: 22,
            maxHeight: 22,
            maxWidth: 22,
          ),
          alignment: Alignment.center,
          child: Text(
            status,
            style: textStyle?.copyWith(
                  color: _statusColor,
                ) ??
                GoogleFonts.montserrat(
                  color: _statusColor,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
      );

  String _statusTooltip() {
    switch (status) {
      case 'A':
        return 'Available';
      case 'H':
        return 'On Hold';
      case 'M':
        return 'On Memo';
      case 'S':
        return 'Sold';
      default:
        return 'Contact supplier for availability';
    }
  }

  Color get _statusColor => DiamondUtils.statusColor(status);
}
