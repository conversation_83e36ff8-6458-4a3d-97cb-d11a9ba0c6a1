import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../data/config/design_config.dart';
import '../../routes/app_pages.dart';
import '../../utils/web_navigator_utils.dart';

/// registration web navigator
class RegistrationWebNavigator extends StatelessWidget {
  const RegistrationWebNavigator({super.key});

  @override
  Widget build(BuildContext context) => Navigator(
        key: DesignConfig.listingSideMenuKey,
        initialRoute: Routes.NEW_COMPANY_REGISTRATION,
        onDidRemovePage: (Page<Object?> page) {},
        pages: [],
        observers: [SideMenuNavigatorObserver()],
        onGenerateRoute: (settings) {
          // Find the route that matches the settings
          var route = AppPages.routes
              .firstWhereOrNull((route) => route.name == settings.name);

          if (route != null) {
            return GetPageRoute(
              settings: settings,
              page: route.page,
              binding: route.binding,
              routeName: route.name,
            );
          }
          return GetPageRoute(
            settings: settings,
            page: () => const SizedBox.shrink(),
          );
        },
      );
}
