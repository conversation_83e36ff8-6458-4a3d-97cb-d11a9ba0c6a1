enum ChatType {
  LOGIN_INQUIRY,

  DIAMOND_DETAILS,

  BUY_REQUEST_ORDER,

  BUY_ORDER,

  JEWELLERY;
}

String getChatType(ChatType chatType) {
  switch (chatType) {
    case ChatType.LOGIN_INQUIRY:
      return 'login_inquiry';
    case ChatType.DIAMOND_DETAILS:
      return 'diamond_details';
    case ChatType.BUY_REQUEST_ORDER:
      return 'buy_request_order';
    case ChatType.BUY_ORDER:
      return 'buy_order';
    case ChatType.JEWELLERY:
      return 'jewellery';
    default:
      return 'diamond_details';
  }
}

ChatType getChatTypeEnum(String? chatType) {
  switch (chatType) {
    case 'login_inquiry':
      return ChatType.LOGIN_INQUIRY;
    case 'diamond_details':
      return ChatType.DIAMOND_DETAILS;
    case 'buy_request_order':
      return ChatType.BUY_REQUEST_ORDER;
    case 'buy_order':
      return ChatType.BUY_ORDER;
    case 'jewellery':
      return ChatType.JEWELLERY;
    default:
      return ChatType.BUY_ORDER;
  }
}
