import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/logger.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:socket_io_client/socket_io_client.dart';

class SocketEvents {
  static const String joinRoom = 'joinRoom';
  static const String joinedRoom = 'joinedRoom';
  static const String leaveRoom = 'leaveRoom';
  static const String inquiry = 'inquiry';
  static const String sendMessage = 'sendMessage';
  static const String receiveMessage = 'receiveMessage';
  static const String getMessage = 'getMessage';
  static const String getCurrentMessage = 'getCurrentMessage';
}

class AppSocket {
  static String? socketId;

  static IO.Socket socket = IO.io(
    ApiConstants.kSocketUrl,
    OptionBuilder()
        .setTransports(<String>['websocket'])
        .enableForceNewConnection()
        .build(),
  );

  /// is the socket disconnected
  static bool get disconnected => socket.disconnected;

  /// is the socket connected
  static bool get connected => socket.connected;

  static void initSocket({Function()? onConnect}) {
    try {
      if (socket.disconnected) {
        socket
          ..connect()
          ..onConnect((dynamic data) {
            socketId = socket.id;

            logD('connected, data: $data, socketId: $socketId');
            onConnect?.call();
          });
      } else if (socket.connected) {
        onConnect?.call();
      }
    } on Exception catch (e, t) {
      logE('${e.toString()},$t');
    }
  }

  // Emit a message
  static void emit(
    String event,
    dynamic data, {
    Function(dynamic data)? ack,
  }) {
    initSocket(
      onConnect: () {
        if (ack == null) {
          socket.emit(
            event,
            data,
          );
        } else {
          socket.emitWithAck(
            event,
            data,
            ack: ack,
          );
        }
      },
    );
  }

  /// Leave room
  static void leaveRoom({required Map<String, dynamic> data}) {
    off(event: SocketEvents.joinedRoom);
    off(event: SocketEvents.getMessage);
    off(event: SocketEvents.getCurrentMessage);
    if (AppSocket.connected) {
      AppSocket.emit(
        SocketEvents.leaveRoom,
        data,
      );
    }
  }

  /// Listen to an event
  static void on({
    required String event,
    required Function(dynamic data) onEvent,
  }) =>
      socket.on(event, onEvent);

  /// Listen to an event
  static void off({
    required String event,
  }) =>
      socket.off(event);
}
