import 'package:diamond_company_app/app/data/config/logger.dart';

/// remove empty values
Map<String, dynamic>? removeEmptyValues(Map<String, dynamic>? json) {
  json?.removeWhere((key, value) {
    if (value is Map<String, dynamic>) {
      // If the value is another JSON object, recursively call removeEmptyValues
      json[key] = removeEmptyValues(value);
    } else if (value is List) {
      // If the value is a list, iterate through each item
      var updatedList = value.map((item) {
        if (item is Map<String, dynamic>) {
          // If the item is a JSON object, recursively call removeEmptyValues
          return removeEmptyValues(item);
        }
        return item;
      }).toList();
      json[key] = updatedList;
    }
    return value.toString().isEmpty || value == null || value == 'null';
  });

  return json;
}

/// to json recursive
Map<String, dynamic> toJsonRecursive(Map<String, dynamic> map) =>
    map.map((key, value) {
      logI('value type');
      logI(value.runtimeType);
      if (value is Map<String, dynamic>) {
        // If the value is another map, recursively call toJsonRecursive
        return MapEntry(key, toJsonRecursive(value));
      } else {
        // If the value is not a map, call its toJson method if available
        // if (value is Object && value.runtimeType != String) {
        //   return MapEntry(key, (value as dynamic).toJson());
        // }
        return MapEntry(key, value);
      }
    });
