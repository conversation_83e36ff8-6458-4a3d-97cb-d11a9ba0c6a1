import 'package:diamond_company_app/app/data/models/diamond/diamond_entity.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';

/// offer ext
extension OfferExt on StockOffer {
  /// vendor side price per caret
  double vendorSidePricePerPrice(DiamondEntity stock) =>
      (updatedOfferPrice ?? 0) / (stock.weight ?? 0);

  /// vendor side last offer price per caret
  double vendorSideLastOfferPricePerPrice(DiamondEntity stock) =>
      (updatedLastOfferPrice ?? 0) / (stock.weight ?? 0);

  /// user side price per caret
  double userSidePricePerPrice(DiamondEntity stock) =>
      (offerPrice ?? 0) / (stock.weight ?? 0);

  /// user side last offer price per caret
  double userLastOfferPricePerPrice(DiamondEntity stock) =>
      (lastOfferPrice ?? 0) / (stock.weight ?? 0);
}
