import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/buy_request/buy_request.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/get_utils.dart';

/// buy request utils
class BuyRequestUtils {
  /// is stock available
  static bool isStockAvailable(StockId stock) => stock.isAvailable ?? false;

  /// status string
  static String getStatusString(BuyRequestStatus? buyRequestStatus) {
    switch (buyRequestStatus) {
      case BuyRequestStatus.PENDING:
        return LocaleKeys.buy_requests_in_progress.tr;
      case BuyRequestStatus.UPDATED:
        return LocaleKeys.buy_requests_in_progress.tr;
      case BuyRequestStatus.ACCEPTED:
        return LocaleKeys.my_order_detail_accepted.tr;
      case BuyRequestStatus.CANCELED:
        return LocaleKeys.my_orders_cancelled.tr;
      case BuyRequestStatus.AUTOCANCELED:
        return LocaleKeys.my_orders_cancelled.tr;
      default:
        return '';
    }
  }

  /// status chip color
  static Color getStatusColor(BuyRequestStatus? buyRequestStatus) {
    switch (buyRequestStatus) {
      case BuyRequestStatus.PENDING:
        return AppColors.k70777E;
      case BuyRequestStatus.UPDATED:
        return AppColors.k70777E;
      case BuyRequestStatus.ACCEPTED:
        return AppColors.k128807;
      case BuyRequestStatus.CANCELED:
        return AppColors.kFFE4E4;
      case BuyRequestStatus.AUTOCANCELED:
        return AppColors.kFFE4E4;
      default:
        return AppColors.k101C28;
    }
  }

  /// status chip color
  static Color getStatusTextColor(BuyRequestStatus? buyRequestStatus) {
    switch (buyRequestStatus) {
      case BuyRequestStatus.PENDING:
        return AppColors.kffffff;
      case BuyRequestStatus.UPDATED:
        return AppColors.kffffff;
      case BuyRequestStatus.ACCEPTED:
        return AppColors.kffffff;
      case BuyRequestStatus.CANCELED:
        return AppColors.k101C28;
      case BuyRequestStatus.AUTOCANCELED:
        return AppColors.k101C28;
      default:
        return AppColors.k101C28;
    }
  }
}
