import 'package:flutter/services.dart';

/// Remove only input formatter
class RemoveOnlyInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Allow the change if the new length is less than the old length (deletion)
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }
    // Otherwise, return the old value (no change allowed)
    return oldValue;
  }
}
