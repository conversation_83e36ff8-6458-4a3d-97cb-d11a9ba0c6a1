import 'package:diamond_company_app/app/data/models/melle_attributes.dart';
import 'package:get/get.dart';

/// melee ext
extension MeleeExt on MelleAttributes {
  /// is show cart
  RxBool get showCart => (pricePerCaret ?? 0).isGreaterThan(0).obs;

  /// seive size list
  List<String> get seiveSizeList => (measurements ?? [])
      .map((e) => (e.sieveSize?.isEmpty ?? false) ? '-' : e.sieveSize ?? '')
      .toList();

  /// Check if all sieve sizes are empty or replaced with '-'
  bool get areAllSieveSizesEmpty =>
      seiveSizeList.every((String sieveSize) => sieveSize.isEmpty);

  /// milimeter list
  List<String> get milimeterList => (measurements ?? [])
      .map((e) => (e.milimeter?.isEmpty ?? false) ? '-' : e.milimeter ?? '')
      .toList();

  /// pointer list
  List<String> get pointerList => (measurements ?? [])
      .map((e) => (e.pointer?.isEmpty ?? false) ? '-' : e.pointer ?? '')
      .toList();
}
