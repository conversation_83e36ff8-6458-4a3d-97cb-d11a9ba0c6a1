import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:get/get.dart';

/// Create vendor utils
class CreateVendorUtils {
  /// First name
  static String firstName = 'First Name';

  /// Last name
  static String lastName = 'Last Name';

  /// Email
  static String email = 'Email';

  /// Phone
  static String phone = 'Phone';

  /// Password
  static String password = 'Password';

  /// API URL
  static String apiURL = 'API URL';

  /// Company name
  static String companyName = 'Company Name';

  /// Bank name
  static String bankName = 'Bank Name';

  /// Account holder
  static String accountHolder = 'Account Holder Name';

  /// Account number
  static String accountNumber = 'Account Number';

  /// Ifsc coded
  static String ifscCode = 'IFSC Code';

  /// Street
  static String street = 'Street';

  /// City
  static String city = 'City';

  /// State
  static String state = 'State';

  /// Country
  static String country = 'Country';

  /// Zip code
  static String zipCode = 'Zip Code';

  /// Margin Percentage
  static String marginPercentage = 'Margin Percentage';

  /// Validate first name
  static String? validateFirstName(String? value) {
    if (value == null || value.isEmpty) {
      return 'First name is required';
    }
    return null;
  }

  /// Validate last name
  static String? validateLastName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Last name is required';
    }
    return null;
  }

  /// Validate email
  static String? validateEmail(String? value) {
    final String? trimmedValue = value?.trim();
    if (trimmedValue?.isEmpty ?? true) {
      return LocaleKeys.validation_email_is_empty.tr;
    } else if (!trimmedValue!.isEmail) {
      return LocaleKeys.validation_email_is_invalid.tr;
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password should be at least 6 characters';
    }
    return null;
  }

  /// Validate company name
  static String? validateCompanyName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Company name is required';
    }
    return null;
  }

  /// Validate company name
  static String? validateApiURL(String? value) {
    if (value == null || value.isEmpty) {
      return 'API URL is required';
    } else if (!GetUtils.isURL(value ?? '')) {
      return 'Invalid URL';
    }
    return null;
  }

  /// Validate stock upload type
  static String? validateStockUploadType(dynamic value) {
    if (value == null || value.isEmpty) {
      return 'Stock upload type is required';
    }
    return null;
  }

  /// Validate margin percentage
  static String? validateMarginPercentage(String? value) {
    if (value == null || value.isEmpty) {
      return 'Margin percentage is required';
    }
    return null;
  }

  /// Validate bank name
  static String? validateBankName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Bank name is required';
    }
    return null;
  }

  /// Validate account holder name
  static String? validateAccountHolder(String? value) {
    if (value == null || value.isEmpty) {
      return 'Account holder name is required';
    }
    return null;
  }

  /// Validate Account number
  static String? validateAccountNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Account number is required';
    }
    return null;
  }

  /// Validate ifsc code
  static String? validateIfscCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'IFSC code is required';
    }
    return null;
  }

  /// Validate Phone Number
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }

    if (value.trim().length > 15 || value.trim().length < 7) {
      return 'Please enter valid mobile number';
    }
    return null;
  }
}
