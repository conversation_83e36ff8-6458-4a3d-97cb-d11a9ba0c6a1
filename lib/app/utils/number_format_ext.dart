import 'package:intl/intl.dart';

/// Number format extension
extension NumberFormatExtension on String {
  /// Convert number to price format with two fractional digits
  String toPrice({String locale = 'en_US', String symbol = '\$'}) {
    final NumberFormat format = NumberFormat.currency(
      locale: locale,
      symbol: symbol,
      decimalDigits: 2,
    );
    final double number = double.tryParse(this) ?? 0;
    if (number <= 0) {
      //return 'Free';
      return format.format(0);
    }
    return format.format(number);
  }

  /// Convert number to tax price format with two fractional digits
  double toTaxPrice({double tax = 0}) {
    if (tax == 0) {
      return 0;
    }
    final double number = (double.tryParse(this) ?? 0) * (tax / 100);
    return number;
  }
}
