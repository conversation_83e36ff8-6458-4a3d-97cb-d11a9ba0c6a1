import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/stock_offer/stock_offer.dart';
import 'package:diamond_company_app/app/ui/components/app_text_style.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Get the status icon based on the status
Widget getOfferStatusWidget(OfferStatus? status) => StatusChip(
      text: getOfferStatusText(status),
      textStyle: AppFontStyles.skolaSans(
        fontSize: 35.sp,
        fontWeight: FontWeight.w500,
        color: AppColors.k101C28,
      ),
      color: getOfferStatusColor(status),
    );

/// Get the status color based on the status
Color getOfferStatusColor(OfferStatus? status) {
  switch (status) {
    case OfferStatus.REVISED:
      return AppColors.kE1E5FF;
    case OfferStatus.PENDING:
      return AppColors.kFFF4CC;
    case OfferStatus.ACCEPTED:
      return AppColors.kD6FFE9;
    case OfferStatus.REJECTED:
      return AppColors.kFFE4E4;
    default:
      return AppColors.kffffff;
  }
}

/// Get the status color based on the status
String getOfferStatusText(OfferStatus? status) {
  switch (status) {
    case OfferStatus.REVISED:
      return 'Revised';
    case OfferStatus.PENDING:
      return 'Pending';
    case OfferStatus.ACCEPTED:
      return 'Accepted';
    case OfferStatus.REJECTED:
      return 'Declined';
    default:
      return '';
  }
}
