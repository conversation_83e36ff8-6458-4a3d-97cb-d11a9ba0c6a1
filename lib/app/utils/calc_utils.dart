import 'package:flutter/material.dart';

var shapeList = ['round', 'pear'];

var showShapeList = [
  'round',
  'pear',
  'princess',
  'radiant',
  'asscher',
  'emerald',
  'marquise',
  'baguette',
  'heart',
  'cushion',
  'triangle',
  'oval',
  'other',
];

var colorList = ['d', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm'];

var clarityList = [
  'if',
  'vvs1',
  'vvs2',
  'vs1',
  'vs2',
  'si1',
  'si2',
  'si3',
  'i1',
  'i2',
  'i3'
];

var months = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec'
];

List<List<Color>> gradients = const [
  [
    Color(0xFFFFC3A0),
    Color(0xFFFFAFBD),
  ],
  [
    Color(0xFFA8FF78),
    Color(0xFF78FFD6),
  ],
  [
    Color(0xFFb721ff),
    Color(0xFF21d4fd),
  ],
  [
    Color(0xFFFFFEFF),
    Color(0xFFD7FFFE),
  ],
  [
    Color(0xFFCAC531),
    Color(0xFFF3F9A7),
  ],
  [
    Color(0xFF74ebd5),
    Color(0xFFACB6E5),
  ],
  [
    Color(0xFFB2FEFA),
    Color(0xFF0ED2F7),
  ],
];

class CalcUtils {
  static String parseShape(String shape) {
    String shapeLower = shape.toLowerCase();

    Map<String, List<String>> shapeMap = {
      'round': [
        'round',
        'rd',
        'rbc',
        'br',
        'rnd',
        'b',
        'brilliant',
        'round brilliant'
      ],
      'pear': [
        'pear',
        'ps',
        'p',
        'pear brilliant',
        'pear br',
        'pear shape',
        'psh',
        'pb',
        'pmb',
        'pear modified brilliant',
      ],
      'princess': [
        'princess',
        'pr',
        'prn',
        'prin',
        'pn',
        'pc',
        'princess cut',
        'mdsqb',
        'smb',
      ],
      'radiant': [
        'radiant',
        'ra',
        'r',
        'rc',
        'rad',
        'square radiant',
        'cut cornered rectangular modified brilliant',
        'rdn',
        'crb',
        'rcrb',
        'sq ra',
        'sq rad',
        'sq radiant',
        'square ra',
        'square radiant',
      ],
      'asscher': [
        'asscher',
        'asher',
        'as',
        'ash',
        'a',
        'ac',
        'css',
        'cssc',
      ],
      'emerald': [
        'emerald',
        'ec',
        'em',
        'e',
        'sqe',
        'sec',
        'sqec',
        'sq em',
        'sqem',
        'square em',
        'square emerald',
        'sq emerald',
      ],
      'marquise': [
        'marquise',
        'mq',
        'm',
        'marq',
        'mqb',
      ],
      'baguette': [
        'baguette',
        'bg',
        'bag',
      ],
      'heart': [
        'heart',
        'hs',
        'h',
        'heart shape',
        'heart brilliant',
        'heart br',
        'ht',
        'mhrc',
      ],
      'cushion': [
        'cushion',
        'cu',
        'cus',
        'c',
        'cush',
        'cushion modified',
        'cm',
        'cushion modified brilliant',
        'cmb',
        'cushion mod br',
        'cus mod br',
        'cushion mod',
        'cus mod',
        'cushion brilliant',
        'cub',
        'cubr',
        'cushion br',
        'cushion brilliant',
        'cm-b',
        'lg-cmb',
      ],
      'triangle': [
        'triangle',
        'tr',
        't',
        'tril',
        'trill',
        'trib',
        'trl',
        'triangle',
        'triangular',
        'tzmb',
        'tpmb',
        'tp',
        'trap',
        'trapb',
        'tz',
        'tri',
        'mtrb',
      ],
      'oval': [
        'oval',
        'ov',
        'o',
        'os',
        'oval brilliant',
        'oval br',
        'oval shape',
        'ov br',
        'omb',
      ],
      'other': [
        'other',
        'ot',
        'x',
      ],
    };

    for (var key in shapeMap.keys) {
      if (shapeMap[key]?.contains(shapeLower) ?? false) {
        return key;
      }
    }

    return 'other';
  }

  static String? parseClarity(String clarity) {
    String compareClarity =
        ['flawless', 'fl', 'lc', 'if'].contains(clarity.toLowerCase())
            ? 'if'
            : clarity.toLowerCase();

    if (clarityList.contains(compareClarity)) {
      return compareClarity;
    }
    return null;
  }

  static String? parseColor(String color) {
    if (colorList.contains(color.toLowerCase())) {
      return color.toLowerCase();
    }
    return null;
  }
}
