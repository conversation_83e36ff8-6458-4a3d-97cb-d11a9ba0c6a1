/// registration utils class
class RegistrationFormFields {
  /// company legal name
  static String companyLegalName = 'Company Legal Name';

  /// company operating name
  static String tradeStyle = 'Trade Style';

  /// fed tax id
  static String fedTaxId = 'Fed Tax ID';

  /// resale tax
  static String resaleTax = 'Resale Tax';

  /// jbt id
  static String jbtId = 'JBT ID';

  /// has aml program
  static String hasAmLprogram = 'Has AML program';

  /// how they heard
  static String howTheyHeard = 'How They Heard';

  /// facebook id
  static String facebookId = 'Facebook ID';

  /// instagram id
  static String instagramId = 'Instagram ID';

  /// type of business
  static String typeOfBusiness = 'Type of Business';

  /// business start date
  static String businessStartDate = 'Business Start Date';

  /// years at present location
  static String yearsAtPresentLocation = 'Years at Present Location';

  /// legal organization status
  static String legalOrganizationStatus = 'Legal Organization Status';

  /// order authorized person
  static String orderAuthorizedPerson = 'Order Authorized Person';

  /// document name
  static String documentName = 'Document Name';

  /// trade references
  static String tradeReferences = 'Trade References';

  /// business entity name
  static String businessEntityName = 'Business Entity Name';

  /// signature image name
  static String signatureImageName = 'Signature Image Name';

  /// sign date
  static String signDate = 'Sign Date';

  /// print name
  static String printName = 'Print Name';

  /// physical address street
  static String physical_adress_street = 'Physical address street';

  /// physical address street one
  static String physical_adress_line_two = 'Physical address line two';

  /// physical address state
  static String physical_adress_state = 'Physical address state';

  /// physical address city
  static String physical_adress_city = 'Physical address city';

  /// physical address country
  static String physical_adress_country = 'Physical address country';

  /// physical address zip code
  static String physical_adress_zip_code = 'physical address zip code';

  /// mailing address street
  static String mailing_adress_street = 'mailing address street';

  /// mailing address street one
  static String mailing_adress_line_two = 'mailing address line two';

  /// mailing address state
  static String mailing_adress_state = 'mailing address state';

  /// mailing address city
  static String mailing_adress_city = 'mailing address city';

  /// mailing address country
  static String mailing_adress_country = 'mailing address country';

  /// mailing address zip code
  static String mailing_adress_zip_code = 'mailing address zip code';

  /// phone
  static String phone = 'phone';

  /// mobile
  static String mobile = 'mobile';

  /// fax
  static String fax = 'fax';

  /// website
  static String website = 'website';

  /// email
  static String email = 'email';

  /// incorporation country
  static String registrationOrIncorporationCountry =
      'Registration Or Incorporation Country';

  /// incorporation state
  static String registrationOrIncorporationState =
      'Registration Or Incorporation State';

  /// type
  static String type = 'Type';

  /// first name
  static String firstName = 'First Name';

  /// last name
  static String lastName = 'Last Name';

  /// order authorized person first name
  static String orderAuthorizedPersonFirstName =
      'Order Authorized Person First Name';

  /// order authorized person last name
  static String orderAuthorizedPersonLastName =
      'Order Authorized Person Last Name';

  /// order authorized person title
  static String orderAuthorizedPersonTitle = 'Order Authorized Person Title';

  /// order authorized person phone
  static String orderAuthorizedPersonPhone = 'Order Authorized Person Phone';

  /// trade references company name
  static String tradeReferenceCompanyName = 'Trade References Company Name';

  /// trade references contact person name
  static String tradeReferenceContactPersonName =
      'Trade references contact person name';

  /// trade references phone
  static String tradeReferencePhone = 'Trade references phone';

  /// trade references email
  static String tradeReferenceEmail = 'Trade references email';

  /// cart billing address line1
  static String cartBillingAddressLineOne = 'cart billing address line1';

  /// cart billing address line2
  static String cartBillingAddressLineTwo = 'cart billing address line2';

  /// cart billing address line1
  static String cartShippingAddressLineOne = 'cart shipping address line1';

  /// cart billing address line2
  static String cartShippingAddressLineTwo = 'cart shipping address line2';

  /// cart billing address first name
  static String cartBillingAddressFirstName = 'cart billing address first name';

  /// cart billing address last name
  static String cartBillingAddressLastName = 'cart billing address last name';

  /// cart billing address mobile
  static String cartBillingAddressMobile = 'cart billing address mobile';

  /// cart physical address Mobile
  static String cartPhysicalAddressMobile = 'cart physical address Mobile';
}

/// custom autofill hints
class CustomAutofillHints {
  /// company legal name
  static String companyLegalName = 'company_legal_name';

  /// DBA trade style
  static String dbaTradeStyle = 'dba_trade_style';

  /// fed tax id
  static String fedTaxId = 'fed_tax_id';

  /// resale tax
  static String resaleTax = 'resale_tax';

  /// jbt id
  static String jbtId = 'jbt_id';

  /// full name
  static String fullName = 'full_name';

  /// search dashboard
  static String search = 'search';

  /// address line one
  static String addressLineOne = 'address_line_one';

  /// address line two
  static String addressLineTwo = 'address_line_two';

  /// country
  static String country = 'country';

  /// zipcode
  static String zipcode = 'zipcode';

  /// state
  static String state = 'state';

  /// city
  static String city = 'city';

  /// first name
  static String firstName = 'first_name';

  /// last name
  static String lastName = 'last_name';

  /// phone
  static String phone = 'phone';

  /// telephone
  static String telephone = 'telephone';

  /// fax
  static String fax = 'fax';

  /// website
  static String website = 'website';

  /// email
  static String email = 'email';

  /// facebook profile
  static String facebookProfile = 'facebook_profile';

  /// instagram profile
  static String instagramProfile = 'instagram_profile';

  /// years at present location
  static String yearsAtPresentLocation = 'years_at_present_location';
}
