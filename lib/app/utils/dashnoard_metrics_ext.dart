import 'package:diamond_company_app/app/data/models/dashboard_metrics/dashboard_metrics.dart';

/// extension
extension DashboardExtensions on DashboardMetrics {}

/// extension gold prices
extension GoldPricesExtensions on GoldPrices {
  /// Calculates the change percent for XAU (gold) with two decimal precision.
  double get calculateXauChangePercent {
    if (chgXau == null || xauClose == null || xauClose == 0) {
      return 0; // Return 0 if data is missing or invalid
    }
    return double.parse(((chgXau! / xauClose!) * 100).toStringAsFixed(2));
  }

  /// Calculates the change percent for XAG (silver) with two decimal precision.
  double get calculateXagChangePercent {
    if (chgXag == null || xagClose == null || xagClose == 0) {
      return 0; // Return 0 if data is missing or invalid
    }
    return double.parse(((chgXag! / xagClose!) * 100).toStringAsFixed(2));
  }
}
