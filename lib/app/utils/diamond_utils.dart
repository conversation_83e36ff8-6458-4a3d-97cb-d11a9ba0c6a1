import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:flutter/material.dart' show Colors, Color;

class DiamondUtils {
  static Color statusColor(String status) {
    switch (status) {
      case 'A':
        return AppColors.k128807;
      case 'H':
        return Colors.amber;
      case 'M':
        return AppColors.k7C0B0B;
      case 'S':
        return AppColors.k1A47E8;
      default:
        return AppColors.k111111;
    }
  }

  static String? getFluoAbbreviation(String? value) {
    if (value == null) {
      return null;
    }

    var abbreviations = {
      'none': 'N',
      'very slight': 'VSL',
      'slight': 'SL',
      'faint': 'F',
      'medium': 'M',
      'strong': 'S',
      'very strong': 'VS'
    };

    return abbreviations[value.toLowerCase()] ?? value;
  }

  static String getFinishAbbreviation(String? finish) {
    Map<String, List<String>> finishMap = {
      'id': ['id', 'i', 'ideal'],
      '8x': ['8x', '8 excellent', '8x excellent', '8x ex', '8x excel'],
      'ex': ['ex', 'excellent', 'x', 'excel', 'excell'],
      'vg': ['vg', 'very good', 'very g', 'v good', 'v. good'],
      'gd': ['gd', 'good', 'g'],
      'f': ['f', 'fair', 'fr'],
      'p': ['p', 'poor', 'pr'],
    };

    if (finish != null && finish.isNotEmpty) {
      for (var key in finishMap.keys) {
        if (finishMap[key]!.contains(finish.toLowerCase())) {
          return key.toUpperCase();
        }
      }
    }

    return finish?.isEmpty ?? true ? '-' : (finish ?? '-').toUpperCase();
  }

  static String getShapeAbbreviation(String? shape) {
    String shapeLower = shape?.toLowerCase() ?? '';

    Map<String, List<String>> shapeMap = {
      'rd': [
        'round',
        'rd',
        'rbc',
        'br',
        'rnd',
        'b',
        'brilliant',
        'round brilliant'
      ],
      'ps': [
        'pear',
        'ps',
        'p',
        'pear brilliant',
        'pear br',
        'pear shape',
        'psh',
        'pb',
        'pmb',
      ],
      'pr': [
        'princess',
        'pr',
        'prn',
        'prin',
        'pn',
        'pc',
        'princess cut',
        'mdsqb',
        'smb',
      ],
      'rad': [
        'r',
        'rad',
        'ra',
        'rc',
        'rdn',
        'crb',
        'rcrb',
        'radiant',
        'cut cornered rectangular modified brilliant',
      ],
      'sq rad': [
        'square radiant',
        'sq ra',
        'sq rad',
        'sq radiant',
        'square ra',
        'square radiant',
        'sqr',
        'ccsmb',
      ],
      'as': [
        'asscher',
        'as',
        'ash',
        'a',
        'ac',
        'css',
        'cssc',
      ],
      'em': ['emerald', 'ec', 'em', 'e'],
      'sq em': [
        'sqe',
        'sec',
        'sqec',
        'sq em',
        'sqem',
        'square em',
        'square emerald',
        'sq emerald',
        'sx',
      ],
      'mq': [
        'marquise',
        'marquise brilliant',
        'mq',
        'm',
        'marq',
        'mqb',
      ],
      'bag': ['baguette', 'bg', 'bag'],
      'hs': [
        'heart',
        'hs',
        'h',
        'heart shape',
        'heart brilliant',
        'heart br',
        'ht',
        'mhrc',
      ],
      'cus': [
        'cushion',
        'cu',
        'cus',
        'c',
        'cush',
      ],
      'cu br': [
        'cushion brilliant',
        'cub',
        'cubr',
        'cushion br',
        'cushion brilliant',
      ],
      'cus mod': [
        'cushion modified',
        'cm',
        'cushion mod',
        'cus mod',
      ],
      'cus mod br': [
        'cushion modified brilliant',
        'cmb',
        'cushion mod br',
        'cus mod br',
      ],
      'tbag': ['tbag', 'tapered baguette'],
      'tbu': ['tbu', 'tapered bullet brilliant', 'tapered bullet'],
      'brio': [
        'briolette',
        'brio',
        'briolet',
        'bt',
      ],
      'bu': ['bu', 'bullets'],
      'tri': [
        'triangle',
        'triangular',
        'tri',
        'mtrb',
        'tr',
      ],
      'trap': [
        'trapezoid',
        'tp',
        'trap',
        'trapb',
        'tz',
        'trapeze',
      ],
      'tril': [
        'trilliant',
        't',
        'tril',
        'trill',
        'trib',
        'trl',
        'mtri',
        'trmb',
        'trsc',
      ],
      'ov': [
        'oval',
        'ov',
        'o',
        'os',
        'oval brilliant',
        'oval br',
        'oval shape',
        'ov br',
        'omb',
      ],
    };

    for (var key in shapeMap.keys) {
      if (shapeMap[key]?.contains(shapeLower) ?? false) {
        return key.toUpperCase();
      }
    }

    return shape?.isEmpty ?? true ? '-' : (shape ?? '-').toUpperCase();
  }
}
