import 'dart:io';
import 'dart:typed_data';

import 'package:diamond_company_app/app/ui/components/app_snackbar.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

Future<void> share({
  Uint8List? file,
  String? subject,
  String? text,
  String? mimeType,
  String? fileName,
}) async {
  if (file != null) {
    final XFile xFile = XFile.fromData(
      file,
      name: fileName,
      mimeType: mimeType,
    );

    final String path = (await getTemporaryDirectory()).path;
    final String fullPath = '$path/$fileName';
    if (fileName != null) {
      await xFile.saveTo(fullPath);
    }

    await Share.shareXFiles(
      <XFile>[
        fileName != null ? XFile(fullPath) : xFile,
      ],
      subject: Platform.isIOS ? null : subject,
      text: Platform.isIOS ? null : text,
    );
  } else if (text != null) {
    await Share.share(
      text,
      subject: subject,
    );
  }
}

/// Share image from url
Future<void> shareImageUrl({
  required String imageUrl,
}) async {
  try {
    final File imageFile = await DefaultCacheManager().getSingleFile(imageUrl);

    await share(
      file: imageFile.readAsBytesSync(),
      fileName: 'diamond.jpg',
      mimeType: 'image/jpeg',
    );
  } on Exception {
    appSnackbar(
      message: 'File not found',
      snackBarState: SnackBarState.WARNING,
    );
  }
}
