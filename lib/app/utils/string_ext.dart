import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:get/get_utils/get_utils.dart';

extension StrExt on String {
  /// Get a double value from the string with support for comma
  double get toDouble => double.tryParse(replaceAll(',', '.')) ?? 0.0;

  String get insertComma => isEmpty ? '' : '$this,';

  String get firstChar {
    if (isEmpty) {
      return '';
    } else {
      return this[0];
    }
  }

  /// avg from range
  /// Calculates the average from a range string (e.g., "0.3-0.45" or ".3-.45").
  double get averageFromRange {
    // Regular expression to extract numbers (including decimals) from the string
    final regex = RegExp(r'(\d*\.?\d+)-(\d*\.?\d+)');

    // Match the numbers using the regex
    final match = regex.firstMatch(this);

    if (match != null) {
      // Extract the two numbers from the match
      final firstNumber = double.parse(match.group(1)!);
      final secondNumber = double.parse(match.group(2)!);

      // Return the average of the two numbers
      return (firstNumber + secondNumber) / 2;
    } else {
      return 0;
      //throw FormatException('The string does not contain a valid range.');
    }
  }

  /// Extract digits and decimals from the string, returning as a [double].
  double get toDigits {
    final RegExp regExp = RegExp(
        r'\d+(\.\d+)?'); // Match digits, optionally followed by a decimal and more digits
    final Iterable<Match> matches = regExp.allMatches(this);

    // Concatenate all the digits and decimals found into a single string
    final String digits = matches.map((Match match) => match.group(0)!).join();

    // If no digits found, return 0.0
    return digits.isEmpty ? 0.0 : double.parse(digits);
  }

  /// Sentence capitalization
  String get firstCharCapital =>
      isEmpty ? '' : '${this[0].toUpperCase()}${substring(1)}';

  /// Word Capitalization
  String get wordCapital =>
      isEmpty ? '' : split(' ').map((e) => e.firstCharCapital).join(' ');

  /// Shorten the full name
  String shortenFullName(int maxLength) {
    if (length <= maxLength) {
      return this;
    } else {
      return "${substring(0, maxLength)}...";
    }
  }

  /// Remove comma from string
  String get comaValidation => isEmpty ? '' : '${trim()},';
}

extension UrlChecker on String {
  /// Check if the string is a valid URL
  bool isImageUrl() {
    final String lowerCaseUrl = toLowerCase();
    return lowerCaseUrl.endsWith('.png') ||
        lowerCaseUrl.endsWith('.jpg') ||
        lowerCaseUrl.endsWith('.jpeg') ||
        lowerCaseUrl.endsWith('.gif') ||
        lowerCaseUrl.endsWith('.bmp') ||
        lowerCaseUrl.endsWith('.wbmp') ||
        lowerCaseUrl.endsWith('.webp') ||
        lowerCaseUrl.endsWith('.heic') ||
        lowerCaseUrl.endsWith('.heif') ||
        lowerCaseUrl.endsWith('.tiff') ||
        lowerCaseUrl.endsWith('.tif') ||
        lowerCaseUrl.endsWith('.ico') ||
        lowerCaseUrl.startsWith('data:image');
  }
}

class StringUtils {
  /// List<String?> to comma separated string. Null or empty string will be removed
  static String listToString(
    List<String?> list, {
    String separator = ',',
    bool upperCase = false,
  }) {
    return list
        .where((element) => element?.isNotEmpty ?? false)
        .map((e) => upperCase ? e?.toUpperCase() : e)
        .join(separator);
  }
}

extension StringValidators on String {
  String? containsSpecialCharacter() {
    const specialCharacters = r"!@#$&*~%^()_+=[]{}|\\:;\,.<>?/`-";
    return this.split('').any((char) => specialCharacters.contains(char))
        ? null
        : LocaleKeys.validation_password_special_character.tr;
  }

  String? containsUppercase() {
    return this.contains(RegExp(r'[A-Z]'))
        ? null
        : LocaleKeys.validation_password_uppercase.tr;
  }

  String? containsLowercase() {
    return this.contains(RegExp(r'[a-z]'))
        ? null
        : LocaleKeys.validation_password_lowercase.tr;
  }

  String? containsNumeric() {
    return this.contains(RegExp(r'[0-9]'))
        ? null
        : LocaleKeys.validation_password_numeric.tr;
  }

  String? isValidPassword() {
    if (this.length < 6) {
      return LocaleKeys.validation_password_length.tr;
    }

    return this.containsUppercase() ??
        this.containsLowercase() ??
        this.containsNumeric() ??
        this.containsSpecialCharacter();
  }
}

extension ShapeHexCode on String {
  int toHexCode() {
    switch (toLowerCase()) {
      case 'round':
        return 0xe934;
      case 'oval':
        return 0xe926;
      case 'pear':
        return 0xe928;
      case 'cushion':
        return 0xe908;
      case 'cush mod':
        return 0xe908;
      case 'cushion modified':
        return 0xe908;
      case 'cush brill':
        return 0xea87;
      case 'cushion brilliant':
        return 0xea87;
      case 'emerald':
        return 0xe93e;
      case 'radiant':
        return 0xe92e;
      case 'princess':
        return 0xe92c;
      case 'asscher':
        return 0xe901;
      case 'square':
        return 0xe93a;
      case 'marquise':
        return 0xe91e;
      case 'marquies':
        return 0xe91e;
      case 'heart':
        return 0xe916;
      case 'trilliant':
        return 0xe948;
      case 'trillion':
        return 0xe948;
      case 'euro cut':
        return 0xe90e;
      case 'old miner':
        return 0xe922;
      case 'briolette':
        return 0xe904;
      case 'rose cut':
        return 0xe930;
      case 'lozenge':
        return 0xe91c;
      case 'baguette':
        return 0xe903;
      case 'straight baguette':
        return 0xe903;
      case 't.baguette':
        return 0xea89;
      case 'tapered baguette':
        return 0xea89;
      case 'half moon':
        return 0xe912;
      case 'flanders':
        return 0xe910;
      case 'trapezoid':
        return 0xe942;
      case 'bullets':
        return 0xe906;
      case 'kite':
        return 0xe91a;
      case 'shield':
        return 0xe937;
      case 'star':
        return 0xe93c;
      case 'pentagonal':
        return 0xe92a;
      case 'hexagonal':
        return 0xe918;
      case 'octagonal':
        return 0xe920;
      case 'other':
        return 0x0; // Using 0x0 for unknown cases
      default:
        return 0x0;
    }
  }
}
