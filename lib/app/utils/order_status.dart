import 'package:diamond_company_app/app/data/config/app_colors.dart';
import 'package:diamond_company_app/app/data/models/user_order/user_order.dart';
import 'package:diamond_company_app/app/ui/components/status_chip.dart';
import 'package:diamond_company_app/generated/locales.g.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

/// Get the status icon based on the status
Widget getStatusWidget(OrdersStatus status) {
  if (status == OrdersStatus.PENDING) {
    return StatusChip(
        text: LocaleKeys.my_orders_processing.tr,
        color: getStatusColor(status));
  } else {
    return StatusChip(
      text: getStatusString(status),
      color: getStatusColor(status),
    );
  }
}

/// Get the status color based on the status
Color getStatusColor(OrdersStatus status) {
  switch (status) {
    case OrdersStatus.PENDING:
      return AppColors.kFF9800;
    case OrdersStatus.PROCESSING:
      return AppColors.kA266D0;
    case OrdersStatus.SHIPPED:
      return AppColors.k0B127C;
    case OrdersStatus.DELIVERED:
      return AppColors.k128807;
    case OrdersStatus.CANCELED:
      return AppColors.k7C0B0B;
    default:
      return AppColors.kffffff;
  }
}

/// Get the status based on the status
String getStatusString(OrdersStatus status) {
  switch (status) {
    case OrdersStatus.PENDING:
      return LocaleKeys.my_orders_processing.tr;
    case OrdersStatus.PROCESSING:
      return LocaleKeys.my_orders_processing.tr;
    case OrdersStatus.SHIPPED:
      return LocaleKeys.my_orders_shipped.tr;
    case OrdersStatus.DELIVERED:
      return LocaleKeys.my_orders_delivered.tr;
    case OrdersStatus.CANCELED:
      return LocaleKeys.my_orders_canceled.tr;
    default:
      return '';
  }
}
