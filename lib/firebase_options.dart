// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:mysite/core/config/firebase_config.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// SECURITY NOTE: This now uses SecureFirebaseConfig to avoid exposing
/// sensitive API keys in source code.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    return SecureFirebaseConfig.currentPlatform;
  }

  // Legacy getters for backward compatibility
  static FirebaseOptions get web => SecureFirebaseConfig.web;
  static FirebaseOptions get android => SecureFirebaseConfig.android;
  static FirebaseOptions get ios => SecureFirebaseConfig.ios;
  static FirebaseOptions get macos => SecureFirebaseConfig.macos;
  static FirebaseOptions get windows => SecureFirebaseConfig.windows;
}
