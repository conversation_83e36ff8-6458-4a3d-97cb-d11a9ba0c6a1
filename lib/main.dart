import 'dart:async';
import 'dart:math';

import 'package:catcher_2/catcher_2.dart';
import 'package:diamond_company_app/app/data/config/api_constants.dart';
import 'package:diamond_company_app/app/data/config/design_config.dart';
import 'package:diamond_company_app/app/data/config/error_handling.dart';
import 'package:diamond_company_app/app/data/config/initialize_app.dart';
import 'package:diamond_company_app/app/data/config/translation_api.dart';
import 'package:diamond_company_app/app/data/local/locale_provider.dart';
import 'package:diamond_company_app/app/data/models/cart_item/cart_item_helper.dart';
import 'package:diamond_company_app/app/modules/cart_updated/services/cart_item_service.dart';
import 'package:diamond_company_app/app/utils/app_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'app/data/config/app_themes.dart';
import 'app/routes/app_pages.dart';
import 'app/ui/components/custom_sroll_behaviour.dart';
import 'app/utils/web_utils.dart';

Future<void> main() async {
  // Ensure Flutter binding is initialized first
  WidgetsFlutterBinding.ensureInitialized();

  /// Debug configuration with dialog report mode and console handler.
  /// It will show dialog and once user accepts it, error will be shown
  /// in console.
  final Catcher2Options debugOptions = Catcher2Options(
    SilentReportMode(),
    <ReportHandler>[
      ConsoleHandler(),
    ],
    handleSilentError: false,
  );

  /// Release configuration. Same as above, but once user accepts dialog,
  /// user will be prompted to send email with crash to support.
  final Catcher2Options releaseOptions = Catcher2Options(
    SilentReportMode(),
    <ReportHandler>[
      DiscordHandler(
        'https://ptb.discord.com/api/webhooks/850803931922956296/ntypKkhc1XnGLxy01FZLmwCecy0VE3xZQH_5phYR3zb9Izs4aosSfcJ74_IED3BbdHDP',
        printLogs: true,
        enableApplicationParameters: true,
        enableDeviceParameters: true,
        enableStackTrace: true,
      ),
    ],
    handleSilentError: false,
  );

  Catcher2(
    debugConfig: debugOptions,
    releaseConfig: releaseOptions,
    ensureInitialized: true,
    navigatorKey: GlobalKey<NavigatorState>(),
    runAppFunction: () async {
      await runZonedGuarded(
        () async {
          EasyLoading.instance.userInteractions = false;
          Get.put(AppTranslations());
          TranslationApi.loadTranslations();
          await initializeCoreApp(
            // Set it to true when including firebase app
            developmentApiBaseUrl: ApiConstants.kBaseUrl,
            productionApiBaseUrl: ApiConstants.kBaseUrl,
            setupLocalNotifications: true,
            firebaseApp: false,
          );
          //await Get.putAsync(() => CartService().init());
          // Initialize CartItemHelper
          await CartItemHelper.instance.init();
          await Get.putAsync(() => CartItemService().init());
          runApp(
            const StartTheApp(),
          );
          if (kIsWeb) {
            // Remove the loader after Flutter has rendered
            removeLoader();
          }
        },
        (Object error, StackTrace trace) {
          letMeHandleAllErrors(error, trace);
          Catcher2.reportCheckedError(error, trace);
        },
      );
    },
  );

  //Restrict orientation to Portrait
  await SystemChrome.setPreferredOrientations(
    <DeviceOrientation>[
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ],
  );

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    systemNavigationBarColor: Colors.transparent,
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
  ));

  unawaited(SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge));

  ///To catch widget errors
  /*FlutterError.onError = (errorDetails) {
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      letMeHandleAllErrors(errorDetails.exception, errorDetails.stack);
      Catcher.reportCheckedError(errorDetails.exception, errorDetails.stack);
    });
  };*/
}

/// App starter
class StartTheApp extends StatelessWidget {
  /// Constructor
  const StartTheApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          hideKeyboard();
        },
        child: ScreenUtilInit(
          designSize: const Size(
            DesignConfig.kDesignWidth,
            DesignConfig.kDesignHeight,
          ),
          fontSizeResolver: (num fontSize, ScreenUtil instance) {
            // Custom scaling logic for font sizes
            final double scaleFactor =
                min(instance.screenWidth, DesignConfig.kMinWidthForFont) /
                    DesignConfig.kDesignWidth;

            return fontSize * scaleFactor; // Adjust font size based on width
          },

          rebuildFactor: (MediaQueryData old, MediaQueryData data) => true,
          //excludeWidgets: const ['Text', 'AppButton', 'SideMenu'],
          enableScaleText: () => false,
          enableScaleWH: () {
            // if (Get.currentRoute == Routes.LISTING) {
            //   return false;
            // }

            return true;
          },
          // enableScaleText: () => false,
          // ensureScreenSize: true,
          builder: (context, w) => GetMaterialApp(
            key: GlobalKey(),
            debugShowCheckedModeBanner: false,
            navigatorKey: Catcher2.navigatorKey,
            title: 'Diamond Company',
            initialRoute: AppPages.INITIAL,
            getPages: AppPages.routes,
            translationsKeys: Get.find<AppTranslations>().keys,
            translations: Get.find<AppTranslations>(),
            locale: LocaleProvider.currentLocale,
            fallbackLocale: const Locale('en_US'),
            defaultTransition: Transition.cupertino,
            theme: AppThemes.lightTheme,
            builder: EasyLoading.init(),
            scrollBehavior: CustomScrollBehavior(),
          ),
        ),
      );
}
