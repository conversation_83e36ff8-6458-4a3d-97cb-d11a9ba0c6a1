name: mysite
description: A new Flutter project.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=2.17.6 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # State management
  provider: ^6.0.1
  flutter_bloc: ^8.1.1

  # UI & Animation
  flutter_svg: ^1.1.5
  sizer: ^2.0.15
  carousel_slider: ^5.0.0
  animated_text_kit: ^4.2.1
  cached_network_image: ^3.3.1

  # Utilities
  url_launcher: ^6.1.2
  universal_html: ^2.0.4
  connectivity_plus: ^2.3.7

  # Firebase
  firebase_core: ^3.13.1
  firebase_auth: ^5.5.4
  cloud_firestore: ^5.6.8
  firebase_storage: ^12.4.6

  # File handling (Admin panel)
  file_picker: ^10.1.9

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets removed - all images now loaded from Firebase Storage
  # assets:

  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/montserrat/montserrat.ttf

    - family: Poppins
      fonts:
        - asset: assets/fonts/poppins/Poppins-Regular.ttf
        - asset: assets/fonts/poppins/Poppins-Light.ttf
        - asset: assets/fonts/poppins/Poppins-Bold.ttf
          weight: 900
        - asset: assets/fonts/poppins/Poppins-Medium.ttf
          weight: 300
        - asset: assets/fonts/poppins/Poppins-SemiBold.ttf
          weight: 500
        - asset: assets/fonts/poppins/Poppins-Italic.ttf
          style: italic
